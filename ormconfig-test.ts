import { ConnectionOptions } from 'typeorm';
import 'dotenv/config';

export default {
  type: process.env.DB_DRIVER as any,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: 'bina_test',
  synchronize: true,
  entities: ['src/modules/**/*.entity.ts'],
  migrations: ['database/migrations/**/*.*'],
  seeds: ['database/seeds/**/*{.ts,.js}'],
  factories: ['database/factories/**/*{.ts,.js}'],
  dropSchema: true,
  cli: {
    migrationsDir: 'database/migrations',
  },
} as ConnectionOptions;
