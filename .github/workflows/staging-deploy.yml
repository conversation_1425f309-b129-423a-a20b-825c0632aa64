name: Build and Deploy Docker Image

on:
  push:
    branches:
      - staging

jobs:
  build_and_publish_docker_image:
    name: Build and Deploy Docker Image

    runs-on: ubuntu-latest

    steps:
      - name: Check out repository
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Preset Image Name
        run: echo "IMAGE_URL=$(echo ${{ vars.DOCKERHUB_USERNAME }}/${{ vars.DOCKERHUB_REPO }}:$(echo ${{ github.sha }} | cut -c1-7) | tr '[:upper:]' '[:lower:]')-staging" >> $GITHUB_ENV

      - name: Build and push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ env.IMAGE_URL }}

  deploy_utils_node:
    needs: build_and_publish_docker_image
    name: Deploy Utils Node to run migrations
    runs-on: ubuntu-latest

    steps:
      - name: Check out repository
        uses: actions/checkout@v2

      - name: Preset Image Name
        run: echo "IMAGE_URL=$(echo ${{ vars.DOCKERHUB_USERNAME }}/${{ vars.DOCKERHUB_REPO }}:$(echo ${{ github.sha }} | cut -c1-7) | tr '[:upper:]' '[:lower:]')-staging" >> $GITHUB_ENV

      - name: Deploy Image to Caprover Utils Node
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: '${{ secrets.CAPROVER_SERVER }}'
          app: '${{ secrets.STAGING_UTIL_APP_NAME }}'
          token: '${{ secrets.STAGING_UTIL_APP_TOKEN }}'
          image: ${{ env.IMAGE_URL }}

      - name: Wait till util deploy is complete
        run: |
          while [[ "$(curl -u 'binacloud:binacloud' 'https://bina-be-staging-utils.devops.bina.cloud/api/system/system/migration')" != "$(ls ./database/migrations | tail -n 1 | cut -d'-' -f1)" ]]; do
            echo "$(curl -u 'binacloud:binacloud' 'https://bina-be-staging-utils.devops.bina.cloud/api/system/system/migration')"
            echo "$(ls ./database/migrations | tail -n 1 | cut -d'-' -f1)"
            echo "Waiting for migrations to complete..."
            sleep 5
          done

  deploy_staging:
    needs: deploy_utils_node
    name: Deploy Staging
    runs-on: ubuntu-latest

    steps:
      - name: Check out repository
        uses: actions/checkout@v2

      - name: Preset Image Name
        run: echo "IMAGE_URL=$(echo ${{ vars.DOCKERHUB_USERNAME }}/${{ vars.DOCKERHUB_REPO }}:$(echo ${{ github.sha }} | cut -c1-7) | tr '[:upper:]' '[:lower:]')-staging" >> $GITHUB_ENV

      - name: Deploy Image to Caprover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: '${{ secrets.CAPROVER_SERVER }}'
          app: '${{ secrets.STAGING_APP_NAME }}'
          token: '${{ secrets.STAGING_APP_TOKEN }}'
          image: ${{ env.IMAGE_URL }}
