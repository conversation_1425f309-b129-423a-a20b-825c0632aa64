# name: Test CI

# on:
#   push:
#     branches:
#       - main
#       - staging
# jobs:
#   build:
#     runs-on: ubuntu-latest

#     services:
#       mysql:
#         image: mysql:5.7.9
#         env:
#           MYSQL_ROOT_PASSWORD: bina
#           MYSQL_DATABASE: bina
#         ports:
#           - '3306:3306'
#         options: >-
#           --health-cmd "mysqladmin ping -ppass"
#           --health-interval 10s
#           --health-start-period 10s
#           --health-timeout 5s
#           --health-retries 10

#     steps:
#       - uses: actions/checkout@v4
#       - name: Use Node.js
#         uses: actions/setup-node@v3
#         with:
#           node-version: '18.x'
#       - name: Install dependencies
#         run: yarn --frozen-lockfile
#       - name: Create env file
#         run: |
#           echo "${{ secrets.TEST_ENV_FILE }}" > .env
#       - name: Output .env variables in base64
#         run: 'echo "$TEST_ENV_FILE" | base64'
#         shell: bash
#         env:
#            TEST_ENV_FILE: ${{ secrets.TEST_ENV_FILE }}
#       - name: Output .env
#         env:
#           TEST_ENV_FILE: ${{ toJson(secrets.TEST_ENV_FILE) }}
#         run: echo "$TEST_ENV_FILE"
#       - run: yarn install
#       - run: yarn build
#       - run: yarn typeorm schema:drop && yarn migrate && yarn seed
#       - run: yarn test
