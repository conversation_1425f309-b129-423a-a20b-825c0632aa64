version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        # Update 'VARIANT' to pick an LTS version of Node.js: 18, 16, 14.
        # Append -bullseye or -buster to pin to an OS version.
        # Use -bullseye variants on local arm64/Apple Silicon.
        VARIANT: 18-bullseye

    volumes:
      - ..:/workspace:cached

    # Overrides default command so things don't shut down after the process ends.
    command: sleep infinity

    # Runs app on the same network as the database container, allows "forwardPorts" in devcontainer.json function.
    network_mode: service:db
    # Uncomment the next line to use a non-root user for all processes.
    # user: node

    # Use "forwardPorts" in **devcontainer.json** to forward an app port locally.
    # (Adding the "ports" property to this file will not forward from a Codespace.)

  db:
    image: biarms/mysql:5.7
    restart: unless-stopped
    volumes:
      - mysql-data:/var/lib/postgresql/data
    environment:
      - MYSQL_ROOT_PASSWORD=bina
      - MYSQL_DATABASE=bina
      - MYSQL_USER=bina
      - MYSQL_PASSWORD=bina

volumes:
  mysql-data: null
