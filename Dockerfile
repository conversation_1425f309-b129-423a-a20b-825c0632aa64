# BUILD FOR PRODUCTION
FROM node:18.20.4-bullseye AS builder

ENV NODE_ENV=build
ENV TZ=Asia/Kuala_Lumpur

USER root
WORKDIR /app

RUN apt-get update && apt-get install -y ca-certificates && update-ca-certificates

COPY package.json yarn.lock ./

RUN yarn install --frozen-lockfile

COPY --chown=node:node . .
RUN yarn run build

# # PRODUCTION
# FROM node:18-alpine AS production

# ENV NODE_ENV=production
# ENV TZ=Asia/Kuala_Lumpur

# USER node
# WORKDIR /app

# RUN yarn add ts-node
# RUN npm install --cpu=x64 --os=linux --libc=musl sharp

# COPY --from=builder --chown=node:node /app/package*.json ./
# COPY --from=builder --chown=node:node /app/node_modules/ ./node_modules/
# COPY --from=builder --chown=node:node /app/dist/ ./dist/
# COPY --from=builder --chown=node:node /app/schema.gql ./schema.gql
# COPY --from=builder --chown=node:node /app/dist/ormconfig-prod.js ./ormconfig.js
# COPY --from=builder --chown=node:node /app/tsconfig* ./
# COPY --from=builder --chown=node:node /app/docker-entrypoint.sh ./docker-entrypoint.sh

RUN chmod +x ./docker-entrypoint.sh

ENV PORT=3000

EXPOSE ${PORT}
# HEALTHCHECK --interval=30s --timeout=60s --start-period=5s --retries=5 CMD wget -q --spider http://localhost:${PORT}/api/system/system/alive || exit 1

# CMD ["npm", "run", "start:prod"]
ENTRYPOINT ["sh", "./docker-entrypoint.sh"]
CMD ["/home/<USER>/docker-entrypoint.sh"]