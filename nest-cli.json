{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"plugins": [{"name": "@nestjs/swagger", "options": {"introspectComments": true, "controllerKeyOfComment": "summary", "dtoFileNameSuffix": ["api.dto.ts"]}}, {"name": "@nestjs/graphql", "options": {"typeFileNameSuffix": [".gql.dto.ts", ".input.ts", ".args.ts"], "introspectComments": true}}]}}