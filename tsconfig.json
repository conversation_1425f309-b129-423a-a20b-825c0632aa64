{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "jsx": "react-jsx",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false,
    "esModuleInterop": true,
    "paths": {
      "@common/*": [
        "src/common/*"
      ],
      "@configs/*": [
        "src/configs/*"
      ],
      "@constants": [
        "src/constants"
      ],
      "@constants/*": [
        "src/constants/*"
      ],
      "@decorators/*": [
        "src/decorators/*"
      ],
      "@dto": [
        "src/dto"
      ],
      "@dto/*": [
        "src/dto/*"
      ],
      "@entities": [
        "src/modules/base/entities"
      ],
      "@entities/*": [
        "src/modules/base/entities/*"
      ],
      "@guards": [
        "src/guards"
      ],
      "@guards/*": [
        "src/guards/*"
      ],
      "@hooks/*": [
        "src/hooks/*"
      ],
      "@modules/*": [
        "src/modules/*"
      ],
      "@services/*": [
        "src/services/*"
      ],
      "@utils": [
        "src/utils"
      ],
      "@utils/*": [
        "src/utils/*"
      ],
      "@templates": [
        "src/templates"
      ],
      "@templates/*": [
        "src/templates/*"
      ],
      "@types": [
        "src/types"
      ],
      "@types/*": [
        "src/types/*"
      ],
      "@providers/*": [
        "src/providers/*"
      ]
    }
  },
}