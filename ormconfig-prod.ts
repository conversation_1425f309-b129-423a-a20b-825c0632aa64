import { ConnectionOptions } from 'typeorm';
import 'dotenv/config';

export default {
  type: process.env.DB_DRIVER as any,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  logging: true,
  entities: ['dist/src/**/*.entity.js'],
  migrations: ['dist/database/migrations/**/*.js'],
  seeds: ['dist/database/seeds/**/*.js'],
  factories: ['dist/database/factories/**/*.js'],
  cli: {
    migrationsDir: 'dist/database/migrations',
  },
} as ConnectionOptions;
