NODE_OPTIONS="--max-old-space-size=9216"
NODE_ENV='test'
PORT=3000
APP_NAME="BINA"
JWT_SECRET="shhhhhhhhhhhhhhh"
ADMIN_SECRET="shhhh"
APP_URI='http://localhost:3001'
ADMIN_APP_URL=''
TZ="UTC"
# -------------------------- Express Environments --------------------------
CORS_VALIDATION=false
CORS_ORIGINS_WHITELIST=''
# -------------------------- Database Environments -------------------------
DB_DRIVER=mysql
DB_HOST=localhost
DB_USERNAME=root
DB_PASSWORD=bina
DB_NAME=bina
DB_PORT=3306
# ------------------------------- One Way SMS ------------------------------
SMS_USERNAME=''
SMS_PASSWORD=''
SMS_SENDER_ID=''
# ------------------------- Stripe Environments ---------------------------
STRIPE_CUSTOMER_SUCCESS_URL="/account/wallet?paymentStatus=success"
STRIPE_CUSTOMER_CANCEL_URL="/account/wallet?paymentStatus=cancel"
STRIPE_MERCHANT_SUCCESS_URL="/account/wallet?paymentStatus=success"
STRIPE_MERCHANT_CANCEL_URL="/account/wallet?paymentStatus=cancel"
STRIPE_MERCHANT_SUBSCRIPTION_SUCCESS_URL="/account/subscription?paymentStatus=success"
STRIPE_MERCHANT_SUBSCRIPTION_CANCEL_URL="/account/subscription?paymentStatus=cancel"
# ------------------------- Mailgun Environments ---------------------------
MAILGUN_API_USER='Salwan Samad'
MAILGUN_API_KEY='**************************************************'
MAILGUN_SENDER_EMAIL='<EMAIL>'
MAILGUN_DOMAIN='mail.bina.cloud'
# -------------------------- Autodesk Environments --------------------------
#AUTODESK_CLIENT_ID='********************************'
#AUTODESK_CLIENT_SECRET='Jf4e2ddf9efe34ef'
#AUTODESK_GRANT_TYPE='client_credentials'
#AUTODESK_SCOPE='code:all user-profile:read user:read user:write viewables:read data:read data:create data:write bucket:create bucket:read bucket:update bucket:delete account:read account:write'
#AUTODESK_BUCKET='bina-dev-forge-testing'
AUTODESK_CLIENT_ID='********************************'
AUTODESK_CLIENT_SECRET='vN0hPGEHn6m6Dk2h'
AUTODESK_GRANT_TYPE='client_credentials'
AUTODESK_SCOPE='code:all user-profile:read user:read user:write viewables:read data:read data:create data:write bucket:create bucket:read bucket:update bucket:delete account:read account:write'
# AUTODESK_BUCKET='bina-dev-forge-testing'
AUTODESK_BUCKET='bina-test'
FORGE_API_URL='https://developer.api.autodesk.com'
# -------------------------- Firebase Environments --------------------------
FIREBASE_PROJECT_ID='bona-dev-aeb2d'
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL='<EMAIL>'
# ---------------------- TM One Alpha Edge Environments ----------------------
TMONE_BUCKET="bina-dev-storage"
TMONE_ACCESS_KEY_ID="QN97RS7DLWSLOFSJMSDR"
TMONE_SECRET_ACCESS_KEY="7FLelJ8IqiwSWKg1LSs8wBJfMsNPRLBccm0OVFl2"
TMONE_SERVER="https://obs.my-kualalumpur-1.alphaedge.tmone.com.my"
TMONE_OBS_STORAGE="https://bina-dev-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/"
# ---------------------- Docker Environments ----------------------
BINA_BE_FORWARD_PORT=5111
MYSQL_IMAGE='mariadb:10.4'
MYSQL_ROOT_PASSWORD=bina
MYSQL_DATABASE=bina
MYSQL_USER=root
MYSQL_PASSWORD=bina
MYSQL_FORWARD_PORT=3306
# ---------------------- Novu Environments ----------------------
NOVU_API_KEY=53b4067fd74f9deebbbfb7d2fa4cfbc7
NOVU_BACKEND_URL=https://api.novu.co/v1
# ---------------------- Senangpay Environments ----------------------
SENANGPAY_MERCHANT_ID='368167629764483'
SENANGPAY_SECRET_KEY='5344-445'
SENANGPAY_URI=https://sandbox.senangpay.my # no trailing slash
# ---------------------- Sentry Environments ----------------------
SENTRY_DSN=https://<EMAIL>/4504925033070592