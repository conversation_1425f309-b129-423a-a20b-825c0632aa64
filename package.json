{"name": "bina-be", "version": "3.9.2", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "NODE_OPTIONS=--max_old_space_size=9216 nest build", "prettify": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" --ignore-path .gitignore", "start": "TZ='UTC' nest start", "dev": "TZ='UTC' nest start --watch", "start:debug": "TZ='UTC' nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --setupFiles dotenv/config", "test:watch": "jest --watch --setupFiles dotenv/config", "test:cov": "jest --coverage --setupFiles dotenv/config", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "migrate": "yarn typeorm migration:run", "migrate:revert": "yarn typeorm migration:revert", "migrate:gen": "yarn typeorm migration:generate -n", "seed": "ts-node -r tsconfig-paths/register ./node_modules/typeorm-seeding/dist/cli.js seed", "prepare": "husky install", "cleanup": "rm -rf node_modulesgoogleapis dist && yarn install"}, "dependencies": {"@adminjs/express": "^5.1.0", "@adminjs/nestjs": "^5.1.1", "@adminjs/typeorm": "^2.0.4", "@bull-board/api": "^5.21.4", "@bull-board/express": "^5.21.4", "@bull-board/nestjs": "^5.21.4", "@json2csv/plainjs": "^6.1.2", "@nestjs-query/core": "^0.30.0", "@nestjs-query/query-graphql": "^0.30.0", "@nestjs-query/query-typeorm": "^0.30.0", "@nestjs/apollo": "^10.0.9", "@nestjs/axios": "^2.0.0", "@nestjs/bullmq": "^10.2.0", "@nestjs/common": "^9.1.4", "@nestjs/config": "^3.2.3", "@nestjs/core": "^8.4.7", "@nestjs/graphql": "^10.1.3", "@nestjs/jwt": "^8.0.1", "@nestjs/mapped-types": "*", "@nestjs/passport": "^8.2.1", "@nestjs/platform-express": "^8.0.0", "@nestjs/platform-socket.io": "^8.4.7", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "^5.2.1", "@nestjs/typeorm": "^8.0.3", "@nestjs/websockets": "^8.4.7", "@novu/node": "^0.24.0", "@paralleldrive/cuid2": "^2.2.0", "@sentry/node": "^7.107.0", "@sentry/profiling-node": "^7.107.0", "@sentry/tracing": "^7.45.0", "@types/randomcolor": "^0.5.9", "adminjs": "^6.8.7", "apollo-server-express": "^3.6.7", "apple-signin-auth": "^1.7.5", "aws-sdk": "^2.1135.0", "axios": "^1.3.4", "bcrypt": "^5.0.1", "body-parser": "^1.20.2", "bullmq": "^5.13.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "cookie-parser": "^1.4.6", "dataloader": "^2.1.0", "dotenv": "^16.0.0", "esdk-obs-nodejs": "^3.21.6", "express-formidable": "^1.2.0", "express-mysql-session": "^2.1.8", "express-session": "^1.17.3", "firebase-admin": "^11.0.1", "form-data": "^4.0.0", "generate-password": "^1.7.0", "google-auth-library": "^9.6.3", "googleapis": "^133.0.0", "graphql": "^16.4.0", "graphql-subscriptions": "^2.0.0", "graphql-type-json": "^0.3.2", "graphql-upload": "^13.0.0", "haversine-distance": "^1.2.1", "hbs": "^4.2.0", "helmet": "^5.0.2", "husky": ">=6", "joi": "^17.6.0", "json-to-csv-export": "^2.1.1", "jsonwebtoken": "^8.5.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "mailgun.js": "^8.0.0", "mime-types": "^2.1.35", "moment": "^2.29.3", "moment-timezone": "^0.5.40", "mysql2": "^2.3.3", "nanoid": "^3.3.4", "otp-generator": "^4.0.0", "passport": "^0.5.2", "passport-apple": "^2.0.1", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-http": "^0.3.0", "passport-jwt": "^4.0.0", "passport-remember-me": "0.0.1", "pdf-lib": "^1.17.1", "prom-client": "^15.1.0", "puppeteer": "^21.7.0", "query-string": "^7.1.1", "ramda": "^0.28.0", "randomcolor": "^0.6.2", "react-jsx": "^1.0.0", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "sharp": "0.31.3", "swagger": "^0.7.5", "swagger-ui-express": "^4.3.0", "typeorm": "^0.2.45", "typeorm-seeding": "^1.6.1", "uuid": "^8.3.2", "validator": "^13.11.0", "winston": "^3.12.0", "xhr2": "^0.2.1", "xlsx": "^0.18.5"}, "resolutions": {"@tiptap/core": "2.0.3", "@tiptap/pm": "2.0.3", "@tiptap/starter-kit": "2.0.3"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@nestjs/testing": "^8.0.0", "@types/bcrypt": "^5.0.0", "@types/cookie-parser": "^1.4.3", "@types/cron": "^2.0.0", "@types/express": "^4.17.13", "@types/graphql-upload": "^8.0.11", "@types/hbs": "^4.0.1", "@types/jest": "27.4.1", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.182", "@types/moment-timezone": "^0.5.30", "@types/node": "^16.0.0", "@types/passport-facebook": "^2.1.11", "@types/passport-jwt": "^3.0.6", "@types/supertest": "^2.0.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-unused-imports": "^2.0.0", "jest": "^28.0.0", "jest-mock": "^29.7.0", "lint-staged": "^12.4.1", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "^28.0.0", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "testTimeout": 20000, "moduleNameMapper": {"^@common/(.*)$": "<rootDir>/../src/common/$1", "^@configs/(.*)$": "<rootDir>/../src/configs/$1", "^@constants$": "<rootDir>/../src/constants", "^@constants/(.*)$": "<rootDir>/../src/constants/$1", "^@decorators/(.*)$": "<rootDir>/../src/decorators/$1", "^@dto$": "<rootDir>/../src/dto", "^@dto/(.*)$": "<rootDir>/../src/dto/$1", "^@entities$": "<rootDir>/../src/modules/base/entities", "^@entities/(.*)$": "<rootDir>/../src/modules/base/entities/$1", "^@guards$": "<rootDir>/../src/guards", "^@guards/(.*)$": "<rootDir>/../src/guards/$1", "^@hooks/(.*)$": "<rootDir>/../src/hooks/$1", "^@modules/(.*)$": "<rootDir>/../src/modules/$1", "^@services/(.*)$": "<rootDir>/../src/services/$1", "^@utils$": "<rootDir>/../src/utils", "^@utils/(.*)$": "<rootDir>/../src/utils/$1", "^@templates$": "<rootDir>/../src/templates", "^@templates/(.*)$": "<rootDir>/../src/templates/$1", "^@types$": "<rootDir>/../src/types", "^@types/(.*)$": "<rootDir>/../src/types/$1", "^@providers/(.*)$": "<rootDir>/../src/providers/$1"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix"], "{*.json,.{babelrc,eslintrc,prettierrc}}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --parser json --write"], "*.{css,scss}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write"], "*.{html,md,yml}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write"], "*.js": "eslint --cache --fix"}}