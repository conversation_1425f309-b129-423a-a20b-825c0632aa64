import { Injectable, PipeTransform, UsePipes } from '@nestjs/common';
import { uploadReadableStreamToS3 } from '@providers/s3.service';
import _ from 'lodash';

type PathData = {
  path: string[];
  value: string | Promise<any>;
};

type FieldReplacement = [searchValuePattern: string, replacement: string];

type Options = {
  s3path?: string;
  fieldReplacements?: FieldReplacement[];
  isPlainObject?: boolean;
};

export const UseUploadFilePipe = (options?: Options) => UsePipes(new UploadFilePipe((options = {})));

@Injectable()
export class UploadFilePipe implements PipeTransform {
  constructor(private options?: Options) {
    this.options.s3path = options?.s3path ?? 'files/';
    this.options.fieldReplacements = options?.fieldReplacements ?? [];
  }

  async transform(payload: any) {
    if ((!this.options.isPlainObject && _.isPlainObject(payload)) || _.isEmpty(payload)) return payload;

    const paths = findFilePaths(payload);
    if (!paths) return payload;

    const uploadedPaths = await uploadFiles(this.options.s3path, paths);
    const newPaths = replacePath(uploadedPaths, this.options.fieldReplacements);

    newPaths.forEach(({ path, value }) => {
      _.set(payload, path, value);
    });

    return payload;
  }
}

const findFilePaths = (object: any, path = [], found = []): PathData[] => {
  _.toPairs(object).forEach(([key, value]) => {
    if (isFile(value)) {
      found.push({ path: [...path, key], value });
    }
    if (_.isObject(value)) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      findFilePaths(value, [...path, key], found);
    }
  });
  return found;
};

const isFile = (value): boolean => {
  return value?.then;
};

const uploadFiles = async (s3path: string, paths: PathData[]): Promise<PathData[]> => {
  return (await Promise.all(
    paths.map(
      ({ path, value: file }) =>
        new Promise(async (resolve, reject) => {
          try {
            console.log(file, 'file')
            const { filename, mimetype, createReadStream } = await file;
            const stream = createReadStream();

            const uploaded = await uploadReadableStreamToS3(stream, s3path, filename, {
              publicAccess: true,
              contentType: mimetype
            });

            resolve({ path, value: uploaded.Location });
          } catch (error) {
            reject(error);
          }
        })
    )
  )) as PathData[];
};

const replacePath = (paths: PathData[], fieldReplacements: FieldReplacement[]): PathData[] => {
  const replacedPaths = paths.map(data => {
    const lastPath = data.path[data.path.length - 1] ?? '';
    const foundFieldReplacement = fieldReplacements.find(([from]) => lastPath === from);
    if (foundFieldReplacement) {
      data.path[data.path.length - 1] = foundFieldReplacement[1];
    }
    return data;
  });

  return replacedPaths;
};
