import 'dotenv/config';
import { graphqlUploadExpress } from 'graphql-upload';
import { OriginConfig } from '@configs/app.config';
import { IS_PRODUCTION_ENV } from '@constants';
import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { join } from 'path';
import bodyParser from 'body-parser';
import * as Sentry from '@sentry/node';
import { SentryInterceptor } from '@common/interceptors/sentry.interceptors';
import { EventEmitter } from 'events';
import { nodeProfilingIntegration } from "@sentry/profiling-node";
import { NestJSLoggerService } from '@modules/logger/services/nestjs-logger.service';
import { ExpressAdapter } from '@bull-board/express';
import { createBullBoard } from '@bull-board/api';
import { Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { BullBoardAuthMiddleware } from '@modules/logger/middleware/bull-board.middleware';

async function bootstrap() {
  
  (EventEmitter.prototype as any)._maxListeners = 0;
  
  const app = await NestFactory.create<NestExpressApplication>(AppModule,{
    bufferLogs: true,
  });

  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/oakajcuqnnfubla/bull-board');

  const bimProcess = app.get<Queue>(getQueueToken('bim-process'));
  const requestForSignatureProcess = app.get<Queue>(getQueueToken('request-for-signature-notification-process'));
  const emailCorrespondence = app.get<Queue>(getQueueToken('email-correspondence-process'));

  createBullBoard({
    queues: [
      new BullMQAdapter(bimProcess),
      new BullMQAdapter(requestForSignatureProcess),
      new BullMQAdapter(emailCorrespondence),
    ],
    serverAdapter
  });

  app.use('/oakajcuqnnfubla/bull-board',
    new BullBoardAuthMiddleware().use, 
    serverAdapter.getRouter()
  );

  app.useLogger(app.get(NestJSLoggerService));
  // CORS
  app.enableCors({
    credentials: true,
    exposedHeaders: 'Content-Disposition',
    origin: OriginConfig,
  });

  // Middlewares
  app.use(
    helmet({
      crossOriginEmbedderPolicy: IS_PRODUCTION_ENV,
      contentSecurityPolicy: IS_PRODUCTION_ENV
    })
  );
  app.use(cookieParser());

  // set graphql upload limit 500mb
  app.use('/graphql', graphqlUploadExpress({ maxFileSize: 500 * 1024 * 1024, maxFiles: 30 }));

  // Pipes
  app.useGlobalPipes(new ValidationPipe());

  // Swagger
  app.setGlobalPrefix('api');
  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.setBaseViewsDir(join(__dirname.replace('dist', ''), '..', 'src/views'));
  app.setViewEngine('html');

  if (process.env.SWAGGER === 'true') {
    const config = new DocumentBuilder()
      .setTitle(`${process.env.APP_NAME} API`)
      .setVersion('1.1')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);
  }

  //Set payload limit error`
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '1mb', extended: true }));

  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    tracesSampleRate: 0.5,
    profilesSampleRate: 0.5,
    environment: process.env.NODE_ENV,
    enabled: process.env.NODE_ENV === 'production',
    integrations: [
      new Sentry.Integrations.Http({ tracing: true }),
      new Sentry.Integrations.GraphQL(),
      new Sentry.Integrations.Mysql(),
      new Sentry.Integrations.Apollo({ useNestjs: true }),
      nodeProfilingIntegration(),
    ]
  });
  app.useGlobalInterceptors(new SentryInterceptor());

  await app.listen(process.env.PORT ?? 3000, async () =>{
    console.log(`Application is running on: ${await app.getUrl()}`);
    if (typeof process.send === 'function') {
      process.send('ready');
    }
  });

  const shutdown = async (signal: string) => {
    console.log(`${signal} received, closing server...`);
    await app.close();
    console.log('Server closed gracefully');
    process.exit(0);
  };

  if (process.env.NODE_ENV === 'production') {
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
  }
}
bootstrap();
