import { RoleTypeEnum } from '@constants';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthData } from '@types';
import { getRepository } from 'typeorm';

@Injectable()
export class GqlRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;

    const roles = this.reflector.get<string[]>('roles', context.getHandler());

    if (!roles) {
      return true;
    }
    const user = request.user as AuthData;
    if (!user) return false;
    if (user.type === RoleTypeEnum.Admin) return true;

    const foundRole = roles.find(r => r === user.type)?.length > 0;
    if (!foundRole) throw new ForbiddenException('Insufficient Role permission for this action.');
    return true;
  }
}

@Injectable()
export class GqlProjectRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;

    const projectRoles = this.reflector.get<string[]>('projectRoles', context.getHandler());

    if (!projectRoles) {
      return true;
    }
    const user = request.user as AuthData;
    if (!user) return false;
    if (user.type === RoleTypeEnum.Admin) return true;

    const projectId = Number(request.headers['project-id']);
    if (!projectId) return false;

    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId: user.id, projectId });
    if (!projectUser) return false;

    const foundRole = projectRoles.find(r => r === projectUser.role)?.length > 0;

    if (!foundRole) throw new ForbiddenException('Insufficient Role permission for this action.');
    return true;
  }
}
