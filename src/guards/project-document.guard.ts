import { ProjectDocumentPermissionType } from '@constants';
import { ProjectUserPermissions } from '@constants/project-document-permissions';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { getRepository } from 'typeorm';

@Injectable()
export class ProjectDocumentGuard implements CanActivate {
  constructor(public permissionType: ProjectDocumentPermissionType) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const { req } = ctx.getContext();
    let user = req.user;
    const projectId = Number(req.headers['project-id']);

    if (!projectId) {
      throw new UnauthorizedException('Project ID is missing');
    }
    if (!user) {
      throw new UnauthorizedException('User is missing');
    }

    user = await getRepository(UserEntity).findOne({ id: user.id }, { relations: ['projectUsers'] });
    const projectUser = user.projectUsers.find(projectUser => projectUser.projectId === projectId);
    projectUser.role = projectUser.role;

    const permissions = ProjectUserPermissions(projectUser.role);

    if (!permissions[this.permissionType]) throw new UnauthorizedException();

    return permissions[this.permissionType];
  }
}
