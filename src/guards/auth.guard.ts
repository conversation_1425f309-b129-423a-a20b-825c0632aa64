import JwtConfig from '@configs/jwt.config';
import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthGuard } from '@nestjs/passport';
import { AuthData } from '@types';
import Jwt from 'jsonwebtoken';
import { ExtractJwt } from 'passport-jwt';
/* --------------------------- Admin Auth Guards -------------------------- */
@Injectable()
export class AdminJwtAuthGuard extends AuthGuard('admin-jwt') {}
@Injectable()
export class UserJwtAuthGuard extends AuthGuard('user-jwt') {}

@Injectable()
export class GqlAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context);
    const info = ctx.getInfo();
    const request = ctx.getContext().req;    

    if (info.fieldName === 'updateUserSignature') {
      return true;
    }

    if (info.fieldName === 'generateOtp'|| info.fieldName === 'verifyOtp') {
      return true;
    }

    const jwtToken = ExtractJwt.fromAuthHeaderAsBearerToken()(request);

    if (!jwtToken) throw new UnauthorizedException();
    const decodedJwt = Jwt.decode(jwtToken) as AuthData;

    let user;
    if (decodedJwt?.type === 'User') {
      user = Jwt.verify(jwtToken, JwtConfig.userAccessTokenConfig.secret);
    } else {
      user = Jwt.verify(jwtToken, JwtConfig.adminAccessTokenConfig.secret);
    }
    if (!user) throw new UnauthorizedException();
    request.user = user;
    return true;
  }
}
