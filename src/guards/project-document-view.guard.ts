import { CategoryType, ProjectDocumentUserPermissionType } from '@constants';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { getRepository } from 'typeorm';

@Injectable()
export class ProjectDocumentViewGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const filter = ctx.getArgs()?.filter;
    if (!filter) return true;
    const categories = [CategoryType.WorkProgramme, CategoryType.Correspondence, CategoryType.ProjectDocument];
    if (filter.category && !categories.includes(filter.category)) return true;

    const { req } = ctx.getContext();
    const user = req.user;
    const projectId = Number(req.headers['project-id']);
    const projectDocumentId = ctx.getArgs().id;
    const projectDocument = await getRepository(ProjectDocumentEntity).findOne({
      where: {
        id: projectDocumentId,
        projectId
      }
    });

    if (!projectId) throw new UnauthorizedException('Project ID is missing');

    if (!user) throw new UnauthorizedException('User is missing');

    if (!projectDocument) throw new UnauthorizedException('Document not found');

    const projectDocumentUser = await getRepository(ProjectDocumentUserEntity).findOne({
      where: {
        userId: user.id,
        projectDocumentId
      }
    });

    if (projectDocumentUser?.type === ProjectDocumentUserPermissionType.Include || projectDocument.addedBy === user.id)
      return true;

    if (projectDocumentUser?.type === ProjectDocumentUserPermissionType.Exclude || projectDocument.addedBy !== user.id)
      throw new UnauthorizedException('You do not have permission to view this document');

    throw new UnauthorizedException('Document not found');
  }
}
