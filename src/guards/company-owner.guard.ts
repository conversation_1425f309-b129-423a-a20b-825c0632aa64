import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { getRepository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class CompanyOwnerGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req;
    const args = ctx.getArgs();

    // Get user from request
    const user = request.user;
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    // Admin can always perform operations
    if (user.type === RoleTypeEnum.Admin) {
      return true;
    }

    // For regular users, check if they are company owner
    if (user.type === RoleTypeEnum.User) {
      return await this.checkCompanyOwnership(user.id, args);
    }

    throw new UnauthorizedException('Insufficient permissions');
  }

  private async checkCompanyOwnership(userId: number, args: any): Promise<boolean> {
    try {
      // Get user's company
      const user = await getRepository(UserEntity).findOne({
        where: { id: userId }
      });

      if (!user || !user.companyId) {
        throw new UnauthorizedException('User company not found');
      }

      // Get company to check ownership
      const company = await getRepository(CompanyEntity).findOne({
        where: { id: user.companyId }
      });

      if (!company) {
        throw new UnauthorizedException('Company not found');
      }

      // Check if user is company owner
      if (company.ownerId !== userId) {
        throw new UnauthorizedException('Only company owners can perform this action');
      }

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Failed to verify company ownership');
    }
  }
}
