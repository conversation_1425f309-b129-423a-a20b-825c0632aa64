import { UserEntity } from '@modules/user/entity/user.entity';
import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import moment from 'moment';
import { getRepository } from 'typeorm';

@Injectable()
export class CompanySubscriptionGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const { req } = ctx.getContext();
    const user = req.user;

    if (!user) {
      throw new UnauthorizedException('User is missing');
    }

    const me = await getRepository(UserEntity).findOne({
      where: { id: user.id },
      relations: ['company', 'company.companySubscriptions', 'company.companySubscriptions.subscriptionPackage']
    });

    if (!me?.company?.companySubscriptions || me.company.companySubscriptions.length === 0) {
      throw new UnauthorizedException('Sorry you do not have an active subscription');
    }

    // Find the most recent subscription
    const sortedSubscriptions = me.company.companySubscriptions.sort((a, b) =>
      new Date(b.subscriptionEndDate).getTime() - new Date(a.subscriptionEndDate).getTime()
    );

    const latestSubscription = sortedSubscriptions[0];

    // Check if it's a free trial subscription (both conditions must be met)
    const isFreeTrialPayment = latestSubscription.paymentMethod?.toLowerCase()?.trim() === 'free trial';
    const isFreeTrialPackage = latestSubscription.subscriptionPackage?.title?.toLowerCase()?.trim()?.includes('free trial');
    const isFreeTrial = isFreeTrialPayment && isFreeTrialPackage;

    // Apply different grace periods based on subscription type
    // For free trials: No grace period
    // For regular subscriptions: 10-day grace period
    const gracePeriodDays = isFreeTrial ? 0 : 10;

    // Check if subscription is still valid (including grace period if applicable)
    const isValid = latestSubscription.subscriptionEndDate > moment().subtract(gracePeriodDays, "days").toDate();

    if (!isValid) {
      throw new UnauthorizedException('Sorry you do not have an active subscription');
    }

    return true;
  }
}
