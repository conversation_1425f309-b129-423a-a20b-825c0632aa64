<html>
  <head>
    <style>
        body {
          margin: 0
        }
        #tree_view_div {
          width: 100%;
          height: 100%;
          padding: 70% 0;
          background-color: #F0FFFF;
        }
    </style>

        <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <script type="text/javascript">
      google.charts.load('current', {packages:["orgchart"]});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = new google.visualization.DataTable();
        data.addColumn('string', 'FolderName');
        data.addColumn('string', 'ParentName');
        data.addColumn('string', 'ToolTip');

        // For each orgchart box, provide the name, manager, and tooltip to show.
        data.addRows({{{treeDocumentsInString}}});

        // Create the chart.
        var chart = new google.visualization.OrgChart(document.getElementById('tree_view_div'));
        // Draw the chart, setting the allowHtml option to true for the tooltips.
        chart.draw(data, {'allowHtml':true});
      }
   </script>
    </head>
  <body>
    <div id="tree_view_div"></div>
  </body>
</html>