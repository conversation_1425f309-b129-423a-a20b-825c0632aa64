import { TaskStatusType, ProjectDocumentStatus, workflowType } from '@constants';
import * as _ from 'lodash';

export const replaceSpaceWithDash = (word: string) => {
  return _.replace(word, new RegExp(' ', 'g'), '-');
};

export const isValidUrl = (url: string) => {
  try {
    new URL(url);
    return true;
  } catch (err) {
    return false;
  }
};

export const getStatusName = (status: TaskStatusType) => {
  switch (status) {
    case TaskStatusType.Open:
      return 'Open';
    case TaskStatusType.InProgress:
      return 'In Progress';
    case TaskStatusType.Completed:
      return 'Completed';
    case TaskStatusType.Hold:
      return 'Hold';
  }
}

export const getDocumentStatusName = (status: ProjectDocumentStatus) => {
  switch (status) { 
    case ProjectDocumentStatus.Pending:
      return 'Pending';
    case ProjectDocumentStatus.Draft:
      return 'Draft';
    case ProjectDocumentStatus.Submitted:
      return 'Submitted';
    case ProjectDocumentStatus.InReview:
      return 'In Review';
    case ProjectDocumentStatus.Rejected:
      return 'Rejected';
    case ProjectDocumentStatus.Approved:
      return 'Approved';
  }
}

export const getEllipsisText = (text: string) => {
  return text.length > 50 ? `${text?.substring?.(0, 50)}...` : text;
}

export const getWorkflowNotificationPath = (workflow: workflowType) => workflow === workflowType.Linear ? 'documentDetail' : 'dynamicDocumentDetail';

export const getDocumentNotificationPayload = (
  groupName: string,
  action: 'Request Approval' | 'Amend' | 'Resubmit' | 'Submit' | 'Answer',
  defaultHeader: string,
  defaultHead: string,
  defaultMessage: string,
  defaultTail: string,
) => {
  let header = '';
  let message = '';
  let head = '';
  let tail = ''

  switch (groupName) {

    case 'NCR':
      switch (action) {
        case 'Request Approval':
          header = 'NCR Reports';
          head = defaultHead;
          message = `submitted a Non Conformance Report `;
          tail = ` ${defaultTail}`
          break;
        case 'Amend':
          header = 'Amendment Request';
          head = defaultHead;
          message = ` has requested an amendment for `;
          tail = defaultTail;
          break;
        case 'Resubmit':
          header = 'Resubmission Update';
          head = defaultHead;
          message = ` has amended `;
          tail = defaultTail;
          break;
        case 'Submit':
          header = 'NCR Report Update';
          head = defaultHead;
          message = ` has submitted a reply to your Non Conformance Report, `;
          tail = defaultTail;
          break;
        case 'Answer':
          header = defaultHeader
          head = defaultHead;
          message = defaultMessage
          tail = defaultTail;
          break;
        default:
          return { header: defaultHeader, message: defaultMessage, head: defaultHead, tail: defaultTail};
      }
      break;
    case 'TQ':
      switch (action) {
        case 'Request Approval':
          header = 'RFI: Request for Information';
          head = defaultHead;
          message = ` submitted a Request For Information `;
          tail = defaultTail
          break;
        case 'Answer':
          header = 'RFI: Document Update';
          head = defaultHead;
          message = ` has answered your Request For Information regarding `;
          tail = defaultTail;
          break;
        case 'Amend':
          header = 'Amendment Request';
          head = defaultHead;
          message = ` has requested an amendment for `;
          tail = defaultTail;
          break;
        default:
          return { header: defaultHeader, message: defaultMessage, head: defaultHead, tail: defaultTail};
      }
      break;
    default:
      return { header: defaultHeader, message: defaultMessage, head: defaultHead, tail: defaultTail};
  }

  return { header, message, head, tail };
};

