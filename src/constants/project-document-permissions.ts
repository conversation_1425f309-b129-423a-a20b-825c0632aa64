import { ProjectUserRoleType, ProjectDocumentPermissionType } from '@constants';

export const ProjectUserPermissions = (role: ProjectUserRoleType) => {
  switch (role) {
    case ProjectUserRoleType.ProjectOwner:
      return {
        [ProjectDocumentPermissionType.CanView]: true,
        [ProjectDocumentPermissionType.CanDownload]: true,
        [ProjectDocumentPermissionType.CanEdit]: true,
        [ProjectDocumentPermissionType.CanDelete]: true,
        [ProjectDocumentPermissionType.CanShare]: true
      };
    case ProjectUserRoleType.CloudCoordinator:
      return {
        [ProjectDocumentPermissionType.CanView]: true,
        [ProjectDocumentPermissionType.CanDownload]: true,
        [ProjectDocumentPermissionType.CanEdit]: true,
        [ProjectDocumentPermissionType.CanDelete]: true,
        [ProjectDocumentPermissionType.CanShare]: true
      };
    case ProjectUserRoleType.CanEdit:
      return {
        [ProjectDocumentPermissionType.CanView]: true,
        [ProjectDocumentPermissionType.CanDownload]: true,
        [ProjectDocumentPermissionType.CanEdit]: true,
        [ProjectDocumentPermissionType.CanDelete]: true,
        [ProjectDocumentPermissionType.CanShare]: true
      };
    case ProjectUserRoleType.CanView:
      return {
        [ProjectDocumentPermissionType.CanView]: true,
        [ProjectDocumentPermissionType.CanDownload]: true,
        [ProjectDocumentPermissionType.CanEdit]: false,
        [ProjectDocumentPermissionType.CanDelete]: false,
        [ProjectDocumentPermissionType.CanShare]: false
      };
    default:
      return {
        [ProjectDocumentPermissionType.CanView]: false,
        [ProjectDocumentPermissionType.CanDownload]: false,
        [ProjectDocumentPermissionType.CanEdit]: false,
        [ProjectDocumentPermissionType.CanDelete]: false,
        [ProjectDocumentPermissionType.CanShare]: false
      };
  }
};
