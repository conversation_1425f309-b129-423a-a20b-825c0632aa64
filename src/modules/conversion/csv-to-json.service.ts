import { Injectable } from '@nestjs/common';
import * as XLSX from 'xlsx';
import { FileUpload } from 'graphql-upload';
import moment from 'moment';

@Injectable()
export class CsvToJsonService {
  async parseScurveFile(file: FileUpload | Buffer, type: 'physical' | 'financial') {
    const buffer = await this.convertFileUploadToBuffer(file);
    const workbook = XLSX.read(buffer, { type: 'buffer' });

    const sheetNames = workbook.SheetNames;

    const physicalSheetIndex = 2;
    const financialSheetIndex = 0;

    let data: any[] = [];

    if (type === 'physical') {
      const physicalRows = XLSX.utils.sheet_to_json(workbook.Sheets[sheetNames[physicalSheetIndex]], {
        header: 1,
        defval: ''
      }) as any[];

      data = this.processPhysicalData(physicalRows);
    } else {
      const financialRows = XLSX.utils.sheet_to_json(workbook.Sheets[sheetNames[financialSheetIndex]], {
        header: 1,
        defval: ''
      }) as any[];

      data = this.processFinancialData(financialRows);
    }
    return JSON.stringify(data);
  }

  private async convertFileUploadToBuffer(file: FileUpload | Buffer): Promise<Buffer> {
    if (Buffer.isBuffer(file)) return file;

    const targetedFile = await file;
    const createReadStream = targetedFile.createReadStream;

    let buffer = Buffer.from('');
    for await (const chunk of createReadStream()) {
      buffer = Buffer.concat([buffer, chunk]);
    }

    return buffer;
  }

  private processPhysicalData(data: any[]): any[] {
    const rowData: any[] = [];

    const headers = data[1].slice(1).map((header, index) => ({ header, index: index + 1 }));

    headers.forEach(header => {
      const baseIndex = header.index;
      
      const plannedValueRaw = data[3][baseIndex];
      const plannedValue = plannedValueRaw !== '' ? this.parseNumber(plannedValueRaw * 100) : null;
      rowData.push({
        year: this.ExcelDateToJSDate(data[2][header.index]), // Convert Excel serial date to JS date
        value: plannedValue,
        category: 'SCHEDULE'
      });

      const actualValueRaw = data[4][baseIndex];
      const actualValue = actualValueRaw !== '' ? this.parseNumber(actualValueRaw * 100) : null;
      const progressDays = data[10][baseIndex] !== '' ? Number(data[10][baseIndex]) : null;
      
      rowData.push({
        year: this.ExcelDateToJSDate(data[2][header.index]),
        value: actualValue,
        category: 'ACTUAL',
        progressDays: progressDays
      });
    });            

    return rowData;
  }

  private processFinancialData(rows: any[]): any[] {
    const result: any[] = [];
    
    

    const keys = Object.keys(rows[1]);
    keys.forEach((key) => {
      const index = +key;      
        
        const year = this.ExcelDateToJSDate(rows[7]?.slice(1)[index]);

        result.push({
            year: year,
            value: rows[8]?.slice(1)[index] !== '' ? Number(rows[8].slice(1)[index] / 1000) : null,
            category: 'SCHEDULE ( x RM1000 )',
            plannedClaim: rows[12]?.slice(1)[index] !== '' ? Number(rows[12]?.slice(1)[index] / 1000) : null,
            percentValue: (rows[2].slice(1) as any)[index] !== '' ? (rows[2].slice(1) as any)[index] : null
        });

        result.push({
            year: year,
            value:                 (rows[9].slice(1) as any)[index] !== ''
            ? ((rows[9].slice(1) as any)[index] / 1000)?.toFixed(5)
            : null,
            category: 'ACTUAL ( x RM1000 )',
            currentClaim:                 (rows[13].slice(1) as any)[index] !== ''
            ? ((rows[13].slice(1) as any)[index] / 1000)?.toFixed(5)
            : null,
            percentValue: (rows[3].slice(1) as any)[index] !== '' ? (rows[3].slice(1) as any)[index] : null,
            progressDays: (rows[16]?.slice(1) as any)[index] !== '' ? +(rows[16]?.slice(1) as any)[index] : null,
            monthlyClaim: rows[13]?.slice(1)[index] !== '' ? rows[13]?.slice(1)[index] : null
        });
    });    
    return result;
}


  ExcelDateToJSDate(date: any) {
    return moment(new Date(Math.round((date - 25569) * 86400 * 1000))).format('MMM-YY');
  }

  private parseNumber(value: any): number | null {
    if (value === '' || value === null || value === undefined) {
      return null;
    }
    const number = Number(value);
    if (isNaN(number)) {
      return null;
    }
    return parseFloat(number.toFixed(2));
  }
}
