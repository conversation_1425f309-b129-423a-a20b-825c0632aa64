import { GetAuthData, GetProjectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { DashboardService } from './dashboard.service';
import * as Dto from './dto/dashboard.api.dto';

@ApiTags('Dashboard API')
@Controller('dashboard')
export class DashboardController {
  constructor(private dashboardService: DashboardService) {}

  @UseApiUserAuthGuard()
  @Post('overview')
  async getOverview(@GetProjectData() projectId: number, @Body() input: Dto.OverviewDto) {
    return await this.dashboardService.getOverview(projectId, input.TaskGroupId, input.workspaceGroupId);
  }

  @UseApiUserAuthGuard()
  @Post('tasks-events-list')
  async getTasksAndEventsList(
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number,
    @Body() input: Dto.GetCalenderMonthDto
  ) {
    const tasksAndEventsList = await this.dashboardService.getTasksAndEventsList(user.id, projectId, input.month);
    return tasksAndEventsList;
  }

  @Get('restructure-tasks-events-list') 
  async restructureTasksAndEventsList() {
    return await this.dashboardService.restructureTasksAndEventsList();
  }
  
}
