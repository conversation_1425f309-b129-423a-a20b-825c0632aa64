import { getErrorMessage } from '@common/error';
import { CategoryType, ProjectDocumentStatus, TaskStatusType } from '@constants';
import { EventEntity } from '@modules/event/entity/event.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _ from 'lodash';
import moment from 'moment';
import { Repository, getRepository } from 'typeorm';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(TaskEntity)
    private taskRepo: Repository<TaskEntity>,
    @InjectRepository(ProjectDocumentEntity)
    private workspaceRepo: Repository<ProjectDocumentEntity>,
    @InjectRepository(EventEntity)
    private eventRepo: Repository<EventEntity>,
    @InjectRepository(ProjectGroupEntity)
    private taskGroupRepo: Repository<ProjectGroupEntity>,
    @InjectRepository(WorkspaceGroupEntity)
    private workspaceGroupRepo: Repository<WorkspaceGroupEntity>
  ) {}

  async getOverview(projectId: number, TaskGroupId: string, WorkspaceGroupId: string) {
    try {
      let taskName = null;
      let workspaceGroupName = null;

      const allTasks = await this.taskRepo.find({
        where: {
          projectId,
          ...(TaskGroupId ? { groupId: parseInt(TaskGroupId) } : {})
        }
      });

      const countOfCompletedTasks = allTasks.filter(task => task.status === TaskStatusType.Completed).length;
      const countOfInProgressTasks = allTasks.filter(
        task => task.status === TaskStatusType.InProgress && task.dueDate !== null
      ).length;
      const countOfOpenTasks = allTasks.filter(task => task.status === TaskStatusType.Open).length;
      const countOfHoldTasks = allTasks.filter(task => task.status === TaskStatusType.Hold).length;

      // Fetch all workspace tasks for the given project (and optionally workspace group)
      const allWorkspaceTasks = await this.workspaceRepo.find({
        where: {
          projectId,
          category: CategoryType.AllForm,
          ...(WorkspaceGroupId ? { workspaceGroupId: parseInt(WorkspaceGroupId) } : {})
        }
      });

      // Filter and count workspace tasks based on their statuses
      const submittedWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.Submitted
      ).length;
      const inReviewWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.InReview
      ).length;
      const pendingWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.Pending
      ).length;
      const inProgressWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.InProgress
      ).length;
      const approvedWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.Approved
      ).length;
      const rejectedWorkspaceTasksCount = allWorkspaceTasks.filter(
        task => task.status === ProjectDocumentStatus.Rejected
      ).length;

      if (TaskGroupId) {
        const selectedTask = await getRepository(ProjectGroupEntity).findOne({
          where: {
            projectId,
            id: parseInt(TaskGroupId)
          }
        });

        taskName = selectedTask?.title;
      }

      if (WorkspaceGroupId) {
        const selectedWorkspace = await getRepository(WorkspaceGroupEntity).findOne({
          where: {
            projectId,
            id: parseInt(WorkspaceGroupId)
          }
        });

        workspaceGroupName = selectedWorkspace?.name;
      }

      const param = {
        tasks: {
          countOfCompletedTasks,
          countOfInProgressTasks,
          countOfOpenTasks,
          countOfHoldTasks,
          name: taskName
        },
        workspace: {
          submittedWorkspaceTasks: submittedWorkspaceTasksCount,
          inReviewWorkspaceTasks: inReviewWorkspaceTasksCount,
          pendingWorkspaceTasks: pendingWorkspaceTasksCount,
          inProgressWorkspaceTasks: inProgressWorkspaceTasksCount,
          approvedWorkspaceTasks: approvedWorkspaceTasksCount,
          rejectedWorkspaceTasks: rejectedWorkspaceTasksCount,
          name: workspaceGroupName
        }
      };

      return { ...param };
    } catch (e) {
      getErrorMessage(e, 'DashboardService', 'getOverview');
    }
  }

  async getTasksAndEventsList(userId: number, projectId: number, month: string) {
    try {
      const tasks = await this.getTaskList(userId, projectId);
      const events = await this.getEventList(userId, projectId);

      // To get tasks and events on selected month
      const taskList = tasks.filter(task => {
        return moment(task.dueDate).format('YYYY-MM') === month;
      });

      const eventList = events.filter(event => {
        return moment(event.startAt).format('YYYY-MM') === month || moment(event.endAt).format('YYYY-MM') === month;
      });

      // To group tasks and events by date
      const taskListGroupByDate = _.toPairsIn(_.groupBy(taskList, task => task.dueDate));
      const eventListGroupByDate = _.toPairsIn(_.groupBy(eventList, event => event.startAt));

      // To combine tasks and events based on date
      const list = {};

      taskListGroupByDate.forEach(([key, value]) => {
        if (!list[key]) list[key] = {};
        list[key].task = value;
      });
      eventListGroupByDate.forEach(([key, value]) => {
        if (!list[key]) list[key] = {};
        list[key].event = value;
      });
      const rawTasksAndEventsList = _.toPairsIn(list);

      // To sort the tasks and events list
      const tasksAndEventsList = rawTasksAndEventsList.sort((a, b) => {
        return new Date(a[0]).getTime() - new Date(b[0]).getTime();
      });
      return tasksAndEventsList;
    } catch (e) {
      getErrorMessage(e, 'DashboardService', 'getTasksAndEventsList');
    }
  }

  // Get Task List
  private async getTaskList(userId: number, projectId: number) {
    try {
      const tasks = await this.taskRepo.find({
        where: {
          projectId
        },
        relations: ['assignees', 'assignees.user']
      });

      const tasksAssignedToMe = await Promise.all(
        tasks.map(async task => {
          if (_.find(await task.assignees, { userId })) return task;
          return null;
        })
      );

      const taskList = await tasksAssignedToMe.filter(task => {
        return task !== null && task?.status !== TaskStatusType.Completed;
      });
      return taskList;
    } catch (e) {
      getErrorMessage(e, 'DashboardService', 'getTaskList');
    }
  }

  // Get Event List
  private async getEventList(userId: number, projectId?: number) {
    try {
      const eventList = await getRepository(EventEntity)
        .createQueryBuilder('event')
        .leftJoinAndSelect('event.eventAssignees', 'eventAssignees')
        .leftJoinAndSelect('eventAssignees.user', 'user')
        .where('event.projectId = :projectId', { projectId })
        .andWhere('(event.userId = :userId OR user.id = :userId OR event.scheduleForAll = :scheduleForAll)', {
          userId,
          scheduleForAll: true
        })
        .getMany();
      return eventList;
    } catch (e) {
      console.log(e);
      getErrorMessage(e, 'DashboardService', 'getEventList');
    }
  }

  async restructureTasksAndEventsList() {
    // Get all events
    const events = await this.eventRepo.find({
      where: {
        scheduleForAll: true
      }
    });

    // Use a map to track duplicates
    const uniqueEventsMap = new Map();

    events.forEach(event => {
      events.forEach(otherEvent => {
        if (
          event.id !== otherEvent.id &&
          event.title === otherEvent.title &&
          this.areDatesEqualByHourMinuteSecond(event.startAt, otherEvent.startAt) &&
          this.areDatesEqualByHourMinuteSecond(event.endAt, otherEvent.endAt)
        ) {
          // If the current event has a smaller ID (older), keep it
          if (event.id < otherEvent.id) {
            uniqueEventsMap.set(otherEvent.id, true); // Mark the otherEvent for deletion
          } else {
            uniqueEventsMap.set(event.id, true); // Mark the event for deletion
          }
        }
      });
    });

    // Filter out the events to delete (those marked as true in the map)
    const eventsToDelete = events.filter(event => uniqueEventsMap.get(event.id));

    // Delete the duplicates, preserving one instance of each set
    const eventsToDeleteIds = eventsToDelete.map(event => event.id);
    await this.eventRepo.softDelete(eventsToDeleteIds);
  }

  areDatesEqualByHourMinuteSecond(date1, date2) {
    const format = 'HH:mm';
    return moment(date1).format(format) === moment(date2).format(format);
  }
}
