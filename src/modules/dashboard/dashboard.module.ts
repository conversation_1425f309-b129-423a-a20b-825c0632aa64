import { EventEntity } from '@modules/event/entity/event.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';

@Module({
  imports: [TypeOrmModule.forFeature([TaskEntity, EventEntity, ProjectDocumentEntity, ProjectGroupEntity, WorkspaceGroupEntity])],
  providers: [DashboardService],
  controllers: [DashboardController]
})
export class DashboardModule {}
