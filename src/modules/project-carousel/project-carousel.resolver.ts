import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Parent, ResolveField } from '@nestjs/graphql';
import { ProjectCarouselDto } from './dto/project-carousel.gql.dto';
import { ProjectCarouselService } from './project-carousel.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => ProjectCarouselDto)
export class ProjectCarouselResolver {
  constructor(private carouselService: ProjectCarouselService) {}

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: ProjectCarouselDto) {
    return await this.carouselService.getPresignedUrl(parent);
  }
}
