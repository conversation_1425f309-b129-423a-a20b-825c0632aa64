import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectCarouselEntity } from './entity/project-carousel.entity';
import {
  ProjectCarouselDto,
  CreateProjectCarouselInputDTO,
  UpdateProjectCarouselInputDTO
} from './dto/project-carousel.gql.dto';
import { ProjectCarouselService } from './project-carousel.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { ProjectCarouselSubscriber } from './project-carousel.subscriber';
import { ProjectCarouselResolver } from './project-carousel.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectCarouselEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: ProjectCarouselService,
          DTOClass: ProjectCarouselDto,
          EntityClass: ProjectCarouselEntity,
          CreateDTOClass: CreateProjectCarouselInputDTO,
          UpdateDTOClass: UpdateProjectCarouselInputDTO,
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          }
          // guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard],
        }
      ],
      services: [ProjectCarouselService, ProjectCarouselSubscriber, ProjectCarouselResolver]
    })
  ]
})
export class ProjectCarouselModule {}
