import { InjectRepository } from '@nestjs/typeorm';
import { ProjectCarouselEntity } from './entity/project-carousel.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Repository } from 'typeorm';
import { FileService } from '@modules/integration/file.service';
import { Injectable } from '@nestjs/common';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class ProjectCarouselService extends TypeOrmQueryService<ProjectCarouselEntity> {
  constructor(
    @InjectRepository(ProjectCarouselEntity)
    private projectCarouselRepo: Repository<ProjectCarouselEntity>,
    private fileService: FileService,
    private tmOneService: TMOneService
  ) {
    super(projectCarouselRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(project_carousel: ProjectCarouselEntity) {
    try {
      let key = project_carousel.fileKey;
      // fallback to fileUrl if fileKey is missing
      if (!key){
        const fileName = project_carousel.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
