import { BaseEntity } from '@modules/base/base';
import { <PERSON>ield, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';
import { ProjectEntity } from '@modules/project/entity/project.entity';

@ObjectType()
@Entity('project_carousels')
export class ProjectCarouselEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text', { nullable: true })
  fileUrl: string;

  @FilterableField()
  @Column('text')
  type: string;

  @Column('text', { nullable: true })
  fileKey: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectEntity, project => project.carousels)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;
}
