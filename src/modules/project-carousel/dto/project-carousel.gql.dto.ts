import { defaultQueryOptions, SourceType } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { ProjectCarouselEntity } from '../entity/project-carousel.entity';
// import { ProjectAuthorizer } from '../../project/project.authorizer';

@ObjectType('ProjectCarousel')
// @Authorize(ProjectAuthorizer)
@QueryOptions({ ...defaultQueryOptions })
export class ProjectCarouselDto extends ProjectCarouselEntity {
  //? offline mode
  remoteId?: string;
}

@InputType()
export class CreateProjectCarouselInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) projectId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  localProjectDocumentId?: string;
  fileKey?: string;
  fileName?: string;
  @Field({ nullable: true }) recordSource?: SourceType;
}
@InputType()
export class UpdateProjectCarouselInputDTO extends PartialType(CreateProjectCarouselInputDTO) {
  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;

  name?: string;
  type?: string;
  updatedBy?: string;

}
