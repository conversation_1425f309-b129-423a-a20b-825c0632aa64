import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { ProjectCarouselEntity } from './entity/project-carousel.entity';
import * as _ from 'lodash';
import { FileUpload } from 'graphql-upload';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { FileService } from '@modules/integration/file.service';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class ProjectCarouselSubscriber implements EntitySubscriberInterface<ProjectCarouselEntity> {
  constructor(connection: Connection, private fileService: FileService, private webSocketService: WebSocketService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectCarouselEntity;
  }

  async beforeInsert(event: InsertEvent<ProjectCarouselEntity>) {
    const { entity } = event;

    const file: any = entity.fileUrl;

    if (!file) throw new BadRequestException('No Document has been upload');

    if (file && _.isObject(file)) {
      const folder = '';
      const { filename, key, fileSize, type, channel } = await this.fileService.uploadGqlFile(
        file as FileUpload,
        folder
      );
      entity.fileKey = key;
      entity.name = filename;
      entity.type = type;
    }
  }

  async beforeUpdate(event: UpdateEvent<ProjectCarouselEntity>) {
    try {
      const { entity } = event;

      const file: any = entity.fileUrl;

      if (file && _.isObject(file)) {
        const folder = '';
        const { filename, key, fileSize, type, channel } = await this.fileService.uploadGqlFile(
          file as FileUpload,
          folder
        );
        entity.fileKey = key;
        entity.name = filename;
        entity.type = type;
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectCarouselSubscriber', 'beforeUpdate');
    }
  }
}
