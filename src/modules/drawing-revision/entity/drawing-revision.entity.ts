import { BaseEntity } from '@modules/base/base';
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { ObjectType } from '@nestjs/graphql';
import { FilterableField } from '@nestjs-query/query-graphql';
import { CategoryType, DrawingStatus } from '@constants';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';

@ObjectType()
@Entity('drawing_revisions')
export class DrawingRevisionEntity extends BaseEntity {
  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  fileName: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileUrl: string;

  @FilterableField({ nullable: false })
  @Column('text', { nullable: false })
  fileKey: string;

  @FilterableField({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectDocumentId: number;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  version: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  versionName: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  notes: string;

  @FilterableField({ nullable: true })
  @Column('longtext', { nullable: true })
  xfdf: string;

  @FilterableField(() => DrawingStatus, { nullable: true })
  @Column('enum', { enum: DrawingStatus, nullable: true, default: DrawingStatus.Obsolete })
  status: DrawingStatus;

  @FilterableField(() => CategoryType, { nullable: true })
  @Column('enum', { enum: CategoryType, nullable: true, default: CategoryType.TwoDDrawings })
  category: CategoryType;

  // Relations
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.drawingRevisions)
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;
}
