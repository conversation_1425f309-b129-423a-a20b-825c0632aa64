import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
  getRepository
} from 'typeorm';
import { DrawingRevisionService } from './drawing-revision.service';
import { DrawingRevisionEntity } from './entity/drawing-revision.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { getErrorMessage } from '@common/error';
import moment from 'moment';
import { FileService } from '@modules/integration/file.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectUserRoleType } from '@constants';

@Injectable()
@EventSubscriber()
export class DrawingRevisionSubscriber implements EntitySubscriberInterface<DrawingRevisionEntity> {
  constructor(
    connection: Connection,
    private drawingRevisionService: DrawingRevisionService,
    private fileService: FileService,
    private novuService: NovuService
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DrawingRevisionEntity;
  }

  async beforeInsert(event: InsertEvent<DrawingRevisionEntity>) {
    try {
      const { entity } = event;
      let fileKey;
      let fileName;

      const fileObject = entity?.fileUrl as any; //FE upload filebject via fileUrl
      if (typeof fileObject !== 'string') {
        const projectDocRepo = await event.manager
          .createQueryBuilder(ProjectDocumentEntity, 'ProjectDoc')
          .select('ProjectDoc.fileKey', 'fileKey')
          .addSelect('ProjectDoc.name', 'name')
          .where('ProjectDoc.id = :id', { id: entity.projectDocumentId })
          .getRawOne();

        // find latest version of drawing revision
        const drawingRevisionRepo = await event.manager
          .createQueryBuilder(DrawingRevisionEntity, 'drawingRevision')
          .select('MAX(drawingRevision.version)', 'maxVersion')
          .where('drawingRevision.projectDocumentId = :projectId', { projectId: entity.projectDocumentId })
          .getRawOne();

        if (drawingRevisionRepo === null) {
          entity.version = 1;
        } else {
          entity.version = drawingRevisionRepo.maxVersion + 1;
        }

        // check if have same name will add (number) behind it
        const drawingRevisionRepoTwo = await event.manager
          .createQueryBuilder(DrawingRevisionEntity, 'drawingRevision')
          .select('versionName', 'versionName')
          .where('drawingRevision.projectDocumentId = :projectId', { projectId: entity.projectDocumentId })
          .getRawMany();

        //check name is already exist or not
        const versionNames = drawingRevisionRepoTwo.filter(item => {
          if (!item.versionName) return;
          item.versionName.replace(/\([^)]*\)/g, '').trim() === entity.versionName;
        });

        if (versionNames.length >= 1) {
          const escapedSubstrings = versionNames.map(item => item.versionName);

          if (escapedSubstrings.includes(entity.versionName)) {
            // if it exists, extract the numeric part within parentheses
            entity.versionName = `${entity.versionName} (${escapedSubstrings.length})`;
          }
        }

        if (entity.versionName === null || entity.versionName === undefined) {
          entity.versionName = moment().tz('Asia/Kuala_Lumpur').format('DD-MM-YYYY');
        }

        // Auto name by date, format DD-MM-YYYY
        const subStringName = entity.versionName?.substring(0, 10);

        // check version name is format DD-MM-YYYY
        if (subStringName === moment().tz('Asia/Kuala_Lumpur').format('DD-MM-YYYY')) {
          // check if version name is already exist
          const dateChecking = drawingRevisionRepoTwo.filter(
            item => item.versionName?.substring(0, 10) === subStringName
          );

          if (dateChecking.length > 0) {
            // if it exists, extract the numeric part within parentheses
            entity.versionName = `${moment().tz('Asia/Kuala_Lumpur').format('DD-MM-YYYY')} (${dateChecking.length})`;
          }
        }

        if (fileObject && typeof fileObject !== 'string') {
          const { key, filename } = await this.fileService.uploadGqlFile(fileObject as any);
          fileKey = key;
          fileName = filename;
          // update project document
          await event.manager
            .createQueryBuilder(ProjectDocumentEntity, 'ProjectDoc')
            .update(ProjectDocumentEntity)
            .set({ fileKey: key, name: filename, notes: entity.notes, versionName: entity.versionName })
            .where('id = :id', { id: entity.projectDocumentId })
            .execute();
          entity.fileKey = fileKey;
          entity.fileUrl = fileKey;
          entity.fileName = fileName;
        } else {
          entity.fileUrl = entity.fileKey;
        }
      }
    } catch (e) {
      getErrorMessage(e, 'DrawingRevisionSubscriber', 'beforeInsert');
    }
  }

  async beforeUpdate(event: UpdateEvent<DrawingRevisionEntity>) {
    try {
      const { entity } = event;

      if (entity.projectDocumentId) {
        // check if have same name will add (number) behind it

        const drawingRevisionRepo = await event.manager
          .createQueryBuilder(DrawingRevisionEntity, 'drawingRevision')
          .select('versionName', 'versionName')
          .where('drawingRevision.projectDocumentId = :projectId', { projectId: entity.projectDocumentId })
          .getRawMany();

        //check name is already exist or not
        const versionNames = await drawingRevisionRepo.filter(item => {
          if (!item.versionName) return false; // Added a return statement

          return item.versionName.replace(/\([^)]*\)/g, '').trim() === entity.versionName; // Added a return statement
        });

        if (versionNames.length >= 1) {
          const escapedSubstrings = versionNames.map(item => item.versionName);

          if (escapedSubstrings.includes(entity.versionName)) {
            // if it exists, extract the numeric part within parentheses
            entity.versionName = `${entity.versionName} (${escapedSubstrings.length})`;
          }
        }

        // check if xdfd is same as in project document
        const projectDocRepo = await event.manager
          .createQueryBuilder(ProjectDocumentEntity, 'ProjectDoc')
          .select('ProjectDoc.xfdf', 'xfdf')
          .where('ProjectDoc.id = :id', { id: entity.projectDocumentId })
          .getRawOne();

        if (projectDocRepo.xfdf === entity.xfdf) return;

        // update project document
        await event.manager
          .createQueryBuilder(ProjectDocumentEntity, 'ProjectDoc')
          .update(ProjectDocumentEntity)
          .set({ xfdf: entity.xfdf })
          .where('id = :id', { id: entity.projectDocumentId })
          .execute();
      } else {
      }
    } catch (e) {
      getErrorMessage(e, 'DrawingRevisionSubscriber', 'afterUpdate');
    }
  }

  async afterUpdate(event: UpdateEvent<DrawingRevisionEntity>) {
    const { entity } = event;

    //get latest drawing
    const latest = await event.manager
      .createQueryBuilder(DrawingRevisionEntity, 'drawingRevision')
      .select('MAX(drawingRevision.version)', 'version')
      .where('drawingRevision.projectDocumentId = :projectId', { projectId: entity.projectDocumentId })
      .getRawOne();

    if (latest?.version === entity.version) {
      //update filename
      await event.manager
        .createQueryBuilder(ProjectDocumentEntity, 'ProjectDoc')
        .update(ProjectDocumentEntity)
        .set({ name: entity.fileName })
        .where('id = :id', { id: entity.projectDocumentId })
        .execute();
    }
  }

  async afterInsert(event: InsertEvent<DrawingRevisionEntity>): Promise<InsertEvent<DrawingRevisionEntity>> {
    try {
      if (event?.entity && event.entity.version !== 1) {
        await this.twoDDrawingNotification(event.entity);
      }

      return event;
    } catch (error) {
      getErrorMessage(error, 'DrawingRevisionSubscriber', 'afterInsert');
    }
  }

  private async twoDDrawingNotification(revision: DrawingRevisionEntity) {
    try {
      const projectDocument = await getRepository(ProjectDocumentEntity).findOne({
        where: { id: revision.projectDocumentId }
      });

      const project = await getRepository(ProjectEntity).findOne({
        where: { id: projectDocument.projectId },
        relations: ['company']
      });

      if (!project.isNotify2DUploaded) return;

      const mobileLink = `drawing-revision/${revision?.projectDocumentId}`;
      const link = `/drawings/2D-drawing/${
        projectDocument?.projectDocumentId ? projectDocument.projectDocumentId : ''
      }?drawingId=${revision.projectDocumentId}&projectId=${project.id}&companyId=${project?.companyId}`;

      const ownerId = revision.createdBy;

      const projectUsers = await getRepository(ProjectUserEntity)
        .createQueryBuilder('projectUser')
        .leftJoinAndSelect('projectUser.user', 'user')
        .where('(projectUser.projectId = :projectId) AND (projectUser.user.id <> :ownerId)', {
          projectId: project.id,
          ownerId
        })
        .andWhere('projectUser.role <> :role', { role: ProjectUserRoleType.CanView })
        .getMany();

      const uploadBy = await getRepository(UserEntity).findOne({ id: revision.createdBy });

      projectUsers.forEach(async (projectUser: ProjectUserEntity) => {
        const owner = projectUser.user
        const payload: INovuPayload = {
          event: 'upload-drawing',
          user: {
            avatar: uploadBy.avatar,
            name: uploadBy.name,
            email: uploadBy.email
          },
          header: '📝 New Drawing Revision',
          company: project?.company.name,
          title: project.title,
          head: uploadBy?.name,
          body: `has uploaded a new drawing revision`,
          tail: revision?.fileName,
          bodyColon: true,
          subscriber: {
            firstName: owner.name
          },
          link: {
            mobile: mobileLink,
            web: link,
            redirect: link,
            uri: ''
          }
        };

        return this.novuService.trigger('secondary-workflow', {
          to: {
            subscriberId: owner.id.toString(),
            email: owner.email
          },
          payload,
          overrides: {
            android: {
              priority: 'high'
            },
            fcm: {
              data: {
                link: mobileLink.toString(),
                projectId: project.id.toString(),
                companyId: project.companyId?.toString()
              }
            }
          }
        });
      });
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentSubscriber', 'twoDDrawingNotification');
    }
  }
}
