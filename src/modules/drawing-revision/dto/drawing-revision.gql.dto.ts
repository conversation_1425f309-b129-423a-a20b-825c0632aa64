import { defaultQueryOptions, SourceType } from '@constants';
import {
  BeforeCreateOne,
  CreateOneInputType,
  FilterableRelation,
  QueryOptions,
  Relation
} from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { DrawingRevisionEntity } from '../entity/drawing-revision.entity';
import { GqlContext } from '@types';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { relationOption } from '@constants/query.constant';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';

@ObjectType('DrawingRevision')
@QueryOptions({ ...defaultQueryOptions })
@FilterableRelation('user', () => UserDto, relationOption(true))
@Relation('projectDocument', () => ProjectDocumentDto, relationOption())
@BeforeCreateOne((instance: CreateOneInputType<CreateDrawingRevisionInputDTO>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  instance.input.createdBy = createdBy;
  return instance;
})
export class DrawingRevisionDto extends DrawingRevisionEntity {
  //? offline mode
  remoteId?: string;
}
@InputType()
export class CreateDrawingRevisionInputDTO {
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;
  @Field(() => String) versionName?: string;
  @Field(() => String) notes?: string;
  @Field(() => String) xfdf?: string;
  @Field(() => Number) projectDocumentId: number;
  @Field(() => Number) createdBy?: number;

  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  localProjectDocumentId?: string;
  fileKey?: string;
  fileName?: string;
  @Field({ nullable: true }) recordSource?: SourceType;
}
@InputType()
export class UpdateDrawingRevisionInputDTO extends PartialType(CreateDrawingRevisionInputDTO) {
  //? for offline
  @Field() localId?: string;
  @Field() remoteId?: number;
  _changed?: string;
  created_at?: Date;
  deleted_at?: Date;
}
