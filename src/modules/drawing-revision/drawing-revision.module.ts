import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { DrawingRevisionService } from './drawing-revision.service';
import { CreateDrawingRevisionInputDTO, DrawingRevisionDto } from './dto/drawing-revision.gql.dto';
import { DrawingRevisionEntity } from './entity/drawing-revision.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { DrawingRevisionSubscriber } from './drawing-revision.subscriber';
import { DrawingRevisionResolver } from './drawing-revision.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([DrawingRevisionEntity]), IntegrationModule],

      resolvers: [
        {
          // ServiceClass: ChangeLogService,
          DTOClass: DrawingRevisionDto,
          EntityClass: DrawingRevisionEntity,
          CreateDTOClass: CreateDrawingRevisionInputDTO,
          // UpdateDTOClass: UpdateChangeLogInputDTO,

          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [DrawingRevisionService, DrawingRevisionEntity, DrawingRevisionSubscriber, DrawingRevisionResolver]
    })
  ]
})
export class DrawingRevisionModule {}
