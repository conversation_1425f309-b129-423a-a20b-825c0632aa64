import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { DrawingRevisionEntity } from './entity/drawing-revision.entity';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class DrawingRevisionService extends TypeOrmQueryService<DrawingRevisionEntity> {
  constructor(
    @InjectRepository(DrawingRevisionEntity)
    drawingRevisionRepo: Repository<DrawingRevisionEntity>,
    private tmOneService: TMOneService
  ) {
    super(drawingRevisionRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(drawing_revision: DrawingRevisionEntity) {
    try {
      let key = drawing_revision.fileKey;
      // fallback to fileUrl if fileKey is missing
      if (!key){
        const fileName = drawing_revision.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
