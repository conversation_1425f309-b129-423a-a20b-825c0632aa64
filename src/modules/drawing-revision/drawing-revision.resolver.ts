import { UseGuards } from '@nestjs/common';
import { <PERSON>solver, ResolveField, Parent } from '@nestjs/graphql';
import { GqlAuthGuard } from '@guards/auth.guard';
import { DrawingRevisionDto } from './dto/drawing-revision.gql.dto';
import { DrawingRevisionService } from './drawing-revision.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => DrawingRevisionDto)
export class DrawingRevisionResolver {
  constructor(private drawingRevisionService: DrawingRevisionService) {}

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: DrawingRevisionDto) {
    return await this.drawingRevisionService.getPresignedUrl(parent);
  }
}
