import { BaseEntity } from '@modules/base/base';
import { Entity, Column, OneToOne, JoinColumn } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { UserEntity } from '@modules/user/entity/user.entity';

@ObjectType()
@Entity('user_settings')
export class UserSettingsEntity extends BaseEntity {
  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  productTourCompleted: string;

  /* -------------------------------- Relations ------------------------------- */

  @OneToOne(() => UserEntity, user => user.userSettings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
