import { defaultQueryOptions } from '@constants';
import { FilterableRelation, QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { relationOption } from '@constants/query.constant';
import { UserSettingsEntity } from '../entity/user-settings.entity';
import { UserDto } from '@modules/user/dto/user.gql.dto';

@ObjectType('UserSettings')
@FilterableRelation('users', () => UserDto, relationOption(true))

@QueryOptions({ ...defaultQueryOptions })
export class UserSettingsDto extends UserSettingsEntity {}

@InputType()
export class CreateUserSettingsInputDTO {
  userId: number;
  productTourCompleted: string;
}
@InputType()
export class UpdateUserSettingsInputDTO extends PartialType(CreateUserSettingsInputDTO) {}
