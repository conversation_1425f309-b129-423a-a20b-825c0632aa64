import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber } from 'typeorm';
import { UserSettingsEntity } from './entity/user-settings.entity';

@Injectable()
@EventSubscriber()
export class UserSettingsSubscriber implements EntitySubscriberInterface<UserSettingsEntity> {
  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return UserSettingsEntity;
  }
}
