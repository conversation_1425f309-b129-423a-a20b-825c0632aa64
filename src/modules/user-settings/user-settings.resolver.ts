import { Args, Mutation, Resolver } from "@nestjs/graphql";
import { UserSettingsDto } from "./dto/user-settings.gql.dto";
import { UserSettingsService } from "./user-settings.service";

@Resolver(() => UserSettingsDto)
export class UserSettingsResolver {
  constructor(private userSettingsService: UserSettingsService) {}

  @Mutation(() => UserSettingsDto)
  async addProductTourCompleted(@Args('userId') userId: number, @Args('productTour') productTour: string): Promise<UserSettingsDto> {
    return await this.userSettingsService.addProductTourCompleted(userId, productTour);
  }
}