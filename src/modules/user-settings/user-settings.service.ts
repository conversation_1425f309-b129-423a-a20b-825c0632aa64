// draft for me standard service
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserSettingsEntity } from './entity/user-settings.entity';
import { Repository } from 'typeorm';
import { UserSettingsDto } from './dto/user-settings.gql.dto';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class UserSettingsService extends TypeOrmQueryService<UserSettingsEntity> {
  constructor(@InjectRepository(UserSettingsEntity) private userSettingsRepository: Repository<UserSettingsEntity>) {
    super(userSettingsRepository, { useSoftDelete: true });
  }

  async addProductTourCompleted(userId: number, productTour: string): Promise<UserSettingsDto> {
    const userSettings = await this.userSettingsRepository.findOne({ where: { userId } });
    if (!userSettings) {
      throw new Error('User settings not found');
    }

    // Parse productTourCompleted string to an array
    let productTourCompletedArray: string[] = [];

    if (userSettings.productTourCompleted) {
      try {
        productTourCompletedArray = JSON.parse(userSettings.productTourCompleted || '[]');
      } catch (error) {
        throw new Error('Failed to parse product tour data');
      }
    }

    // Ensure the product tour is not already in the list
    if (!productTourCompletedArray.includes(productTour)) {
      productTourCompletedArray.push(productTour);
    }

    // Convert the array back to a JSON string
    userSettings.productTourCompleted = JSON.stringify(productTourCompletedArray);

    return this.userSettingsRepository.save(userSettings);
  }
}
