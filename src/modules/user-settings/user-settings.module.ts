import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { UserSettingsEntity } from './entity/user-settings.entity';
import { UserSettingsService } from './user-settings.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { CreateUserSettingsInputDTO, UserSettingsDto } from './dto/user-settings.gql.dto';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([UserSettingsEntity])],
      resolvers: [
        {
          ServiceClass: UserSettingsService,
          DTOClass: UserSettingsDto,
          EntityClass: UserSettingsEntity,
          CreateDTOClass: CreateUserSettingsInputDTO
        }
      ],
      services: [UserSettingsService]
    }),
    IntegrationModule
  ]
  //   controllers: [UserSettingsController]
})
export class UserSettingsModule {}
