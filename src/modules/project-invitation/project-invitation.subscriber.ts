import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { ProjectInvitationEntity } from './entity/project-invitation.entity';
import { nanoid } from 'nanoid/async';
import { UserEntity } from '@modules/user/entity/user.entity';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class ProjectInvitationSubscriber implements EntitySubscriberInterface<ProjectInvitationEntity> {
  constructor(connection: Connection, private mailgunService: MailgunService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectInvitationEntity;
  }

  async beforeInsert(event: InsertEvent<ProjectInvitationEntity>) {
    const { entity } = event;

    entity.invitationRef = await nanoid(50);
  }

  async afterInsert(event: InsertEvent<ProjectInvitationEntity>) {
    try {
      const { entity } = event;
      const { APP_URI } = process.env;

      const invitationUrl = new URL(`${APP_URI}/invitation/`);
      invitationUrl.searchParams.append('invitation_ref', entity.invitationRef);
      const loginLink = new URL(`${APP_URI}/login`);
      const signupLink = new URL(`${APP_URI}/sign-up`);

      const inviter = await event.manager.getRepository(UserEntity).findOne({ id: entity.createdBy });
      const message = `${inviter.name} invited you to collaborate in Bina Cloud under project`;

      const msg = inviter.name + ' has invited user with email ' + entity.email;
      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: inviter.id,
        projectId: entity.projectId,
        // taskId: entity.id,
        resourceId: entity.id,
        module: AuditLogModuleType.Member,
        action: AuditLogActionType.Invited,
        content: msg
      });
      await auditLog.save();

      await this.mailgunService.sendMail({
        to: entity.email,
        subject: 'Project Invitation',
        text: `Hi,\n\n${inviter.name} invited you to join project "${entity.projectTitle}".\n\nClick on the link below to join this project.\n${invitationUrl}`,
        name: inviter.name,
        projectTitle: entity.projectTitle,
        url: invitationUrl,
        template: 'user_onboarding',
        message,
        loginLink,
        signupLink,
        title: entity.projectTitle
      });
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationSubscriber', 'afterInsert');
    }
  }
}
