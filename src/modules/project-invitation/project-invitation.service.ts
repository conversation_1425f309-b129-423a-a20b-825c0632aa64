import { BadRequestException, ConflictException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ProjectInvitationEntity } from './entity/project-invitation.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import * as Dto from './dto/project-invitation.api.dto';
import moment from 'moment';
import * as _ from 'lodash';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import JwtConfig from '@configs/jwt.config';
import { AuditLogActionType, AuditLogModuleType, ProjectUserRoleType, RoleTypeEnum } from '@constants';
import { AuthData, ExpireDate } from '@types';
import { JwtService } from '@nestjs/jwt';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { getErrorMessage } from '@common/error';

@Injectable()
export class ProjectInvitationService extends TypeOrmQueryService<ProjectInvitationEntity> {
  constructor(
    @InjectRepository(ProjectInvitationEntity)
    private projectInvitationRepo: Repository<ProjectInvitationEntity>,
    private jwtService: JwtService,
    private novuService: NovuService
  ) {
    super(projectInvitationRepo, { useSoftDelete: true });
  }

  // Create Project Invitations
  async createProjectInvitations(userId: number, projectId: number, projectInvitations: Dto.ProjectInvitationType[]) {
    try {
      const inviter = await getRepository(UserEntity).findOne({ id: userId });

      // Throw Forbiddden Error If User is Neither Project Owner nor Cloud Coordinator in The Project
      const inviterInProject = await getRepository(ProjectUserEntity).findOne({ userId, projectId });
      if (
        inviterInProject.role !== ProjectUserRoleType.ProjectOwner &&
        inviterInProject.role !== ProjectUserRoleType.CloudCoordinator
      )
        throw new ForbiddenException('Insufficient Role permission for this action.');

      // Get the project to find the company
      const project = await getRepository(ProjectEntity).findOne({ id: projectId });
      if (!project) {
        throw new BadRequestException('Project not found');
      }

      // Check if the company has reached its user limit
      const company = await getRepository(CompanyEntity).findOne(
        { id: project.companyId },
        { relations: ['_users'] }
      );

      if (!company) {
        throw new BadRequestException('Company does not exist');
      }

      const currentUserCount = company._users?.length || 0;

      if (currentUserCount >= company.maxUsers) {
        throw new BadRequestException(`The company has reached its maximum user limit of ${company.maxUsers}. Please upgrade your subscription to add more users.`);
      }

      await this.verifyInvitedEmail(projectId, projectInvitations);

      const newProjectInvitations = await this.uniqByWithinInvitedEmail(projectInvitations);

      for (const newProjectInvitation of newProjectInvitations) {
        await this.createProjectInvitation(userId, projectId, inviter.companyId, newProjectInvitation);
      }
      return;
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'createProjectInvitations');
    }
  }

  // Query to create a Project Invitation
  private async createProjectInvitation(
    userId: number,
    projectId: number,
    companyId: number,
    input: Dto.ProjectInvitationType
  ) {
    try {
      const { email, role } = input;
      const project = await getRepository(ProjectEntity).findOne({ id: projectId });
      const newProjectInvitation = await this.projectInvitationRepo.create({
        projectId,
        companyId,
        createdBy: userId,
        email,
        role,
        expireAt: moment().add(1, 'd').toDate(),
        projectTitle: project.title
      });

      return await this.projectInvitationRepo.save(newProjectInvitation);
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'createProjectInvitation');
    }
  }

  // Verification of email invited to project
  private async verifyInvitedEmail(projectId: number, projectInvitations: Dto.ProjectInvitationType[]) {
    try {
      const projectUsers = await getRepository(ProjectUserEntity).find({
        where: { projectId },
        relations: ['user']
      });

      const duplicateEmailRows = await Promise.all(
        projectInvitations.map(async (projectInvitation, index) => {
          const duplicateEmail = await _.find(projectUsers, ['user.email', projectInvitation.email]);
          if (duplicateEmail) return `"${projectInvitation.email}"`;
          return null;
        })
      );

      // Splice null from the array of error
      const errorRows = duplicateEmailRows.filter(duplicateEmailRow => {
        return duplicateEmailRow !== null;
      });

      if (errorRows.length !== 0) {
        const errors = _.join(errorRows, ', ');
        throw new ConflictException(`The Email: ${errors} had invited to this project.`);
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'verifyInvitedEmail');
    }
  }

  private async uniqByWithinInvitedEmail(projectInvitations: Dto.ProjectInvitationType[]) {
    try {
      return await _.uniqBy(projectInvitations, projectInvitation => {
        return projectInvitation.email;
      });
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'uniqByWithinInvitedEmail');
    }
  }

  /**--------------------------- To get project title by project invitation --------------------------------- */
  async getProjectInviterAndTitle(invitationRef: string) {
    try {
      const projectInvitation = await this.getProjectInvitation(invitationRef);
      if (!projectInvitation) throw new BadRequestException('Project invitation not found');

      const projectTitle = projectInvitation.projectTitle;
      const projectId = projectInvitation.projectId;

      const inviter = await getRepository(UserEntity).findOne({ id: projectInvitation.createdBy });
      const inviterName = inviter.name;
      return { projectTitle, inviterName, projectId };
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'getProjectInviterAndTitle');
    }
  }

  /**---------------------- Project Invitation Event after invitation URL clicked ---------------------------- */
  async getProjectInvitation(invitationRef: string) {
    try {
      const projectInvitation = await this.projectInvitationRepo.findOne({ invitationRef });
      if (!projectInvitation) throw new BadRequestException('Project invitation not found');
      return projectInvitation;
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'getProjectInvitation');
    }
  }

  async projectInvitation(invitationRef: string) {
    try {
      const projectInvitation = await this.projectInvitationRepo.findOne({ invitationRef });
      if (!projectInvitation) throw new BadRequestException('Project invitation not found');

      // Check is project invitation was expired
      if (moment(moment().toDate()).isAfter(projectInvitation.expireAt))
        throw new BadRequestException('This invitation has expired');

      const user = await getRepository(UserEntity).findOne({ email: projectInvitation.email });

      // Check is project invitation was accepted
      if (projectInvitation.isAccepted) {
        return this.getSignInToken(user);
      }

      // THIS BLOCK OF CONDITION NEEDS OPTIMIZATION ON LOGIC
      if (user) {
        await this.projectInvitationIsAccepted(projectInvitation);
        await this.inviteNewProjectUser(projectInvitation, user.id);
        return this.getSignInToken(user);
      } else {
        const newUserByInvitation = await this.createNewUserByInvitation(projectInvitation);
        await this.projectInvitationIsAccepted(projectInvitation);
        await this.inviteNewProjectUser(projectInvitation, newUserByInvitation.id);
        return this.getSignInToken(newUserByInvitation);
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'projectInvitation');
    }
  }

  // Update project invitation to accepted
  private async projectInvitationIsAccepted(projectInvitation: ProjectInvitationEntity) {
    try {
      const { projectId, email } = projectInvitation;
      const projectInvitations = await this.projectInvitationRepo.find({ projectId, email });
      const project = await getRepository(ProjectEntity).findOne(
        { id: projectId },
        { relations: ['projectUsers', 'projectUsers.user'] }
      );

      // TODO: TRY CATCH FOR ERRORS OF NULL ENTITY (TEMPERED PAYLOAD)
      const user = await getRepository(UserEntity).findOne({ email: email }, { relations: ['companies'] });
      const company = await getRepository(CompanyEntity).findOne({ id: project.companyId });

      // Check if the company has reached its user limit
      const companyWithUsers = await getRepository(CompanyEntity).findOne(
        { id: company.id },
        { relations: ['_users'] }
      );

      if (!companyWithUsers) {
        throw new BadRequestException('Company does not exist');
      }

      const currentUserCount = companyWithUsers._users?.length || 0;

      if (currentUserCount >= companyWithUsers.maxUsers) {
        throw new BadRequestException(`The company has reached its maximum user limit of ${companyWithUsers.maxUsers}. Please upgrade your subscription to add more users.`);
      }

      user.companies = [...user.companies, company];
      user.companyId = company.id;
      await getRepository(UserEntity).save(user);

      const msg = user.email + ' has joined the project with role ' + projectInvitation.role;
      const auditLog = getRepository(AuditLogEntity).create({
        userId: user.id,
        projectId: projectId,
        // taskId: entity.id,
        resourceId: user.id,
        module: AuditLogModuleType.Member,
        action: AuditLogActionType.Joined,
        content: msg
      });
      await auditLog.save();

      return await Promise.all(
        projectInvitations.map(async invitation => {
          await this.projectInvitationRepo.merge(invitation, { isAccepted: true });
          return await this.projectInvitationRepo.save(invitation);
        })
      ).then(() => {
        const projectMembers = project.projectUsers.filter(projectUser => {
          return (
            projectUser.role === ProjectUserRoleType.ProjectOwner ||
            projectUser.role === ProjectUserRoleType.CloudCoordinator
          );
        });
        projectMembers.forEach(projectMember => {
          const payload: INovuPayload = {
            user: {
              avatar: user.avatar,
              name: user.name,
              email: user.email
            },
            event: 'invitation-accepted',
            header: 'Invitation Accepted',
            company: '',
            title: project.title,
            body: `A new member has joined. ${
              user.name || user.email
            } has accepted and is now a member of your project. Happy collaborating!`
          };

          this.novuService.trigger('secondary-workflow', {
            to: {
              subscriberId: projectMember.user.id.toString(),
              email: projectMember.user.email
            },
            payload,
            overrides: {
              android: {
                priority: 'high'
              }
            }
          });
        });
      });
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'projectInvitationIsAccepted');
    }
  }

  // Invite user to project
  private async inviteNewProjectUser(projectInvitation: ProjectInvitationEntity, userId: number) {
    try {
      const { projectId, role, createdBy } = projectInvitation;
      const newProjectUser = await getRepository(ProjectUserEntity).create({
        userId,
        projectId,
        role,
        addedBy: createdBy
      });
      return await getRepository(ProjectUserEntity).save(newProjectUser);
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'inviteNewProjectUser');
    }
  }

  // Create new user via invitation
  private async createNewUserByInvitation(projectInvitation: ProjectInvitationEntity) {
    try {
      const { email, companyId } = projectInvitation;
      const newUser = getRepository(UserEntity).create({
        email,
        companyId,
        isEmailVerified: true
      });
      return await getRepository(UserEntity).save(newUser);
    } catch (e) {
      getErrorMessage(e, 'ProjectInvitationService', 'createNewUserByInvitation');
    }
  }

  /**-------------------------- User Sign In after invitation URL clicked -------------------------------- */
  private getAuthData(user: UserEntity) {
    return {
      id: user.id,
      type: RoleTypeEnum.User
    } as AuthData;
  }

  // Get AccessToken and RefreshToken to SignIn
  private async getSignInToken(user: UserEntity) {
    const refreshToken = await this.getRefreshToken(user);
    const accessToken = await this.getAccessToken(user);
    const accessTokenExpiry = await this.getAccessTokenExpiry(accessToken);
    user.refreshToken = refreshToken;
    await getRepository(UserEntity).save(user);

    return { accessToken, refreshToken, accessTokenExpiry };
  }

  private getAccessToken(user: UserEntity) {
    return this.jwtService.sign(
      { ...this.getAuthData(user), type: RoleTypeEnum.User },
      JwtConfig.userAccessTokenConfig
    );
  }

  private getRefreshToken(user: UserEntity) {
    return this.jwtService.sign(this.getAuthData(user), JwtConfig.userRefreshTokenConfig);
  }

  private getAccessTokenExpiry(accessToken: string) {
    const decodedToken = this.jwtService.decode(accessToken) as ExpireDate;
    const accessTokenExpiry = decodedToken?.exp;
    return accessTokenExpiry;
  }
}
