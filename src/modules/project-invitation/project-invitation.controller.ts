import { Get<PERSON>uthData, GetProjectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { BadRequestException, Body, Controller, Get, Post, Query, Patch, HttpException } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ProjectInvitationService } from './project-invitation.service';
import { ProjectUserService } from '../project-user/project-user.service';
import * as Dto from './dto/project-invitation.api.dto';
import { AuthData } from '@types';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { MoreThan, Not, getRepository } from 'typeorm';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { ProjectUserRoleType } from '@constants';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectInvitationEntity } from './entity/project-invitation.entity';
import { getErrorMessage } from '@common/error';

@ApiTags('Project Invitation API')
@Controller('projects')
export class ProjectInvitationController {
  constructor(
    private projectInvitationService: ProjectInvitationService,
    private projectUserService: ProjectUserService
  ) {}

  @UseApiUserAuthGuard()
  @Post('create-invitations')
  async createProjectInvitations(
    @Body() projectInvitation: Dto.ProjectInvitationInputDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ) {
    try {
      const subscription = await getRepository(ProjectEntity).findOne({
        where: { id: projectId }
      });

      const activeSubscription = await getRepository(CompanySubscriptionEntity).findOne(
        {
          companyId: subscription.companyId,
          subscriptionEndDate: MoreThan(new Date())
        },
        {
          order: {
            subscriptionEndDate: 'DESC'
          },
          relations: ['subscriptionPackage']
        }
      );

      if(!activeSubscription)
        throw new HttpException(
          `You do not have an active subscription, please subscribe again.`
        , 400);

      const getProjectMembers = getRepository(ProjectUserEntity).find({
        where: { projectId, role: Not(ProjectUserRoleType.CanView) }
      });

      const getProjectInvitationList = getRepository(ProjectInvitationEntity).find({
        where: { projectId, isAccepted: false, role: Not(ProjectUserRoleType.CanView) }
      });

      const [projectMembers, projectInvitationList] = await Promise.all([getProjectMembers, getProjectInvitationList]);

      const totalMembers = projectMembers.length + projectInvitationList.length;

      const hasNonViewerRole = projectInvitation.projectInvitations.some(
        invitation => invitation.role !== ProjectUserRoleType.CanView
      );

      if (activeSubscription.subscriptionPackage.totalUsers <= totalMembers && hasNonViewerRole) {
        throw new BadRequestException(
          `You have reached the maximum number of ${activeSubscription.subscriptionPackage.totalUsers} members limit`
        );
      }

      const capacity = activeSubscription.subscriptionPackage.totalUsers - totalMembers;

      const filteredInvitation = projectInvitation.projectInvitations.filter(
        invitation => invitation.role !== ProjectUserRoleType.CanView
      );

      if (capacity < filteredInvitation.length && hasNonViewerRole) {
        throw new BadRequestException(
          `Max members reached! You can invite ${capacity} more 'Can Edit' or 'Cloud Coordinator' roles. Upgrade plan for more.`
        );
      }

      const projectInvitations = projectInvitation.projectInvitations;
      if (!projectInvitations) throw new BadRequestException(`Input body is invalid`);
      if (!projectId) throw new BadRequestException('Project Id not found');

      // try
      await this.projectInvitationService.createProjectInvitations(user.id, projectId, projectInvitations);
      return { message: 'Invitation had sent' };
    } catch (e) {
      getErrorMessage(e, 'createProjectInvitations', 'ProjectInvitationController');
    }
  }

  @Post('inviter-and-title')
  async getProjectInviterAndTitle(@Body() body: Dto.ProjectInvitationRef) {
    const { projectTitle, inviterName, projectId } = await this.projectInvitationService.getProjectInviterAndTitle(
      body.invitationRef
    );
    return { projectTitle, inviterName, projectId };
  }

  @Get('invitation')
  async projectInvitations(@Query() query) {
    const { accessToken, refreshToken, accessTokenExpiry } = await this.projectInvitationService.projectInvitation(
      query.invitation_ref
    );

    return {
      accessToken,
      refreshToken,
      accessTokenExpiry
    };
    // return { message: `You have invited to project ` };
  }

  @UseApiUserAuthGuard()
  @Patch('update-member-role')
  async updateMemberRole(
    @Body() body: Dto.ProjectInvitationUpdateRoleInputDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ): Promise<{ message: string }> {
    try {
      const subscription = await getRepository(ProjectEntity).findOne({
        where: { id: projectId }
      });

      const activeSubscription = await getRepository(CompanySubscriptionEntity).findOne(
        {
          companyId: subscription.companyId,
          subscriptionEndDate: MoreThan(new Date())
        },
        {
          order: {
            subscriptionEndDate: 'DESC'
          },
          relations: ['subscriptionPackage']
        }
      );

      const getProjectMembers = getRepository(ProjectUserEntity).find({
        where: { projectId, role: Not(ProjectUserRoleType.CanView) }
      });

      const getProjectInvitationList = getRepository(ProjectInvitationEntity).find({
        where: { projectId, isAccepted: false, role: Not(ProjectUserRoleType.CanView) }
      });

      const [projectMembers, projectInvitationList] = await Promise.all([getProjectMembers, getProjectInvitationList]);

      const totalMembers = projectMembers.length + projectInvitationList.length;

      const projectUser = await getRepository(ProjectUserEntity).findOne({
        where: { projectId, userId: body.userId }
      });

      if (!projectUser) throw new BadRequestException('User not found');

      if (
        activeSubscription.subscriptionPackage.totalUsers <= totalMembers &&
        body.role !== ProjectUserRoleType.CanView &&
        projectUser.role === ProjectUserRoleType.CanView
      ) {
        throw new BadRequestException('You have reached the maximum number of members');
      }

      return await this.projectUserService.updateMemberRole(body, user, projectId);
    } catch (e) {
      getErrorMessage(e, 'updateMemberRole', 'ProjectInvitationController');
    }
  }
}
