import { defaultQueryOptions, ProjectUserRoleType } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ProjectInvitationEntity } from '../entity/project-invitation.entity';

@ObjectType('ProjectInvitation')
@QueryOptions({ ...defaultQueryOptions })
export class ProjectInvitationDto extends ProjectInvitationEntity {}

@InputType()
export class CreateProjectInvitationInputDTO {
  projectId?: number;
  companyId?: number;
  isAccepted?: boolean;
  invitationRef?: string;
  expireAt?: Date;
  email: string;
  role: ProjectUserRoleType;
  projectTitle: string;
}
@InputType()
export class UpdateProjectInvitationInputDTO extends PartialType(CreateProjectInvitationInputDTO) {}
