import { ProjectUserRoleType } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('project_invitations')
export class ProjectInvitationEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column({ unsigned: true })
  companyId: number;

  @FilterableField()
  @Column('boolean', { default: false })
  isAccepted: boolean;

  @FilterableField({ nullable: true })
  @Column('varchar')
  invitationRef: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime')
  expireAt: Date;

  @FilterableField()
  @Column('varchar')
  email: string;

  @FilterableField(() => ProjectUserRoleType)
  @Column('enum', { enum: ProjectUserRoleType, default: ProjectUserRoleType.CanView })
  role: ProjectUserRoleType;

  @FilterableField()
  @Column('varchar')
  projectTitle: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectEntity, project => project.invitations)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;
}
