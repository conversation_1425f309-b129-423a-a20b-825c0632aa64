import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectInvitationEntity } from './entity/project-invitation.entity';
import { ProjectUserEntity } from '../project-user/entity/project-user.entity';
import {
  ProjectInvitationDto,
  CreateProjectInvitationInputDTO,
  UpdateProjectInvitationInputDTO
} from './dto/project-invitation.gql.dto';
import { ProjectInvitationService } from './project-invitation.service';
import { ProjectUserService } from '../project-user/project-user.service';
import { ProjectInvitationController } from './project-invitation.controller';
import { ProjectInvitationSubscriber } from './project-invitation.subscriber';
import { JwtModule } from '@nestjs/jwt';
import { AdminJwtAuthStrategy, UserJwtAuthStrategy } from '@modules/auth/strategy/auth.strategy';
import { AdminEntity } from '@modules/admin/entity/admin.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard, GqlProjectRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';

const strategies = [AdminJwtAuthStrategy, UserJwtAuthStrategy];

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([ProjectInvitationEntity, AdminEntity, UserEntity, ProjectUserEntity]),
        JwtModule.register({}),
        IntegrationModule
      ],
      resolvers: [
        {
          ServiceClass: ProjectInvitationService,
          DTOClass: ProjectInvitationDto,
          EntityClass: ProjectInvitationEntity,
          CreateDTOClass: CreateProjectInvitationInputDTO,
          UpdateDTOClass: UpdateProjectInvitationInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard]
        }
      ],
      services: [ProjectInvitationSubscriber, ProjectInvitationService, MailgunService, ProjectUserService]
    }),
    IntegrationModule
  ],
  providers: [...strategies, MailgunService],
  controllers: [ProjectInvitationController]
})
export class ProjectInvitationModule {}
