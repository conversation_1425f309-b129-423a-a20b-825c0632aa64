import {
    Inject,
    MiddlewareConsumer,
    Module,
    NestModule,
    OnApplicationBootstrap,
  } from '@nestjs/common';
  import { ConfigModule, ConfigType } from '@nestjs/config';
  import prometheusConfig from './config/prometheus.config';
  import { PrometheusService } from './services/prometheus.service';
  import { PrometheusController } from './controllers/prometheus.controller';
  import { MeasureHttp } from './middleware/measure-http.middleware';
  
  @Module({
    imports: [ConfigModule.forFeature(prometheusConfig)],
    providers: [PrometheusService],
    exports: [PrometheusService],
    controllers: [PrometheusController],
  })
  export class PrometheusModule implements NestModule, OnApplicationBootstrap {
    constructor(
      @Inject(prometheusConfig.KEY)
      private readonly config: ConfigType<typeof prometheusConfig>,
      private prometheus: PrometheusService,
    ) {}
  
    onApplicationBootstrap() {
      if (this.config.enableDefaultMetrics) {
        this.prometheus.enableDefaultMetrics();
      }
    }
  
    configure(consumer: MiddlewareConsumer): any {
      if (this.config.enableHttpMetrics) {
        consumer.apply(MeasureHttp).forRoutes('*');
      }
    }
  }