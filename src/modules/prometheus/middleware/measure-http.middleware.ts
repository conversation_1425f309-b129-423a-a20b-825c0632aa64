import { Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response } from 'express';
import { PrometheusService } from '../services/prometheus.service';
import { Histogram } from 'prom-client';
import prometheusConfig from '../config/prometheus.config';
import { ConfigType } from '@nestjs/config';

@Injectable()
export class MeasureHttp implements NestMiddleware {
  httpRequests: Histogram;

  constructor(
    @Inject(prometheusConfig.KEY)
    private readonly config: ConfigType<typeof prometheusConfig>,
    private readonly prometheus: PrometheusService,
  ) {
    this.httpRequests = this.prometheus.createHistogram({
      help: 'Tracks HTTP requests',
      name: 'http_requests',
      labelNames: ['method', 'path', 'statusCode', 'operationName'],
      buckets: [0.05, 0.1, 0.3, 0.7, 1, 2, 5, 10],
    });
  }

  use(req: Request, res: Response, next: () => void) {
    const start = Date.now();
    res.once('finish', () => {
      const { method, route, body } = req;
      const { statusCode } = res;
      const duration = (Date.now() - start) / 1000;
      this.httpRequests.observe(
        {
          method,
          // utilizes route path instead of req.path to make sure is static (ie not affected by param values), the goal is to minimize cardinality
          path: route?.path || 'unmatched',
          statusCode,
          operationName: body?.operationName || 'unknown',
        },
        duration,
      );
    });
    next();
  }
}