import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { TimezoneDto } from './dto/timezone.gql.dto';
import { TimezoneEntity } from './entity/timezone.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([TimezoneEntity])],
      resolvers: [
        {
          DTOClass: TimezoneDto,
          EntityClass: TimezoneEntity,
          create: {
            disabled: true
          },
          update: {
            disabled: true
          },
          delete: {
            disabled: true
          }
        }
      ]
    })
  ]
})
export class TimezoneModule {}
