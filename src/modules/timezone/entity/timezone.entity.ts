import { BaseEntity } from '@modules/base/base';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType } from '@nestjs/graphql';
import { Column, Entity, OneToMany } from 'typeorm';

@ObjectType()
@Entity('timezones')
export class TimezoneEntity extends BaseEntity {
  @FilterableField()
  @Column('varchar', { length: 200 })
  name: string;

  @FilterableField()
  @Column('varchar', { length: 200 })
  value: string;

  @OneToMany(() => UserEntity, users => users.timezone)
  users: UserEntity[];
}
