import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ChecklistEntity } from '../entity/checklist.entity';

@ObjectType('Checklist')
@QueryOptions({ ...defaultQueryOptions })
export class ChecklistDto extends ChecklistEntity {}

@InputType()
export class CreateChecklistInputDTO {
  taskId: number;
  title: string;
}
@InputType()
export class UpdateChecklistInputDTO extends PartialType(CreateChecklistInputDTO) {}
