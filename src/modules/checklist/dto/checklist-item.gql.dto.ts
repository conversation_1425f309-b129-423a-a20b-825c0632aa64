import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ChecklistItemEntity } from '../entity/checklist-item.entity';

@ObjectType('ChecklistItem')
@QueryOptions({ ...defaultQueryOptions })
export class ChecklistItemDto extends ChecklistItemEntity {}

@InputType()
export class CreateChecklistItemInputDTO {
  checklistId: number;
  description: string;
  completed: boolean;
}
@InputType()
export class UpdateChecklistItemInputDTO extends PartialType(CreateChecklistItemInputDTO) {}
