import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ChecklistEntity } from './entity/checklist.entity';
import { ChecklistItemEntity } from './entity/checklist-item.entity';
import { ChecklistDto, CreateChecklistInputDTO, UpdateChecklistInputDTO } from './dto/checklist.gql.dto';
import {
  ChecklistItemDto,
  CreateChecklistItemInputDTO,
  UpdateChecklistItemInputDTO
} from './dto/checklist-item.gql.dto';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ChecklistEntity, ChecklistItemEntity])],
      resolvers: [
        {
          DTOClass: ChecklistDto,
          EntityClass: ChecklistEntity,
          CreateDTOClass: CreateChecklistInputDTO,
          UpdateDTOClass: UpdateChecklistInputDTO
        },
        {
          DTOClass: ChecklistItemDto,
          EntityClass: ChecklistItemEntity,
          CreateDTOClass: CreateChecklistItemInputDTO,
          UpdateDTOClass: UpdateChecklistItemInputDTO
        }
      ],
      services: []
    })
  ]
})
export class ChecklistModule {}
