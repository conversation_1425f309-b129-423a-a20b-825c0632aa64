import { BaseEntity } from '@modules/base/base';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ChecklistItemEntity } from './checklist-item.entity';

@ObjectType()
@Entity('checklists')
export class ChecklistEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  taskId: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => TaskEntity, task => task.checklists)
  @JoinColumn({ name: 'taskId' })
  task: TaskEntity;

  @OneToMany(() => ChecklistItemEntity, checklistItems => checklistItems.checklist)
  checklistItems: ChecklistItemEntity[];
}
