import { BaseEntity } from '@modules/base/base';
import { <PERSON>ield, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';
import { ChecklistEntity } from './checklist.entity';

@ObjectType()
@Entity('checklist_items')
export class ChecklistItemEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  checklistId: number;

  @FilterableField()
  @Column('text')
  description: string;

  @FilterableField()
  @Column('boolean')
  completed: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ChecklistEntity, checklist => checklist.checklistItems)
  @JoinColumn({ name: 'checklistId' })
  checklist: ChecklistEntity;
}
