import { Injectable, Logger } from '@nestjs/common';
import { SalesOrderEntity } from './entity/sales-order.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Not, Repository, getManager, getRepository } from 'typeorm';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { createId } from '@paralleldrive/cuid2';
import { SenangPayFormDto, PaymentStatusDto, SalesOrderDto, BillplzFormDto } from './dto/sales-order.gql.dto';
import { SenangPayService } from '@modules/integration/senangpay/senangpay.service';
import { BillplzService } from '@modules/integration/billplz/billplz.service';
import { generateInvoiceNumber } from '@common/common-helper';
import { getErrorMessage } from '@common/error';
import { SubscriptionPackageService } from '@modules/subscription-package/subscription-package.service';
import { calculateSubscriptionPrice } from '@common/pricing-helper';
import { PaymentStatus } from './enums/payment-status.enum';
import { SalesOrderStatus } from '@constants';
import moment from 'moment';

interface BillplzBillDetails {
  id: string;
  collection_id: string;
  paid_at: string;
  state: string;
  reference_1: string;
}

@Injectable()
export class SalesOrderService {
  private readonly logger = new Logger(SalesOrderService.name);

  constructor(
    @InjectRepository(SalesOrderEntity)
    private readonly salesOrderRepo: Repository<SalesOrderEntity>,
    private senangPayService: SenangPayService,
    private billplzService: BillplzService,
    private subscriptionPackageService: SubscriptionPackageService
  ) {}

  async createSenangPayForm(userId: number, subscriptionPackageId: string): Promise<SenangPayFormDto[]> {
    try {
      const entityManager = getManager();
      let data: SenangPayFormDto[] = [];

      try {
        await entityManager.transaction(async (transactionalEntityManager: EntityManager) => {
          const [user, subscriptionPackage] = await Promise.all([
            transactionalEntityManager
              .getRepository(UserEntity)
              .findOne(userId, { relations: ['myCompany', 'myCompany.companySubscriptions'] }),
            transactionalEntityManager.getRepository(SubscriptionPackageEntity).findOne(subscriptionPackageId)
          ]);

          const activeSubscription = user?.myCompany?.companySubscriptions.find(
            subscription => subscription.subscriptionEndDate > new Date()
          );

          if (activeSubscription) {
            throw new Error('You already have an active subscription');
          }

          const salesOrderRepo = transactionalEntityManager.getRepository(SalesOrderEntity);
          const pendingSalesOrders = await salesOrderRepo.find({
            userId,
            companyId: user.companyId,
            status: SalesOrderStatus.Pending,
            subscriptionPackageId: Not(subscriptionPackage.id),
            isRecurrence: false
          });

          if (pendingSalesOrders.length > 0) {
            await salesOrderRepo.update(
              pendingSalesOrders.map(salesOrder => salesOrder.id),
              {
                status: SalesOrderStatus.Cancelled
              }
            );
          }

          let salesOrder = await salesOrderRepo.findOne({
            userId,
            companyId: user.companyId,
            status: SalesOrderStatus.Pending,
            subscriptionPackageId: subscriptionPackage.id,
            isRecurrence: false
          });

          if (!salesOrder) {
            salesOrder = await salesOrderRepo.save({
              userId,
              cuid: createId(),
              companyId: user.companyId,
              subscriptionPackageId: subscriptionPackage.id,
              total: subscriptionPackage.amount,
              invoiceNumber: generateInvoiceNumber()
            });
          }

          const hash = this.senangPayService.generateHash(
            subscriptionPackage.title,
            subscriptionPackage.amount,
            salesOrder.cuid.toString()
          );

          data = [
            {
              name: 'hash',
              value: hash
            },
            {
              name: 'detail',
              value: subscriptionPackage.title
            },
            {
              name: 'amount',
              value: subscriptionPackage.amount.toString()
            },
            {
              name: 'order_id',
              value: salesOrder.cuid.toString()
            },
            {
              name: 'name',
              value: user.name ?? ''
            },
            {
              name: 'email',
              value: user.email ?? ''
            },
            {
              name: 'phone',
              value: user.phoneNo ?? ''
            }
          ];
        });
      } catch (error) {
        getErrorMessage(error, 'SalesOrderService', 'createSenangPayForm');
      }

      return data;
    } catch (e) {
      getErrorMessage(e, 'SalesOrderService', 'createSenangPayForm');
    }
  }

  async createSenangPayPaymentForm(userId: number, orderId: string): Promise<SenangPayFormDto[]> {
    const entityManager = getManager();
    let data;

    try {
      await entityManager.transaction(async (transactionalEntityManager: EntityManager) => {
        const salesOrderRepo = transactionalEntityManager.getRepository(SalesOrderEntity);

        const [user, salesOrder] = await Promise.all([
          transactionalEntityManager
            .getRepository(UserEntity)
            .findOne(userId, { relations: ['myCompany', 'myCompany.companySubscriptions'] }),
          salesOrderRepo.findOne(orderId, { relations: ['subscriptionPackage'] })
        ]);

        const pendingSalesOrders = await salesOrderRepo.find({
          userId,
          companyId: user.companyId,
          subscriptionPackageId: Not(salesOrder.subscriptionPackage.id),
          status: SalesOrderStatus.Pending,
          isRecurrence: false
        });

        if (pendingSalesOrders.length > 0) {
          await salesOrderRepo.update(
            pendingSalesOrders.map(salesOrder => salesOrder.id),
            {
              status: SalesOrderStatus.Cancelled
            }
          );
        }

        const hash = this.senangPayService.generateHash(
          salesOrder.subscriptionPackage.title,
          salesOrder.subscriptionPackage.amount,
          salesOrder.cuid.toString()
        );

        data = [
          {
            name: 'hash',
            value: hash
          },
          {
            name: 'detail',
            value: salesOrder.subscriptionPackage.title
          },
          {
            name: 'amount',
            value: salesOrder.subscriptionPackage.amount.toString()
          },
          {
            name: 'order_id',
            value: salesOrder.cuid.toString()
          },
          {
            name: 'name',
            value: user.name ?? ''
          },
          {
            name: 'email',
            value: user.email ?? ''
          },
          {
            name: 'phone',
            value: user.phoneNo ?? ''
          }
        ];
      });

      return data;
    } catch (error) {
      getErrorMessage(error, 'SalesOrderService', 'createSenangPayPaymentForm');
    }
  }

  async checkSenangPayPaymentStatus(
    orderId: string,
    transactionId: string,
    hash: string,
    msg: string
  ): Promise<PaymentStatusDto> {
    try {
      const salesOrderRepo = getRepository(SalesOrderEntity);
      const userRepo = getRepository(UserEntity);

      const salesOrder = await salesOrderRepo.findOne({ cuid: orderId }, { relations: ['subscriptionPackage'] });

      if (!salesOrder) {
        throw new Error('Invalid request');
      }

      if (salesOrder.status === SalesOrderStatus.Paid || salesOrder.status === SalesOrderStatus.Declined) {
        return {
          status: salesOrder.status,
          message: salesOrder.message.replace(/_/g, ' '),
          orderId,
          name: salesOrder.subscriptionPackage.title
        };
      }

      const paymentStatus = await this.senangPayService.transactionStatus(transactionId);
      const user = await userRepo.findOne(salesOrder.userId);

      if (!user) {
        throw new Error('Invalid request');
      }

      const salesOrders = await salesOrderRepo.find({
        userId: user.id,
        companyId: user.companyId,
        status: SalesOrderStatus.Pending
      });

      const otherOrders = salesOrders.filter(so => so.cuid !== orderId);
      await Promise.all(otherOrders.map(order => salesOrderRepo.update(order.id, { status: SalesOrderStatus.Cancelled })));

      const paymentStatusValue = paymentStatus.data[0].payment_info.status;
      const paymentMethod = paymentStatus.data[0].payment_info.payment_mode;
      let newStatus: SalesOrderStatus;

      if (paymentStatusValue === 'paid') {
        newStatus = SalesOrderStatus.Paid;
      } else if (paymentStatusValue === 'failed') {
        newStatus = SalesOrderStatus.Declined;
      } else if (paymentStatusValue === 'pending payment') {
        newStatus = SalesOrderStatus.InProgress;
      } else {
        throw new Error('Payment failed, please contact admin to resolve the issue.');
      }

      await salesOrderRepo.update(
        { cuid: orderId },
        {
          status: newStatus,
          transactionId,
          message: msg,
          hash,
          data: JSON.stringify(paymentStatus),
          paymentMethod
        }
      );

      return {
        status: newStatus,
        message: msg.replace(/_/g, ' '),
        orderId,
        name: salesOrder.subscriptionPackage.title
      };
    } catch (e) {
      getErrorMessage(e, 'SalesOrderService', 'checkSenangPayPaymentStatus');
    }
  }

  async getInvoice(userId: number, cuid: string): Promise<SalesOrderDto> {
    const salesOrder = await this.salesOrderRepo.findOne({ userId, cuid }, { relations: ['subscriptionPackage'] });

    if (!salesOrder) {
      throw new Error('Invalid request');
    }

    return salesOrder;
  }

  /**
   * Create a Billplz payment form for subscription purchase
   * @param userId User ID
   * @param subscriptionPackageId Subscription package ID
   * @param teamSize Team size for pricing calculation
   * @param isYearly Flag for yearly billing
   * @returns Billplz form data
   */
  async createBillplzForm(
    userId: number,
    subscriptionPackageId: string,
    teamSize: number,
    isYearly: boolean
  ): Promise<BillplzFormDto> {
    try {
      const entityManager = getManager();
      let data: BillplzFormDto;

      try {
        await entityManager.transaction(async (transactionalEntityManager: EntityManager) => {
          const [user, subscriptionPackage] = await Promise.all([
            transactionalEntityManager
              .getRepository(UserEntity)
              .findOne(userId, { relations: ['myCompany', 'myCompany.companySubscriptions'] }),
            transactionalEntityManager.getRepository(SubscriptionPackageEntity).findOne(subscriptionPackageId)
          ]);

          const activeSubscription = user?.myCompany?.companySubscriptions.find(
            subscription => subscription.subscriptionEndDate > new Date()
          );

          if (activeSubscription) {
            throw new Error('You already have an active subscription');
          }

          const salesOrderRepo = transactionalEntityManager.getRepository(SalesOrderEntity);
          const pendingSalesOrders = await salesOrderRepo.find({
            userId,
            companyId: user.companyId,
            status: SalesOrderStatus.Pending,
            subscriptionPackageId: Not(subscriptionPackage.id),
            isRecurrence: false
          });

          if (pendingSalesOrders.length > 0) {
            await salesOrderRepo.update(
              pendingSalesOrders.map(salesOrder => salesOrder.id),
              {
                status: SalesOrderStatus.Cancelled
              }
            );
          }

          let salesOrder = await salesOrderRepo.findOne({
            userId,
            companyId: user.companyId,
            status: SalesOrderStatus.Pending,
            subscriptionPackageId: subscriptionPackage.id,
            isRecurrence: false
          });

          // Always calculate the price on the backend based on team size and billing period
          // This ensures the frontend can't manipulate the price
          const validTeamSize = Math.max(1, teamSize); // Ensure at least 1 user
          const validIsYearly = Boolean(isYearly);

          // Get current date for billing calculations
          const today = new Date();

          // Calculate the complete subscription price using the enhanced pricing helper
          const pricingResult = calculateSubscriptionPrice(subscriptionPackage, validTeamSize, validIsYearly, today);

          // Log billing details for debugging
          this.logger.debug(`Current date: ${moment(today).format('YYYY-MM-DD')}`);
          this.logger.debug(`Is first day of month: ${pricingResult.isFirstDayOfMonth}`);
          this.logger.debug(`Is after mid-month (14th): ${pricingResult.isAfterMidMonth}`);
          this.logger.debug(`Proration applied: ${pricingResult.firstPeriod.isProrated}`);

          // Create a shorter description that shows both periods while staying within Billplz's 200-character limit
          let shortDescription = `${subscriptionPackage.title} (${validTeamSize}) | ${
            pricingResult.firstPeriod.label
          }: ${pricingResult.firstPeriod.formattedStartDate}-${
            pricingResult.firstPeriod.formattedEndDate
          } RM${pricingResult.firstPeriod.totalAmount.toFixed(2)} | Next: ${
            pricingResult.fullMonthPeriod.formattedStartDate
          }-${pricingResult.fullMonthPeriod.formattedEndDate} RM${pricingResult.fullMonthPeriod.totalAmount.toFixed(
            2
          )} | Total: RM${pricingResult.combined.totalAmount.toFixed(2)}`;

          // Check if description is too long and truncate if necessary
          if (shortDescription.length > 190) {
            // Leave some buffer
            // Create a more compact fallback description
            shortDescription = `${subscriptionPackage.title} (${validTeamSize}) | ${
              pricingResult.firstPeriod.formattedStartDate
            }-${pricingResult.fullMonthPeriod.formattedEndDate} | Total: RM${pricingResult.combined.totalAmount.toFixed(
              2
            )}`;

            // If still too long, use the most minimal version
            if (shortDescription.length > 190) {
              shortDescription = `${
                subscriptionPackage.title
              } (${validTeamSize}) | Total: RM${pricingResult.combined.totalAmount.toFixed(2)}`;
            }
          }

          // Log the description length for debugging
          this.logger.debug(`Description length: ${shortDescription.length} characters`);
          this.logger.debug(`Description: ${shortDescription}`);

          // Create the billing period label
          const billingPeriod = validIsYearly ? 'Yearly' : 'Monthly';

          // Store the full description in the order data for reference
          const fullDescription = `${subscriptionPackage.title} - ${billingPeriod} (${validTeamSize} users)
1. ${pricingResult.firstPeriod.label} (${moment(pricingResult.firstPeriod.startDate).format('MMM D')} - ${moment(
            pricingResult.firstPeriod.endDate
          ).format('MMM D')}): RM${pricingResult.firstPeriod.totalAmount}
   Base: RM${pricingResult.firstPeriod.baseAmount}, SST: RM${pricingResult.firstPeriod.sstAmount}
2. Full month (${moment(pricingResult.fullMonthPeriod.startDate).format('MMM D')} - ${moment(
            pricingResult.fullMonthPeriod.endDate
          ).format('MMM D')}): RM${pricingResult.fullMonthPeriod.totalAmount}
   Base: RM${pricingResult.fullMonthPeriod.baseAmount}, SST: RM${pricingResult.fullMonthPeriod.sstAmount}
Total: RM${pricingResult.combined.totalAmount} (Base: RM${pricingResult.combined.baseAmount}, SST: RM${
            pricingResult.combined.sstAmount
          })`;

          // Use the short description for Billplz
          const description = shortDescription;

          // Store the original amount for reference
          const originalAmount = subscriptionPackage.amount;

          // Prepare data to store with the order
          const orderData = {
            teamSize: validTeamSize,
            isYearly: validIsYearly,
            originalAmount: originalAmount,
            fullDescription: fullDescription, // Store the full description for reference
            billingDetails: {
              firstPeriod: {
                isProrated: pricingResult.firstPeriod.isProrated,
                isAfterMidMonth: pricingResult.isAfterMidMonth,
                dayOfMonth: pricingResult.dayOfMonth,
                label: pricingResult.firstPeriod.label,
                startDate: moment(pricingResult.firstPeriod.startDate).format('YYYY-MM-DD'),
                endDate: moment(pricingResult.firstPeriod.endDate).format('YYYY-MM-DD'),
                amount: pricingResult.firstPeriod.totalAmount,
                baseAmount: pricingResult.firstPeriod.baseAmount,
                sstAmount: pricingResult.firstPeriod.sstAmount
              },
              fullMonthPeriod: {
                startDate: moment(pricingResult.fullMonthPeriod.startDate).format('YYYY-MM-DD'),
                endDate: moment(pricingResult.fullMonthPeriod.endDate).format('YYYY-MM-DD'),
                amount: pricingResult.fullMonthPeriod.totalAmount,
                baseAmount: pricingResult.fullMonthPeriod.baseAmount,
                sstAmount: pricingResult.fullMonthPeriod.sstAmount
              },
              combinedAmount: pricingResult.combined.totalAmount,
              combinedBaseAmount: pricingResult.combined.baseAmount,
              combinedSstAmount: pricingResult.combined.sstAmount,
              subscriptionEndDate: pricingResult.formattedSubscriptionEndDate
            }
          };

          if (!salesOrder) {
            salesOrder = await salesOrderRepo.save({
              userId,
              cuid: createId(),
              companyId: user.companyId,
              subscriptionPackageId: subscriptionPackage.id,
              total: pricingResult.combined.totalAmount,
              invoiceNumber: generateInvoiceNumber(),
              data: JSON.stringify(orderData)
            });
          } else {
            // Update existing sales order with new calculation
            await salesOrderRepo.update(
              { id: salesOrder.id },
              {
                total: pricingResult.combined.totalAmount,
                data: JSON.stringify(orderData)
              }
            );

            // Refresh sales order data
            salesOrder = await salesOrderRepo.findOne(salesOrder.id);
          }

          // Create a bill in Billplz
          const callbackUrl = `${process.env.API_URL}/api/gateway/billplz/callback`;
          const redirectUrl = `${process.env.APP_URI}/payment/status`;

          try {
            const billResponse = await this.billplzService.createBill(
              description,
              pricingResult.combined.totalAmountInCents, // Combined amount in cents
              salesOrder.cuid.toString(),
              user.name || 'Customer',
              user.email,
              callbackUrl,
              redirectUrl
            );

            if (!billResponse) {
              throw new Error('Failed to create bill: No response received from payment gateway');
            }

            if (!billResponse.id) {
              throw new Error('Failed to create bill: Invalid response from payment gateway');
            }

            // Store the Billplz bill ID in the sales order
            await salesOrderRepo.update(
              { id: salesOrder.id },
              {
                transactionId: billResponse.id,
                data: JSON.stringify({
                  ...JSON.parse(salesOrder.data || '{}'),
                  billplzResponse: billResponse
                })
              }
            );

            // Return the bill URL as a form field
            data = {
              fieldType: 'billplz_url',
              paymentUrl: billResponse.url
            };
          } catch (billplzError) {
            throw new Error(`Failed to create bill: ${billplzError.message}`);
          }
        });
      } catch (error) {
        const errorMsg = `${error.message || 'Unknown error'} - Please check Billplz API key and configuration.`;
        getErrorMessage(new Error(errorMsg), 'SalesOrderService', 'createBillplzForm');
      }

      return data;
    } catch (e) {
      getErrorMessage(e, 'SalesOrderService', 'createBillplzForm');
      throw e;
    }
  }

  async checkBillplzPaymentStatus(
    orderId: string,
    billId: string,
    paid: boolean,
    transactionId: string, // Used in the payment gateway callback
    xSignature: string
  ): Promise<PaymentStatusDto> {
    try {
      this.logger.debug(`Processing Billplz payment status - orderId: ${orderId}, billId: ${billId}, paid: ${paid}`);

      const salesOrderRepo = getRepository(SalesOrderEntity);
      const userRepo = getRepository(UserEntity);

      // Look up the sales order by cuid
      this.logger.debug(`Looking up sales order with cuid: ${orderId}`);
      const salesOrder = await salesOrderRepo.findOne({ cuid: orderId }, { relations: ['subscriptionPackage'] });

      if (!salesOrder) {
        this.logger.error(`Sales order not found with cuid: ${orderId}`);
        throw new Error('Invalid request');
      }

      this.logger.debug(`Found sales order: ID=${salesOrder.id}, Status=${salesOrder.status}, Package=${salesOrder.subscriptionPackage?.title}`);


      if (salesOrder.status === SalesOrderStatus.Paid || salesOrder.status === SalesOrderStatus.Declined) {
        return {
          status: salesOrder.status,
          message: salesOrder.message || 'Payment processed',
          orderId,
          name: salesOrder.subscriptionPackage.title
        };
      }

      const user = await userRepo.findOne(salesOrder.userId);

      if (!user) {
        throw new Error('Invalid request');
      }

      // Cancel other pending orders
      const salesOrders = await salesOrderRepo.find({
        userId: user.id,
        companyId: user.companyId,
        status: SalesOrderStatus.Pending
      });

      const otherOrders = salesOrders.filter(so => so.cuid !== orderId);
      await Promise.all(otherOrders.map(order => salesOrderRepo.update(order.id, { status: SalesOrderStatus.Cancelled })));

      // Determine payment status
      let newStatus: SalesOrderStatus;
      let message: string;

      if (paid) {
        newStatus = SalesOrderStatus.Paid;
        message = 'Payment successful';

        // Parse existing sales order data
        let orderData: any = {};
        try {
          if (salesOrder.data) {
            orderData = JSON.parse(salesOrder.data);
          }
        } catch (e) {
          this.logger.error(`Error parsing sales order data: ${e.message}`);
        }

        // Create cleaned data object with important information preserved
        const cleanedData = {
          // Keep essential subscription details
          teamSize: orderData.teamSize,
          isYearly: orderData.isYearly,

          // Keep billing calculation details if they exist
          billingDetails: orderData.billingDetails,

          // Add payment confirmation details
          payment: {
            id: billId,
            paid: paid,
            paidAt: new Date().toISOString(),
            transactionId: transactionId || billId,
            paymentMethod: 'billplz'
          }
        };

        // If there are any other important fields to preserve, add them here
        if (orderData.originalAmount) cleanedData['originalAmount'] = orderData.originalAmount;
        if (orderData.fullDescription) cleanedData['fullDescription'] = orderData.fullDescription;

        this.logger.debug(`Updating sales order with cleaned data: ${JSON.stringify(cleanedData)}`);

        // Update sales order with cleaned data
        await salesOrderRepo.update(
          { cuid: orderId },
          {
            status: newStatus,
            transactionId: billId,
            message: message,
            hash: xSignature,
            data: JSON.stringify(cleanedData),
            paymentMethod: 'billplz'
          }
        );

        // Get the updated sales order with ID
        const updatedSalesOrder = await salesOrderRepo.findOne({ cuid: orderId });

        if (updatedSalesOrder) {
          // Activate the subscription
          const activationResult = await this.subscriptionPackageService.activateSubscription(
            updatedSalesOrder.id,
            'billplz'
          );

          if (!activationResult) {
            console.error(`Failed to activate subscription for sales order ${updatedSalesOrder.id}`);
          }
        }
      } else {
        newStatus = SalesOrderStatus.Declined;
        message = 'Payment failed';

        // Parse existing sales order data
        let orderData: any = {};
        try {
          if (salesOrder.data) {
            orderData = JSON.parse(salesOrder.data);
          }
        } catch (e) {
          this.logger.error(`Error parsing sales order data: ${e.message}`);
        }

        // Create cleaned data object with important information preserved
        const cleanedData = {
          // Keep essential subscription details
          teamSize: orderData.teamSize,
          isYearly: orderData.isYearly,

          // Keep billing calculation details if they exist
          billingDetails: orderData.billingDetails,

          // Add payment failure details
          payment: {
            id: billId,
            paid: paid,
            failedAt: new Date().toISOString(),
            transactionId: transactionId || billId,
            paymentMethod: 'billplz'
          }
        };

        // If there are any other important fields to preserve, add them here
        if (orderData.originalAmount) cleanedData['originalAmount'] = orderData.originalAmount;
        if (orderData.fullDescription) cleanedData['fullDescription'] = orderData.fullDescription;

        this.logger.debug(`Updating sales order with declined payment data: ${JSON.stringify(cleanedData)}`);

        // Update sales order with cleaned data
        await salesOrderRepo.update(
          { cuid: orderId },
          {
            status: newStatus,
            transactionId: billId,
            message: message,
            hash: xSignature,
            data: JSON.stringify(cleanedData),
            paymentMethod: 'billplz'
          }
        );
      }

      return {
        status: newStatus,
        message: message,
        orderId,
        name: salesOrder.subscriptionPackage.title
      };
    } catch (e) {
      getErrorMessage(e, 'SalesOrderService', 'checkBillplzPaymentStatus');
    }
  }

  async handleBillplzRedirect(
    billId: string,
    paid: boolean,
    xSignature: string,
    transactionId: string
  ): Promise<PaymentStatusDto> {
    try {
      // Get bill details from Billplz to find the order ID
      const billDetails = await this.billplzService.getBill(billId);

      if (!billDetails || !billDetails.reference_1) {
        throw new Error('Failed to get bill details');
      }

      const orderId = billDetails.reference_1;

      // Process the payment status
      return this.checkBillplzPaymentStatus(orderId, billId, paid, transactionId, xSignature);
    } catch (e) {
      getErrorMessage(e, 'SalesOrderService', 'handleBillplzRedirect');
      return {
        status: SalesOrderStatus.Declined,
        message: 'Failed to process payment',
        orderId: '',
        name: ''
      };
    }
  }

  async getBillplzStatus(billId: string): Promise<string> {
    try {
      const billDetails = await this.billplzService.getBill(billId);

      if (!billDetails) {
        return PaymentStatus.NOT_PAID;
      }

      return billDetails.state === 'paid' ? PaymentStatus.PAID : PaymentStatus.NOT_PAID;
    } catch (e) {
      this.logger.error(`Error in getBillplzStatus for billId ${billId}: ${e.message}`);
      return PaymentStatus.NOT_PAID;
    }
  }
}
