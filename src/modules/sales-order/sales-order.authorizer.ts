import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { RoleTypeEnum } from '@constants';
import { SalesOrderDto } from './dto/sales-order.gql.dto';
import { getRepository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';

@Injectable()
export class SalesOrderAuthorizer implements Authorizer<SalesOrderDto> {
  async authorize(context: any): Promise<Filter<SalesOrderDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const userId = context?.req?.user?.id;
    if (!userId) throw new UnauthorizedException('User not found');

    const user = await getRepository(UserEntity).findOne(
      {
        id: userId
      },
      { relations: ['myCompany'] }
    );

    if (!user.myCompany) throw new UnauthorizedException('Company not found');

    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({
        companyId: { eq: user.myCompany.id }
      });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
