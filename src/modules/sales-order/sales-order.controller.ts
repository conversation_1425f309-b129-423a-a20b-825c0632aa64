import { Body, Controller, Post, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { PaymentStatusDto, BillplzCallbackDto } from './dto/sales-order.gql.dto';
import { SalesOrderService } from './sales-order.service';
import { BillplzService } from '@modules/integration/billplz/billplz.service';

@Controller('/gateway')
export class SalesOrderController {
  constructor(private salesOrderService: SalesOrderService, private billplzService: BillplzService) {}
  private readonly logger = new Logger(BillplzService.name);

  @Post('senangpay/callback')
  async senangpay(@Body() body: any): Promise<PaymentStatusDto> {
    const { order_id, transaction_id, hash, msg } = body;
    if (!order_id || !transaction_id || !hash || !msg) {
      return { status: 'failed', message: 'Invalid request' };
    }
    return this.salesOrderService.checkSenangPayPaymentStatus(order_id, transaction_id, hash, msg);
  }

  @Post('billplz/callback')
  @HttpCode(HttpStatus.OK) // Billplz expects 200
  async billplzCallback(
    @Body() body: BillplzCallbackDto,
  ): Promise<'OK'> {
    this.logger.log(`Received Billplz callback: ${JSON.stringify(body)}`);

    if (!body.id || !body.x_signature) {
      this.logger.warn('Missing id or x_signature; ignoring');
      return 'OK';
    }

    const valid = this.billplzService.verifyCallbackSignature(body);
    if (!valid) {
      this.logger.warn(`Invalid X-Signature for bill ${body.id}`);
      return 'OK';
    }

    this.logger.log(`Signature verified for bill ${body.id}; processing…`);

    try {
      let orderId: string;

      // Check if reference_1 exists in the callback data
      if (body.reference_1) {
        this.logger.debug(`Using reference_1 from callback: ${body.reference_1}`);
        orderId = body.reference_1;
      } else {
        // If reference_1 is missing, get bill details from Billplz API
        this.logger.debug(`Missing reference_1 in callback data, fetching bill details from Billplz API for bill ${body.id}`);
        const billDetails = await this.billplzService.getBill(body.id);

        if (!billDetails || !billDetails.reference_1) {
          this.logger.warn(`Could not retrieve reference_1 for bill ${body.id} from Billplz API`);
          return 'OK';
        }

        orderId = billDetails.reference_1;
        this.logger.debug(`Retrieved order ID (reference_1): ${orderId} for bill ${body.id} from Billplz API`);
      }

      // Process the payment with the retrieved orderId
      await this.salesOrderService.checkBillplzPaymentStatus(
        orderId,               // Use reference_1 as orderId which matches the sales order cuid
        body.id,               // billId
        body.paid === 'true',  // paid status
        body.id,               // transactionId = billId
        body.x_signature       // x_signature
      );
    } catch (err) {
      this.logger.error(
        `Error processing payment: ${err.message}`,
        err.stack
      );
      // still return OK to Billplz
    }

    return 'OK';
  }

  // @Get('billplz/redirect')
  // async billplzRedirect(@Query() query: BillplzRedirectDto): Promise<PaymentStatusDto> {
  //   const billId = query['billplz[id]'];
  //   const paid = query['billplz[paid]'] === 'true';
  //   const xSignature = query['billplz[x_signature]'];
  //   const transactionId = query['billplz[transaction_id]'] || '';

  //   if (!billId) {
  //     return { status: 'failed', message: 'Invalid request' };
  //   }

  //   // For redirect URLs, we also need to verify the X-Signature
  //   // Create a data object from the query parameters
  //   const data = {
  //     id: billId,
  //     paid: paid.toString(),
  //     paid_at: query['billplz[paid_at]'] || ''
  //   };

  //   // Verify the signature
  //   const isValidSignature = this.billplzService.verifyXSignature(data, xSignature);

  //   if (!isValidSignature) {
  //     return { status: 'failed', message: 'Invalid signature' };
  //   }

  //   // The orderId will be retrieved from the bill details using the billId
  //   return this.salesOrderService.handleBillplzRedirect(billId, paid, xSignature, transactionId);
  // }
}
