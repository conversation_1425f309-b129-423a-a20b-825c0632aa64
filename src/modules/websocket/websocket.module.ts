import { Global, Module } from '@nestjs/common';
import { WebSocketService } from './websocket.service';
import { WebSocketGateway } from './websocket.gateway';
import { TaskCommentEntity } from '@modules/task-comment/entity/task-comment.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([TaskCommentEntity])],
  providers: [WebSocketGateway, WebSocketService],
  exports: [WebSocketService]
})
export class WebSocketModule {}
