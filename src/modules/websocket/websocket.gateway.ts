import { Logger } from '@nestjs/common';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway as Gateway,
  WebSocketServer
} from '@nestjs/websockets';
import { Socket, Server } from 'socket.io';
import { WebSocketService } from './websocket.service';

@Gateway({ transport: ['websocket'], cors: true })
export class WebSocketGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  constructor(private socketService: WebSocketService) {}

  @WebSocketServer() server: Server;
  private logger: Logger = new Logger('WebSocketGateway');

  afterInit(server: Server) {
    this.logger.log(`Initialized!`);
    this.socketService.socket = server;
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}.`);
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}.`);
  }

  // @SubscribeMessage('new-comment-to-task')
  // handleMessage(
  //   @ConnectedSocket() client: Socket,
  //   @MessageBody() content: string,
  // ): void {
  //   this.server.emit('message', content);
  // }

  @SubscribeMessage('joinRoom')
  async handleJoinRoom(client: Socket, room: string) {
    await client.join(room);
    this.logger.log(`Client (${client.id}) joined room: (${room}).`);
  }

  @SubscribeMessage('leaveRoom')
  async handleLeaveRoom(client: Socket, room: string) {
    await client.leave(room);
    this.logger.log(`Client (${client.id}) left room: (${room}).`);
  }
}
