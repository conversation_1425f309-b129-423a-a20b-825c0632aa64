import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ScheduleLinksEntity } from './entity/schedule-links.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class ScheduleLinksService extends TypeOrmQueryService<ScheduleLinksEntity> {
  constructor(
    @InjectRepository(ScheduleLinksEntity)
    private schedulesMediaRepo: Repository<ScheduleLinksEntity>
  ) {
    super(schedulesMediaRepo, { useSoftDelete: true });
  }
}
