import { BaseEntity } from '@modules/base/base';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';

@ObjectType()
@Entity('schedules_links')
export class ScheduleLinksEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  ownerId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectScheduleId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  suid: string;

  @FilterableField({ nullable: true })
  @Column('varchar')
  lag_unit: string;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  lag: number;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  source: number;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  target: number;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  type: number;

  /* -------------------------------- Relations ------------------------------- */

  @ManyToOne(() => UserEntity, owner => owner.schedulesLinks)
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @ManyToOne(() => ProjectEntity, project => project.links)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => ProjectScheduleEntity, project => project.scheduleLinks)
  @JoinColumn({ name: 'projectScheduleId' })
  projectSchedule: ProjectScheduleEntity;
}
