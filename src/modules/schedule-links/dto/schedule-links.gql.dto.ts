import { defaultQueryOptions } from '@constants';
import {
  BeforeCreateMany,
  BeforeCreateOne,
  CreateManyInputType,
  CreateOneInputType,
  FilterableOffsetConnection,
  IDField,
  QueryOptions
} from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ScheduleLinksEntity } from '../entity/schedule-links.entity';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { relationOption } from '@constants/query.constant';
import { GqlContext } from '@types';
import { ProjectScheduleDTO } from '@modules/project-schedules/dto/project-schedule.gql.dto';

@ObjectType('SchedulesLinks')
@FilterableOffsetConnection('owner', () => UserDto, relationOption(true))
@FilterableOffsetConnection('projectSchedules', () => ProjectScheduleDTO, relationOption(true))
@BeforeCreateOne((instance: CreateOneInputType<CreateScheduleLinksInputDTO>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  instance.input.ownerId = createdBy;
  const projectId = Number(context.req.headers['project-id']);
  instance.input.projectId = projectId;
  return instance;
})
@BeforeCreateMany((instance: CreateManyInputType<CreateScheduleLinksInputDTO>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  const projectId = Number(context.req.headers['project-id']);

  instance.input = instance.input.map(c => ({ ...c, createdBy, ownerId: createdBy, projectId }));
  return instance;
})
@QueryOptions({ ...defaultQueryOptions })
export class ScheduleLinksDto extends ScheduleLinksEntity {}

@InputType()
export class CreateScheduleLinksInputDTO {
  @IDField(() => ID) ownerId?: number;
  @IDField(() => ID) projectId?: number;
  @IDField(() => ID) projectScheduleId?: number;
  suid?: string;
  lag_unit?: string;
  lag?: number;
  source?: number;
  target?: number;
  type?: number;
}

@InputType()
export class UpdateScheduleLinksInputDTO extends PartialType(CreateScheduleLinksInputDTO) {}
