import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import {
  CreateScheduleLinksInputDTO,
  ScheduleLinksDto,
  UpdateScheduleLinksInputDTO
} from './dto/schedule-links.gql.dto';
import { ScheduleLinksEntity } from './entity/schedule-links.entity';
import { ScheduleLinksService } from './schedule-links.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ScheduleLinksEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: ScheduleLinksDto,
          EntityClass: ScheduleLinksEntity,
          CreateDTOClass: CreateScheduleLinksInputDTO,
          UpdateDTOClass: UpdateScheduleLinksInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ScheduleLinksService, ScheduleLinksEntity]
    })
  ]
})
export class ScheduleLinksModule {}
