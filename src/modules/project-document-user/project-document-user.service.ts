import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AuthData } from '@types';
import { ProjectDocumentUserEntity } from './entities/project-document-user.entity';
import { In, Repository, getRepository } from 'typeorm';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import {
  AuditLogActionType,
  AuditLogModuleType,
  CategoryType,
  ProjectDocumentDriveType,
  ProjectDocumentUserPermissionType,
  ProjectUserRoleType
} from '@constants';
import _ from 'lodash';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { getErrorMessage } from '@common/error';

@Injectable()
export class ProjectDocumentUserService extends TypeOrmQueryService<ProjectDocumentEntity> {
  constructor(
    @InjectRepository(ProjectDocumentUserEntity)
    private projectDocumentUserRepo: Repository<ProjectDocumentUserEntity>,
    @InjectRepository(ProjectDocumentEntity)
    private projectDocumentRepo: Repository<ProjectDocumentEntity>
  ) {
    super(projectDocumentRepo, { useSoftDelete: true });
  }

  async listOfUsersWithAccessToDocument(projectDocumentId: number) {
    try {
      const project_document = await this.projectDocumentRepo
        .createQueryBuilder('project_documents')
        .select(["SUBSTRING_INDEX(project_documents.mpath, '.', 1) as parentId", "project_documents.mpath as mpath", "project_documents.name as name"])
        .where({ id: projectDocumentId })
        .getRawOne();

      const users = await this.projectDocumentUserRepo
        .find({
          where: {
            projectDocumentId: project_document.parentId,
            type: ProjectDocumentUserPermissionType.Include
          },
          relations: ['user']
        })
        .then(users => users.map(user => user.user))
        // ONLY USERS WITH PASSWORD SETUP DONE WILL BE RETRIEVED
        // we also excluded removed users
        .then((users) => users.filter(user => user.password !== null && user.password !== undefined && user.removedAt == null));

      const projectDocumentOwner = await this.projectDocumentRepo.findOne(projectDocumentId);

      return _.uniqBy(users, 'id').filter(user => user.id !== projectDocumentOwner?.addedBy);
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentUserService', 'listOfUsersWithAccessToDocument');
    }
  }

  async listOfUsersWithNoAccessToDocument(projectDocumentId: number) {
    try {
      return await this.projectDocumentUserRepo
        .find({
          where: {
            projectDocument: {
              id: projectDocumentId
            },
            type: ProjectDocumentUserPermissionType.Exclude
          },
          relations: ['user']
        })
        .then(users => users.map(user => user.user));
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentUserService', 'listOfUsersWithNoAccessToDocument');
    }
  }

  async addPermission(userId: number, projectDocumentId: number, user: AuthData, projectId: number) {
    try {
      const parentDocument = await this.projectDocumentRepo.findOne(projectDocumentId, {
        where: { projectId },
        relations: ['owner', 'projectDocumentUsers', 'projectDocumentUsers.user']
      });

      parentDocument.updatedAt = new Date();

      if (!parentDocument) throw new Error('Document not found');

      const myProjectRole = await getRepository(ProjectUserEntity)
        .findOne({
          where: {
            userId: user.id,
            projectId
          }
        })
        .then(projectUser => projectUser?.role);

      if (
        parentDocument?.addedBy !== user.id &&
        myProjectRole !== ProjectUserRoleType.ProjectOwner &&
        myProjectRole !== ProjectUserRoleType.CloudCoordinator
      )
        throw new Error('You are not the owner of this document');

      if (parentDocument?.addedBy === userId) throw new Error('You can not add yourself to the document');

      if ((await this.listOfUsersWithAccessToDocument(projectDocumentId)).find(user => user.id === userId))
        throw new Error('User already has access to this document');

      const projectDocumentUser = await this.projectDocumentUserRepo.create({
        projectDocumentId: parentDocument.id,
        userId: userId,
        type: ProjectDocumentUserPermissionType.Include
      })

      await this.projectDocumentUserRepo.save(projectDocumentUser);

      // AUDIT LOG FOR CLOUD DOCS (SHARE)
      if (
        (parentDocument.category === CategoryType.ProjectDocument ||
          parentDocument.category === CategoryType.WorkProgramme ||
          parentDocument.category === CategoryType.Correspondence) &&
        parentDocument.driveType === ProjectDocumentDriveType.Shared
      ) {
        // UPDATEDBY COLUMN IN DATABASE TO BE RESOLVED
        const rawUser = await getRepository(UserEntity).findOne({ where: { id: user.id } });
        const sharedUser = await getRepository(UserEntity).findOne({ where: { id: userId } });

        const msg =
          rawUser.name +
          ' shared the folder ' +
          parentDocument.name +
          ' with ' +
          sharedUser.name +
          ' in ' +
          parentDocument.category.replace(/([a-z])([A-Z])/g, '$1 $2');

        const auditLog = getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: parentDocument.projectId,
          // taskId: parentDocument.id,
          resourceId: parentDocument.id,
          module: AuditLogModuleType.CloudDocument,
          action: AuditLogActionType.Shared,
          content: msg
        });
        await auditLog.save();
      }

      return await this.projectDocumentUserRepo.findOne({
        where: {
          projectDocumentId,
          userId
        }
      });
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentUserService', 'addPermission');
    }
  }

  async removePermission(userId: number, projectDocumentId: number, user: AuthData, projectId: number) {
    try {
      const treeRepo = this.projectDocumentRepo.manager.getTreeRepository(ProjectDocumentEntity);
      const parentDocument = await this.projectDocumentRepo.findOne(projectDocumentId, {
        where: { projectId },
        relations: ['owner', 'projectDocumentUsers', 'projectDocumentUsers.user']
      });
      const myProjectRole = await getRepository(ProjectUserEntity)
        .findOne({
          where: {
            userId: user.id,
            projectId
          }
        })
        .then(projectUser => projectUser?.role);

      if (!parentDocument) throw new Error('Document not found');

      if (
        parentDocument?.addedBy !== user.id &&
        myProjectRole !== ProjectUserRoleType.ProjectOwner &&
        myProjectRole !== ProjectUserRoleType.CloudCoordinator
      )
        throw new Error('You are not the owner of this document');

      if (parentDocument?.addedBy === userId) throw new Error('You can not remove yourself to the document');

      if ((await this.listOfUsersWithNoAccessToDocument(projectDocumentId)).find(user => user.id === userId))
        throw new Error('User already excluded from this document');

      const descendants = await treeRepo.findDescendants(parentDocument);

      const projectDocumentUsers = await this.projectDocumentUserRepo.find({
        where: {
          projectDocumentId: In(descendants.map(descendant => descendant.id)),
          userId
        }
      });

      await this.projectDocumentUserRepo.softRemove(projectDocumentUsers);

      return {
        affected: projectDocumentUsers.length
      };
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentUserService', 'removePermission');
    }
  }
}
