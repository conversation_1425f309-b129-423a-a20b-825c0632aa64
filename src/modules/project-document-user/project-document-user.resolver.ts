import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { ProjectDocumentUserService } from './project-document-user.service';
import { ProjectDocumentUserEntity } from './entities/project-document-user.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { AuthData } from '@types';
import { ProjectDocumentUserDeleteDTO } from './dto/project-document-user-delete.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { ProjectDocumentGuard } from '@guards/project-document.guard';
import { ProjectDocumentPermissionType } from '@constants';

@UseGuards(GqlAuthGuard)
@Resolver(() => ProjectDocumentUserEntity)
export class ProjectDocumentUserResolver {
  constructor(private readonly projectDocumentUserService: ProjectDocumentUserService) {}

  @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Query(() => [UserDto])
  listOfUsersWithAccessToDocument(@Args('projectDocumentId', { type: () => Int }) projectDocumentId: number) {
    return this.projectDocumentUserService.listOfUsersWithAccessToDocument(projectDocumentId);
  }

  @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Mutation(() => ProjectDocumentUserEntity)
  addProjectDocumentUser(
    @Args('userId', { type: () => Int }) userId: number,
    @Args('projectDocumentId', { type: () => Int }) projectDocumentId: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    try {
      return this.projectDocumentUserService.addPermission(userId, projectDocumentId, user, projectId);
    } catch (_e) {
      throw new Error('Something went wrong');
    }
  }

  @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Mutation(() => ProjectDocumentUserDeleteDTO)
  removeProjectDocumentUser(
    @Args('userId', { type: () => Int }) userId: number,
    @Args('projectDocumentId', { type: () => Int }) projectDocumentId: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    try {
      return this.projectDocumentUserService.removePermission(userId, projectDocumentId, user, projectId);
    } catch (_e) {
      throw new Error('Something went wrong');
    }
  }
}
