import { UserEntity } from '@modules/user/entity/user.entity';
import { OffsetConnection } from '@nestjs-query/query-graphql';
import { ObjectType } from '@nestjs/graphql';
import { ProjectDocumentUserEntity } from '../entities/project-document-user.entity';

@ObjectType('ProjectDocumentUser')
@OffsetConnection('user', () => UserEntity)
export class ProjectDocumentUserDTO extends ProjectDocumentUserEntity {}
