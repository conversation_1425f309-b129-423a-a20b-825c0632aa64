import { ObjectType } from '@nestjs/graphql';
import { ProjectDocumentUserPermissionType } from '@constants';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID } from '@nestjs/graphql';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { BaseEntity } from '@modules/base/base';

@ObjectType()
@Entity('project_document_users')
export class ProjectDocumentUserEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @Index()
  @FilterableField(() => ProjectDocumentUserPermissionType)
  @Column('enum', {
    enum: ProjectDocumentUserPermissionType,
    default: ProjectDocumentUserPermissionType.Include
  })
  type: ProjectDocumentUserPermissionType;

  @Index()
  @ManyToOne(() => UserEntity, user => user.projectDocumentUsers)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.projectDocumentUsers)
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;
}
