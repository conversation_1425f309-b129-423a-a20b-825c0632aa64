import { Module } from '@nestjs/common';
import { ProjectDocumentUserService } from './project-document-user.service';
import { ProjectDocumentUserResolver } from './project-document-user.resolver';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectDocumentUserEntity } from './entities/project-document-user.entity';
import { ProjectDocumentUserDTO } from './dto/project-document-user.dto';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([ProjectDocumentUserEntity, ProjectDocumentEntity]),
        IntegrationModule
      ],
      resolvers: [
        {
          ServiceClass: ProjectDocumentUserService,
          DTOClass: ProjectDocumentUserDTO,
          EntityClass: ProjectDocumentUserEntity,
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard],
          create: { disabled: true },
          update: { disabled: true },
          delete: { disabled: true }
        }
      ],
      services: [ProjectDocumentUserResolver, ProjectDocumentUserService]
    })
  ]
})
export class ProjectDocumentUserModule {}
