import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceAttachmentEntity } from './entity/workspace-attachment.entity';
import { Injectable } from '@nestjs/common';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class WorkspaceAttachmentService extends TypeOrmQueryService<WorkspaceAttachmentEntity> {
  constructor(
    @InjectRepository(WorkspaceAttachmentEntity)
    private workspaceAttachmentRepo: Repository<WorkspaceAttachmentEntity>,
    private tmOneService: TMOneService
  ) {
    // pass the use soft delete option to the service.
    super(workspaceAttachmentRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(workspace_attachment: WorkspaceAttachmentEntity) {
    try {
      let key = workspace_attachment.fileKey;
      // fallback to fileUrl if fileK<PERSON> is missing
      if (!key){
        const fileName = workspace_attachment.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
