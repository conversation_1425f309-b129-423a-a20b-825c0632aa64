import { BadRequestException, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  In,
  InsertEvent,
  UpdateEvent
} from 'typeorm';
import { WorkspaceAttachmentEntity } from './entity/workspace-attachment.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '@modules/integration/file.service';
import * as _ from 'lodash';
import { getEllipsisText, getWorkflowNotificationPath } from '@constants/function';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogActionType, AuditLogModuleType, ProjectDocumentStatus } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { getErrorMessage } from '@common/error';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class WorkspaceAttachmentSubscriber implements EntitySubscriberInterface<WorkspaceAttachmentEntity> {
  constructor(connection: Connection, private fileService: FileService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return WorkspaceAttachmentEntity;
  }

  // Can be removed if the db remigrated
  // removed and see if it breaks anything
  // async afterLoad(entity: WorkspaceAttachmentEntity, event: LoadEvent<WorkspaceAttachmentEntity>) {
  //   const fileUrl = entity.fileUrl;
  //   if (!isValidUrl(fileUrl) && !(entity.type === 'dwg' || entity.type === 'rvt'))
  //     entity.fileUrl = process.env.TMONE_OBS_STORAGE + _.replace(fileUrl, '/', '');
  // }

  async beforeUpdate(event: UpdateEvent<WorkspaceAttachmentEntity>) {
    const { entity } = event;
    
    const fileObject: any = entity.fileUrl; // fileUrl is the file uploaded from the client
    if (!fileObject) throw new BadRequestException('No Document has been upload');
    if (fileObject && _.isObject(fileObject)) {
      const folder = 'Workspace-Attachments';
      const { filename, key, type } = await this.fileService.uploadGqlFile(fileObject as FileUpload, folder);
      entity.fileKey = key;
      entity.name = filename;
      entity.type = type;
    }
  }

  async beforeInsert(event: InsertEvent<WorkspaceAttachmentEntity>) {
    try {
      const { entity } = event;
      if (entity.recordSource === 'OfflineApp') {
        entity.fileUrl = entity.fileKey;
        return;
      }

      const fileObject: any = entity.fileUrl;
      if (!fileObject) throw new BadRequestException('No Document has been upload');
      if (fileObject && _.isObject(fileObject)) {
        const folder = 'Workspace-Attachments';
        const { filename, key, type } = await this.fileService.uploadGqlFile(fileObject as FileUpload, folder);
        entity.fileUrl = key;
        entity.name = filename;
        entity.type = type;
      }
    } catch (error) {
      getErrorMessage(error, 'WorkspaceAttachmentSubscriber', 'beforeInsert');
    }
  }

  async afterInsert(event: InsertEvent<WorkspaceAttachmentEntity>) {
    try {
      const { entity } = event;

      // CAPTURING FOR WORKSPACE ATTACHMENT
      if (entity.userId) {
        const doc = await event.manager.getRepository(ProjectDocumentEntity).findOne({
          where: {
            id: entity?.projectDocumentId
          },
          relations: ['requestForSignatures', 'workspaceCCs']
        });

        if (!doc || doc === undefined) return;

        if (doc?.status !== ProjectDocumentStatus.Draft) {
          const owner = { userId: doc?.addedBy };
          const assignees = (await doc?.requestForSignatures) ?? [];
          const copies = (await doc?.workspaceCCs) ?? [];

          const assigneesIds = assignees?.map(assignee => {
            return { userId: assignee?.signById };
          });
          const copiesIds = copies?.map(copy => {
            return { userId: copy?.ccId };
          });

          const users = _.uniqBy([...assigneesIds, ...copiesIds, owner], 'userId');
          const project = await event.manager
            .getRepository(ProjectEntity)
            .findOne({ id: doc?.projectId }, { relations: ['company'] });

          const sanitizedUsers = await event.manager.find(UserEntity, {
            where: {
              id: In(users.map(user => user.userId))
            }
          });

          const link = `/viewer/all-form-viewer?documentId=${doc.id}&projectId=${doc.projectId}&companyId=${project?.companyId}`;
          const mobileLink = `${getWorkflowNotificationPath(doc?.workflow)}/${doc.id}/document`;

          sanitizedUsers?.forEach(async user => {
            try {
              const body = ` add attachment in ${doc?.name}: ${getEllipsisText(entity?.name)}`;
              return this.attachmentNotification(user, project, doc, link, mobileLink, body, body, project?.company);
            } catch (error) {
              getErrorMessage(error, 'TaskAttachmentSubscriber', 'afterInsert');
            }
          });
        }

        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });
        const msg = user.name + ' added ' + entity.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: doc.projectId,
          // taskId: task.id,
          resourceId: doc.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.AddAttachment,
          content: msg
        });
        await auditLog.save();
      }
    } catch (error) {
      getErrorMessage(error, 'WorkspaceAttachmentSubscriber', 'afterInsert');
    }
  }

  attachmentNotification(user, project, task, link, mobileLink, owner, body, companyName = null) {
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      company: companyName.name,
      title: project.title,
      head: owner?.name,
      body: body,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      pushMessageTitle: 'New - Workspace Attachment',
      subscriber: {
        firstName: user.name
      }
    };

    this.novuService.trigger('new-attachment', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          android: {
            priority: 'high'
          },
          data: {
            link: mobileLink.toString(),
            projectId: task.projectId.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }
}
