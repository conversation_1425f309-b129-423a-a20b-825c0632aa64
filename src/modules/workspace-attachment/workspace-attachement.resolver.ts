import { UseGuards } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { GqlAuthGuard } from "@guards/auth.guard";
import { WorkspaceAttachmentDto } from "./dto/workspace-attachment.gql.dto";
import { WorkspaceAttachmentService } from "./workspace-attachment.service";

@UseGuards(GqlAuthGuard)
@Resolver(() => WorkspaceAttachmentDto)

export class WorkspaceAttachmentResolver {
  constructor(private workspaceAttachmentService: WorkspaceAttachmentService) { }

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: WorkspaceAttachmentDto) {
    return await this.workspaceAttachmentService.getPresignedUrl(parent);
  }
}
