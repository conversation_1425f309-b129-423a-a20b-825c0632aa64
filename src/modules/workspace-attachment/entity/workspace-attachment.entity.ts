import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('workspace_attachments')
export class WorkspaceAttachmentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text')
  fileUrl: string;

  @FilterableField()
  @Column('text')
  fileKey: string;

  @FilterableField()
  @Column('text')
  type: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, document => document.workspaceAttachments, {
    orphanedRowAction: 'soft-delete'
  })
  @JoinColumn({ name: 'projectDocumentId' })
  document: ProjectDocumentEntity;

  @ManyToOne(() => UserEntity, user => user.workspaceAttachments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
