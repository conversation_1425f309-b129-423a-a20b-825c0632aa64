import { defaultQueryOptions, SourceType } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { WorkspaceAttachmentEntity } from '../entity/workspace-attachment.entity';

@ObjectType('WorkspaceAttachment')
@QueryOptions({ ...defaultQueryOptions })
export class WorkspaceAttachmentDto extends WorkspaceAttachmentEntity {
  //? offline mode
  remoteId?: number;
}

@InputType()
export class CreateWorkspaceAttachmentInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) userId?: number;
  @IDField(() => Number) projectDocumentId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  localProjectDocumentId?: string;
  fileKey?: string;
  name?: string;
  type?: string;
  @Field({ nullable: true }) recordSource?: SourceType;
  
}

@InputType()
export class UpdateWorkspaceAttachmentInputDTO extends PartialType(CreateWorkspaceAttachmentInputDTO) {
  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
}
