import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import {
  WorkspaceAttachmentDto,
  CreateWorkspaceAttachmentInputDTO,
  UpdateWorkspaceAttachmentInputDTO
} from './dto/workspace-attachment.gql.dto';
import { WorkspaceAttachmentEntity } from './entity/workspace-attachment.entity';
import { WorkspaceAttachmentService } from './workspace-attachment.service';
import { WorkspaceAttachmentSubscriber } from './workspace-attachment.subscriber';
import { WorkspaceAttachmentResolver } from './workspace-attachement.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([WorkspaceAttachmentEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: WorkspaceAttachmentService,
          DTOClass: WorkspaceAttachmentDto,
          EntityClass: WorkspaceAttachmentEntity,
          CreateDTOClass: CreateWorkspaceAttachmentInputDTO,
          UpdateDTOClass: UpdateWorkspaceAttachmentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [WorkspaceAttachmentSubscriber, WorkspaceAttachmentService, WorkspaceAttachmentResolver]
    })
  ]
})
export class WorkspaceAttachmentModule {}
