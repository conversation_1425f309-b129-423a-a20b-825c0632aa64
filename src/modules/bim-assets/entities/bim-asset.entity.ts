import { BaseEntity } from '@modules/base/base';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';

@ObjectType()
@Entity('bim_asset')
export class BimAssetEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  dbId: number;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  assetKey: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  assetValue: string;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectDocumentId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.bimAssets)
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;

  @ManyToOne(() => ProjectEntity, project => project.bimAssets)
  @JoinColumn({ name: 'projectId' })
  projects: ProjectEntity;
}