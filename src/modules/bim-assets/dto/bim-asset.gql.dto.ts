import { defaultQueryOptions } from '@constants';
import { BeforeCreateMany, BeforeCreateOne, CreateManyInputType, CreateOneInputType, FilterableRelation, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { relationOption } from '@constants/query.constant';
import { ProjectDto } from '@modules/project/dto/project.gql.dto';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { BimAssetEntity } from '../entities/bim-asset.entity';
import { GqlContext } from '@types';

@ObjectType('BimAsset')
@FilterableRelation('projectDocument', () => ProjectDocumentDto, relationOption(true))
@FilterableRelation('projects', () => ProjectDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class BimAssetDto extends BimAssetEntity {}
@BeforeCreateOne(
  (instance: CreateOneInputType<BimAssetDto>, context: GqlContext) => {
    const projectId = Number(context.req.headers["project-id"]);
    instance.input.projectId = projectId;
    return instance;
  }
)
@BeforeCreateMany(
  (instance: CreateManyInputType<BimAssetDto>, context: GqlContext) => {
    const projectId = Number(context.req.headers["project-id"]);
    instance.input.map((item) => ({ ...item, projectId }));
    return instance;
  }
)

@InputType()
export class CreateBimAssetInputDTO {
  @IDField(() => ID) dbId: number;
  @IDField(() => ID) projectDocumentId: number;
  @IDField(() => ID) projectId?: number;
  assetKey: string;
  assetValue: string;
}
@InputType()
export class UpdateBimAssetInputDTO extends PartialType(CreateBimAssetInputDTO) {
  @IDField(() => ID) id: number;
}
