import { Module } from '@nestjs/common';
import { BimAssetsService } from './bim-assets.service';
import { BimAssetsResolver } from './bim-assets.resolver';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { BimAssetEntity } from './entities/bim-asset.entity';
import { IntegrationModule } from '@modules/integration/integration.module';
import { BimAssetDto, CreateBimAssetInputDTO, UpdateBimAssetInputDTO } from './dto/bim-asset.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([BimAssetEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: BimAssetDto,
          EntityClass: BimAssetEntity,
          CreateDTOClass: CreateBimAssetInputDTO,
          UpdateDTOClass: UpdateBimAssetInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ BimAssetsService, BimAssetsResolver]
    }),
    IntegrationModule
  ]
})
export class BimAssetsModule {}
