import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import {
  WorkspacePhotoDto,
  CreateWorkspacePhotoInputDTO,
  UpdateWorkspacePhotoInputDTO
} from './dto/workspace-photo.gql.dto';
import { WorkspacePhotoEntity } from './entity/workspace-photo.entity';
import { WorkspacePhotoService } from './workspace-photo.service';
import { WorkspacePhotoSubscriber } from './workspace-photo.subscriber';
import { WorkspacePhotoResolver } from './workspace-photo.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([WorkspacePhotoEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: WorkspacePhotoService,
          DTOClass: WorkspacePhotoDto,
          EntityClass: WorkspacePhotoEntity,
          CreateDTOClass: CreateWorkspacePhotoInputDTO,
          UpdateDTOClass: UpdateWorkspacePhotoInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [WorkspacePhotoSubscriber, WorkspacePhotoService, WorkspacePhotoResolver]
    })
  ]
})
export class WorkspacePhotoModule {}
