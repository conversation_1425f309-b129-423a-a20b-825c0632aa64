import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspacePhotoEntity } from './entity/workspace-photo.entity';
import { Injectable } from '@nestjs/common';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class WorkspacePhotoService extends TypeOrmQueryService<WorkspacePhotoEntity> {
  constructor(
    @InjectRepository(WorkspacePhotoEntity)
    private workspacePhotoRepo: Repository<WorkspacePhotoEntity>,
    private tmOneService: TMOneService
  ) {
    // pass the use soft delete option to the service.
    super(workspacePhotoRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(workspace_photo: WorkspacePhotoEntity) {
    try {
      let key = workspace_photo.fileKey;
      // fallback to fileUrl if file<PERSON><PERSON> is missing
      if (!key){
        const fileName = workspace_photo.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
