import { UseGuards } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { GqlAuthGuard } from "@guards/auth.guard";
import { WorkspacePhotoService } from "./workspace-photo.service";
import { WorkspacePhotoDto } from "./dto/workspace-photo.gql.dto";

@UseGuards(GqlAuthGuard)
@Resolver(() => WorkspacePhotoDto)

export class WorkspacePhotoResolver {
  constructor(private workspacePhotoService: WorkspacePhotoService) { }

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: WorkspacePhotoDto) {
    return await this.workspacePhotoService.getPresignedUrl(parent);
  }
}
