import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { WorkspacePhotoEntity } from './entity/workspace-photo.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '@modules/integration/file.service';
import * as _ from 'lodash';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class WorkspacePhotoSubscriber implements EntitySubscriberInterface<WorkspacePhotoEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return WorkspacePhotoEntity;
  }

  // Can be removed if the db remigrated
  // removed and see if it breaks anything
  // async afterLoad(entity: WorkspacePhotoEntity, event: LoadEvent<WorkspacePhotoEntity>) {
  //   const fileUrl = entity.fileUrl;
  //   if (!isValidUrl(fileUrl)) entity.fileUrl = process.env.TMONE_OBS_STORAGE + _.replace(fileUrl, '/', '');
  // }

  async beforeInsert(event: InsertEvent<WorkspacePhotoEntity>) {
    const { entity } = event;

    const file: any = entity.fileUrl;
    if (!file) throw new BadRequestException('No Document has been upload');
    if (file && _.isObject(file)) {
      const folder = 'Workspace-Photos';
      const { filename, key, type } = await this.fileService.uploadGqlFile(file as FileUpload, folder);
      entity.fileUrl = key;
      entity.name = filename;
      entity.type = type;
    }
  }
  async afterInsert(event: InsertEvent<WorkspacePhotoEntity>) {
    try {
      const { entity } = event;

      // CAPTURING FOR WORKSPACE ATTACHMENT
      if (entity.userId) {
        const doc = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.projectDocumentId });

        if (!doc || doc === undefined) return;

        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

        const msg = user.name + ' added ' + entity.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: doc.projectId,
          // taskId: task.id,
          resourceId: doc.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.AddPhoto,
          content: msg
        });
        await auditLog.save();
      }
    } catch (error) {
      getErrorMessage(error, 'WorkspacePhotoSubscriber', 'afterInsert');
    }
  }
}
