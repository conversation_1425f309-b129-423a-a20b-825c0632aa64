import { defaultQueryOptions, SourceType } from '@constants';
import { QueryOptions, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, InputType, Field, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { WorkspacePhotoEntity } from '../entity/workspace-photo.entity';

@ObjectType('WorkspacePhoto')
@QueryOptions({ ...defaultQueryOptions })
export class WorkspacePhotoDto extends WorkspacePhotoEntity {
  //? for offline
  @Field() remoteId?: number;
}

@InputType()
export class CreateWorkspacePhotoInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) userId?: number;
  @IDField(() => Number) projectDocumentId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  localProjectDocumentId?: string;
  fileKey?: string;
  name?: string;
  type?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class UpdateWorkspacePhotoInputDTO extends PartialType(CreateWorkspacePhotoInputDTO) {

  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
}
