import { WorkspaceCCStatus } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('workspace_ccs')
export class WorkspaceCCEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  ownerId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  ccId: number;

  @FilterableField(() => WorkspaceCCStatus)
  @Column('enum', { enum: WorkspaceCCStatus, default: WorkspaceCCStatus.Unsent })
  status: WorkspaceCCStatus;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.workspaceCCs, {
    orphanedRowAction: 'delete'
  })
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;

  @ManyToOne(() => UserEntity, user => user.sendCCs)
  @JoinColumn({ name: 'ownerId' })
  ccOwner: UserEntity;

  @ManyToOne(() => UserEntity, user => user.receiveCCs)
  @JoinColumn({ name: 'ccId' })
  ccUser: UserEntity;
}
