import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceCCEntity } from './entity/workspace-cc.entity';
import { AssignWorkspaceCCInputDTO } from './dto/workspace-cc.gql.dto';

@Injectable()
export class WorkspaceCCService extends TypeOrmQueryService<WorkspaceCCEntity> {
  constructor(
    @InjectRepository(WorkspaceCCEntity)
    private workspaceCCRepo: Repository<WorkspaceCCEntity>,
  ) {
    super(workspaceCCRepo, { useSoftDelete: true });
  }

  async assignWorkspaceCC(data: AssignWorkspaceCCInputDTO): Promise<WorkspaceCCEntity> {
    const queryRunner = this.workspaceCCRepo.manager.connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { projectDocumentId, cc } = data;

      console.log(!cc, cc.length === 0);

      if (!cc || cc.length === 0) {
        return;
      }

      const existingWorkspaceCCs = await queryRunner.manager.find(WorkspaceCCEntity, {
        where: { projectDocumentId },
        select: ['id', 'ccId']
      });

      const existingCCIds = new Set(existingWorkspaceCCs.map(cc => cc.ccId));
      const inputCCIds = new Set(cc.map(c => c.ccId));

      const ccIdsToDelete = existingWorkspaceCCs
        .filter(existingCC => !inputCCIds.has(existingCC.ccId))
        .map(existingCC => existingCC.id);

      if (ccIdsToDelete.length > 0) {
        await queryRunner.manager.softDelete(WorkspaceCCEntity, ccIdsToDelete);
      }

      const ccToAdd = cc.filter(c => !existingCCIds.has(c.ccId));

      if (ccToAdd.length > 0) {
        const newWorkspaceCCs = ccToAdd.map(c =>
          queryRunner.manager.create(WorkspaceCCEntity, {
            projectDocumentId,
            ccId: c.ccId,
            ownerId: c.ownerId,
          })
        );

        await queryRunner.manager.save(WorkspaceCCEntity, newWorkspaceCCs);
      }

      await queryRunner.commitTransaction();

      return queryRunner.manager.findOne(WorkspaceCCEntity, {
        where: { projectDocumentId },
        select: ['id', 'ccId']
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error(`Error in assignWorkspaceCC for projectDocumentId ${data.projectDocumentId}:`, error);
      throw new Error('Failed to assign workspace CCs');
    } finally {
      await queryRunner.release();
    }
  }
}
