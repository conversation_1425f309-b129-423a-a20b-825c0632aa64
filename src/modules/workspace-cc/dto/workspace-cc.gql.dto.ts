import { defaultQueryOptions, SourceType, WorkspaceCCStatus } from '@constants';
import { relationOption } from '@constants/query.constant';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { WorkspaceCCEntity } from '../entity/workspace-cc.entity';

@ObjectType('WorkspaceCC')
@QueryOptions({ ...defaultQueryOptions })
@Relation('projectDocument', () => ProjectDocumentDto, relationOption())
@Relation('ccOwner', () => UserDto, relationOption())
@Relation('ccUser', () => UserDto, relationOption())
export class WorkspaceCCDto extends WorkspaceCCEntity {
  //? offline mode
  remoteId?: number;
}

@InputType()
export class CreateWorkspaceCCInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) ownerId?: number;
  @IDField(() => Number) ccId?: number;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  localProjectDocumentId?: string;
  projectDocumentId: number | null;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class AssignWorkspaceCCInputDTO {
  @IDField(() => ID) projectDocumentId: number;
  @Field(() => [CreateWorkspaceCCInputDTO])
  cc?: CreateWorkspaceCCInputDTO[];
}

@InputType()
export class UpdateWorkspaceCCInputDTO extends PartialType(CreateWorkspaceCCInputDTO) {
  status?: WorkspaceCCStatus;

  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
}
