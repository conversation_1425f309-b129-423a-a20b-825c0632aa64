import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  getRepository,
  InsertEvent,
  UpdateEvent
} from 'typeorm';
import { WorkspaceCCEntity } from './entity/workspace-cc.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { AuditLogActionType, AuditLogModuleType, WorkspaceCCStatus } from '@constants';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { NovuService } from '@modules/integration/novu/novu.service';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import { getWorkflowNotificationPath } from '@constants/function';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';

const { APP_URI } = process.env;

@Injectable()
@EventSubscriber()
export class WorkspaceCCSubscriber implements EntitySubscriberInterface<WorkspaceCCEntity> {
  constructor(connection: Connection, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return WorkspaceCCEntity;
  }

  async afterInsert(event: InsertEvent<WorkspaceCCEntity>) {
    try {
      const { entity } = event;

      const ccUser = await event.manager.getRepository(UserEntity).findOne({ id: entity.ccId });
      const owner = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });
      const form = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.projectDocumentId });

      const project = await event.manager.getRepository(ProjectEntity).findOne({ id: form.projectId });

      const msg = owner.name + ' added ' + ccUser.name + ' in Cc';

      //? find the if the cc is already in the audit log
      const isAuditLogExist = await event.manager.getRepository(AuditLogEntity).findOne({
        where: {
          userId: owner.id,
          projectId: project.id,
          content: msg,
          resourceId: entity.projectDocumentId
        }
      });

      if (isAuditLogExist) return;

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: owner.id,
        projectId: project.id,
        // taskId: entity.id,
        resourceId: entity.projectDocumentId,
        module: AuditLogModuleType.Workspace,
        action: AuditLogActionType.AssignedCc,
        content: msg
      });
      await auditLog.save();
    } catch (error) {
      getErrorMessage(error, 'WorkspaceCCSubscriber', 'afterInsert');
    }
  }

  async afterUpdate(event: UpdateEvent<WorkspaceCCEntity>) {
    try {
      const { entity } = event;

      // Send email and notification once cc(s) had been sent
      if (entity.status === WorkspaceCCStatus.Sent) {
        // Send email
        const ccUser = await event.manager.getRepository(UserEntity).findOne({ id: entity.ccId });
        const owner = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });
        const form = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.projectDocumentId });

        const mobileLink = `${getWorkflowNotificationPath(form?.workflow)}/${form.id}/document`;
        const project = await event.manager.getRepository(ProjectEntity).findOne({ id: form.projectId });
        const company = await event.manager.getRepository(CompanyEntity).findOne({ id: project.companyId });
        const redirect = `/digital-form/all-form?documentDetailsId=${form.id}&projectId=${form.projectId}&companyId=${company.id}&workflowType=${form.workflow}`;
        const link = `/viewer/all-form-viewers?documentId=${form.id}&projectId=${form.projectId}&companyId=${company.id}`;

        const document = await event.manager
          .getRepository(ProjectDocumentEntity)
          .findOne({ id: entity.projectDocumentId });

        const groupName = await event.manager.findOne(WorkspaceGroupEntity, {
          where: { id: document.workspaceGroupId },
          relations: ['parent', 'parent.workspaceGroupUsers']
        });
        const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);

        const notRestricted =
          groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(ccUser.id);

        const header = '📌 New Document Approval';

        //Mobile Notifications
        const notification = getRepository(NotificationTransactionEntity).create({
          userId: owner.id,
          actorId: ccUser.id,
          title: project.title,
          actionName: form.name,
          actionType: 'has CC you to a document approval',
          deeplink: link,
          mobileDeeplink: mobileLink
        });

        await getRepository(NotificationTransactionEntity).save(notification);
        if (notRestricted) {
          this.novuService.trigger('secondary-workflow', {
            to: {
              subscriberId: ccUser.id.toString(),
              email: ccUser.email
            },
            payload: {
              user: {
                avatar: owner.avatar,
                name: owner.name,
                email: owner.email
              },
              event: 'workspace-cc',
              header,
              title: project.title,
              head: owner.name,
              body: 'CC you to a document approval',
              tail: form.name,
              bodyColon: 'boolean',
              headBold: 'boolean',
              tailBold: 'boolean',
              link: {
                mobile: mobileLink,
                web: link,
                uri: APP_URI,
                redirect: redirect
              }
            },
            overrides: {
              fcm: {
                android: {
                  priority: 'high'
                },
                data: {
                  link: mobileLink.toString(),
                  projectId: project.id.toString() ?? '',
                  companyId: company?.id.toString()
                }
              }
            }
          });
        }
      }
    } catch (error) {
      getErrorMessage(error, 'WorkspaceCCSubscriber', 'afterUpdate');
    }
  }
}
