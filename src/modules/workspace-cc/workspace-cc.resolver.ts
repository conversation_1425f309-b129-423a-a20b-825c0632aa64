import { UseGuards } from "@nestjs/common";
import { Resolver, Mutation, Args } from "@nestjs/graphql";
import { GqlAuthGuard } from "@guards/auth.guard";
import { WorkspaceCCDto,  AssignWorkspaceCCInputDTO } from "./dto/workspace-cc.gql.dto";
import { WorkspaceCCService } from "./workspace-cc.service";

@UseGuards(GqlAuthGuard)
@Resolver(() => WorkspaceCCDto)

export class WorkspaceCCResolver {
  constructor(private workspaceAttachmentService: WorkspaceCCService) { }

  @Mutation(() => WorkspaceCCDto)
  async assignWorkspaceCC(
    @Args('input') input: AssignWorkspaceCCInputDTO,
  ) {
    return await this.workspaceAttachmentService.assignWorkspaceCC(
        input
    );
  }
}
