import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { WorkspaceCCEntity } from './entity/workspace-cc.entity';
import { WorkspaceCCDto, CreateWorkspaceCCInputDTO, UpdateWorkspaceCCInputDTO } from './dto/workspace-cc.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { WorkspaceCCService } from './workspace-cc.service';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { WorkspaceCCSubscriber } from './workspace-cc.subscriber';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { IntegrationModule } from '@modules/integration/integration.module';
import { WorkspaceCCResolver } from './workspace-cc.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([WorkspaceCCEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: WorkspaceCCService,
          DTOClass: WorkspaceCCDto,
          EntityClass: WorkspaceCCEntity,
          CreateDTOClass: CreateWorkspaceCCInputDTO,
          UpdateDTOClass: UpdateWorkspaceCCInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          create: {
            many: { disabled: true },
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          }
        }
      ],
      services: [WorkspaceCCSubscriber, WorkspaceCCService, MailgunService, WorkspaceCCResolver]
    }),
    IntegrationModule
  ]
})
export class WorkspaceCCModule {}
