import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotificationTransactionEntity } from './entity/notification-transaction.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class NotificationTransactionService extends TypeOrmQueryService<NotificationTransactionEntity> {
  constructor(
    @InjectRepository(NotificationTransactionEntity)
    private notificationTransactionRepo: Repository<NotificationTransactionEntity>
  ) {
    super(notificationTransactionRepo, { useSoftDelete: true });
  }
}
