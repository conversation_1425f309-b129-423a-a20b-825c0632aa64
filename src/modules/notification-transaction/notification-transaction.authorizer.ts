import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { NotificationTransactionDto } from './dto/notification-transaction.gql.dto';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class NotificationTransactionAuthorizer implements Authorizer<NotificationTransactionDto> {
  authorize(context: any): Promise<Filter<NotificationTransactionDto>> {
    if (!context?.req?.user) throw new UnauthorizedException('User not found');
    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({ userId: { eq: context.req.user.id } });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    if (relationName === 'users' && context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({ userId: { eq: context.req.user.id } });
    }
    return Promise.resolve({});
  }
}
