import { BaseEntity } from '@modules/base/base';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, Field } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('notification_transactions')
export class NotificationTransactionEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  actorId: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  thumbnail: string;

  @FilterableField()
  @Column('text')
  deeplink: string;

  @FilterableField({ nullable: true })
  @Column('text')
  mobileDeeplink: string;

  @FilterableField()
  @Column('boolean', { default: false })
  read: boolean;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  actionName: string;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  actionType: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  type: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.notifications)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => UserEntity, user => user.sentNotifications)
  @JoinColumn({ name: 'actorId' })
  actor: UserEntity;
}
