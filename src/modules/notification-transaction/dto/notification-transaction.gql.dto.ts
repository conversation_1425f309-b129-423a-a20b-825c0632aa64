import { defaultQueryOptions } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { Authorize, BeforeCreateOne, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { NotificationTransactionEntity } from '../entity/notification-transaction.entity';
import { NotificationTransactionAuthorizer } from '../notification-transaction.authorizer';
import * as Hooks from '@hooks/nest-graphql.hooks';

@BeforeCreateOne(Hooks.CreatedByOneHook)
@ObjectType('NotificationTransaction')
@Relation('actor', () => UserDto, relationOption())
@Authorize(NotificationTransactionAuthorizer)
@QueryOptions({ ...defaultQueryOptions })
export class NotificationTransactionDto extends NotificationTransactionEntity {}

@InputType()
export class CreateNotificationTransactionInputDTO {
  @IDField(() => ID) userId: number;
  title: string;
  thumbnail: string;
  deeplink: string;
  mobileDeeplink: string;
  read: boolean;
  actionType: string;
  actionName: string;
  type: string;
  
}
@InputType()
export class UpdateNotificationTransactionInputDTO extends PartialType(CreateNotificationTransactionInputDTO) {}
