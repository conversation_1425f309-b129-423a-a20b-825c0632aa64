import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { NotificationTransactionEntity } from './entity/notification-transaction.entity';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class NotificationTransactionSubscriber implements EntitySubscriberInterface<NotificationTransactionEntity> {
  constructor(
    connection: Connection,
    
    private webSocketService: WebSocketService
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return NotificationTransactionEntity;
  }

  async beforeCreateOne(event: InsertEvent<NotificationTransactionEntity>) {
    const { entity } = event;

    entity.actorId = entity.createdBy;
    entity.createdBy = null;
  }

  async afterInsert(event: InsertEvent<NotificationTransactionEntity>) {
    try {
      const { entity } = event;

      const user = await event.manager
        .getRepository(UserEntity)
        .findOne({ id: entity.userId }, { relations: ['fcmTokens'] });
      if (!user) throw new BadRequestException('User not found');

      const userFcmTokens = user.fcmTokens;

      // if (userFcmTokens && !_.isEmpty(userFcmTokens)) {
      //   for (const userFcmToken of userFcmTokens) {
      //     await this.firebaseService.sendNotification({ ...entity }, userFcmToken.fcmToken);
      //   }
      // }

      const socketEvent = 'event:new-notification';
      const room = `notification-room-of-user-${entity.userId}`;
      await this.webSocketService.socket.to(room).emit(socketEvent, entity);
    } catch (e) {
      getErrorMessage(e, 'NotificationTransactionSubscriber', 'afterInsert');
    }
  }
}
