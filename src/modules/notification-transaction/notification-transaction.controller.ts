import { GetProjectData, GetHeaders, GetAuthData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { NovuService } from '@modules/integration/novu/novu.service';
import {
  GetNotificationByIdDto,
  markNotificationAsReadDto,
  setDeviceTokenDto
} from './dto/notification-transaction.api.dto';

@ApiTags('Notification API')
@Controller('notifications')
export class NotificationTransactionController {
  constructor(private novuService: NovuService) {}

  @UseApiUserAuthGuard()
  @Post('get-notifications')
  async getNotifications(@GetAuthData() authData: AuthData, @Body() body: GetNotificationByIdDto) {
    const userId = authData.id;
    return await this.novuService.getNotification(userId, body.page);
  }

  @UseApiUserAuthGuard()
  @Get('get-notification-unseen-count')
  async getNotificationUnseenCount(@GetAuthData() authData: AuthData) {
    const userId = authData.id;
    return await this.novuService.getUnreadNotification(userId);
  }

  @UseApiUserAuthGuard()
  @Post('mark-as-read')
  async markAsRead(@GetAuthData() authData: AuthData, @Body() body: markNotificationAsReadDto) {
    const userId = authData.id;
    return await this.novuService.markAsRead(userId, body.messageId);
  }

  @UseApiUserAuthGuard()
  @Post('set-device-token')
  async setDeviceToken(@GetAuthData() authData: AuthData, @Body() body: setDeviceTokenDto) {
    const userId = authData.id;
    return await this.novuService.setDeviceToken(JSON.stringify(userId), body.deviceToken);
  }

  @UseApiUserAuthGuard()
  @Post('remove-device-token')
  async removeDeviceToken(@GetAuthData() authData: AuthData, @Body() body: setDeviceTokenDto) {
    const userId = authData.id;
    return await this.novuService.removeDeviceToken(JSON.stringify(userId), body.deviceToken);
  }
}
