import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { NotificationTransactionEntity } from './entity/notification-transaction.entity';
import {
  NotificationTransactionDto,
  CreateNotificationTransactionInputDTO,
  UpdateNotificationTransactionInputDTO
} from './dto/notification-transaction.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NotificationTransactionService } from './notification-transaction.service';
import { NotificationTransactionSubscriber } from './notification-transaction.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { NotificationTransactionController } from './notification-transaction.controller';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([NotificationTransactionEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: NotificationTransactionService,
          DTOClass: NotificationTransactionDto,
          EntityClass: NotificationTransactionEntity,
          CreateDTOClass: CreateNotificationTransactionInputDTO,
          UpdateDTOClass: UpdateNotificationTransactionInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [NotificationTransactionSubscriber, NotificationTransactionService]
    })
  ],
  controllers: [NotificationTransactionController]
})
export class NotificationTransactionModule {}
