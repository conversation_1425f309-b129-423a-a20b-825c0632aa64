import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { ProjectDocumentCommentType } from '../dto/project-document-comment.type';

@ObjectType()
@Entity('project_document_comments')
export class ProjectDocumentCommentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  message: string;

  @FilterableField({ nullable: true })
  @Column('enum', { enum: ProjectDocumentCommentType, default: ProjectDocumentCommentType.Workspace, nullable: true })
  commentType: ProjectDocumentCommentType;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.comments)
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;

  @ManyToOne(() => UserEntity, user => user.projectDocumentComments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
