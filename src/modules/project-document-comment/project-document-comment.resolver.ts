import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { AuthData } from '@types';
import {
  CreateProjectDocumentCommentInputDTO,
  ProjectDocumentCommentDto
} from './dto/project-document-comment.gql.dto';
import { ProjectDocumentCommentService } from './project-document-comment.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => ProjectDocumentCommentDto)
export class ProjectDocumentCommentResolver {
  constructor(private readonly commentService: ProjectDocumentCommentService) {}

  @Mutation(() => ProjectDocumentCommentDto)
  async createProjectDocumentComment(
    @Args('input') input: CreateProjectDocumentCommentInputDTO,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    const { message, mentions, projectDocumentId, commentType } = input;
    const { id: userId } = user;
    return await this.commentService.createComment(message, mentions, projectDocumentId, userId, commentType);
  }
}
