import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectDocumentCommentEntity } from './entity/project-document-comment.entity';
import {
  ProjectDocumentCommentDto,
  CreateProjectDocumentCommentInputDTO,
  UpdateProjectDocumentCommentInputDTO
} from './dto/project-document-comment.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { ProjectDocumentCommentSubscriber } from './project-document-comment.subscriber';
import { ProjectDocumentCommentService } from './project-document-comment.service';
import { ProjectDocumentCommentResolver } from './project-document-comment.resolver';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectDocumentCommentEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: ProjectDocumentCommentService,
          DTOClass: ProjectDocumentCommentDto,
          EntityClass: ProjectDocumentCommentEntity,
          CreateDTOClass: CreateProjectDocumentCommentInputDTO,
          UpdateDTOClass: UpdateProjectDocumentCommentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          enableSubscriptions: true
        }
      ],
      services: [ProjectDocumentCommentSubscriber, ProjectDocumentCommentService, ProjectDocumentCommentResolver]
    })
  ]
})
export class ProjectDocumentCommentModule {}
