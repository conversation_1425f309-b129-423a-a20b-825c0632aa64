import { defaultQueryOptions, SourceType } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserEntity } from '@modules/user/entity/user.entity';
import { BeforeCreateOne, FilterableRelation, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ProjectDocumentCommentEntity } from '../entity/project-document-comment.entity';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectDocumentCommentType } from './project-document-comment.type';

@ObjectType('ProjectDocumentComment')
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
@FilterableRelation('user', () => UserEntity, relationOption())
@FilterableRelation('projectDocument', () => ProjectDocumentEntity, relationOption())
export class ProjectDocumentCommentDto extends ProjectDocumentCommentEntity {
  remoteId?: number;
}

@InputType()
export class CreateProjectDocumentCommentInputDTO {
  @IDField(() => ID) projectDocumentId: number;
  message: string;
  mentions?: string[];
  userId?: number;

  @Field(() => ProjectDocumentCommentType)
  commentType: ProjectDocumentCommentType;
  
  //? offline mode
  @Field() localId?: string;
  @Field() created_at?: Date;
  @Field() updated_at?: Date;
  @Field() localProjectDocumentId?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}
@InputType()
export class UpdateProjectDocumentCommentInputDTO extends PartialType(CreateProjectDocumentCommentInputDTO) {
    //? for offline
    @Field({ nullable: true }) localId?: string;
    @Field({ nullable: true }) created_at?: Date;
    @Field({ nullable: true }) _changed?: string;
    @Field({ nullable: true }) remoteId?: number;
}