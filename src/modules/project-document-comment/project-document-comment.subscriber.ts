import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, In, InsertEvent } from 'typeorm';
import { ProjectDocumentCommentEntity } from './entity/project-document-comment.entity';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import { uniqBy } from 'lodash';
import { getEllipsisText, getWorkflowNotificationPath } from '@constants/function';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class ProjectDocumentCommentSubscriber implements EntitySubscriberInterface<ProjectDocumentCommentEntity> {
  constructor(connection: Connection, private webSocketService: WebSocketService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectDocumentCommentEntity;
  }

  async afterInsert(event: InsertEvent<ProjectDocumentCommentEntity>) {
    try {
      const { entity } = event;
      const { message, commentType } = entity;

      const document = await event.manager.getRepository(ProjectDocumentEntity).findOne({
        where: {
          id: entity.projectDocumentId
        },
        relations: ['workspaceCCs', 'requestForSignatures']
      });

      if (!document || document === undefined) return;

      const project = await event.manager.findOne(ProjectEntity, { id: document.projectId });
      const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/comment`;
      const commentMessage = this.CommentMessage(entity.message);
      const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });

      const groupName = await event.manager.findOne(WorkspaceGroupEntity, {
        where: { id: document.workspaceGroupId },
        relations: ['parent', 'parent.workspaceGroupUsers']
      });
      const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);
      let link;

      if (commentType === 'BIM') {
        link = `/drawings/BIM-view?documentId=${document.projectDocumentId}`;
      } else {
        link = `/digital-form/all-form?documentDetailsId=${document.id}&companyId=${companyName.id}&projectId=${project.id}&status=${document.status}&workflowType=${document.workflow}`;
      }

      const mentionRegex = /\B@\[([^\]]+)\]\((\d+)\)/g;
      const mentionMatches = message.matchAll(mentionRegex);

      const mentionIds = [];
      for (const match of mentionMatches) {
        const [, , id] = match;
        mentionIds.push(id);
      }

      if (mentionIds?.length === 0) {
        try {
          if (!document) {
            throw new Error(`Task with ID ${entity.projectDocumentId} not found`);
          }

          if (!project) {
            throw new Error(`Project with ID ${document?.projectId} not found`);
          }

          const assignees = document.requestForSignatures ?? [];
          const copies = document.workspaceCCs ?? [];

          const assigneesIds = assignees.map(assignee => {
            return { userId: assignee.signById };
          });
          const copiesIds = copies.map(copy => {
            return { userId: copy.ccId };
          });

          const documentOwner = { userId: document.addedBy };
          const users = uniqBy([...assigneesIds, ...copiesIds, documentOwner], 'userId');

          const commentedUser = await event.manager.findOne(UserEntity, { id: entity.userId });
          const sanitizedUsers = await event.manager.find(UserEntity, {
            where: {
              id: In(users.map(user => user.userId))
            }
          });
          const header = `💬 Document - New Comment`;

          return sanitizedUsers
            ?.filter(user => user?.id !== entity?.userId)
            ?.forEach(async user => {
              const notRestricted =
                groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(user.id);
              if (notRestricted) {
                try {
                  const body = `commented in Document ${document.name}: ${getEllipsisText(commentMessage)}`;
                  return this.commentNotification(
                    user,
                    project,
                    document,
                    link,
                    mobileLink,
                    commentedUser,
                    body,
                    companyName,
                    header
                  );
                } catch (error) {
                  getErrorMessage(error, 'ProjectDocumentCommentSubscriber', 'afterInsert');
                }
              }
            });
        } catch (error) {
          getErrorMessage(error, 'ProjectDocumentCommentSubscriber', 'afterInsert');
        }
      }

      if (mentionIds.length > 0) {
        for (const userId of mentionIds) {
          try {
            const commentedUser = await event.manager.findOne(UserEntity, { id: entity.userId });
            const user = await event.manager.findOne(UserEntity, { id: userId });

            if (!document) {
              throw new Error(`Document with ID ${entity.projectDocumentId} not found`);
            }
            const project = await event.manager.findOne(ProjectEntity, { id: document.projectId });
            if (!project) {
              throw new Error(`Project with ID ${document.projectId} not found`);
            }
            const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/comment`;
            const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });
            const body = `mentioned you in Document ${document.name}: ${getEllipsisText(commentMessage)}`;
            const header = `💬 Document - New Comment`;

            let link;

            if (commentType === 'BIM') {
              link = `drawings/BIM-view?documentId=${document.projectDocumentId}&companyId=${companyName.id}&projectId=${project.id}`;
            } else {
              link = `/digital-form/all-form?documentDetailsId=${document.id}&companyId=${companyName.id}&projectId=${project.id}&status=${document.status}&workflowType=${document.workflow}`;
            }
            const notRestricted =
              groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(user.id);

            if (notRestricted) {
              this.commentNotification(
                user,
                project,
                document,
                link,
                mobileLink,
                commentedUser,
                body,
                companyName,
                header
              );
            }
          } catch (error) {
            throw new error(`Error processing mention for user ID ${userId}: ${error}`);
          }
        }
      }

      const socketEvent = 'event:new-project-document-comment';
      const room = `project-document-comment-room-${entity.projectDocumentId}`;
      await this.webSocketService.socket.to(room).emit(socketEvent, entity);
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentCommentSubscriber', 'afterInsert');
    }

    // const projectDocument = await event.manager
    //   .getRepository(ProjectDocumentEntity)
    //   .findOne({ id: entity.projectDocumentId });
    // const auditLog = event.manager.getRepository(AuditLogEntity).create({
    //   userId: entity.userId,
    //   projectId: projectDocument.projectId,
    //   module: AuditLogModuleType.DocumentComment,
    //   action: AuditLogActionType.Create,
    //   content: entity.message,
    //   resourceId: entity.id,
    // });
    // await event.manager.getRepository(AuditLogEntity).save(auditLog);
  }

  commentNotification(user, project, document, link, mobileLink, commentedUser, body, companyName = null, header) {
    const payload: INovuPayload = {
      user: {
        avatar: commentedUser.avatar,
        name: commentedUser.name,
        email: commentedUser.email
      },
      event: 'workspace-comment',
      header: header,
      company: companyName.name,
      title: project.title,
      head: commentedUser?.name,
      body: body,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: commentedUser.name
      }
    };

    this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          data: {
            link: mobileLink.toString(),
            projectId: project.id.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }

  CommentMessage = (message: string) => {
    const mentionRegex = /\B@\[([^\]]+)\]\(\d+\)/g;
    const replacedContent = message.replace(mentionRegex, '@$1');

    return replacedContent;
  };
}
