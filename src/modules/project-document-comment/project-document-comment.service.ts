import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ProjectDocumentCommentEntity } from './entity/project-document-comment.entity';
import { Injectable } from '@nestjs/common';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getErrorMessage } from '@common/error';
import { ProjectDocumentCommentType } from './dto/project-document-comment.type';

const { APP_URI } = process.env;

@Injectable()
export class ProjectDocumentCommentService extends TypeOrmQueryService<ProjectDocumentCommentEntity> {
  constructor(
    @InjectRepository(ProjectDocumentCommentEntity)
    @InjectRepository(ProjectUserEntity)
    private projectDocumentCommentRepo: Repository<ProjectDocumentCommentEntity>
  ) {
    // pass the use soft delete option to the service.
    super(projectDocumentCommentRepo, { useSoftDelete: true });
  }

  async createComment(message: string, mentions: string[], projectDocumentId: number, userId: number, commentType: ProjectDocumentCommentType) {
    try {
      const newComment = getRepository(ProjectDocumentCommentEntity).create({ userId, message, projectDocumentId, commentType });
      const comment = await getRepository(ProjectDocumentCommentEntity).save({ ...newComment });

      return comment;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentCommentService', 'createComment');
    }
  }
}
