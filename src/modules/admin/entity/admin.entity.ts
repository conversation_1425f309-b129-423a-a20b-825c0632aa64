import { RoleTypeEnum } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType } from '@nestjs/graphql';
import { hashPassword } from '@providers/bcrypt.service';
import { BeforeInsert, BeforeUpdate, Column, Entity, getRepository } from 'typeorm';

@ObjectType()
@Entity('admins')
export class AdminEntity extends BaseEntity {
  @FilterableField()
  @Column('varchar', { length: 20, default: RoleTypeEnum.Admin })
  type: string;

  @FilterableField()
  @Column('boolean', { default: true })
  isActive: boolean;

  @FilterableField()
  @Column('varchar', { length: 200 })
  name: string;

  @FilterableField()
  @Column('varchar', { length: 200, unique: true })
  email: string;

  @Column('varchar')
  password: string;

  /* -------------------------------- Token ------------------------------- */
  @Column('varchar', { nullable: true })
  refreshToken: string;

  @Column('text', { nullable: true })
  resetPasswordToken: string;

  /* -------------------------------- Hash Password ------------------------------- */
  @BeforeInsert()
  hashPassword() {
    this.password = hashPassword(this.password);
  }

  @BeforeUpdate()
  async hashPasswordUpdate() {
    const adminRepo = getRepository(AdminEntity);
    const admin = await adminRepo.findOne({ id: this.id });
    if (this.password !== admin.password) {
      this.password = hashPassword(this.password);
    }
  }
}
