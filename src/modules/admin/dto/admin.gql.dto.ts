import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { IsEmail } from 'class-validator';
import { AdminEntity } from '../entity/admin.entity';

@ObjectType('Admin')
@QueryOptions({ ...defaultQueryOptions })
export class AdminDto extends AdminEntity {}

@InputType()
export class CreateAdminInputDTO {
  name?: string;
  @IsEmail() email: string;
  password: string;
  isActive?: boolean;
}
@InputType()
export class UpdateAdminInputDTO extends PartialType(CreateAdminInputDTO) {}
