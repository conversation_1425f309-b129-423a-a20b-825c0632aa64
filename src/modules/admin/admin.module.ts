import { RoleTypeEnum } from '@constants';
import { Roles } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { AdminResolver } from './admin.resolver';
import { AdminService } from './admin.service';
import { AdminDto, CreateAdminInputDTO, UpdateAdminInputDTO } from './dto/admin.gql.dto';
import { AdminEntity } from './entity/admin.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([AdminEntity])],
      resolvers: [
        {
          ServiceClass: AdminService,
          DTOClass: AdminDto,
          EntityClass: AdminEntity,
          CreateDTOClass: CreateAdminInputDTO,
          UpdateDTOClass: UpdateAdminInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          decorators: [Roles(RoleTypeEnum.Admin)]
        }
      ],
      services: [AdminService, AdminResolver]
    })
  ],
  controllers: []
})
export class AdminModule {}
