import { QueryService } from '@nestjs-query/core';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminEntity } from './entity/admin.entity';

@QueryService(AdminEntity)
export class AdminService extends TypeOrmQueryService<AdminEntity> {
  constructor(
    @InjectRepository(AdminEntity)
    private adminRepo: Repository<AdminEntity>
  ) {
    // pass the use soft delete option to the service.
    super(adminRepo, { useSoftDelete: true });
  }
}
