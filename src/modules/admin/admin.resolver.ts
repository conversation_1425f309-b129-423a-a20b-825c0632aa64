import { RoleTypeEnum } from '@constants';
import { GqlGetGqlAuthData, Roles } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { InjectQueryService } from '@nestjs-query/core';
import { ForbiddenException, UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { AuthData } from '@types';
import { AdminDto, UpdateAdminInputDTO } from './dto/admin.gql.dto';
import { AdminService } from './admin.service';
import { AdminEntity } from './entity/admin.entity';
import { GqlRolesGuard } from '@guards/roles.guard';

@UseGuards(GqlAuthGuard, GqlRolesGuard)
@Resolver(() => AdminDto)
export class AdminResolver {
  constructor(
    @InjectQueryService(AdminEntity)
    private readonly adminService: AdminService
  ) {}

  @Roles(RoleTypeEnum.Admin)
  @Query(() => AdminDto)
  async getAdminMe(@GqlGetGqlAuthData() admin: AuthData): Promise<any> {
    return this.adminService.findById(admin.id);
  }

  @Roles(RoleTypeEnum.Admin)
  @Mutation(() => AdminDto)
  async updateAdminMe(@GqlGetGqlAuthData() admin: AuthData, @Args('input') input: UpdateAdminInputDTO): Promise<any> {
    if (admin.type !== RoleTypeEnum.Admin)
      throw new ForbiddenException(`This action only for Role ${RoleTypeEnum.Admin}`);
    return this.adminService.updateOne(admin.id, input);
  }
}
