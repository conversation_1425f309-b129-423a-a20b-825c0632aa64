import { Modu<PERSON> } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectTeamEntity } from './entity/project-team.entity';
import { ProjectTeamDto, CreateProjectTeamInputDTO, UpdateProjectTeamInputDTO } from './dto/project-team.gql.dto';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectTeamEntity])],
      resolvers: [
        {
          DTOClass: ProjectTeamDto,
          EntityClass: ProjectTeamEntity,
          CreateDTOClass: CreateProjectTeamInputDTO,
          UpdateDTOClass: UpdateProjectTeamInputDTO
        }
      ],
      services: []
    })
  ]
})
export class ProjectTeamModule {}
