import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, JoinTable, JoinColumn, ManyToMany, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('project_teams')
export class ProjectTeamEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectEntity, project => project.projectTeams)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToMany(() => UserEntity, user => user.projectTeams, { cascade: true })
  @JoinTable({ name: 'project_team_users' })
  users: UserEntity[];
}
