import { defaultQueryOptions } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ProjectTeamEntity } from '../entity/project-team.entity';

@ObjectType('ProjectTeam')
@QueryOptions({ ...defaultQueryOptions })
export class ProjectTeamDto extends ProjectTeamEntity {}

@InputType()
export class CreateProjectTeamInputDTO {
  @IDField(() => ID) projectId: number;
  title: string;
}
@InputType()
export class UpdateProjectTeamInputDTO extends PartialType(CreateProjectTeamInputDTO) {}
