import { defaultQueryOptions, SourceType } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { TasksMediaEntity } from '../entity/tasks-media.entity';

@ObjectType('TasksMedia')
@QueryOptions({ ...defaultQueryOptions })
export class TasksMediaDto extends TasksMediaEntity {
  remoteId?: number;
}

@InputType()
export class CreateTasksMediaInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) userId?: number;
  @IDField(() => Number) taskId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;

  //? For offline sync use
  @Field() localId?: string;
  @Field() localTaskId?: string;
  @Field() updatedBy?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}
@InputType()
export class UpdateTasksMediaInputDTO extends PartialType(CreateTasksMediaInputDTO) {


  //? For offline sync use
  @Field() updatedBy?: string;
}
