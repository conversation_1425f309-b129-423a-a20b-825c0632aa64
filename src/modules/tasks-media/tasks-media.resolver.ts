import { UseGuards } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>, <PERSON>solveField, Parent } from "@nestjs/graphql";
import { GqlAuthGuard } from "@guards/auth.guard";
import { TasksMediaDto } from "./dto/tasks-media.gql.dto";
import { TasksMediaService } from "./tasks-media.service";

@UseGuards(GqlAuthGuard)
@Resolver(() => TasksMediaDto)

export class TasksMediaResolver {
  constructor(private tasksMediaService: TasksMediaService) { }

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: TasksMediaDto) {
    return await this.tasksMediaService.getPresignedUrl(parent);
  }
}
