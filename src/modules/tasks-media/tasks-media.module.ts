import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { TasksMediaEntity } from './entity/tasks-media.entity';
import { TasksMediaDto, CreateTasksMediaInputDTO, UpdateTasksMediaInputDTO } from './dto/tasks-media.gql.dto';
import { TasksMediaService } from './tasks-media.service';
import { TasksMediaSubscriber } from './task-media.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { TasksMediaResolver } from './tasks-media.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([TasksMediaEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: TasksMediaService,
          DTOClass: TasksMediaDto,
          EntityClass: TasksMediaEntity,
          CreateDTOClass: CreateTasksMediaInputDTO,
          UpdateDTOClass: UpdateTasksMediaInputDTO,
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard]
        }
      ],
      services: [TasksMediaSubscriber, TasksMediaService, TasksMediaResolver]
    })
  ]
})
export class TasksMediaModule {}
