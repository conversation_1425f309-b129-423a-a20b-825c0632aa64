import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TasksMediaEntity } from './entity/tasks-media.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class TasksMediaService extends TypeOrmQueryService<TasksMediaEntity> {
  constructor(
    @InjectRepository(TasksMediaEntity)
    private tasksMediaRepo: Repository<TasksMediaEntity>,
    private tmOneService: TMOneService
  ) {
    super(tasksMediaRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(project_carousel: TasksMediaEntity) {
    try {
      let key = project_carousel.fileKey;
      // fallback to fileUrl if fileKey is missing
      if (!key){
        const fileName = project_carousel.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
