import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { TasksMediaEntity } from './entity/tasks-media.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '@modules/integration/file.service';
import * as _ from 'lodash';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { SoftRemoveEvent } from 'typeorm/subscriber/event/SoftRemoveEvent';

@Injectable()
@EventSubscriber()
export class TasksMediaSubscriber implements EntitySubscriberInterface<TasksMediaEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TasksMediaEntity;
  }

  async beforeInsert(event: InsertEvent<TasksMediaEntity>) {
    const { entity } = event;

    const fileObject: any = entity.fileUrl;// fileUrl is the file uploaded from the client
    if (!fileObject) throw new BadRequestException('No Document has been upload');
    if (fileObject && _.isObject(fileObject)) {
      const folder = 'Task-Medias';
      const { filename, key, type } = await this.fileService.uploadGqlFile(fileObject as FileUpload, folder);
      entity.fileKey = key;
      entity.fileUrl = key;
      entity.name = filename;
      entity.type = type;
    }
  }

  async afterInsert(event: InsertEvent<TasksMediaEntity>) {
    const { entity } = event;

    // CAPTURING FOR TASK MEDIA
    if (entity.userId) {
      const task = await event.manager.getRepository(TaskEntity).findOne({ id: entity.taskId });
      const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

      const msg = user.name + ' added ' + entity.name;

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.userId,
        projectId: task.projectId,
        taskId: task.id,
        resourceId: entity.id,
        module: AuditLogModuleType.Task,
        action: AuditLogActionType.AddPhoto,
        content: msg
      });
      await auditLog.save();
    }
  }

  async beforeSoftRemove(event: SoftRemoveEvent<TasksMediaEntity>) {
    try {
      const { entity } = event;
  
      const task = await event.manager.getRepository(TaskEntity).findOne({id: entity.taskId})
      const user = await event.manager.getRepository(UserEntity).findOne({ id: task?.ownerId })
      
      if (user) {
        const msg = (user.name +' deleted '+ entity.name)
    
        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: task.ownerId,
          projectId: task.projectId,
          taskId: task.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Task,
          action: AuditLogActionType.RemovePhoto,
          content: msg
        });
        await auditLog.save()
      }
    } catch (error) {
      throw new BadRequestException(error.message)      
    }
  }
}
