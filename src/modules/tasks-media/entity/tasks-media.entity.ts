import { BaseEntity } from '@modules/base/base';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('tasks_medias')
export class TasksMediaEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  taskId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text')
  fileUrl: string;

  @FilterableField()
  @Column('text')
  fileKey: string;

  @FilterableField()
  @Column('text')
  type: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => TaskEntity, task => task.medias, { orphanedRowAction: 'soft-delete' })
  @JoinColumn({ name: 'taskId' })
  task: TaskEntity;

  @ManyToOne(() => UserEntity, user => user.taskMedias)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
