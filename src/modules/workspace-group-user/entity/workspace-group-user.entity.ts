import { ObjectType } from '@nestjs/graphql';
import { IDField } from '@nestjs-query/query-graphql';
import { ID } from '@nestjs/graphql';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { BaseEntity } from '@modules/base/base';

@ObjectType()
@Entity('workspace_group_users')
export class WorkspaceGroupUserEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  workspaceGroupId: number;

  @ManyToOne(() => UserEntity, user => user.workspaceGroupUsers)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => WorkspaceGroupEntity, workspaceGroup => workspaceGroup.workspaceGroupUsers)
  @JoinColumn({ name: 'workspaceGroupId' })
  workspaceGroup: WorkspaceGroupEntity;
}
