import { Module } from '@nestjs/common';
import { WorkspaceGroupUserService } from './workspace-group-user.service';
import { WorkspaceGroupUserResolver } from './workspace-group-user.resolver';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { WorkspaceGroupUserEntity } from './entity/workspace-group-user.entity';
import { IntegrationModule } from '@modules/integration/integration.module';
import { WorkspaceGroupUserDTO } from './dto/workspace-group-user.gql.dto';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([
          WorkspaceGroupUserEntity,
          WorkspaceGroupEntity,
          UserEntity,
          ProjectUserEntity
        ]),
        IntegrationModule
      ],
      services: [WorkspaceGroupUserService, WorkspaceGroupUserResolver],
      resolvers: [{
        DTOClass: WorkspaceGroupUserDTO,
        EntityClass: WorkspaceGroupUserEntity,
      }],
    })
  ],
})
export class WorkspaceGroupUserModule {}
