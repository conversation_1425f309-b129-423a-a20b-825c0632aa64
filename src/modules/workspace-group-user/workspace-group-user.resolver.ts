import { Resolver, Query, Args, Int, Mutation } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { WorkspaceGroupUserService } from './workspace-group-user.service';
import { WorkspaceGroupUserEntity } from './entity/workspace-group-user.entity';
import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { AuthData } from '@types';
import { GqlAuthGuard } from '@guards/auth.guard';
import { WorkspaceGroupUserDeleteDTO } from './dto/workspace-group-user.gql.dto';

@UseGuards(GqlAuthGuard)
@Resolver(() => WorkspaceGroupUserEntity)
export class WorkspaceGroupUserResolver {
  constructor(private readonly workspaceGroupUserService: WorkspaceGroupUserService) {}

  // @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Query(() => [UserDto])
  listOfUsersWithAccessToWorkspaceGroup(@Args('workspaceGroupId', { type: () => Int }) workspaceGroupId: number) {
    return this.workspaceGroupUserService.listOfUsersWithAccessToWorkspaceGroup(workspaceGroupId);
  }

  // @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Mutation(() => WorkspaceGroupUserEntity)
  addWorkspaceGroupUser(
    @Args('userId', { type: () => Int }) userId: number,
    @Args('workspaceGroupId', { type: () => Int }) workspaceGroupId: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    try {
      console.log('user', user);
      return this.workspaceGroupUserService.addPermission(userId, workspaceGroupId, user, projectId);
    } catch (_e) {
      throw new Error('Something went wrong');
    }
  }

  // @UseGuards(new ProjectDocumentGuard(ProjectDocumentPermissionType.CanShare))
  @Mutation(() => WorkspaceGroupUserDeleteDTO)
  removeWorkspaceGroupUser(
    @Args('userId', { type: () => Int }) userId: number,
    @Args('workspaceGroupId', { type: () => Int }) workspaceGroupId: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    try {
      return this.workspaceGroupUserService.removePermission(userId, workspaceGroupId, user, projectId);
    } catch (_e) {
      throw new Error('Something went wrong');
    }
  }
}
