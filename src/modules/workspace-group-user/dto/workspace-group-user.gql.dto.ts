import { UserEntity } from '@modules/user/entity/user.entity';
import { OffsetConnection, FilterableRelation } from '@nestjs-query/query-graphql';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { relationOption } from '@constants/query.constant';
import { WorkspaceGroupUserEntity } from '../entity/workspace-group-user.entity';

@ObjectType('WorkspaceGroupUser')
@FilterableRelation('user', () => UserEntity, relationOption(true))
@OffsetConnection('user', () => UserEntity)
export class WorkspaceGroupUserDTO extends WorkspaceGroupUserEntity {}

@ObjectType()
export class WorkspaceGroupUserDeleteDTO {
  @Field(() => Int)
  affected: number;
}

