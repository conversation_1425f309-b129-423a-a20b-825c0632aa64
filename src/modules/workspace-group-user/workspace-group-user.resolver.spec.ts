import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceGroupUserResolver } from './workspace-group-user.resolver';
import { WorkspaceGroupUserService } from './workspace-group-user.service';

describe('WorkspaceGroupUserResolver', () => {
  let resolver: WorkspaceGroupUserResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [WorkspaceGroupUserResolver, WorkspaceGroupUserService],
    }).compile();

    resolver = module.get<WorkspaceGroupUserResolver>(WorkspaceGroupUserResolver);
  });

  it('should be defined', () => {
    expect(resolver).toBeDefined();
  });
});
