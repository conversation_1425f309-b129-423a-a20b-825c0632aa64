import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { WorkspaceGroupUserEntity } from './entity/workspace-group-user.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuthData } from '@types';
import { Repository } from 'typeorm';
import { getErrorMessage } from '@common/error';
import { uniqBy } from 'lodash';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectUserRoleType } from '@constants';

@Injectable()
export class WorkspaceGroupUserService extends TypeOrmQueryService<WorkspaceGroupUserEntity> {
  constructor(
    @InjectRepository(WorkspaceGroupUserEntity)
    private workspaceGroupUserRepo: Repository<WorkspaceGroupUserEntity>,
    @InjectRepository(WorkspaceGroupEntity)
    private workspaceGroupRepo: Repository<WorkspaceGroupEntity>,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    @InjectRepository(ProjectUserEntity)
    private projectUserRepo: Repository<ProjectUserEntity>

  ) {
    super(workspaceGroupUserRepo, { useSoftDelete: true });
  }

  async listOfUsersWithAccessToWorkspaceGroup(workspaceGroupId: number) {
    try {

      const workspaceGroup = await this.workspaceGroupRepo.findOne({id: workspaceGroupId});
      if (!workspaceGroup) {
        throw new Error('Workspace Group not found');
      }

      const users = await this.workspaceGroupUserRepo
        .find({
          where: {
            workspaceGroup: {
              id: workspaceGroupId
            }
          },
          relations: ['user']
        })
        .then(users => users.map(user => user.user))
        // ONLY USERS WITH PASSWORD SETUP DONE WILL BE RETRIEVED
        .then(users => users.filter(user => user.password !== null && user.password !== undefined))
        // exclude the creator of the workspace group
        .then(users => users.filter(user => user.id !== workspaceGroup.createdBy));

      return uniqBy(users, 'id');
    } catch (e) {
      getErrorMessage(e, 'WorkspaceGroupUserService', 'listOfUsersWithAccessToWorkspaceGroup');
    }
  }

  async addPermission(userId: number, workspaceGroupId: number, user: AuthData, projectId: number) {
    try {

      await this.checkPermission(userId, workspaceGroupId, user, projectId);

      if ((await this.listOfUsersWithAccessToWorkspaceGroup(workspaceGroupId)).find(user => user.id === userId))
        throw new Error('User already has access to this document');

      const owner = await this.workspaceGroupUserRepo.findOne({
        where: {
          user: {
            id: user.id
          },
          workspaceGroup: {
            id: workspaceGroupId
          }
        }
      });

      // if the owner is not in the workspace group, add the owner to the workspace group
      if (!owner) {
        const ownerWorkspaceGroupUser = new WorkspaceGroupUserEntity();
        ownerWorkspaceGroupUser.userId = user.id;
        ownerWorkspaceGroupUser.workspaceGroupId = workspaceGroupId;
        ownerWorkspaceGroupUser.createdBy = user.id;
  
        await ownerWorkspaceGroupUser.save();
      }

      const workspaceGroupUser = new WorkspaceGroupUserEntity();
      workspaceGroupUser.userId = userId;
      workspaceGroupUser.workspaceGroupId = workspaceGroupId;
      workspaceGroupUser.createdBy = user.id;

      await workspaceGroupUser.save();

      return workspaceGroupUser;

    } catch (e) {
      getErrorMessage(e, 'WorkspaceGroupUserService', 'addPermission');
    }
  }

  async removePermission(userId: number, workspaceGroupId: number, user: AuthData, projectId: number) {
    try {

      await this.checkPermission(userId, workspaceGroupId, user, projectId);

      const workspaceGroupUser = await this.workspaceGroupUserRepo.findOne(
        {
          where: {
            user: {
              id: userId
            },
            workspaceGroup: {
              id: workspaceGroupId
            }
          }
        }
      );

      if (!workspaceGroupUser) {
        throw new Error('Workspace Group User not found');
      }

      await workspaceGroupUser.remove();

      // if only the owner is left in the workspace group, remove the owner from the workspace group
      const workspaceGroupUsers = await this.workspaceGroupUserRepo.find({
        where: {
          workspaceGroup: {
            id: workspaceGroupId
          }
        }
      });

      if (workspaceGroupUsers.length === 1) {
        const owner = workspaceGroupUsers[0];

        if (owner.userId === user.id) {
          await owner.remove();
          return { affected: 2 };
        }
      }

      return { affected: 1 };
    } catch (e) {
      getErrorMessage(e, 'WorkspaceGroupUserService', 'removePermission');
    }
  }

  private async checkPermission(userId: number, workspaceGroupId: number, user: AuthData, projectId: number) {
    const workspaceGroup = await this.workspaceGroupRepo.findOne({id: workspaceGroupId});
    if (!workspaceGroup) {
      throw new Error('Workspace Group not found');
    }

    const userEntity = await UserEntity.findOne({id: userId});
    if (!userEntity) {
      throw new Error('User not found');
    }

    const userProjectRole = await this.projectUserRepo.findOne({
      where: {
        userId: user.id,
        projectId
      }
    })
    .then(projectUser => projectUser?.role);

    if (
      workspaceGroup?.createdBy !== user.id &&
      userProjectRole !== ProjectUserRoleType.ProjectOwner &&
      userProjectRole !== ProjectUserRoleType.CloudCoordinator
    )
      throw new Error('You have no permission to share this workspace group');
  
    return true
  }
}
