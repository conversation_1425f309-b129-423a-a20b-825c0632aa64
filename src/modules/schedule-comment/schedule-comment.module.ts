import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ScheduleCommentEntity } from './entity/schedule-comment.entity';
import { CreateScheduleCommentInputDTO, ScheduleCommentDto, UpdateScheduleCommentInputDTO } from './dto/schedule-comment.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { ScheduleCommentSubscriber } from './schedule-comment.subscriber';
import { ScheduleCommentService } from './schedule-comment.service';
import { ScheduleCommentResolver } from './schedule-comment.resolver';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ScheduleCommentEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: ScheduleCommentService,
          DTOClass: ScheduleCommentDto,
          EntityClass: ScheduleCommentEntity,
          CreateDTOClass: CreateScheduleCommentInputDTO,
          UpdateDTOClass: UpdateScheduleCommentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          enableSubscriptions: true
        }
      ],
      services: [ScheduleCommentSubscriber, ScheduleCommentService, ScheduleCommentResolver]
    })
  ]
})
export class ScheduleCommentModule {}
