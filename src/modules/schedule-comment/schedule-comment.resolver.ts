import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { AuthData } from '@types';
import { ScheduleCommentDto, CreateScheduleCommentInputDTO } from './dto/schedule-comment.gql.dto';
import { ScheduleCommentService } from './schedule-comment.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => ScheduleCommentDto)
export class ScheduleCommentResolver {
  constructor(private readonly commentService: ScheduleCommentService) {}

  @Mutation(() => ScheduleCommentDto)
  async createScheduleComment(@Args('input') input: CreateScheduleCommentInputDTO, @GqlGetGqlAuthData() user: AuthData) {
    const { message, mentions, scheduleId } = input;
    const { id: userId } = user;
    return await this.commentService.createComment(message, mentions, scheduleId, userId, input.createdBy);
  }
}
