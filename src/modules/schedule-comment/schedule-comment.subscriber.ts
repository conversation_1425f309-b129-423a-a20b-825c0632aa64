import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { ScheduleCommentEntity } from './entity/schedule-comment.entity';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { getErrorMessage } from '@common/error';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getEllipsisText } from '@constants/function';

const { APP_URI } = process.env;

@Injectable()
@EventSubscriber()
export class ScheduleCommentSubscriber implements EntitySubscriberInterface<ScheduleCommentEntity> {
  constructor(connection: Connection, private webSocketService: WebSocketService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ScheduleCommentEntity;
  }

  // async beforeInsert(event: InsertEvent<ScheduleCommentEntity>) {
  //   const { entity } = event;
  // }

  async afterInsert(event: InsertEvent<ScheduleCommentEntity>) {
    try {
      const { entity } = event;
      const { message } = entity;

      if (!entity?.isNotified) return;

      const mentionRegex = /\B@\[([^\]]+)\]\((\d+)\)/g;
      const mentionMatches = message.matchAll(mentionRegex);

      const schedule = await event.manager.findOne(ScheduleEntity, { id: entity.scheduleId });
      if (!schedule) {
        throw new Error(`Schedule with ID ${entity.scheduleId} not found`);
      }
      const project = await event.manager.findOne(ProjectEntity, { id: schedule.projectId });
      if (!project) {
        throw new Error(`Project with ID ${schedule.projectId} not found`);
      }

      const mentionIds = [];
      for (const match of mentionMatches) {
        const [, , id] = match;
        mentionIds.push(id);
      }

      if (mentionIds.length > 0) {
        for (const userId of mentionIds) {
          try {
            const owner = await event.manager.findOne(UserEntity, { id: entity.userId });
            const user = await event.manager.findOne(UserEntity, { id: userId });
            const mobileLink = ``;
            const commentMessage = this.CommentMessage(entity.message);
            const body = `commented on ${schedule.name}: ${getEllipsisText(commentMessage)}`;
            const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });
            const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&wbs=${schedule?.wbs}&id=${schedule.id}&projectId=${schedule?.projectId}&companyId=${project?.companyId}&status=${schedule?.status}`;
            this.statusNotification(user, project, schedule, link, mobileLink, owner, body, companyName);
          } catch (error) {
            throw new error(`Error processing mention for user ID ${userId}: ${error}`);
          }
        }
      } 

      const socketEvent = 'event:new-schedule-comment';
      const room = `schedule-comment-room-${entity.scheduleId}`;
      await this.webSocketService.socket.to(room).emit(socketEvent, entity);
    } catch (error) {
      getErrorMessage(error, 'ScheduleCommentSubscriber', 'afterInsert');
    }
  }

  statusNotification(user, project, schedule, link, mobileLink, owner, message, companyName = null) {
    try {
      const payload: INovuPayload = {
        user: {
          avatar: owner.avatar,
          name: owner.name,
          email: owner.email
        },
        company: companyName.name,
        title: project.title,
        head: owner.name,
        body: message,
        header: `💬 Schedule - New Comment`,
        headColon: true,
        link: {
          mobile: mobileLink,
          web: link,
          uri: APP_URI,
          redirect: link
        },
        subscriber: {
          firstName: user.name
        }
      };

      this.novuService.trigger('secondary-workflow', {
        to: {
          subscriberId: user.id.toString(),
          email: user.email
        },
        payload,
        overrides: {
          android: {
            priority: 'high'
          },
          fcm: {
            android: {
              priority: 'high'
            },
            data: {
              link: mobileLink.toString(),
              projectId: schedule.projectId.toString(),
              companyId: companyName?.id.toString()
            }
          }
        }
      });
    } catch (error) {
      getErrorMessage(error, 'ScheduleCommentSubscriber', 'statusNotification');
    }
  }

  CommentMessage = (message: string) => {
    const mentionRegex = /\B@\[([^\]]+)\]\(\d+\)/g;
    const replacedContent = message.replace(mentionRegex, '@$1');

    return replacedContent;
  };
}
