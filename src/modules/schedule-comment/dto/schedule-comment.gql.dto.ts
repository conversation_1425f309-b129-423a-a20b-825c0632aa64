import { defaultQueryOptions } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserEntity } from '@modules/user/entity/user.entity';
import { BeforeCreateOne, FilterableRelation, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ScheduleCommentEntity } from '../entity/schedule-comment.entity';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';

@ObjectType('ScheduleComment')
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
@FilterableRelation('user', () => UserEntity, relationOption())
@FilterableRelation('schedule', () => ScheduleEntity, relationOption())
export class ScheduleCommentDto extends ScheduleCommentEntity {}

@InputType()
export class CreateScheduleCommentInputDTO {
  @IDField(() => ID) scheduleId: number;
  message: string;
  mentions?: string[];
  createdBy?: number;
}
@InputType()
export class UpdateScheduleCommentInputDTO extends PartialType(CreateScheduleCommentInputDTO) {}

@InputType()
export class FilterScheduleCommentByScheduleIdDTO {
  scheduleId: number;
}
