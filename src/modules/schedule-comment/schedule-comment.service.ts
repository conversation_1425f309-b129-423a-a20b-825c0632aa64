import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ScheduleCommentEntity } from './entity/schedule-comment.entity';
import { Injectable } from '@nestjs/common';

const { APP_URI } = process.env;

@Injectable()
export class ScheduleCommentService extends TypeOrmQueryService<ScheduleCommentEntity> {
  constructor(
    @InjectRepository(ScheduleCommentEntity)
    private scheduleCommentRepo: Repository<ScheduleCommentEntity>
  ) {
    // pass the use soft delete option to the service.
    super(scheduleCommentRepo, { useSoftDelete: true });
  }

  async createComment(message: string, mentions: string[], scheduleId: number, userId: number, createdBy: number) {
    const newComment = getRepository(ScheduleCommentEntity).create({ userId, message, scheduleId, createdBy});
    const comment = await getRepository(ScheduleCommentEntity).save({ ...newComment });

    return comment;
  }
}
