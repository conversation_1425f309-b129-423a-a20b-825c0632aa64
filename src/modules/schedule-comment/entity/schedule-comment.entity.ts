import { BaseEntity } from '@modules/base/base';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('schedule_comments')
export class ScheduleCommentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  scheduleId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  message: string;

  @Column('boolean', { default: true })
  isNotified: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ScheduleEntity, schedule => schedule.comments, {
    // orphanedRowAction: 'soft-delete',
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'scheduleId' })
  schedule: ScheduleEntity;

  @ManyToOne(() => UserEntity, user => user.scheduleComments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
