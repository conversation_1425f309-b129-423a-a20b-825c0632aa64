import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { ChangeLogEntity } from './entity/change-log.entity';
import _ from 'lodash';
import { FileService } from '@modules/integration/file.service';
import { createReadStream } from 'fs';


@Injectable()
@EventSubscriber()
export class ChangeLogSubscriber implements EntitySubscriberInterface<ChangeLogEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ChangeLogEntity;
  }

  async beforeInsert(event: InsertEvent<ChangeLogEntity>) {
    const { entity } = event;

    const file: any = entity.imageUrl;
    if (!file) throw new BadRequestException('No image has been upload');

    const preparedFile = await this.prepareFileObject(file);

    if (file && _.isObject(file)) {
      const folder = 'ChangeLog';
      const { key } = await this.fileService.uploadGqlFile(preparedFile as any, folder);
      entity.imageUrl = key;
    }
  }

  async prepareFileObject(file) {
  return Promise.resolve({
    filename: file?.name,
    mimetype: file?.type,
    encoding: '7bit',
    createReadStream: () => createReadStream(file.path)
  });
}
}
