import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ChangeLogEntity } from '../entity/change-log.entity';

@ObjectType('ChangeLog')
@QueryOptions({ ...defaultQueryOptions })
export class ChangeLogDto extends ChangeLogEntity {}
@InputType()
export class CreateChangeLogInputDTO {
  descriptions?: string;
}
@InputType()
export class UpdateChangeLogInputDTO extends PartialType(CreateChangeLogInputDTO) {}
