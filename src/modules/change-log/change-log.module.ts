import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { ChangeLogService } from './change-log.service';
import { ChangeLogDto } from './dto/change-log.gql.dto';
import { ChangeLogEntity } from './entity/change-log.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ChangeLogSubscriber } from './change-log.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ChangeLogEntity]), IntegrationModule],
      resolvers: [
        {
          // ServiceClass: ChangeLogService,
          DTOClass: ChangeLogDto,
          EntityClass: ChangeLogEntity,
          // CreateDTOClass: CreateChangeLogInputDTO,
          // UpdateDTOClass: UpdateChangeLogInputDTO,

          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],

      services: [ChangeLogService, ChangeLogEntity, ChangeLogSubscriber]
    })
  ]
})
export class ChangeLogModule {}
