import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { ChangeLogEntity } from './entity/change-log.entity';

@Injectable()
export class ChangeLogService extends TypeOrmQueryService<ChangeLogEntity> {
  constructor(
    @InjectRepository(ChangeLogEntity)
    private changeLogRepo: Repository<ChangeLogEntity>
  ) {
    super(changeLogRepo, { useSoftDelete: true });
  }
}
