import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, In, Not, Repository } from 'typeorm';
import { WorkspaceGroupEntity } from './entity/workspace-group.entity';
import { BadRequestException, ConflictException, Injectable } from '@nestjs/common';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { CategoryType, ProjectDocumentStatus, ProjectUserRoleType } from '@constants';
import { AuthData } from '@types';
import { CreateWorkspaceGroupInputDTO, UpdateWorkspaceGroupInputDTO } from './dto/workspace-group.gql.dto';
import { getErrorMessage } from '@common/error';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectDocumentService } from '@modules/project-document/project-document.service';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';

@Injectable()
export class WorkspaceGroupService extends TypeOrmQueryService<WorkspaceGroupEntity> {
  constructor(
    @InjectRepository(WorkspaceGroupEntity)
    private workspaceGroupRepo: Repository<WorkspaceGroupEntity>,
    private projectDocumentService: ProjectDocumentService
  ) {
    // pass the use soft delete option to the service.
    super(workspaceGroupRepo, { useSoftDelete: true });
  }

  async getTotalOfDocuments(projectId: number) {
    const totalCount = await getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .where(
        '(projectDocument.projectId = :projectId) AND (projectDocument.status != :status) AND (projectDocument.category = :category)',
        { status: ProjectDocumentStatus.Draft, category: CategoryType.AllForm, projectId }
      )
      .getCount();

    return totalCount;
  }

  async getGroupsDocumentsCount(projectId: number) {
    try {
      const workspaceGroups = await this.workspaceGroupRepo.find({
        where: { projectId },
        order: { createdAt: 'DESC' }
      });

      // Count of the "ungroup documents"'s documents
      const ungroup = workspaceGroups.filter(workspaceGroup => {
        return workspaceGroup.name === 'Ungroup Documents';
      });
      const countOfUngroup = await this.countForWorkspaceDocuments(ungroup);

      // Count of the other custom groups' documents
      const customGroups = workspaceGroups.filter(workspaceGroup => {
        return workspaceGroup.name !== 'Ungroup Documents';
      });
      const countOfCustomGroups = await this.countForWorkspaceDocuments(customGroups);

      return [...countOfUngroup, ...countOfCustomGroups];
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'getGroupsDocumentsCount');
    }
  }

  private queryGroups(projectId: number, filterName: string) {

    const groups = getRepository(WorkspaceGroupEntity)
      .createQueryBuilder('group')
      .orderBy('group.createdAt', 'ASC')
      .leftJoinAndSelect('group.documents', 'documents')
      .leftJoinAndSelect('group.creator', 'creator')
      .leftJoinAndSelect('group.workspaceGroupUsers', 'workspaceGroupUsers')
      .leftJoinAndSelect('workspaceGroupUsers.user', 'user')
      .where('(group.projectId = :projectId)', { projectId })
      .andWhere('(group.workspaceGroupId IS NULL)');

    groups.addSelect(
      `CASE
    WHEN group.name = 'Ungroup Documents' THEN 4
    WHEN group.name = 'Site Diary' THEN 3
    WHEN group.name = 'Non Conformance Report' OR group.name = 'NON CONFORMANCE REPORT' THEN 2
    WHEN group.name = 'Request For Information' OR group.name = 'REQUEST FOR INFORMATION' THEN 1
    WHEN group.name REGEXP '^[0-9]' THEN 5
    ELSE 6
    END`,
      'custom_order'
    );

    groups.orderBy({
      custom_order: 'ASC',
      'group.name': 'ASC'
      // 'group.createdAt': 'DESC'
    });

    groups
      ?.leftJoinAndSelect('group.children', 'children')
      .leftJoinAndSelect('children.documents', 'childrenDocuments');

    if (filterName) {
      const name = filterName;
      if (name !== null) {
        groups.andWhere(
          '(group.name LIKE :name) OR (children.name LIKE :name) AND (group.projectId = :projectId) AND (children.projectId = :projectId)',
          {
            name: `%${name}%`,
            projectId
          }
        );
      }
    }
    
    return groups;
  }

  async getWorkspaceGroups(projectId: number, userId: number, query: any, toCount?: boolean) {
    
    try {
      const projectUserRole = await getRepository(ProjectUserEntity)
        .findOne({
          projectId,
          userId
        })
        .then(projectUser => projectUser.role);

      const groups = this.queryGroups(projectId, query?.filter?.name?.like);
      groups?.skip(query?.paging?.offset).take(query?.paging?.limit - 1);
      // project owner can see all groups
      if (projectUserRole != ProjectUserRoleType.ProjectOwner) {
        const accessibleWorkspaceGroupIds = await this.projectDocumentService.getAccessibleWorkspaceGroupId(
          userId,
          projectId
        );
        groups.andWhere('group.id IN (:...accessibleWorkspaceGroupIds)', { accessibleWorkspaceGroupIds });
      }

      const response = await groups.getMany();
      
      if (toCount) {
        const totalCount = await groups.getCount();
        return totalCount;
      }

      const getCalculatedParentCounts = await this.calculateParentCounts(response);

      return getCalculatedParentCounts
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'getWorkspaceGroups');
    }
  }

  async getCountWorkspaceGroup(projectId: number, query: any) {
    try {
      const groups = this.queryGroups(projectId, query?.name?.like);
      const totalCount = await groups.getCount();

      return totalCount;
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'getCountWorkspaceGroup');
    }
  }

  mapGroupWithStatus(group) {
    try {
      const sanitizedDocuments = group.documents?.filter?.(document => document.status !== ProjectDocumentStatus.Draft);
      const totalCount = sanitizedDocuments?.length;
      const submittedCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.Submitted)?.length ?? 0;
      const inReviewCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.InReview)?.length ?? 0;
      const approvedCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.Approved)?.length ?? 0;
      const rejectedCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.Rejected)?.length ?? 0;
      const inProgressCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.InProgress)?.length ?? 0;
      const pendingCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.Pending)?.length ?? 0;
      const amendCount =
        sanitizedDocuments?.filter?.(document => document?.status === ProjectDocumentStatus?.Amend)?.length ?? 0;

      return {
        ...group,
        totalCount,
        submittedCount,
        inReviewCount,
        approvedCount,
        rejectedCount,
        inProgressCount,
        pendingCount,
        amendCount
      };
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'mapGroupWithStatus');
    }
  }

  async calculateParentCounts(groups: WorkspaceGroupEntity[]) {
    try {
      const groupsWithAggregatedCounts = await Promise.all(
        groups?.map?.(async group => {
          if (group.name === 'Ungroup Documents' || group.name === 'Site Diary') {
            //? add empty children array to the ungroup documents group
            group.children = [];
            return this.mapGroupWithStatus(group);
          }
          const children = group.children;
          //? if no children, return the empty children array
          if (!children?.length) {
            return {
              ...group,
              children: [],
              totalCount: 0,
              submittedCount: 0,
              inReviewCount: 0,
              amendCount: 0,
              approvedCount: 0,
              rejectedCount: 0,
              inProgressCount: 0,
              pendingCount: 0
            };
          }

          const childrenWithStatus = children.map(child => this.mapGroupWithStatus(child));
          const totalCount = childrenWithStatus.reduce((acc, curr) => acc + curr.totalCount, 0);
          const submittedCount = childrenWithStatus.reduce((acc, curr) => acc + curr.submittedCount, 0);
          const inReviewCount = childrenWithStatus.reduce((acc, curr) => acc + curr.inReviewCount, 0);
          const amendCount = childrenWithStatus.reduce((acc, curr) => acc + curr.amendCount, 0);
          const approvedCount = childrenWithStatus.reduce((acc, curr) => acc + curr.approvedCount, 0);
          const rejectedCount = childrenWithStatus.reduce((acc, curr) => acc + curr.rejectedCount, 0);
          const inProgressCount = childrenWithStatus.reduce((acc, curr) => acc + curr.inProgressCount, 0);
          const pendingCount = childrenWithStatus.reduce((acc, curr) => acc + curr.pendingCount, 0);

          return {
            ...group,
            children: childrenWithStatus,
            totalCount,
            submittedCount,
            inReviewCount,
            amendCount,
            approvedCount,
            rejectedCount,
            inProgressCount,
            pendingCount
          };
        })
      );

      const sortedGroupsWithAggregatedCounts = groupsWithAggregatedCounts.map(group => {
        if (group.children && group.children.length) {
          group.children.sort((a: WorkspaceGroupEntity, b: WorkspaceGroupEntity) =>
            a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' })
          );
        }
        return group;
      });

      sortedGroupsWithAggregatedCounts.sort((a: WorkspaceGroupEntity, b: WorkspaceGroupEntity) => {
        if (
          a.name === 'Ungroup Documents' ||
          a.name === 'Site Diary' ||
          a.name === 'Request For Information' ||
          a.name === 'REQUEST FOR INFORMATION' ||
          a.name === 'Non Conformance Report' ||
          a.name === 'NON CONFORMANCE REPORT'
        )
          return -1;
        if (
          b.name === 'Ungroup Documents' ||
          b.name === 'Site Diary' ||
          b.name === 'Request For Information' ||
          b.name === 'REQUEST FOR INFORMATION' ||
          b.name === 'Non Conformance Report' ||
          b.name === 'NON CONFORMANCE REPORT'
        )
          return 1;
        return a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' });
      });

      return sortedGroupsWithAggregatedCounts;
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'calculateParentCounts');
    }
  }

  sortGroups(groups: WorkspaceGroupEntity[]) {
    try {
      const specialGroups = ['Ungroup Documents', 'Site Diary', 'Request For Information', 'Non Conformance Report'];

      // Sort the response by special groups to be the first elements in the array
      specialGroups.forEach(groupName => {
        const index = groups.findIndex(group => group.name === groupName);
        if (index !== -1) {
          const group = groups.splice(index, 1);
          groups.unshift(...group);
        }
      });

      return groups;
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'sortGroups');
    }
  }

  async createWorkspaceGroup(projectId: number, input: CreateWorkspaceGroupInputDTO, user: AuthData) {
    try {
      const { name, code, workspaceGroupId } = input;
      const sanitizedWorkspaceGroupId = workspaceGroupId ? workspaceGroupId : null;

      const workspaceGroup = this.workspaceGroupRepo.create({
        name,
        projectId,
        createdBy: user.id,
        code,
        workspaceGroupId: sanitizedWorkspaceGroupId
      });

      return await this.workspaceGroupRepo.save(workspaceGroup);
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'createWorkspaceGroup');
    }
  }

  async updateWorkspaceGroup(projectId: number, input: UpdateWorkspaceGroupInputDTO, user: AuthData) {
    try {
      const workspaceGroup = await this.workspaceGroupRepo.findOne({
        id: parseInt(input.id),
        projectId,
        workspaceGroupId: null
      });
      const isCodeChanged = workspaceGroup?.code !== input?.code;
      const isNameChanged = workspaceGroup?.name !== input?.name;

      if (isNameChanged) {
        const group = await getRepository(WorkspaceGroupEntity)
          .createQueryBuilder('workspaceGroup')
          .where('BINARY workspaceGroup.name = :name', { name: input.name })
          .andWhere('workspaceGroup.projectId = :projectId', { projectId });

        if (input?.workspaceGroupId) {
          group.andWhere('workspaceGroup.workspaceGroupId = :workspaceGroupId', {
            workspaceGroupId: input.workspaceGroupId
          });
        } else {
          group.andWhere('workspaceGroup.workspaceGroupId IS NULL');
        }
        const isNameExist = await group.getOne();

        if (isNameExist) {
          throw new ConflictException('This name is already exist');
        }
      }

      if (isCodeChanged && workspaceGroup?.workspaceGroupId === null) {
        const isCodeExist = await this.workspaceGroupRepo.findOne({ code: input.code, projectId });
        if (isCodeExist) {
          throw new ConflictException('This code already exist');
        }

        const children = await this.workspaceGroupRepo.find({ where: { workspaceGroupId: parseInt(input.id) } });
        const childrenIds = children.map(child => child.id);

        await this.workspaceGroupRepo.update({ id: In(childrenIds) }, { code: input.code });
      }

      return await this.workspaceGroupRepo.save({
        id: parseInt(input.id),
        name: input.name,
        code: input?.code ? input?.code : workspaceGroup?.code,
        projectId,
        updatedBy: user.id,
        workspaceGroupId: input.workspaceGroupId ? input.workspaceGroupId : null
      });
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'updateWorkspaceGroup');
    }
  }

  async setSubgroup(subGroup: boolean) {
    // get all group and count
    try {
      const groups = await this.workspaceGroupRepo.find();
      const groupsCount = groups.length;
      for (const group of groups) {
        let code;
        const child = group.name + ' - child';
        if (group.name == 'Ungroup Documents') {
          code = 'UD';
        } else if (group.name == 'Site Diary') {
          code = 'SD';
        } else {
          const name = group.name;
          const firstTwoLetters = name.slice(0, 2);
          const lastLetter = name.charAt(name.length - 2);
          code = `${firstTwoLetters}${lastLetter}`;
        }
        //update the group code with code above
        await this.workspaceGroupRepo.update({ id: group.id }, { code });
        if (group.name != 'Ungroup Documents' && group.name != 'Site Diary') {
          const workspaceGroup = this.workspaceGroupRepo.create({
            name: child,
            projectId: group.projectId,
            createdBy: group.createdBy,
            workspaceGroupId: group.id
          });
          await this.workspaceGroupRepo.save(workspaceGroup);
        }
      }
      return { groupsCount };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async workspaceScript() {
    try {
      const existingGroups = [];
      const nonExistingGroups = [];
      // Get lists of project ID
      const projects = await getRepository(ProjectEntity).createQueryBuilder('project').select('project.id').getMany();
      const projectIds = projects.map(project => project.id);

      // Iterate through project IDs
      for (const projectId of projectIds) {
        // Check if TQ and NCR already exist in that project
        const checkTQ = await this.workspaceGroupRepo.findOne({ projectId, name: 'Request For Information' });
        const checkNCR = await this.workspaceGroupRepo.findOne({ projectId, name: 'Non Conformance Report' });

        if (checkTQ || checkNCR) {
          // If both TQ and NCR exist, add project ID to existingGroups
          existingGroups.push(projectId);
        } else {
          // If either TQ or NCR doesn't exist, add them and project ID to nonExistingGroups
          nonExistingGroups.push(projectId);
          const workspaceGroup = this.workspaceGroupRepo.create({
            name: 'Request For Information',
            code: 'TQ',
            projectId
          });
          await this.workspaceGroupRepo.save(workspaceGroup);

          const workspaceGroup2 = this.workspaceGroupRepo.create({
            name: 'Non Conformance Report',
            code: 'NCR',
            projectId
          });
          await this.workspaceGroupRepo.save(workspaceGroup2);
          // Create children for the workspace groups
          const tqChildren = [{ name: 'RFI1' }, { name: 'RFI2' }];
          const ncrChildren = [{ name: 'NCR1' }, { name: 'NCR2' }];

          // Save children for TQ workspace group
          for (const child of tqChildren) {
            const tqChildEntity = this.workspaceGroupRepo.create({
              ...child,
              workspaceGroupId: workspaceGroup.id,
              projectId
            });
            await this.workspaceGroupRepo.save(tqChildEntity);
          }

          // Save children for NCR workspace group
          for (const child of ncrChildren) {
            const ncrChildEntity = this.workspaceGroupRepo.create({
              ...child,
              workspaceGroupId: workspaceGroup2.id,
              projectId
            });
            await this.workspaceGroupRepo.save(ncrChildEntity);
          }
        }
      }
      //return existing and non existing values
      return { existingGroups, nonExistingGroups };
    } catch (error) {
      // Handle errors
      console.error('Error:', error);
      return []; // Return empty array in case of error
    }
  }

  private async countForWorkspaceDocuments(workspaceGroups: WorkspaceGroupEntity[]) {
    try {
      return await Promise.all(
        workspaceGroups.map(async workspaceGroup => {
          const workSpaceId = workspaceGroup.id;
          const workspaceGroupName = workspaceGroup.name;
          const totalCount = await getRepository(ProjectDocumentEntity).count({
            workspaceGroupId: workspaceGroup.id,
            status: Not(ProjectDocumentStatus.Draft)
          });
          const submittedCount = await getRepository(ProjectDocumentEntity).count({
            workspaceGroupId: workspaceGroup.id,
            status: ProjectDocumentStatus.Submitted
          });
          const inReviewCount = await getRepository(ProjectDocumentEntity).count({
            workspaceGroupId: workspaceGroup.id,
            status: ProjectDocumentStatus.InReview
          });
          const approvedCount = await getRepository(ProjectDocumentEntity).count({
            workspaceGroupId: workspaceGroup.id,
            status: ProjectDocumentStatus.Approved
          });
          const rejectedCount = await getRepository(ProjectDocumentEntity).count({
            workspaceGroupId: workspaceGroup.id,
            status: ProjectDocumentStatus.Rejected
          });

          return {
            workSpaceId,
            workspaceGroupName,
            totalCount,
            submittedCount,
            inReviewCount,
            approvedCount,
            rejectedCount
          };
        })
      );
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupService', 'countForWorkspaceDocuments');
    }
  }
}
