import { defaultQueryOptions, SourceType } from '@constants';
import { relationOption } from '@constants/query.constant';
import {
  Authorize,
  BeforeCreateOne,
  FilterableRelation,
  FilterableUnPagedRelation,
  QueryArgsType,
  QueryOptions,
  UnPagedRelation
} from '@nestjs-query/query-graphql';
import { Field, ArgsType, InputType, ObjectType, PartialType, ID } from '@nestjs/graphql';
import { WorkspaceGroupEntity } from '../entity/workspace-group.entity';
import * as Hooks from '@hooks/nest-graphql-projectId.hooks';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { WorkspaceGroupAuthorizer } from '../workspace-group.authorizer';
import { WorkspaceGroupUserDTO } from '@modules/workspace-group-user/dto/workspace-group-user.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { UserEntity } from '@modules/user/entity/user.entity';

@ObjectType('WorkspaceGroup')
@Authorize(WorkspaceGroupAuthorizer)
@UnPagedRelation('documents', () => ProjectDocumentDto, {
  ...relationOption(true),
  relationName: 'documents'
})
@FilterableRelation('parent', () => WorkspaceGroupDto, relationOption(true))
@FilterableUnPagedRelation('children', () => WorkspaceGroupDto, relationOption(true))
@FilterableRelation('creator', () => UserDto, relationOption(true))
@UnPagedRelation('workspaceGroupUsers', () => WorkspaceGroupUserDTO, {
  ...relationOption(true),
  relationName: 'workspaceGroupUsers'
})
@BeforeCreateOne(Hooks.ProjectIdOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class WorkspaceGroupDto extends WorkspaceGroupEntity {
  submittedCount?: number;
  inReviewCount?: number;
  approvedCount?: number;
  rejectedCount?: number;
  inProgressCount?: number;
  pendingCount?: number;
  amendCount?: number;
  totalCount?: number;
  code?: string;
  
  //? for offline sync
  remoteId?: number;
}

@ObjectType('WorkspaceGroupWithChildren')
@QueryOptions({ ...defaultQueryOptions })
export class WorkspaceGroupWithChildrenDto extends WorkspaceGroupDto {
  children: WorkspaceGroupDto[];
  creator?: UserEntity;
  workspaceGroupUsers: WorkspaceGroupUserDTO[];
}

@ArgsType()
export class WorkspaceGroupQuery extends QueryArgsType(WorkspaceGroupWithChildrenDto) {}
export const WorkspaceGroupConnection = WorkspaceGroupQuery.ConnectionType;

@InputType()
export class CreateWorkspaceGroupInputDTO {
  @Field(() => ID) id?: string;
  @Field(() => ID) projectId: number;
  @Field() name: string;
  @Field({ nullable: true }) workspaceGroupId?: number;
  @Field({ nullable: true }) code?: string;
  @Field({ nullable: true }) localId?: string;

  //? For offline sync use
  @Field({ nullable: true }) updatedBy?: string;
  @Field({ nullable: true }) _changed?: string;
  @Field({ nullable: true }) updated_at?: number;
  @Field({ nullable: true }) created_at?: number;
  @Field({ nullable: true }) remoteId?: number;
  @Field({ nullable: true }) localGroupId?: string;
  @Field({ nullable: true }) recordSource?: SourceType;
}

@InputType()
export class UpdateWorkspaceGroupInputDTO extends PartialType(CreateWorkspaceGroupInputDTO) {}
