import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import {
  WorkspaceGroupConnection,
  WorkspaceGroupQuery,
  WorkspaceGroupDto,
  CreateWorkspaceGroupInputDTO,
  UpdateWorkspaceGroupInputDTO
} from './dto/workspace-group.gql.dto';
import { WorkspaceGroupService } from './workspace-group.service';
import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { Filter } from '@nestjs-query/core';
import { AuthData } from '@types';

@UseGuards(GqlAuthGuard)
@Resolver()
export class WorkspaceGroupResolver {
  constructor(private groupService: WorkspaceGroupService) {}

  @Query(() => WorkspaceGroupConnection)
  async getWorkspaceGroups(
    @Args() query: WorkspaceGroupQuery,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
    ) {
    const filter: Filter<WorkspaceGroupDto> = {
      ...query.filter
    };

    const response = (await WorkspaceGroupConnection.createFromPromise(
      async q => await this.groupService.getWorkspaceGroups(projectId, user.id, q, false) as any,
      { ...query, ...{ filter }},
    )) as any;       

    const totalCount = await this.groupService.getWorkspaceGroups(projectId, user.id, query, true)        

    return {
      ...response,
      totalCount
    };
  }

  @Mutation(() => WorkspaceGroupDto)
  async createWorkspaceGroup(
    @Args('input') input: CreateWorkspaceGroupInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.groupService.createWorkspaceGroup(projectId, input, user);
  }

  @Mutation(() => WorkspaceGroupDto)
  async updateWorkspaceGroup(
    @Args('input') input: UpdateWorkspaceGroupInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.groupService.updateWorkspaceGroup(projectId, input, user);
  }
}
