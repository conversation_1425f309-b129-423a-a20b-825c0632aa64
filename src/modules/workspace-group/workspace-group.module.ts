import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { WorkspaceGroupEntity } from './entity/workspace-group.entity';
import {
  WorkspaceGroupDto,
  CreateWorkspaceGroupInputDTO,
  UpdateWorkspaceGroupInputDTO
} from './dto/workspace-group.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { WorkspaceGroupService } from './workspace-group.service';
import { WorkspaceGroupController } from './workspace-group.controller';
import { WorkspaceGroupSubscriber } from './workspace-group.subscriber';
import { WorkspaceGroupResolver } from './workspace-group.resolver'
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectDocumentService } from '@modules/project-document/project-document.service';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([
        WorkspaceGroupEntity,
        ProjectDocumentEntity,
        ProjectDocumentUserEntity,
        WorkspaceAttachmentEntity,
        WorkspacePhotoEntity
      ]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: WorkspaceGroupService,
          DTOClass: WorkspaceGroupDto,
          EntityClass: WorkspaceGroupEntity,
          CreateDTOClass: CreateWorkspaceGroupInputDTO,
          UpdateDTOClass: UpdateWorkspaceGroupInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [WorkspaceGroupService, WorkspaceGroupSubscriber, WorkspaceGroupResolver, ProjectDocumentService]
    })  
  ],
  controllers: [WorkspaceGroupController]
})
export class WorkspaceGroupModule {}
