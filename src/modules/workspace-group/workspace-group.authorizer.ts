import { UnauthorizedException, Injectable, BadRequestException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { WorkspaceGroupDto } from './dto/workspace-group.gql.dto';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getRepository } from 'typeorm';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class WorkspaceGroupAuthorizer implements Authorizer<WorkspaceGroupDto> {
  async authorize(context: any): Promise<Filter<WorkspaceGroupDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const projectId = context?.req?.headers?.['project-id'];
    if (!projectId) throw new BadRequestException('Project Id not found');
    const userId = context?.req?.user?.id;
    if (!projectId) throw new UnauthorizedException('User not found');
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

    if (projectUser) {
      return Promise.resolve({
        projectId: { eq: projectId }
      });
    }
    throw new UnauthorizedException('You are not involved in this project');
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
