import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { WorkspaceGroupUserEntity } from '@modules/workspace-group-user/entity/workspace-group-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn, OneToMany, Tree, TreeChildren, TreeParent } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';

@ObjectType()
@Entity('workspace_groups')
@Tree('materialized-path')
export class WorkspaceGroupEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column('varchar')
  name: string;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  workspaceGroupId: number;

  @FilterableField()
  @Column({ type: 'varchar' })
  code?: string;

  /* -------------------------------- Relations ------------------------------- */
  @OneToMany(() => ProjectDocumentEntity, documents => documents.workspaceGroup)
  documents: ProjectDocumentEntity[];

  @OneToMany(() => WorkspaceGroupUserEntity, documents => documents.workspaceGroup)
  workspaceGroupUsers: WorkspaceGroupUserEntity[];

  @ManyToOne(() => ProjectEntity, project => project.workspaceGroups)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => UserEntity, user => user.workspaceGroups)
  @JoinColumn({ name: 'createdBy' })
  creator?: UserEntity;

  @TreeChildren({ cascade: ['soft-remove', 'remove', 'recover'] })
  children: WorkspaceGroupEntity[];

  @TreeParent({ onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workspaceGroupId' })
  parent: WorkspaceGroupEntity;
}
