import { GetAuthD<PERSON>, GetProjectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { BadRequestException, Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { getRepository } from 'typeorm';
import { WorkspaceGroupService } from './workspace-group.service';

@ApiTags('WorkspaceGroup API')
@Controller('workspaceGroup/overview')
export class WorkspaceGroupController {
  constructor(private workspaceService: WorkspaceGroupService) {}

  @UseApiUserAuthGuard()
  @Get('get-total-documents')
  async getTotalOfDocuments(@GetProjectData() projectId: number, @GetAuthData() user: AuthData) {
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId: user.id, projectId });
    if (!projectUser) throw new BadRequestException('You are not involved in this project');

    const totalCount = await this.workspaceService.getTotalOfDocuments(projectId);
    return totalCount;
  }

  @UseApiUserAuthGuard()
  @Get('groups-documents-count')
  async getGroupsDocumentsCount(@GetProjectData() projectId: number, @GetAuthData() user: AuthData) {
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId: user.id, projectId });
    if (!projectUser) throw new BadRequestException('You are not involved in this project');

    const workspaceDocumentCounts = await this.workspaceService.getGroupsDocumentsCount(projectId);
    return workspaceDocumentCounts;
  }

  @Get('workspace-sub-group')
  async setWorkspaceSubGroup(@Query('subGroup') subGroup: boolean) {
    const siteDiaryGroup = await this.workspaceService.setSubgroup(subGroup);
    return siteDiaryGroup;
  }

  // one off script, please remove after use
  @Get('workspace-script')
  async setWorkspaceScript(@Query('script') script: boolean) {
    const siteDiaryScript = await this.workspaceService.workspaceScript();
    return siteDiaryScript;
  }
}
