import { ConflictException, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent
} from 'typeorm';
import { WorkspaceGroupEntity } from './entity/workspace-group.entity';
import { SoftRemoveEvent } from 'typeorm/subscriber/event/SoftRemoveEvent';
import { ProjectDocumentEntity } from '../project-document/entity/project-document.entity';
import { getErrorMessage } from '@common/error';
import { SourceType } from '@constants';

@Injectable()
@EventSubscriber()
export class WorkspaceGroupSubscriber implements EntitySubscriberInterface<WorkspaceGroupEntity> {
  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return WorkspaceGroupEntity;
  }

  async beforeInsert(event: InsertEvent<WorkspaceGroupEntity>) {
    try {
      const { entity } = event;
      const group = event.manager
        .getRepository(WorkspaceGroupEntity)
        .createQueryBuilder('workspaceGroup')
        .where('BINARY workspaceGroup.name = :name', { name: entity.name })
        .andWhere('workspaceGroup.projectId = :projectId', { projectId: entity.projectId });

      if (entity?.workspaceGroupId) {
        group.andWhere('workspaceGroup.workspaceGroupId = :workspaceGroupId', {
          workspaceGroupId: entity.workspaceGroupId
        });
      } else {
        group.andWhere('workspaceGroup.workspaceGroupId IS NULL');
      }

      const isExist = await group.getOne();

      if (isExist) {
        throw new ConflictException('This name is already exist');
      }

      if (entity.recordSource === SourceType.OfflineApp) return;

      //? if the group is not a root group, then get the parent group's code and append the new group's name to it
      if (entity?.workspaceGroupId) {
        const parentGroup = await event.manager
          .getRepository(WorkspaceGroupEntity)
          .findOne({ id: entity.workspaceGroupId });
        entity.code = parentGroup.code;
      } else {
        const isCodeExist = await event.manager
          .getRepository(WorkspaceGroupEntity)
          .findOne({ code: entity.code, projectId: entity.projectId });
        if (isCodeExist) {
          throw new ConflictException('This code already exist');
        }
      }

      return;
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupSubscriber', 'beforeInsert');
    }
  }

  async beforeSoftRemove(event: SoftRemoveEvent<WorkspaceGroupEntity>): Promise<void | Promise<any>> {
    try {
      const { entity } = event;
      // find all the documents that are in the workspace group that is being deleted and set the workspaceGroupId to Ungroup Documents
      const deletedGroup = await event.manager.getRepository(WorkspaceGroupEntity).findOne({ id: entity.id });
      const ungroupedGroup = await event.manager
        .getRepository(WorkspaceGroupEntity)
        .findOne({ name: 'Ungroup Documents', projectId: entity.projectId });

      const projectDocuments = await event.manager
        .getRepository(ProjectDocumentEntity)
        .find({ workspaceGroupId: deletedGroup.id });

      for (const doc of projectDocuments) {
        doc.workspaceGroupId = ungroupedGroup.id;
        await event.manager.getRepository(ProjectDocumentEntity).save(doc);
      }

      return;
    } catch (error) {
      getErrorMessage(error, 'WorkspaceGroupSubscriber', 'beforeSoftRemove');
    }
  }
}
