import { Test, TestingModule } from '@nestjs/testing';
import { ProjectGroupController } from './project-group.controller';
import { ProjectGroupService } from './project-group.service';

describe('ProjectGroupController', () => {
  let controller: ProjectGroupController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [{
        provide: ProjectGroupService,
        useValue: {},
      }],
      controllers: [ProjectGroupController],
    }).compile();

    controller = module.get<ProjectGroupController>(ProjectGroupController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
