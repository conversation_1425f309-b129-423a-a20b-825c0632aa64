import { Test, TestingModule } from '@nestjs/testing';
import { ProjectGroupService } from './project-group.service';

describe('ProjectGroupService', () => {
  let service: ProjectGroupService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [{
        provide: ProjectGroupService,
        useValue: {},
      }],
    }).compile();

    service = module.get<ProjectGroupService>(ProjectGroupService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
