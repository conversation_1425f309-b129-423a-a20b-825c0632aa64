import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, Tree, TreeChildren, TreeParent } from 'typeorm';

@ObjectType()
@Entity('project_groups')
@Tree('materialized-path')
export class ProjectGroupEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectGroupId: number;

  /* -------------------------------- Relations ------------------------------- */

  @OneToMany(() => TaskEntity, tasks => tasks.group)
  @JoinColumn({ name: 'taskId' })
  tasks: TaskEntity[];

  @ManyToOne(() => ProjectEntity, project => project.groups)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @TreeChildren({ cascade: ['soft-remove', 'remove', 'recover'] })
  children: ProjectGroupEntity[];

  @TreeParent({ onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectGroupId' })
  parent: ProjectGroupEntity;
}
