import { Body, Controller, Post, Get, Patch, Delete } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ProjectGroupService } from './project-group.service';
import { BadRequestException, Query } from '@nestjs/common';
import * as Dto from './dto/project-group.api.dto';
import { GetAuthData, GetProjectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { AuthData } from '@types';
import { getRepository } from 'typeorm';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';

@ApiTags('Project Group API')
@Controller('project-group')
export class ProjectGroupController {
  constructor(private groupService: ProjectGroupService) {}

  @UseApiUserAuthGuard()
  @Post('create')
  async createProjectGroup(
    @Body() body: Dto.ProjectGroupInputDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ): Promise<{ id: number }> {
    if (!body.title) throw new BadRequestException('No Group name');
    if (!projectId) throw new BadRequestException('Project Id not found');

    return await this.groupService.createProjectGroup(body, user, projectId);
  }

  @UseApiUserAuthGuard()
  @Get('options')
  async allProjectGroupsOptions(@GetAuthData() user: AuthData, @GetProjectData() projectId: number, @Query() query) {
    if (!projectId) throw new BadRequestException('Project Id not found');

    return await this.groupService.allProjectGroupsOptions(projectId);
  }

  @UseApiUserAuthGuard()
  @Get('all')
  async allProjectGroups(@GetAuthData() user: AuthData, @GetProjectData() projectId: number, @Query() query) {
    if (!projectId) throw new BadRequestException('Project Id not found');

    return await this.groupService.allProjectGroups(projectId);
  }

  @UseApiUserAuthGuard()
  @Post('searchProjectGroups')
  async searchProjectGroups(
    @Body() body: Dto.ProjectGroupSearchDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number,
    @Query() query
  ) {
    if (!projectId) throw new BadRequestException('Project Id not found');
    return await this.groupService.searchProjectGroups(body.title, projectId);
  }

  @UseApiUserAuthGuard()
  @Patch('update')
  async updateProjectGroup(
    @Body() body: Dto.ProjectGroupUpdateDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ): Promise<{ message: string }> {
    if (!body.title) throw new BadRequestException('Payload not found');
    if (!projectId) throw new BadRequestException('Project Id not found');

    return await this.groupService.updateProjectGroup(body, projectId);
  }

  @UseApiUserAuthGuard()
  @Delete('delete')
  async deleteProjectGroup(
    @Body() body: Dto.ProjectGroupDeleteDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ): Promise<any> {
    if (!body.id) throw new BadRequestException('Payload not found');
    if (!projectId) throw new BadRequestException('Project Id not found');

    return await this.groupService.deleteProjectGroup(body, user, projectId);
  }

  @UseApiUserAuthGuard()
  @Get('groups-documents-count')
  async getGroupsDocumentsCount(@GetProjectData() projectId: number, @GetAuthData() user: AuthData) {
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId: user.id, projectId });
    if (!projectUser) throw new BadRequestException('You are not involved in this project');

    const workspaceDocumentCounts = await this.groupService.getGroupsTaskCount(projectId);
    return workspaceDocumentCounts;
  }

  @UseApiUserAuthGuard()
  @Get('get-total-documents')
  async getTotalOfDocuments(@GetProjectData() projectId: number, @GetAuthData() user: AuthData) {
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId: user.id, projectId });
    if (!projectUser) throw new BadRequestException('You are not involved in this project');

    const totalCount = await this.groupService.getTotalOfTask(projectId);
    return totalCount;
  }

  @Get('task-group-sub-group')
  async setWorkspaceSubGroup(@Query('subGroup') subGroup: boolean) {
    const siteDiaryGroup = await this.groupService.setSubgroup(subGroup);
    return siteDiaryGroup;
  }
}
