import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectGroupEntity } from './entity/project-group.entity';
import { ProjectGroupService } from './project-group.service';
import { ProjectGroupResolver } from './project-group.resolver';
import { ProjectGroupController } from './project-group.controller';
import { CreateProjectGroupInputDTO, ProjectGroupDto, UpdateProjectGroupInputDTO } from './dto/project-group.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectGroupEntity, TaskEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: ProjectGroupService,
          DTOClass: ProjectGroupDto,
          EntityClass: ProjectGroupEntity,
          CreateDTOClass: CreateProjectGroupInputDTO,
          UpdateDTOClass: UpdateProjectGroupInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ProjectGroupService]
    }),
    IntegrationModule
  ],
  providers: [ProjectGroupService, ProjectGroupResolver, ProjectGroupEntity, TaskEntity],
  controllers: [ProjectGroupController]
})
export class ProjectGroupModule {}
