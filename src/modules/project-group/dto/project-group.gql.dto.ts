import { defaultQueryOptions } from '@constants';
import { TaskDto } from '@modules/task/dto/task.gql.dto';
import { ProjectDto } from '@modules/project/dto/project.gql.dto';
import { FilterableRelation, QueryArgsType, QueryOptions, UnPagedRelation } from '@nestjs-query/query-graphql';
import { Field, ObjectType, PartialType, ID, InputType, ArgsType } from '@nestjs/graphql';
import { ProjectGroupEntity } from '../entity/project-group.entity';
import { relationOption } from '@constants/query.constant';
import { IDField } from '@nestjs-query/query-graphql';
import { SourceType } from '@constants';


@ObjectType('ProjectGroup')
@UnPagedRelation('tasks', () => TaskDto, relationOption(true))
@FilterableRelation('parent', () => ProjectGroupDto, relationOption(true))
@UnPagedRelation('children', () => ProjectGroupDto, relationOption(true))
@FilterableRelation('project', () => ProjectDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class ProjectGroupDto extends ProjectGroupEntity {
  totalCount?: number;
  openCount?: number;
  inProgressCount?: number;
  holdCount?: number;
  closedCount?: number;


  //? for offline sync
  remoteId?: number;
}

@ObjectType('ProjectGroupWithChildren')
@QueryOptions({ ...defaultQueryOptions })
export class ProjectGroupWithChildrenDto extends ProjectGroupDto {
  children: ProjectGroupDto[];
}

@ArgsType()
export class ProjectGroupQuery extends QueryArgsType(ProjectGroupWithChildrenDto) {}
export const ProjectGroupConnection = ProjectGroupQuery.ConnectionType;

@InputType()
export class GetProjectGroupDTO {
  @IDField(() => ID) projectId?: number;
}

@InputType()
export class CreateProjectGroupInputDTO {
  @Field(() => ID) projectId: number;
  @Field() title: string;
  @Field({ nullable: true }) projectGroupId?: number;
  @Field({ nullable: true }) localId?: string;

  //? For offline sync use
  @Field({ nullable: true }) updatedBy?: string;
  @Field({ nullable: true }) _changed?: string;
  @Field({ nullable: true }) updated_at?: number;
  @Field({ nullable: true }) created_at?: number;
  @Field({ nullable: true }) remoteId?: number;
  @Field({ nullable: true }) localGroupId?: string;
  @Field({ nullable: true }) recordSource?: SourceType;
}

@InputType()
export class UpdateProjectGroupInputDTO extends PartialType(CreateProjectGroupInputDTO) {}
