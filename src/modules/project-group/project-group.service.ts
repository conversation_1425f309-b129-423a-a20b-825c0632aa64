import { Injectable, BadRequestException, ConflictException } from '@nestjs/common';
import { ProjectGroupEntity } from './entity/project-group.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { getRepository, Repository, Like, Not } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { TaskStatusType } from '@constants';
import { ProjectGroupQuery } from './dto/project-group.gql.dto';
import { getErrorMessage } from '@common/error';

@Injectable()
export class ProjectGroupService extends TypeOrmQueryService<ProjectGroupEntity> {
  constructor(
    @InjectRepository(ProjectGroupEntity)
    public repo: Repository<ProjectGroupEntity>,
    @InjectRepository(TaskEntity)
    public taskRepo: Repository<TaskEntity>
  ) {
    super(repo, { useSoftDelete: true });
  }

  async createProjectGroup(data: any, user: any, projectId: any) {
    const { projectGroupId } = data;
    try {
      const projectGroup = await getRepository(ProjectGroupEntity)
        .createQueryBuilder('projectGroup')
        .where('BINARY projectGroup.title = :title', { title: data.title })
        .andWhere('projectGroup.projectId = :projectId', { projectId: +projectId })
      
      if (projectGroupId) { 
        projectGroup.andWhere('projectGroup.projectGroupId = :projectGroupId', { projectGroupId: +projectGroupId })
      }

      const exist = await projectGroup.getOne();
      
      if (exist) throw new ConflictException(`${projectGroupId ? 'Sub ' : ''}Group with same name already exist`);

      let model;

      // TODO: DANIAL - CONSIDER USING THIS RATHER THAN IF ELSE -> ...(projectGroupId ? {projectGroupId}: {})
      if (projectGroupId) {
        model = this.repo.create({
          projectId: projectId,
          projectGroupId,
          createdBy: user.id,
          title: data.title
        });
      } else {
        model = this.repo.create({
          projectId: projectId,
          createdBy: user.id,
          title: data.title
        });
      }

      const res = await this.repo.save(model);

      return { id: res.id };
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'createProjectGroup');
    }
  }

  async allProjectGroupsOptions(projectId: any) {
    return await getRepository(ProjectGroupEntity).find({ projectId: +projectId });
  }

  async allProjectGroups(projectId: any) {
    return await getRepository(ProjectGroupEntity).find({
      relations: ['tasks'],
      where: { projectId: +projectId }
    });
  }

  async updateProjectGroup(data: any, projectId: any) {
    try {
      const exist = await getRepository(ProjectGroupEntity).findOne({ id: data.id });
      const duplicate = await getRepository(ProjectGroupEntity).findOne({
        title: data.title,
        projectId: +projectId
      });

      if (!exist) throw new BadRequestException('Group not found');
      if (duplicate) throw new ConflictException('Group with same name already exist');

      const model = await this.repo.update(data.id, {
        title: data.title
      });

      if (model) return { status: true, message: 'Updated group successfully', data: model };

      throw new BadRequestException('Failed to update group');
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'updateProjectGroup');
    }
  }

  async deleteProjectGroup(data: any, user: any, projectId) {
    try {
      const exist = await getRepository(ProjectGroupEntity).findOne({
        id: data.id,
        projectId,
        title: Not('Ungroup Tasks')
      });

      if (!exist) throw new BadRequestException('Group not found');

      const ungroup = await getRepository(ProjectGroupEntity).findOne({ title: 'Ungroup Tasks', projectId });
      const tasks = await getRepository(TaskEntity).find({ groupId: exist.id, projectId });

      const taskIds = tasks.map(task => task.id);

      if (taskIds.length > 0) {
        await this.taskRepo.update(taskIds, {
          groupId: ungroup.id
        });
      }

      return await this.repo.softDelete({ id: exist.id, projectId });
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'deleteProjectGroup');
    }
  }

  // searching project groups by title
  async searchProjectGroups(title: any, projectId: any) {
    try {
      // return all project groups if title is empty
      if (_.isEmpty(title))
        return await getRepository(ProjectGroupEntity)
          .createQueryBuilder('group')
          .leftJoinAndSelect('group.tasks', 'task')
          .where('group.projectId = :projectId', { projectId: +projectId })
          .orderBy('CASE WHEN group.title = :title THEN 0 ELSE 1 END', 'ASC')
          .addOrderBy('group.createdAt', 'DESC')
          .setParameter('title', 'Ungroup Tasks')
          .getMany();

      return await getRepository(ProjectGroupEntity).find({
        relations: ['tasks'],
        where: { projectId: +projectId, title: Like(`%${title}%`) }
      });
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'searchProjectGroups');
    }
  }

  async getTotalOfTask(projectId: number) {
    try {
      const queryBuilder = getRepository(TaskEntity)
        .createQueryBuilder('task')
        .where('(task.projectId = :projectId) AND (task.groupId IS NOT NULL)', { projectId });

      const totalCount = await queryBuilder.getCount();

      return totalCount;
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'getTotalOfTask');
    }
  }

  async getGroupsTaskCount(projectId: number) {
    try {
      const taskGroups = await this.repo.find({
        where: { projectId },
        order: { createdAt: 'DESC' }
      });

      // Count of the "ungroup documents"'s documents
      const ungroup = taskGroups.filter(taskGroup => {
        return taskGroup.title === 'Ungroup Tasks';
      });

      const countOfUngroup = await this.countForProjectGroup(ungroup);

      // Count of the other custom groups' documents
      const customGroups = taskGroups.filter(projectGroup => {
        return projectGroup.title !== 'Ungroup Tasks';
      });
      const countOfCustomGroups = await this.countForProjectGroup(customGroups);

      return [...countOfUngroup, ...countOfCustomGroups];
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'getGroupsTaskCount');
    }
  }

  private async countForProjectGroup(taskGroups: ProjectGroupEntity[]) {
    try {
      return await Promise.all(
        taskGroups.map(async taskGroup => {
          const taskGroupId = taskGroup.id;
          const taskGroupName = taskGroup.title;
          const totalCount = await getRepository(TaskEntity).count({
            groupId: taskGroupId
          });
          const openCount = await getRepository(TaskEntity).count({
            groupId: taskGroup.id,
            status: TaskStatusType.Open
          });
          const inProgressCount = await getRepository(TaskEntity).count({
            groupId: taskGroup.id,
            status: TaskStatusType.InProgress
          });
          const holdCount = await getRepository(TaskEntity).count({
            groupId: taskGroup.id,
            status: TaskStatusType.Hold
          });
          const closedCount = await getRepository(TaskEntity).count({
            groupId: taskGroup.id,
            status: TaskStatusType.Completed
          });

          return {
            taskGroupId,
            taskGroupName,
            totalCount,
            openCount,
            inProgressCount,
            holdCount,
            closedCount
          };
        })
      );
    } catch (e) {
      getErrorMessage(e, 'ProjectGroupService', 'countForWorkspaceDocuments');
    }
  }

  async getProjectGroups(projectId: number, query: ProjectGroupQuery) {
    try {
      const groups = this.queryGroups(projectId, query?.filter?.title?.like);

      groups?.skip(query?.paging?.offset).take(query?.paging?.limit);
      const response = await groups.getMany();

      return await this.calculateParentCounts(response);
    } catch (e) {
      getErrorMessage(e, 'project group service', 'getProjectGroups');
    }
  }

  async getCountProjectGroup(projectId: number, query: any) {
    try {
      const groups = this.queryGroups(projectId, query?.name?.like);
      const totalCount = await groups.getCount();

      return totalCount;
    } catch (error) {
      getErrorMessage(error, 'ProjectGroupService', 'getCountProjectGroup');
    }
  }

  queryGroups(projectId: number, filterName: string) {
    const groups = getRepository(ProjectGroupEntity)
      .createQueryBuilder('group')
      .orderBy('group.title', 'ASC')
      .leftJoinAndSelect('group.tasks', 'tasks')
      .where('(group.projectId = :projectId)', { projectId })
      .andWhere('(group.ProjectGroupId IS NULL)');

    groups.addSelect(
      `CASE 
    WHEN group.title = 'Ungroup Tasks' THEN 0 
    ELSE 2 
       END`,
      'custom_order'
    );

    groups.orderBy({
      custom_order: 'ASC',
      'group.title': 'ASC'
    });

    groups
      ?.leftJoinAndSelect('group.children', 'children')
      .leftJoinAndSelect('children.tasks', 'childrenTasks', 'childrenTasks.projectId = :projectId', { projectId });

    if (filterName) {
      const title = filterName;
      if (title !== null) {
        groups.andWhere(
          '(group.title LIKE :title) OR (children.title LIKE :title) AND (group.projectId = :projectId) AND (children.projectId = :projectId)',
          {
            title: `%${title}%`,
            projectId
          }
        );
      }
    }

    return groups;
  }

  mapGroupWithStatus(group) {
    const openCount = group.tasks.filter(task => task.status === TaskStatusType.Open && task.dueDate !== null).length;
    const inProgressCount = group.tasks.filter(
      task => task.status === TaskStatusType.InProgress && task.dueDate !== null
    ).length;
    const holdCount = group.tasks.filter(task => task.status === TaskStatusType.Hold && task.dueDate !== null).length;
    const closedCount = group.tasks.filter(
      task => task.status === TaskStatusType.Completed && task.dueDate !== null
    ).length;

    const totalCount = openCount + inProgressCount + holdCount + closedCount;

    return {
      ...group,
      totalCount,
      openCount,
      inProgressCount,
      holdCount,
      closedCount
    };
  }

  async calculateParentCounts(groups) {
    try {
      // calculate the counts for the children groups and total count for the parent group
      const groupsWithAggregatedCounts = await Promise.all(
        groups.map(async group => {
          const children = group.children;

          if (group.title === 'Ungroup Tasks') {
            //? add empty children array to the ungroup documents group
            group.children = [];
            return this.mapGroupWithStatus(group);
          }
          //? if no children, return the empty children array
          if (!children?.length) {
            return {
              ...group,
              children: [],
              totalCount: 0,
              openCount: 0,
              inProgressCount: 0,

              closedCount: 0
            };
          }

          const childrenWithStatus = children.map(child => this.mapGroupWithStatus(child));

          const totalCount = childrenWithStatus.reduce((acc, curr) => acc + curr.totalCount, 0);
          const openCount = childrenWithStatus.reduce((acc, curr) => acc + curr.openCount, 0);
          const inProgressCount = childrenWithStatus.reduce((acc, curr) => acc + curr.inProgressCount, 0);
          const holdCount = childrenWithStatus.reduce((acc, curr) => acc + curr.holdCount, 0);
          const closedCount = childrenWithStatus.reduce((acc, curr) => acc + curr.closedCount, 0);

          return {
            ...group,
            children: childrenWithStatus,
            totalCount,
            openCount,
            inProgressCount,
            holdCount,
            closedCount
          };
        })
      );

      return groupsWithAggregatedCounts;
    } catch (e) {
      getErrorMessage(e, 'project group service', 'calculateParentCounts');
    }
  }

  async fetchFixturesGroups(projectId: number) {
    try {
      return await getRepository(ProjectGroupEntity)
        .createQueryBuilder('group')
        .where('((group.projectId = :projectId) AND (group.title = :ungroupTasks))', {
          projectId: projectId,
          unGroupTasks: 'Ungroup Tasks'
        })
        .leftJoin('group.tasks', 'tasks')
        .addSelect(['tasks.status'])
        .getMany();
    } catch (error) {
      getErrorMessage(error, 'project group service', 'fetchFixturesGroups');
    }
  }

  async setSubgroup(subGroup: boolean) {
    try {
      const groups = await this.repo.find();
      const groupsCount = groups.length;
      for (const group of groups) {
        if (group.title !== 'Ungroup Tasks') {
          const child = group.title + ' - child';
          const taskGroup = this.repo.create({
            title: child,
            projectId: group.projectId,
            createdBy: group.createdBy,
            projectGroupId: group.id
          });
          await this.repo.save(taskGroup);
        }
      }
      return { groupsCount };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
