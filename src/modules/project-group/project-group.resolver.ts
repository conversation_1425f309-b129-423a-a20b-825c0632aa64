import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Query, Args } from '@nestjs/graphql';
import { ProjectGroupConnection, ProjectGroupDto, ProjectGroupQuery } from './dto/project-group.gql.dto';
import { ProjectGroupService } from './project-group.service';
import { GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { Filter } from '@nestjs-query/core';

@UseGuards(GqlAuthGuard)
@Resolver()
export class ProjectGroupResolver {
  constructor(private groupService: ProjectGroupService) {}

  @Query(() => ProjectGroupConnection)
  async getProjectGroups(@Args() query: ProjectGroupQuery, @GqlGetGqlProjectData() projectId: number) {
    const filter: Filter<ProjectGroupDto> = {
      ...query.filter
    };

    const response = (await ProjectGroupConnection.createFromPromise(
      async q => await this.groupService.getProjectGroups(projectId, q),
      { ...query, ...{ filter } }
    )) as any;

    const totalCount = await this.groupService.getCountProjectGroup(projectId, filter);

    return {
      ...response,
      totalCount
    };
  }
}
