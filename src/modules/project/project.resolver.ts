import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, ResolveField, Resolver, Parent } from '@nestjs/graphql';
import { AuthData } from '@types';
import { CreateProjectInputDTO, DeleteOneProjectInput, ProjectDto } from './dto/project.gql.dto';
import { ProjectService } from './project.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => ProjectDto)
export class ProjectResolver {
  constructor(private readonly projectService: ProjectService) {}

  // Create New Project
  @Mutation(() => ProjectDto)
  async createNewProject(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: CreateProjectInputDTO
  ): Promise<any> {
    return await this.projectService.createNewProject(input, user.id);
  }

  // Delete Project
  @Mutation(() => ProjectDto)
  async deleteOneProject(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: DeleteOneProjectInput
  ): Promise<any> {
    return await this.projectService.deleteOneProject(input.id, user.id);
  }

  @ResolveField('fileUrlProgress', () => String)
  async fileUrlProgress(@Parent() parent: ProjectDto) {
    return await this.projectService.getPresignedUrl(parent, 'fileProgressKey');
  }

  @ResolveField('ganttCharlUrl', () => String)
  async ganttCharlUrl(@Parent() parent: ProjectDto) {
    return await this.projectService.getPresignedUrl(parent, 'ganttChartKey');
  }

  @ResolveField('fileUrlProgressFinance', () => String)
  async fileUrlProgressFinance(@Parent() parent: ProjectDto) {
    return await this.projectService.getPresignedUrl(parent, 'fileProgressFinanceKey');
  }
}
