import { AuditLogActionType, AuditLogModuleType, ProjectUserRoleType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { FileService } from '@modules/integration/file.service';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { BadRequestException, Injectable } from '@nestjs/common';
import { FileUpload } from 'graphql-upload';
import _ from 'lodash';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, RemoveEvent, UpdateEvent } from 'typeorm';
import { ProjectEntity } from './entity/project.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { getErrorMessage } from '@common/error';
import { CsvToJsonService } from '@modules/conversion/csv-to-json.service';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';

@Injectable()
@EventSubscriber()
export class ProjectSubscriber implements EntitySubscriberInterface<ProjectEntity> {
  constructor(
    connection: Connection,
    private fileService: FileService,
    private csvToJsonService: CsvToJsonService
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectEntity;
  }

  async beforeUpdate(event: UpdateEvent<ProjectEntity>) {
    try {
      const { entity } = event;      

      if (entity.fileUrlProgress) {
        const file: any = entity.fileUrlProgress;
        if (file && _.isObject(file)) {
          const folder = 'Project-SCurve';
          const { key } = await this.fileService.uploadGqlFile(file as FileUpload, folder);
          entity.fileProgressKey = key;
          const data = await this.csvToJsonService.parseScurveFile(file as FileUpload, 'physical');
          entity.fileUrlProgressJson = data;
        }
      }

      if (entity.fileUrlProgressFinance) {
        const fileFinance: any = entity.fileUrlProgressFinance;

        if (fileFinance && _.isObject(fileFinance)) {
          const folder = 'Project-SCurve';
          const { key } = await this.fileService.uploadGqlFile(fileFinance as FileUpload, folder);
          entity.fileProgressFinanceKey = key;
          const data = await this.csvToJsonService.parseScurveFile(fileFinance as FileUpload, 'financial');
          entity.fileUrlProgressFinanceJson = data;
        }
      }

      if (entity.ganttChartUrl) {
        const fileGantt: any = entity.ganttChartUrl;

        if (fileGantt && _.isObject(fileGantt)) {
          const folder = 'Project-Gantt-Charts';
          const { key } = await this.fileService.uploadGqlFile(fileGantt as FileUpload, folder);
          entity.ganttChartKey = key;
        }
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectSubscriber', 'beforeUpdate');
    }
  }

  async beforeInsert(event: InsertEvent<ProjectEntity>): Promise<any> {
    const { entity } = event;        

    //? check if title is empty or empty string, then throw error
    if (!entity.title || entity.title?.trim() === '') {
      throw new BadRequestException('Cannot create project without title');
    }

    // make project email based on project title
    const emailDomain = process.env.MAILGUN_CORRESPONDENCE_DOMAIN;
    const projectEmailPrefix = entity.title
      .trim()
      .split(' ')
      .slice(0, 2)
      .join('')
      .toLowerCase();
    
    entity.projectEmail = `${projectEmailPrefix}@${emailDomain}`;
  }

  async afterInsert(event: InsertEvent<ProjectEntity>) {
    try {
      const { entity } = event;
      
      const projectOwner = event.manager.getRepository(ProjectUserEntity).create({
        userId: entity.userId,
        projectId: entity.id,
        role: ProjectUserRoleType.ProjectOwner,
        addedBy: entity.userId
      });  
      await event.manager.getRepository(ProjectUserEntity).save(projectOwner);  

      // Fetch user along with their company
      const user = await event.manager.getRepository(UserEntity).findOne({
        where: { id: entity.userId },
        relations: ['company'],
      });
      const contactEmail = event.manager.getRepository(ContactsEmailEntity).create({
        name: user.name,        
        position: user.position,
        company: user.company.name,
        projectId: projectOwner.projectId,
        userId: user.id,
        email: user.email,
        avatarKey: user.avatarKey
      })
      await event.manager.getRepository(ContactsEmailEntity).save(contactEmail);

      //* Currently disabled creating folder correspondences In and Out when create a new project
      // const correspondenceIn = event.manager.getRepository(ProjectDocumentEntity).create({
      //   projectId: entity.id,
      //   addedBy: entity.userId,
      //   name: CorrespondenceType.CorrespondenceIn,
      //   fileSystemType: FileSystemType.Folder,
      //   type: 'folder',
      //   category: CategoryType.Correspondence,
      // });
      // await event.manager.getRepository(ProjectDocumentEntity).save(correspondenceIn);

      // const correspondenceOut = event.manager.getRepository(ProjectDocumentEntity).create({
      //   projectId: entity.id,
      //   addedBy: entity.userId,
      //   name: CorrespondenceType.CorrespondenceOut,
      //   fileSystemType: FileSystemType.Folder,
      //   type: 'folder',
      //   category: CategoryType.Correspondence,
      // });
      // await event.manager.getRepository(ProjectDocumentEntity).save(correspondenceOut);

      const workspaceGroup = event.manager.getRepository(WorkspaceGroupEntity).create({
        projectId: entity.id,
        name: 'Ungroup Documents',
        code: 'UD'
      });
      await event.manager.getRepository(WorkspaceGroupEntity).save(workspaceGroup);

      const workspaceGroup2 = event.manager.getRepository(WorkspaceGroupEntity).create({
        projectId: entity.id,
        name: 'Site Diary',
        code: 'SD'
      });
      await event.manager.getRepository(WorkspaceGroupEntity).save(workspaceGroup2);

      const technicalQuery = event.manager.getRepository(WorkspaceGroupEntity).create({
        projectId: entity.id,
        name: 'Request For Information',
        code: 'TQ'
      });
      await event.manager.getRepository(WorkspaceGroupEntity).save(technicalQuery);

      const ncp = event.manager.getRepository(WorkspaceGroupEntity).create({
        projectId: entity.id,
        name: 'Non Conformance Report',
        code: 'NCR'
      });
      await event.manager.getRepository(WorkspaceGroupEntity).save(ncp);

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.userId,
        projectId: entity.id,
        module: AuditLogModuleType.Project,
        action: AuditLogActionType.Create
      });
      await event.manager.getRepository(AuditLogEntity).save(auditLog);

      const projectGroup = event.manager.getRepository(ProjectGroupEntity).create({
        projectId: entity.id,
        title: 'Ungroup Tasks'
      });
      await event.manager.getRepository(ProjectGroupEntity).save(projectGroup);
    } catch (e) {
      getErrorMessage(e, 'ProjectSubscriber', 'afterInsert');
    }
  }

  async afterUpdate(event: UpdateEvent<ProjectEntity>) {
    
    try {
      const notGraphs = !!event.updatedColumns.find(
        column =>
          column.propertyName !== 'fileUrlProgress' &&
          column.propertyName !== 'fileUrlProgressFinance' &&
          column.propertyName !== 'ganttChartUrl'
      );

      if (notGraphs) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: event.entity.userId });
        const msg = user.name + ' updated the project details';

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: event.entity.userId,
          projectId: event.entity.id,
          // taskId: event.entity.id,
          resourceId: event.entity.id,
          module: AuditLogModuleType.Project,
          action: AuditLogActionType.Update,
          content: msg
        });
        await event.manager.getRepository(AuditLogEntity).save(auditLog);
      } else {
        if (event.updatedColumns.find(column => column.propertyName == 'fileUrlProgress')) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: event.entity.userId });
          const msg = user.name + ' updated Physical Curve';

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: event.entity.userId,
            projectId: event.entity.id,
            // taskId: event.entity.id,
            resourceId: event.entity.id,
            module: AuditLogModuleType.SCurve,
            action: AuditLogActionType.Create,
            content: msg
          });
          await event.manager.getRepository(AuditLogEntity).save(auditLog);
        } else if (event.updatedColumns.find(column => column.propertyName == 'fileUrlProgressFinance')) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: event.entity.userId });
          const msg = user.name + ' updated Financial Curve';

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: event.entity.userId,
            projectId: event.entity.id,
            // taskId: event.entity.id,
            resourceId: event.entity.id,
            module: AuditLogModuleType.SCurve,
            action: AuditLogActionType.Create,
            content: msg
          });
          await event.manager.getRepository(AuditLogEntity).save(auditLog);
        } else if (event.updatedColumns.find(column => column.propertyName == 'ganttChartUrl')) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: event.entity.userId });
          const msg = user.name + ' imported a new Schedule file';

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: event.entity.userId,
            projectId: event.entity.id,
            // taskId: event.entity.id,
            resourceId: event.entity.id,
            module: AuditLogModuleType.Schedule,
            action: AuditLogActionType.Add,
            content: msg
          });
          await event.manager.getRepository(AuditLogEntity).save(auditLog);
        }
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectSubscriber', 'afterUpdate');
    }
  }

  async afterSoftRemove(event: RemoveEvent<ProjectEntity>) {
    try {
      const { entity } = event;      

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.userId,
        projectId: entity.id,
        module: AuditLogModuleType.Project,
        action: AuditLogActionType.Delete
      });
      await event.manager.getRepository(AuditLogEntity).save(auditLog);
    } catch (e) {
      getErrorMessage(e, 'ProjectSubscriber', 'afterSoftRemove');
    }
  }
}
