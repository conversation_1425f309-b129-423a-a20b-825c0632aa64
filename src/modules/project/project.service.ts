import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ProjectEntity } from './entity/project.entity';
import { ForbiddenException, Injectable, UnauthorizedException } from '@nestjs/common';
import { CreateProjectInputDTO } from './dto/project.gql.dto';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectUserRoleType } from '@constants';
import moment from 'moment';
import { getErrorMessage } from '@common/error';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class ProjectService extends TypeOrmQueryService<ProjectEntity> {
  constructor(
    @InjectRepository(ProjectEntity)
    private projectRepo: Repository<ProjectEntity>,
    private tMOneService: TMOneService
  ) {
    // pass the use soft delete option to the service.
    super(projectRepo, { useSoftDelete: true });
  }

  async createNewProject(input: CreateProjectInputDTO, userId: number) {
    try {
      const currentUser = await getRepository(UserEntity).findOneOrFail(
        { id: userId },
        {
          relations: [
            'company',
            'company.companySubscriptions',
            'company.projects',
            'company.companySubscriptions.subscriptionPackage'
          ]
        }
      );

      if (!currentUser?.company?.companySubscriptions || currentUser.company.companySubscriptions.length === 0) {
        throw new UnauthorizedException('Sorry you do not have an active subscription');
      }

      // Find the most recent subscription
      const sortedSubscriptions = currentUser.company.companySubscriptions.sort((a, b) =>
        new Date(b.subscriptionEndDate).getTime() - new Date(a.subscriptionEndDate).getTime()
      );

      const latestSubscription = sortedSubscriptions[0];

      // Check if it's a free trial subscription (both conditions must be met)
      const isFreeTrialPayment = latestSubscription.paymentMethod?.toLowerCase()?.trim() === 'free trial';
      const isFreeTrialPackage = latestSubscription.subscriptionPackage?.title?.toLowerCase()?.trim()?.includes('free trial');
      const isFreeTrial = isFreeTrialPayment && isFreeTrialPackage;

      // Apply different grace periods based on subscription type
      // For free trials: No grace period
      // For regular subscriptions: 10-day grace period
      const gracePeriodDays = isFreeTrial ? 0 : 10;

      // Check if subscription is still valid (including grace period if applicable)
      const isValid = latestSubscription.subscriptionEndDate > moment().subtract(gracePeriodDays, "days").toDate();

      if (!isValid) {
        throw new UnauthorizedException('Sorry you do not have an active subscription');
      }

      const subscription = latestSubscription;

      const subscriptionPackage = subscription.subscriptionPackage;

      const totalProjects = currentUser?.company?.projects?.length ?? 0;

      if (totalProjects >= subscriptionPackage.totalProjects) {
        throw new UnauthorizedException(
          'Sorry you have reached your maximum number of ' + subscriptionPackage.totalProjects + ' projects'
        );
      }

      const newProject = this.projectRepo.create({ userId, companyId: currentUser.companyId, ...input });

      return await this.projectRepo.save(newProject);
    } catch (e) {
      getErrorMessage(e, 'ProjectService', 'createNewProject');
    }
  }

  async deleteOneProject(id: number, userId: number) {
    try {
      const project = await this.projectRepo.findOne({ id });
      const projectUser = await getRepository(ProjectUserEntity).findOne({ projectId: id, userId });

      if (projectUser.role !== ProjectUserRoleType.ProjectOwner)
        throw new ForbiddenException('Insufficient Role permission for this action.');

      return await this.projectRepo.softRemove(project);
    } catch (e) {
      getErrorMessage(e, 'ProjectService', 'deleteOneProject');
    }
  }

  async getPresignedUrl(project: ProjectEntity, type: 'fileProgressKey' | 'ganttChartKey' | 'fileProgressFinanceKey' ) {
    try {
      const keyAndFallback = { fileProgressKey: 'fileUrlProgress', ganttChartKey: 'ganttCharUrl', fileProgressFinanceKey: 'fileUrlProgressFinance' };
      let key = project[type];
      // fallback if key is missing
      if (!key){
        const fileName = project[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tMOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
