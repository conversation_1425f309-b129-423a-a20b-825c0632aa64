import { defaultQueryOptions, ProjectStatusType, SourceType } from '@constants';
import { ProjectUserDto } from '@modules/project-user/dto/project-user.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { ProjectGroupDto } from '@modules/project-group/dto/project-group.gql.dto';
import {
  Authorize,
  FilterableOffsetConnection,
  FilterableRelation,
  IDField,
  OffsetConnection,
  QueryOptions,
  Relation
} from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType, Field } from '@nestjs/graphql';
import { ProjectEntity } from '../entity/project.entity';
import { ProjectAuthorizer } from '../project.authorizer';
import { relationOption } from '@constants/query.constant';
import { TaskDto } from '@modules/task/dto/task.gql.dto';
import { ProjectCarouselDto } from '@modules/project-carousel/dto/project-carousel.gql.dto';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { CompanyDto } from '@modules/company/dto/company.gql.dto';

@ObjectType('Project')
@Authorize(ProjectAuthorizer)
@OffsetConnection('tasks', () => TaskDto, relationOption(true))
@OffsetConnection('groups', () => ProjectGroupDto, relationOption(true))
@Relation('company', () => CompanyDto, relationOption(true))
@FilterableRelation('owner', () => UserDto, relationOption(true))
@FilterableOffsetConnection('projectUsers', () => ProjectUserDto, relationOption(true))
@FilterableOffsetConnection('carousels', () => ProjectCarouselDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class ProjectDto extends ProjectEntity {
  //? offline mode
  remoteId?: string;
}

@InputType()
export class CreateProjectInputDTO {
  companyId?: number;
  userId?: number;
  refNo?: string;
  title: string;
  description?: string;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  localProjectDocumentId?: string;
  fileKey?: string;
  fileName?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class UpdateProjectInputDTO extends PartialType(CreateProjectInputDTO) {
  status?: ProjectStatusType;
  managedBy?: string;
  deputySuperintendent?: string;
  client?: string;
  contractor?: string;
  startDate?: Date;
  completionDate?: Date;
  cnsConsultant?: string;
  mneConsultant?: string;
  qsConsultant?: string;
  environmentConsultant?: string;
  metTownId?: string;
  contractValue?: string;
  isRemindSiteDiary?: boolean;
  isNotify2DUploaded?: boolean;
  @Field(() => GraphQLUpload) fileUrlProgress?: FileUpload;
  @Field(() => GraphQLUpload) fileUrlProgressFinance?: FileUpload;
  @Field(() => GraphQLUpload) ganttChartUrl?: FileUpload;
  issuesAndProblem?: string;
  solution?: string;
  fileUrlProgressJson?: string;
  fileUrlProgressFinanceJson?: string;

  //? for offline
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  updated_at?: Date;
  remoteId?: number;
  _changed?: string;
}

@InputType()
export class DeleteOneProjectInput {
  @IDField(() => ID) id: number;
}
