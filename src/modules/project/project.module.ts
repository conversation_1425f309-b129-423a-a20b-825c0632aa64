import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectEntity } from './entity/project.entity';
import { ProjectDto, CreateProjectInputDTO, UpdateProjectInputDTO } from './dto/project.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectRoles } from '@decorators/auth.decorator';
import { ProjectUserRoleType } from '@constants';
import { ProjectSubscriber } from './project.subscriber';
import { ProjectResolver } from './project.resolver';
import { ProjectService } from './project.service';
import { ProjectCarouselEntity } from '@modules/project-carousel/entity/project-carousel.entity';
import { IntegrationModule } from '@modules/integration/integration.module';
import { CompanySubscriptionGuard } from '@guards/companySubscription.guard';
import { CsvToJsonService } from '@modules/conversion/csv-to-json.service';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectEntity, ProjectCarouselEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: ProjectService,
          DTOClass: ProjectDto,
          EntityClass: ProjectEntity,
          CreateDTOClass: CreateProjectInputDTO,
          UpdateDTOClass: UpdateProjectInputDTO,
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: { disabled: true },
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard, CompanySubscriptionGuard]
          // decorators: [UseUploadFilePipe()],
        }
      ],
      services: [ProjectSubscriber, ProjectResolver, ProjectService, CsvToJsonService]
    })
  ]
})
export class ProjectModule {}
