import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { ProjectDto } from './dto/project.gql.dto';
import { RoleTypeEnum } from '@constants';
import { getRepository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';

@Injectable()
export class ProjectAuthorizer implements Authorizer<ProjectDto> {
  async authorize(context: any): Promise<Filter<ProjectDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const userId = context?.req?.user?.id;
    if (!userId) throw new UnauthorizedException('User not found');

    const user = await getRepository(UserEntity).findOneOrFail({ id: userId });

    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({
        projectUsers: {
          userId: { eq: context.req.user.id },
          project: {
            companyId: { eq: user.companyId }
          }
        }
      });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
