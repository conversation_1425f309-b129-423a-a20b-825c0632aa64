import { ProjectStatusType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { BaseEntity } from '@modules/base/base';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { ContactCompanyEntity } from '@modules/contact/entity/contact-company.entity';
import { ContactEntity } from '@modules/contact/entity/contact.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectInvitationEntity } from '@modules/project-invitation/entity/project-invitation.entity';
import { ProjectTeamEntity } from '@modules/project-team/entity/project-team.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectCarouselEntity } from '@modules/project-carousel/entity/project-carousel.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { GraphQLISODateTime, ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ScheduleLinksEntity } from '@modules/schedule-links/entity/schedule-links.entity';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';
import { ProjectOverviewEntity } from '@modules/project-overview/entity/project-overview.entity';
import { BimAssetEntity } from '@modules/bim-assets/entities/bim-asset.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';

@ObjectType()
@Entity('projects')
export class ProjectEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  companyId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  refNo: string;

  @FilterableField()
  @Column('varchar')
  title: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  description: string;

  @FilterableField(() => ProjectStatusType)
  @Column('enum', { enum: ProjectStatusType, default: ProjectStatusType.InProgress })
  status: ProjectStatusType;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  managedBy: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  deputySuperintendent: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  client: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  contractor: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  startDate: Date;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  completionDate: Date;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  cnsConsultant: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  mneConsultant: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  qsConsultant: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  environmentConsultant: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  fileUrlCover: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  metTownId: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileUrlProgress: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileUrlProgressFinance: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  contractValue: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  ganttChartUrl: string;

  @FilterableField()
  @Column('boolean', { default: false })
  isRemindSiteDiary: boolean;

  @FilterableField()
  @Column('boolean', { default: false })
  isNotify2DUploaded: boolean;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  issuesAndProblem: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  solution: string;

  @Column('text', { nullable: true })
  fileProgressKey: string;

  @Column('text', { nullable: true })
  ganttChartKey: string;

  @Column('text', { nullable: true })
  fileProgressFinanceKey: string;

  @FilterableField({ nullable: true })
  @Column('json', { nullable: true })
  fileUrlProgressJson: string;

  @FilterableField({ nullable: true })
  @Column('json', { nullable: true })
  fileUrlProgressFinanceJson: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: false })
  projectEmail: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => CompanyEntity, company => company.projects)
  @JoinColumn({ name: 'companyId' })
  company: CompanyEntity;

  @ManyToOne(() => UserEntity, user => user.projects)
  @JoinColumn({ name: 'userId' })
  owner: UserEntity;

  @OneToMany(() => AuditLogEntity, auditLogs => auditLogs.projects)
  auditLogs: AuditLogEntity[];

  @OneToMany(() => BimAssetEntity, bimAsset => bimAsset.projects)
  bimAssets: BimAssetEntity[];

  @OneToMany(() => ContactEntity, contacts => contacts.project)
  contacts: ContactEntity[];

  @OneToMany(() => ContactCompanyEntity, contactCompany => contactCompany.project)
  contactCompanies: ContactCompanyEntity[];

  @OneToMany(() => ProjectUserEntity, projectUsers => projectUsers.project)
  projectUsers: ProjectUserEntity[];

  @OneToMany(() => ProjectInvitationEntity, projectInvitations => projectInvitations.project)
  invitations: ProjectInvitationEntity[];

  @OneToMany(() => ProjectDocumentEntity, projectDocuments => projectDocuments.project)
  documents: ProjectDocumentEntity[];

  @OneToMany(() => ProjectTeamEntity, projectTeams => projectTeams.project)
  projectTeams: ProjectTeamEntity[];

  @OneToMany(() => TaskEntity, tasks => tasks.project)
  tasks: TaskEntity[];

  @OneToMany(() => ScheduleLinksEntity, scheduleslinks => scheduleslinks.project)
  links: ScheduleLinksEntity[];

  @OneToMany(() => ProjectGroupEntity, groups => groups.project)
  groups: ProjectGroupEntity[];

  @OneToMany(() => ProjectCarouselEntity, carousels => carousels.project)
  carousels: ProjectCarouselEntity[];

  @OneToMany(() => WorkspaceGroupEntity, workspaceGroups => workspaceGroups.project)
  workspaceGroups: WorkspaceGroupEntity[];

  @OneToMany(() => ProjectScheduleEntity, schedules => schedules.project)
  schedules: ProjectScheduleEntity[];

  @OneToMany(() => ProjectOverviewEntity, overviews => overviews.project)
  overviews: ProjectOverviewEntity[];

  @OneToMany(() => ContactsEmailEntity, userEmails => userEmails.project)
  userEmails: ContactsEmailEntity[];

  @OneToMany(() => ContactsEmailEntity, companyEmails => companyEmails.project)
  emails: ContactsEmailEntity[];
}
