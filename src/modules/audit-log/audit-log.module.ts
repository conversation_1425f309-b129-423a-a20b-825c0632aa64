import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { AuditLogEntity } from './entity/audit-log.entity';
import { AuditLogDto, CreateAuditLogInputDTO, UpdateAuditLogInputDTO } from './dto/audit-log.gql.dto';
import { AuditLogService } from './audit-log.service';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { AuditLogSubscriber } from './audit-log.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([AuditLogEntity])],
      resolvers: [
        {
          ServiceClass: AuditLogService,
          DTOClass: AuditLogDto,
          EntityClass: AuditLogEntity,
          CreateDTOClass: CreateAuditLogInputDTO,
          UpdateDTOClass: UpdateAuditLogInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [AuditLogService,AuditLogSubscriber]
    })
  ],
  controllers: []
})
export class AuditLogModule {}
