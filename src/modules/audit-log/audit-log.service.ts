import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLogEntity } from './entity/audit-log.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class AuditLogService extends TypeOrmQueryService<AuditLogEntity> {
  constructor(
    @InjectRepository(AuditLogEntity)
    private auditLogRepo: Repository<AuditLogEntity>
  ) {
    super(auditLogRepo, { useSoftDelete: true });
  }
}
