import { AuditLogActionType, AuditLogModuleType, defaultQueryOptions } from '@constants';
import { IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { AuditLogEntity } from '../entity/audit-log.entity';
import { relationOption } from '@constants/query.constant';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { ProjectDto } from '@modules/project/dto/project.gql.dto';
import { TaskDto } from '@modules/task/dto/task.gql.dto';

@ObjectType('AuditLog')
@Relation('user', () => UserDto, relationOption(true))
@Relation('projects', () => ProjectDto, relationOption(true))
@Relation('task', () => TaskDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class AuditLogDto extends AuditLogEntity {}

@InputType()
export class CreateAuditLogInputDTO {
  @IDField(() => ID) userId: number;
  @IDField(() => ID) projectId: number;
  @IDField(() => ID) resourceId: number;
  module: AuditLogModuleType;
  action: AuditLogActionType;
  content?: string;
  text?: string;
}
@InputType()
export class UpdateAuditLogInputDTO extends PartialType(CreateAuditLogInputDTO) {}
