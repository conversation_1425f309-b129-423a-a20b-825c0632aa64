import { BaseEntity } from '@modules/base/base';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { TaskEntity } from '@modules/task/entity/task.entity';

@ObjectType()
@Entity('audit_logs')
export class AuditLogEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  taskId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  resourceId: number;

  @FilterableField(() => AuditLogModuleType)
  @Column('enum', { enum: AuditLogModuleType })
  module: AuditLogModuleType;

  @FilterableField(() => AuditLogActionType)
  @Column('enum', { enum: AuditLogActionType })
  action: AuditLogActionType;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  content: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  text: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.auditLogs)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => ProjectEntity, project => project.auditLogs)
  @JoinColumn({ name: 'projectId' })
  projects: ProjectEntity;

  @ManyToOne(() => TaskEntity, task => task.auditLogs)
  @JoinColumn({ name: 'taskId' })
  task: TaskEntity;
}
