import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber } from 'typeorm';
import { AuditLogEntity } from './entity/audit-log.entity';

@Injectable()
@EventSubscriber()
export class AuditLogSubscriber implements EntitySubscriberInterface<AuditLogEntity> {
  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return AuditLogEntity;
  }

} 