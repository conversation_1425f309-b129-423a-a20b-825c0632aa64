import { BaseEntity } from "@modules/base/base";
import { ProjectEntity } from "@modules/project/entity/project.entity";
import { FilterableField, IDField } from "@nestjs-query/query-graphql";
import { ID, ObjectType } from "@nestjs/graphql";
import { Column, Entity, JoinColumn, ManyToOne } from "typeorm";

@ObjectType()
@Entity('project_overviews')
export class ProjectOverviewEntity extends BaseEntity {

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;
  
  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: false })
  key: string;

  @FilterableField()
  @Column('varchar')
  value: string;
  
  @FilterableField()
  @Column('int', { default: 0 })
  sequence: number;

  /* -------------------------------- Relations ------------------------------- */

  @ManyToOne(() => ProjectEntity, project => project.overviews)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;
}
