import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, BeforeUpdateOne, CreateOneInputType, FilterableOffsetConnection, QueryArgsType, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { ArgsType, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GqlContext } from '@types';
import { ProjectOverviewEntity } from '../entity/project-overview.entity';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { ProjectDto } from '@modules/project/dto/project.gql.dto';
import { relationOption } from '@constants/query.constant';

@ObjectType('ProjectOverview')
@BeforeCreateOne((instance: CreateOneInputType<ProjectOverviewDto>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  instance.input.createdBy = createdBy;
  const projectId = Number(context.req.headers['project-id']);
  instance.input.projectId = projectId;
  return instance;
})
@BeforeUpdateOne(Hooks.UpdatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class ProjectOverviewDto extends ProjectOverviewEntity {}

@ArgsType()
export class ProjectOverviewQuery extends QueryArgsType(ProjectOverviewDto) {}
export const ProjectOverviewConnection = ProjectOverviewQuery.ConnectionType;
@InputType()
export class CreateProjectOverviewInputDTO {
  key: string;
  value: string;
  sequence: number;
}
@InputType()
export class UpdateProjectOverviewInputDTO extends PartialType(CreateProjectOverviewInputDTO) {
    id?: string;
}
