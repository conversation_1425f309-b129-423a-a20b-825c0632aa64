import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { ProjectOverviewDto } from './dto/project-overview.gql.dto';
import { ProjectOverviewEntity } from './entity/project-overview.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectOverviewEntity])],
      resolvers: [
        {
          DTOClass: ProjectOverviewDto,
          EntityClass: ProjectOverviewEntity,
          create: {
            disabled: false
          },
          update: {
            disabled: false
          },
          delete: {
            disabled: false
          },
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ]
    })
  ]
})
export class ProjectOverviewModule {}
