import { SourceType } from '@constants';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { GraphQLISODateTime, ID, ObjectType } from '@nestjs/graphql';
import {
  BaseEntity as TypeOrmBaseEntity,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from 'typeorm';

@ObjectType()
export class BaseEntity extends TypeOrmBaseEntity {
  @IDField(() => ID)
  @PrimaryGeneratedColumn({ unsigned: true })
  id: number;

  /* ------------------------------- Records ------------------------------ */
  @FilterableField({ nullable: true })
  @Column('int', { nullable: true, unsigned: true })
  createdBy?: number;
  @FilterableField({ nullable: true })
  @Column('int', { nullable: true, unsigned: true })
  updatedBy?: number;
  @FilterableField({ nullable: true })
  @Column('int', { nullable: true, unsigned: true })
  deletedBy?: number;
  /* -------------------------------- Source ------------------------------- */
  @FilterableField({ nullable: false })
  @Column('enum', { enum: SourceType, default: SourceType.Web })
  recordSource: SourceType;
  /* -------------------------------- Timestamp ------------------------------- */
  @FilterableField(() => GraphQLISODateTime)
  @CreateDateColumn()
  createdAt: Date;
  @FilterableField(() => GraphQLISODateTime)
  @UpdateDateColumn()
  updatedAt?: Date;
  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @DeleteDateColumn()
  deletedAt?: Date;
}
