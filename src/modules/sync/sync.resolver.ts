import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { SyncResponseType, PushSyncDataInput } from './dto/sync.gql.dto';

import { SyncService } from './sync.service';
import { AuthData } from '@types';

@UseGuards(GqlAuthGuard)
@Resolver()
export class SyncResolver {
  constructor(private syncService: SyncService) {}

  @Mutation(() => SyncResponseType)
  async syncData(
    @Args('input') input: PushSyncDataInput,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    const res = await this.syncService.pushSyncData(input, user, projectId);

    return res;
  }
}
