import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { SyncService } from './sync.service';
import { SyncController } from './sync.controller';
import { IntegrationModule } from '@modules/integration/integration.module';
import { SyncResolver } from './sync.resolver';
import { ProjectDocumentService } from '@modules/project-document/project-document.service';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([ProjectDocumentEntity]),
        NestjsQueryTypeOrmModule.forFeature([ProjectDocumentUserEntity]),
        NestjsQueryTypeOrmModule.forFeature([WorkspaceAttachmentEntity]),
        NestjsQueryTypeOrmModule.forFeature([WorkspacePhotoEntity]),
        IntegrationModule
      ],
      services: [SyncService, ProjectDocumentService]
    }),
    IntegrationModule
  ],
  providers: [SyncService, SyncResolver],
  controllers: [SyncController]
})
export class SyncModule {}
