import { GetAuthData, GetProjectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Param, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { SyncInputDto, SyncResponseDto } from './dto/sync.api.dto';
import { SyncService } from './sync.service';
import { AuthData } from '@types';

@ApiTags('Sync API')
@Controller('sync')
export class SyncController {
  constructor(private syncService: SyncService) {}

  @UseApiUserAuthGuard()
  @Post('sync/:tableName')
  async sync(
    @Param('tableName') tableName: string,
    @Body() syncInput: SyncInputDto,
    @GetAuthData() authData: AuthData,
    @GetProjectData() projectId: number
  ): Promise<SyncResponseDto> {
    const syncResponse = await this.syncService.syncData(tableName, syncInput, authData, projectId);
    return syncResponse;
  }
}
