import {
  CreateDrawingRevisionInputDTO,
  DrawingRevisionDto,
  UpdateDrawingRevisionInputDTO
} from '@modules/drawing-revision/dto/drawing-revision.gql.dto';
import {
  CreateProjectCarouselInputDTO,
  ProjectCarouselDto,
  UpdateProjectCarouselInputDTO
} from '@modules/project-carousel/dto/project-carousel.gql.dto';
import {
  CreateProjectDocumentCommentInputDTO,
  ProjectDocumentCommentDto,
  UpdateProjectDocumentCommentInputDTO
} from '@modules/project-document-comment/dto/project-document-comment.gql.dto';
import {
  CreateProjectDocumentInputDTO,
  ProjectDocumentDto,
  UpdateProjectDocumentInputDTO
} from '@modules/project-document/dto/project-document.gql.dto';
import {
  CreateProjectGroupInputDTO,
  ProjectGroupDto,
  UpdateProjectGroupInputDTO
} from '@modules/project-group/dto/project-group.gql.dto';
import { CreateProjectInputDTO, ProjectDto, UpdateProjectInputDTO } from '@modules/project/dto/project.gql.dto';
import {
  CreateRequestForSignatureInputDTO,
  RequestForSignatureDto,
  UpdateRequestForSignatureInputDTO
} from '@modules/request-for-signature/dto/request-for-signature.gql.dto';
import {
  CreateTaskCommentInputDTO,
  TaskCommentDto,
  UpdateTaskCommentInputDTO
} from '@modules/task-comment/dto/task-comment.gql.dto';
import { CreateTaskInputDTO, TaskDto, UpdateTaskInputDTO } from '@modules/task/dto/task.gql.dto';
import {
  CreateTasksAttachmentInputDTO,
  TasksAttachmentDto
} from '@modules/tasks-attachment/dto/tasks-attachment.gql.dto';
import {
  CreateTasksMediaInputDTO,
  TasksMediaDto,
  UpdateTasksMediaInputDTO
} from '@modules/tasks-media/dto/tasks-media.gql.dto';
import {
  CreateWorkspaceAttachmentInputDTO,
  UpdateWorkspaceAttachmentInputDTO,
  WorkspaceAttachmentDto
} from '@modules/workspace-attachment/dto/workspace-attachment.gql.dto';
import {
  CreateWorkspaceCCInputDTO,
  UpdateWorkspaceCCInputDTO,
  WorkspaceCCDto
} from '@modules/workspace-cc/dto/workspace-cc.gql.dto';
import {
  CreateWorkspaceDocumentInputDTO,
  UpdateWorkspaceDocumentInputDTO,
  WorkspaceDocumentDto
} from '@modules/workspace-document/dto/workspace-document.dto';
import {
  CreateWorkspaceGroupInputDTO,
  UpdateWorkspaceGroupInputDTO,
  WorkspaceGroupDto
} from '@modules/workspace-group/dto/workspace-group.gql.dto';
import {
  CreateWorkspacePhotoInputDTO,
  UpdateWorkspacePhotoInputDTO,
  WorkspacePhotoDto
} from '@modules/workspace-photo/dto/workspace-photo.gql.dto';
import { Field, ID, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
class ProjectGroupChangeSet {
  @Field(() => [CreateProjectGroupInputDTO], { nullable: true })
  created?: CreateProjectGroupInputDTO[];

  @Field(() => [UpdateProjectGroupInputDTO], { nullable: true })
  updated?: UpdateProjectGroupInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
class WorkspaceGroupChangeSet {
  @Field(() => [CreateWorkspaceGroupInputDTO], { nullable: true })
  created?: CreateWorkspaceGroupInputDTO[];

  @Field(() => [UpdateWorkspaceGroupInputDTO], { nullable: true })
  updated?: UpdateWorkspaceGroupInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
class TaskChangeSet {
  @Field(() => [CreateTaskInputDTO], { nullable: true })
  created?: CreateTaskInputDTO[];

  @Field(() => [UpdateTaskInputDTO], { nullable: true })
  updated?: UpdateTaskInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class TaskAttachmentChangeSet {
  @Field(() => [CreateTasksAttachmentInputDTO], { nullable: true })
  created?: CreateTasksAttachmentInputDTO[];

  @Field(() => [CreateTasksAttachmentInputDTO], { nullable: true })
  updated?: CreateTasksAttachmentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class TaskMediaChangeSet {
  @Field(() => [CreateTasksMediaInputDTO], { nullable: true })
  created?: CreateTasksMediaInputDTO[];

  @Field(() => [UpdateTasksMediaInputDTO], { nullable: true })
  updated?: UpdateTasksMediaInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class TaskCommentChangeSet {
  @Field(() => [CreateTaskCommentInputDTO], { nullable: true })
  created?: CreateTaskCommentInputDTO[];

  @Field(() => [UpdateTaskCommentInputDTO], { nullable: true })
  updated?: UpdateTaskCommentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class ProjectDocumentChangeSet {
  @Field(() => [CreateProjectDocumentInputDTO], { nullable: true })
  created?: CreateProjectDocumentInputDTO[];

  @Field(() => [UpdateProjectDocumentInputDTO], { nullable: true })
  updated?: UpdateProjectDocumentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class DrawingRevisionChangeSet {
  @Field(() => [CreateDrawingRevisionInputDTO], { nullable: true })
  created?: CreateDrawingRevisionInputDTO[];

  @Field(() => [UpdateDrawingRevisionInputDTO], { nullable: true })
  updated?: UpdateDrawingRevisionInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class ProjectChangeSet {
  @Field(() => [CreateProjectInputDTO], { nullable: true })
  created?: CreateProjectInputDTO[];

  @Field(() => [UpdateProjectInputDTO], { nullable: true })
  updated?: UpdateProjectInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class ProjectCarouselChangeSet {
  @Field(() => [CreateProjectCarouselInputDTO], { nullable: true })
  created?: CreateProjectCarouselInputDTO[];

  @Field(() => [UpdateProjectCarouselInputDTO], { nullable: true })
  updated?: UpdateProjectCarouselInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class RequestForSignatureChangeSet {
  @Field(() => [CreateRequestForSignatureInputDTO], { nullable: true })
  created?: CreateRequestForSignatureInputDTO[];

  @Field(() => [UpdateRequestForSignatureInputDTO], { nullable: true })
  updated?: UpdateRequestForSignatureInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class WorkspaceCCChangeSet {
  @Field(() => [CreateWorkspaceCCInputDTO], { nullable: true })
  created?: CreateWorkspaceCCInputDTO[];

  @Field(() => [UpdateWorkspaceCCInputDTO], { nullable: true })
  updated?: UpdateWorkspaceCCInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class WorkspaceAttachmentSet {
  @Field(() => [CreateWorkspaceAttachmentInputDTO], { nullable: true })
  created?: CreateWorkspaceAttachmentInputDTO[];

  @Field(() => [UpdateWorkspaceAttachmentInputDTO], { nullable: true })
  updated?: UpdateWorkspaceAttachmentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class WorkspacePhotosSet {
  @Field(() => [CreateWorkspacePhotoInputDTO], { nullable: true })
  created?: CreateWorkspacePhotoInputDTO[];

  @Field(() => [UpdateWorkspacePhotoInputDTO], { nullable: true })
  updated?: UpdateWorkspacePhotoInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class WorkspaceDocumentSet {
  @Field(() => [CreateWorkspaceDocumentInputDTO], { nullable: true })
  created?: CreateWorkspaceDocumentInputDTO[];

  @Field(() => [UpdateWorkspaceDocumentInputDTO], { nullable: true })
  updated?: UpdateWorkspaceDocumentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class ProjectDocumentCommentSet {
  @Field(() => [CreateProjectDocumentCommentInputDTO], { nullable: true })
  created?: CreateProjectDocumentCommentInputDTO[];

  @Field(() => [UpdateProjectDocumentCommentInputDTO], { nullable: true })
  updated?: UpdateProjectDocumentCommentInputDTO[];

  @Field(() => [ID], { nullable: true })
  deleted?: number[];
}

@InputType()
export class SyncDataInput {
  @Field(() => ProjectGroupChangeSet)
  project_groups?: ProjectGroupChangeSet;

  @Field(() => WorkspaceGroupChangeSet)
  workspace_groups?: WorkspaceGroupChangeSet;

  @Field(() => TaskChangeSet)
  tasks?: TaskChangeSet;

  @Field(() => TaskAttachmentChangeSet)
  tasks_attachments?: TaskAttachmentChangeSet;

  @Field(() => TaskCommentChangeSet)
  task_comments?: TaskCommentChangeSet;

  @Field(() => TaskMediaChangeSet)
  tasks_medias?: TaskMediaChangeSet;

  @Field(() => ProjectDocumentChangeSet)
  project_documents?: ProjectDocumentChangeSet;

  @Field(() => DrawingRevisionChangeSet)
  drawing_revisions?: DrawingRevisionChangeSet;

  @Field(() => ProjectChangeSet)
  projects?: ProjectChangeSet;

  @Field(() => ProjectCarouselChangeSet)
  project_carousels?: ProjectCarouselChangeSet;

  @Field(() => RequestForSignatureChangeSet)
  request_for_signatures?: RequestForSignatureChangeSet;

  @Field(() => WorkspaceCCChangeSet)
  workspace_ccs?: WorkspaceCCChangeSet;

  @Field(() => WorkspaceAttachmentSet)
  workspace_attachments?: WorkspaceAttachmentSet;

  @Field(() => WorkspacePhotosSet)
  workspace_photos?: WorkspacePhotosSet;

  @Field(() => WorkspaceDocumentSet)
  workspace_document?: WorkspaceDocumentSet;

  @Field(() => ProjectDocumentCommentSet)
  project_document_comments?: ProjectDocumentCommentSet;
}

@InputType()
export class PushSyncDataInput {
  @Field(() => SyncDataInput)
  changes: SyncDataInput;

  @Field()
  lastPulledAt: number;
}

//* =================================== RESPONSE ============================= //

@ObjectType()
class FailedRecord {
  @Field(() => ID)
  id: string;

  @Field()
  type: string;
}

@ObjectType()
class ProjectGroupChangesResponse {
  @Field(() => [ProjectGroupDto])
  created?: ProjectGroupDto[];

  @Field(() => [ProjectGroupDto])
  updated?: ProjectGroupDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspaceGroupChangesResponse {
  @Field(() => [WorkspaceGroupDto])
  created?: WorkspaceGroupDto[];

  @Field(() => [WorkspaceGroupDto])
  updated?: WorkspaceGroupDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class TaskChangesResponse {
  @Field(() => [TaskDto])
  created?: TaskDto[];

  @Field(() => [TaskDto])
  updated?: TaskDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class TaskAttachmentChangesResponse {
  @Field(() => [TasksAttachmentDto])
  created?: TasksAttachmentDto[];

  @Field(() => [TasksAttachmentDto])
  updated?: TasksAttachmentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class TaskMediaChangesResponse {
  @Field(() => [TasksMediaDto])
  created?: TasksMediaDto[];

  @Field(() => [TasksMediaDto])
  updated?: TasksMediaDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class TaskCommentChangesResponse {
  @Field(() => [TaskCommentDto])
  created?: TaskCommentDto[];

  @Field(() => [TaskCommentDto])
  updated?: TaskCommentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class ProjectDocumentChangesResponse {
  @Field(() => [ProjectDocumentDto])
  created?: ProjectDocumentDto[];

  @Field(() => [ProjectDocumentDto])
  updated?: ProjectDocumentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class DrawingRevisionChangesResponse {
  @Field(() => [DrawingRevisionDto])
  created?: DrawingRevisionDto[];

  @Field(() => [DrawingRevisionDto])
  updated?: DrawingRevisionDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class ProjectChangesResponse {
  @Field(() => [ProjectDto])
  created?: ProjectDto[];

  @Field(() => [ProjectDto])
  updated?: ProjectDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class ProjectCarouselChangesResponse {
  @Field(() => [ProjectCarouselDto])
  created?: ProjectCarouselDto[];

  @Field(() => [ProjectCarouselDto])
  updated?: ProjectCarouselDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class RequestForSignatureChangesResponse {
  @Field(() => [RequestForSignatureDto])
  created?: RequestForSignatureDto[];

  @Field(() => [RequestForSignatureDto])
  updated?: RequestForSignatureDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspaceCCChangesResponse {
  @Field(() => [WorkspaceCCDto])
  created?: WorkspaceCCDto[];

  @Field(() => [WorkspaceCCDto])
  updated?: WorkspaceCCDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspaceAttachmentChangesResponse {
  @Field(() => [WorkspaceAttachmentDto])
  created?: WorkspaceAttachmentDto[];

  @Field(() => [WorkspaceAttachmentDto])
  updated?: WorkspaceAttachmentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspaceDocumentChangesResponse {
  @Field(() => [WorkspaceDocumentDto])
  created?: CreateWorkspaceDocumentInputDTO[];

  @Field(() => [WorkspaceDocumentDto])
  updated?: WorkspaceDocumentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspacePhotosResponse {
  @Field(() => [WorkspacePhotoDto])
  created?: WorkspacePhotoDto[];

  @Field(() => [WorkspacePhotoDto])
  updated?: WorkspacePhotoDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
class WorkspaceDocumentComment {
  @Field(() => [ProjectDocumentCommentDto])
  created?: ProjectDocumentCommentDto[];

  @Field(() => [ProjectDocumentCommentDto])
  updated?: ProjectDocumentCommentDto[];

  @Field(() => [ID])
  deleted?: number[];

  @Field(() => [FailedRecord])
  failed?: FailedRecord[];
}

@ObjectType()
export class SyncResponseType {
  @Field(() => ProjectGroupChangesResponse)
  project_groups?: ProjectGroupChangesResponse;

  @Field(() => WorkspaceGroupChangesResponse)
  workspace_groups?: WorkspaceGroupChangesResponse;

  @Field(() => TaskChangesResponse)
  tasks?: TaskChangesResponse;

  @Field(() => TaskAttachmentChangesResponse)
  tasks_attachments?: TaskAttachmentChangesResponse;

  @Field(() => TaskCommentChangesResponse)
  task_comments?: TaskCommentChangesResponse;

  @Field(() => TaskMediaChangesResponse)
  tasks_medias?: TaskMediaChangesResponse;

  @Field(() => ProjectDocumentChangesResponse)
  project_documents?: ProjectDocumentChangesResponse;

  @Field(() => DrawingRevisionChangesResponse)
  drawing_revisions?: DrawingRevisionChangesResponse;

  @Field(() => ProjectChangesResponse)
  projects?: ProjectChangesResponse;

  @Field(() => ProjectCarouselChangesResponse)
  project_carousels?: ProjectCarouselChangesResponse;

  @Field(() => RequestForSignatureChangesResponse)
  request_for_signatures?: RequestForSignatureChangesResponse;

  @Field(() => WorkspaceCCChangesResponse)
  workspace_ccs?: WorkspaceCCChangesResponse;

  @Field(() => WorkspaceAttachmentChangesResponse)
  workspace_attachments?: WorkspaceAttachmentChangesResponse;

  @Field(() => WorkspacePhotosResponse)
  workspace_photos?: WorkspacePhotosResponse;

  @Field(() => WorkspaceDocumentChangesResponse)
  workspace_document?: WorkspaceDocumentChangesResponse;

  @Field(() => WorkspaceDocumentComment)
  project_document_comments?: WorkspaceDocumentComment;
}
