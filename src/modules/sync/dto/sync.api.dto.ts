import { AuditLogModuleType } from '@constants';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';

export class PaginationMetadata {
  offset: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

export class SyncInputDto {
  lastPulledAt: Date | null;
  lastPushedAt?: Date | null;
  module?: AuditLogModuleType | null;
  parentIds?: number[];
  offset?: number;
  limit?: number;
}

class TableChangesDto {
  created: ProjectGroupEntity[];
  updated: ProjectGroupEntity[];
  deleted: number[];
}

export class SyncResponseDto {
  changes: { [tableName: string]: TableChangesDto };
  timestamp: number;
  pagination?: PaginationMetadata;
}

export class pushSyncDataDto {
  changes: { [tableName: string]: TableChangesDto };
  timestamp: number;
}
