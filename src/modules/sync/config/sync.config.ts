import { AuditLogModuleType } from '@constants';
import { DrawingRevisionEntity } from '@modules/drawing-revision/entity/drawing-revision.entity';
import { EventEntity } from '@modules/event/entity/event.entity';
import { ProjectCarouselEntity } from '@modules/project-carousel/entity/project-carousel.entity';
import { ProjectDocumentCommentEntity } from '@modules/project-document-comment/entity/project-document-comment.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { TaskCommentEntity } from '@modules/task-comment/entity/task-comment.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';
import { WorkspaceDocumentEntity } from '@modules/workspace-document/entity/workspace-document.entity';
import { WorkspaceGroupUserEntity } from '@modules/workspace-group-user/entity/workspace-group-user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';

export const SyncModuleType = {
  ...AuditLogModuleType,
  ProjectUsers: 'projectUsers',
  ProjectDocuments: 'projectDocuments',
  TaskAttachment: 'taskAttachment',
  TaskMedia: 'taskMedia',
  ProjectDocumentUser: 'projectDocumentUser',
  DrawingRevision: 'drawingRevision',
  ProjectCarousel: 'projectCarousel',
  RequestForSignature: 'requestForSignature',
  WorkspaceCC: 'workspaceCC',
  WorkspaceAttachment: 'workspaceAttachment',
  WorkspacePhotos: 'workspacePhotos',
  WorkspaceDocument: 'workspaceDocument',
  ProjectDocumentComment: 'projectDocumentComment',
  Users: 'users',
  Events: 'events',
  TaskComment: 'taskComment',
  WorkspaceGroupUser: 'workspaceGroupUser'
};

export const pullEntities = [
  {
    module: SyncModuleType.ProjectGroup,
    entity: ProjectGroupEntity,
    dbName: 'project_groups',
    relations: [],
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.Task,
    entity: TaskEntity,
    dbName: 'tasks',
    relations: ['assignees', 'copies', 'documents'],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.TaskAttachment,
    entity: TasksAttachmentEntity,
    dbName: 'tasks_attachments',
    relationKey: 'taskId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.TaskMedia,
    entity: TasksMediaEntity,
    dbName: 'tasks_medias',
    relationKey: 'taskId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.TaskComment,
    entity: TaskCommentEntity,
    dbName: 'task_comments',
    relationKey: 'taskId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.DrawingRevision,
    entity: DrawingRevisionEntity,
    dbName: 'drawing_revisions',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.RequestForSignature,
    entity: RequestForSignatureEntity,
    dbName: 'request_for_signatures',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.WorkspaceCC,
    entity: WorkspaceCCEntity,
    dbName: 'workspace_ccs',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.WorkspaceAttachment,
    entity: WorkspaceAttachmentEntity,
    dbName: 'workspace_attachments',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.WorkspacePhotos,
    entity: WorkspacePhotoEntity,
    dbName: 'workspace_photos',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.WorkspaceDocument,
    entity: WorkspaceDocumentEntity,
    dbName: 'workspace_document',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.ProjectDocumentComment,
    entity: ProjectDocumentCommentEntity,
    dbName: 'project_document_comments',
    relationKey: 'projectDocumentId',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.ProjectUsers,
    entity: ProjectUserEntity,
    dbName: 'project_users',
    relations: ['user'],
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.ProjectDocuments,
    entity: ProjectDocumentEntity,
    dbName: 'project_documents',
    relations: ['workspaceGroup', 'requestForSignatures'],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.ProjectDocumentUser,
    entity: ProjectDocumentUserEntity,
    dbName: 'project_documents_users',
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.WorkspaceGroup,
    entity: WorkspaceGroupEntity,
    dbName: 'workspace_groups',
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.Project,
    entity: ProjectEntity,
    dbName: 'projects',
    relations: [],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: true,

  },
  {
    module: SyncModuleType.ProjectCarousel,
    entity: ProjectCarouselEntity,
    dbName: 'project_carousels',
    relations: [],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: true
  },
  {
    module: SyncModuleType.Users,
    entity: UserEntity,
    dbName: 'users',
    relations: [],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: false
  },
  {
    module: SyncModuleType.Events,
    entity: EventEntity,
    dbName: 'events',
    relations: [],
    fetchRelationByIds: [],
    isShouldIncludeProjectId: true
  }, 
  {
    module: SyncModuleType.WorkspaceGroupUser,
    entity: WorkspaceGroupUserEntity,
    dbName: 'workspace_group_users',
    relationKey: 'workspaceGroupId',
    isShouldIncludeProjectId: false
  }
];

export const pushEntities = [
  {
    module: SyncModuleType.ProjectGroup,
    entity: ProjectGroupEntity,
    dbName: 'project_groups',
    relations: []
  },
  {
    module: SyncModuleType.Task,
    entity: TaskEntity,
    dbName: 'tasks',
    innerRelations: [
      { entity: TasksAttachmentEntity, relationKey: 'tasks_attachments' },
      { entity: TasksMediaEntity, relationKey: 'tasks_medias' }
    ]
  },
  {
    module: SyncModuleType.ProjectUsers,
    entity: ProjectUserEntity,
    dbName: 'project_users',
    relations: ['user']
  },
  {
    module: SyncModuleType.ProjectDocuments,
    entity: ProjectDocumentEntity,
    dbName: 'project_documents'
  },
  {
    module: SyncModuleType.TaskAttachment,
    entity: TasksAttachmentEntity,
    dbName: 'tasks_attachments'
  },
  {
    module: SyncModuleType.TaskMedia,
    entity: TasksMediaEntity,
    dbName: 'tasks_medias'
  },
  {
    module: SyncModuleType.TaskComment,
    entity: TaskCommentEntity,
    dbName: 'task_comments'
  },
  {
    module: SyncModuleType.DrawingRevision,
    entity: DrawingRevisionEntity,
    dbName: 'drawing_revisions'
  },
  {
    module: SyncModuleType.Project,
    entity: ProjectEntity,
    dbName: 'projects'
  },
  {
    module: SyncModuleType.ProjectCarousel,
    entity: ProjectCarouselEntity,
    dbName: 'project_carousels'
  },
  {
    module: SyncModuleType.WorkspaceGroup,
    entity: WorkspaceGroupEntity,
    dbName: 'workspace_groups',
    relations: []
  },
  {
    module: SyncModuleType.RequestForSignature,
    entity: RequestForSignatureEntity,
    dbName: 'request_for_signatures'
  },
  {
    module: SyncModuleType.WorkspaceCC,
    entity: WorkspaceCCEntity,
    dbName: 'workspace_ccs',
  },
  {
    module: SyncModuleType.WorkspaceAttachment,
    entity: WorkspaceAttachmentEntity,
    dbName: 'workspace_attachments'
  },
  {
    module: SyncModuleType.WorkspacePhotos,
    entity: WorkspacePhotoEntity,
    dbName: 'workspace_photos'
  },
  {
    module: SyncModuleType.ProjectDocumentUser,
    entity: ProjectDocumentUserEntity,
    dbName: 'project_documents'
  },
  {
    module: SyncModuleType.WorkspaceDocument,
    entity: WorkspaceDocumentEntity,
    dbName: 'workspace_document'
  },
  {
    module: SyncModuleType.ProjectDocumentComment,
    entity: ProjectDocumentCommentEntity,
    dbName: 'project_document_comments'
  }
];
