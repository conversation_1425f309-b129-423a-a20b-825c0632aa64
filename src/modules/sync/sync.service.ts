import { Injectable } from '@nestjs/common';
import { isNumber } from 'class-validator';
import {
  MoreThan,
  Repository,
  getRepository,
  FindConditions,
  In,
  Not,
  MoreThanOrEqual,
  Brackets,
  get<PERSON>anager,
  EntityManager
} from 'typeorm';
import { SyncInputDto, SyncResponseDto } from './dto/sync.api.dto';
import { CategoryType, ProjectDocumentStatus } from '@constants';
import { AuthData } from '@types';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import moment from 'moment';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getErrorMessage } from '@common/error';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { FileService } from '@modules/integration/file.service';
import { PushSyncDataInput, SyncDataInput } from './dto/sync.gql.dto';
import { pullEntities, pushEntities, SyncModuleType } from './config/sync.config';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';

@Injectable()
export class SyncService {
  constructor(private fileService: FileService) { }

  private readonly DEFAULT_LIMIT = 500;
  private readonly DEFAULT_OFFSET = 0;
  private readonly batchSize = 500;

  async syncData(tableName: string, input: SyncInputDto, authData: AuthData, projectId: number) {
    const { projectIds, user } = await this.getInvolvedProjects(authData);
    const offset = isNumber(input.offset) ? input.offset : this.DEFAULT_OFFSET;
    const limit = isNumber(input.limit) ? input.limit : this.DEFAULT_LIMIT;
    return await this.getSyncData(tableName, input.lastPulledAt, input.module || null, projectIds, user, input.parentIds, offset, limit);
  }

  async getInvolvedProjects(authData: AuthData) {
    const user = await getRepository(UserEntity).findOneOrFail({ id: authData.id });
    const projects = await getRepository(ProjectEntity)
      .createQueryBuilder('project')
      .innerJoin('project.projectUsers', 'projectUser')
      .where('projectUser.userId = :userId', { userId: user.id })
      .select('project.id')
      .cache(true)
      .getMany();

    const projectIds = projects.map(project => project.id);

    return {
      projectIds,
      user,
      projects
    };
  }

  private createProjectDocumentUserQuery(projectIds: number[], userId: number, condition: FindConditions<any>) {
    try {
      if (projectIds.length === 0) return null;
  
      if (condition.deletedAt) {
        return getRepository(ProjectDocumentUserEntity)
          .createQueryBuilder('pdu')
          .where('pdu.userId = :userId', { userId })
          .andWhere('pdu.deletedAt IS NOT NULL')
          .andWhere('pdu.deletedAt > :deletedAt', { deletedAt: condition.deletedAt })
          .withDeleted();
      }
        
      // Keep the basic join condition simple
      const pduJoinCondition = 'pdu.projectDocumentId = projectDocument.id AND pdu.userId = :userId AND pdu.type = :includeType';
      
      const params: any = { userId, includeType: "Include" };
      if (condition.deletedAt) {
        params.deletedAt = condition.deletedAt;
      }
      
      const queryBuilder = getRepository(ProjectDocumentEntity)
        .createQueryBuilder('projectDocument')
        // Join permission rows with the basic join condition.
        .leftJoin(
          'project_document_users',
          'pdu',
          pduJoinCondition,
          params
        );
        
      // Base filters for project and category.
      queryBuilder
        .andWhere('projectDocument.projectId IN (:...projectIds)', { projectIds })
        .andWhere('projectDocument.category IN (:...categories)', {
          categories: [CategoryType.ProjectDocument, CategoryType.Correspondence],
        })
        // Return a document if the user either has an explicit permission or is the creator.
        .andWhere(
          new Brackets(qb => {
            qb.where('pdu.projectDocumentId IS NOT NULL')
              .orWhere('projectDocument.addedBy = :userId', { userId });
          })
        );
        
      // Add other timestamp filters
      if (condition.createdAt) {
        queryBuilder.andWhere('pdu.createdAt >= :createdAt', { createdAt: condition.createdAt });
      }
      if (condition.updatedAt) {
        queryBuilder.andWhere('pdu.updatedAt > :updatedAt', { updatedAt: condition.updatedAt });
      }
        
      return queryBuilder;
    } catch (error) {
      console.error('Error in createProjectDocumentUserQuery:', error);
      throw error;
    }
  }
  
  

  private async getCount(
    repository: Repository<any>,
    condition: FindConditions<any>,
    module?: string,
    userId?: number,
    customCondition: any[] = []
  ): Promise<number> {
    if (module === SyncModuleType.ProjectDocumentUser) {
      const projectIds = condition.rawProjectIds
        ? Array.isArray(condition.rawProjectIds)
          ? condition.rawProjectIds
          : [condition.rawProjectIds]
        : [];

      const queryBuilder = this.createProjectDocumentUserQuery(projectIds, userId, condition);
      if (!queryBuilder) return 0;

      return await queryBuilder.getCount();
    } else {
      let appliedCondition = { ...condition };
      customCondition?.forEach(cond => {
        appliedCondition = { ...appliedCondition, ...cond };
      });
      return await repository.count({
        where: appliedCondition,
        ...(condition.deletedAt && { withDeleted: true })
      });
    }
  }

  async fetchRelatedEntities(repository: Repository<any>, relationKey: string, parentIds: number[], type: string, lastPulledAt: Date | null) {
    let condition;
    switch (type) {
      case 'created':
        condition = {
          [relationKey]: In(parentIds),
          createdAt: MoreThanOrEqual(new Date(lastPulledAt)),
          deletedAt: null
        };
        break;
      case 'updated':
        condition = {
          [relationKey]: In(parentIds),
          updatedAt: MoreThan(new Date(lastPulledAt)),
          createdAt: Not(MoreThanOrEqual(new Date(lastPulledAt))),
          deletedAt: null
        };
        break;
      case 'deleted':
        condition = {
          [relationKey]: In(parentIds),
          deletedAt: MoreThanOrEqual(new Date(lastPulledAt))
        };
        break;
    }
    return condition ? repository.find({ where: condition, withDeleted: true }) : [];
  }

  private sanitizeEntity(entity: any | number, module: string) {
    if (!entity || typeof entity === 'number') return entity;

    const { createdAt, updatedAt, ...rest } = entity;

    return {
      ...rest,
      ...(entity.projectId && { projectId: entity.projectId }),
      remoteId: entity.id,
      server_created_at: new Date(createdAt).getTime(),
      server_updated_at: new Date(updatedAt).getTime(),

      ...(module === SyncModuleType.ProjectUsers && {
        name: entity?.user?.name,
        email: entity?.user?.email,
        avatarUrl: entity?.user?.avatar?.toString?.()
      }),

      ...(module === SyncModuleType.Task && {
        assignees: entity.__assignees__?.map?.(assignee => assignee.id),
        copies: entity.__copies__?.map?.(copy => copy.id),
        documents: entity.documents?.map?.(document => document.id)
      }),

      ...(module === SyncModuleType.ProjectDocuments && {
        allFormCode: parseInt(entity.allFormCode, 10),
        groupCode: `${entity.groupCode}`,
        fileSize: parseFloat(entity.fileSize),
        submittedAt: new Date(entity.submittedAt)?.getTime(),
        autosavedAt: new Date(entity.autosavedAt)?.getTime(),
        assigneeIds: entity.requestForSignatures?.map?.((signature: RequestForSignatureEntity) => signature.signById?.toString?.())
      })
    };
  }

  async getSyncData(
    tableName: string,
    lastPulledAt: Date | null,
    module: any | null,
    projectIds: number[],
    authData: UserEntity,
    parentIds?: number[],
    offset: number = this.DEFAULT_OFFSET,
    limit: number = this.DEFAULT_LIMIT
  ) {
    try {
      const tableConfig = pullEntities.find(t => t.dbName == tableName);

      if (!tableConfig) {
        throw new Error('Table configuration not found.');
      }

      const changes = {};
      let totalCount = 0;

      let created = [];
      let updated = [];
      let deleted = [];

      const repository = getRepository(tableConfig.entity);
      const baseCondition: FindConditions<any> = {
        ...(!tableConfig.isShouldIncludeProjectId
          ? {}
          : tableConfig.module !== SyncModuleType.Project
            ? { projectId: In(projectIds) }
            : { id: In(projectIds) }),
        ...(tableConfig.module === SyncModuleType.ProjectDocumentUser && {
          rawProjectIds: projectIds
        }),
        ...(tableConfig.module === SyncModuleType.ProjectDocuments && {
          category: Not(In([CategoryType.ProjectDocument, CategoryType.Correspondence]))
        }),
        ...(tableConfig.module === SyncModuleType.Users && {
          id: authData.id
        })
      };

      const customCondition =
        tableConfig.module === SyncModuleType.Events
          ? [{ userId: authData.id }, { scheduleForAll: true }, { projectId: In(projectIds) }]
          : [];

      if (lastPulledAt !== null) {
        const createdCondition = {
          ...baseCondition,
          ...(tableConfig.module === SyncModuleType.ProjectDocumentUser
            ? {
              createdAt: new Date(lastPulledAt)
            }
            : {
              createdAt: MoreThanOrEqual(new Date(lastPulledAt))
            })
        };
        const updatedCondition = {
          ...baseCondition,
          ...(tableConfig.module === SyncModuleType.ProjectDocumentUser
            ? {
              updatedAt: new Date(lastPulledAt),
              createdAt: new Date(lastPulledAt)
            }
            : {
              updatedAt: MoreThan(new Date(lastPulledAt)),
              createdAt: Not(MoreThanOrEqual(new Date(lastPulledAt)))
            }),
          ...(tableConfig.module === SyncModuleType.Users && {
            id: authData.id
          })
        };
        const deletedCondition = {
          ...baseCondition,
          ...(tableConfig.module === SyncModuleType.ProjectDocumentUser
            ? {
              deletedAt: new Date(lastPulledAt)
            }
            : {
              deletedAt: MoreThanOrEqual(new Date(lastPulledAt))
            })
        };

        if (parentIds && parentIds.length > 0) {
          const [relatedCreated, relatedUpdated, relatedDeleted] = await Promise.all([
            this.fetchRelatedEntities(repository, tableConfig.relationKey, parentIds, 'created', lastPulledAt),
            this.fetchRelatedEntities(repository, tableConfig.relationKey, parentIds, 'updated', lastPulledAt),
            this.fetchRelatedEntities(repository, tableConfig.relationKey, parentIds, 'deleted', lastPulledAt)
          ]);

          created = relatedCreated || [];
          updated = relatedUpdated || [];
          deleted = relatedDeleted || [];

        } else {
          const [createdCount, updatedCount, deletedCount] = await Promise.all([
            this.getCount(repository, createdCondition, tableConfig.module, authData.id, customCondition),
            this.getCount(repository, updatedCondition, tableConfig.module, authData.id, customCondition),
            this.getCount(repository, deletedCondition, tableConfig.module, authData.id, customCondition)
          ]);

          const [fetchCreated, fetchUpdated, fetchDeleted] = await Promise.all([
            this.fetchInBatches(
              repository,
              createdCondition,
              tableConfig.relations,
              tableConfig.module,
              authData.id,
              customCondition,
              offset,
              limit
            ),
            this.fetchInBatches(
              repository,
              updatedCondition,
              tableConfig.relations,
              tableConfig.module,
              authData.id,
              customCondition,
              offset,
              limit
            ),
            this.fetchInBatches(
              repository,
              deletedCondition,
              null,
              tableConfig.module,
              authData.id,
              customCondition,
              offset,
              limit
            )
          ]);

          created = fetchCreated.records || [];
          updated = fetchUpdated.records || [];
          deleted = fetchDeleted.records || [];

          totalCount = createdCount + updatedCount + deletedCount;
        }
      } else {
        // first time sync
        if (parentIds && parentIds.length > 0) {
          const [relatedCreated] = await Promise.all([
            this.fetchRelatedEntities(repository, tableConfig.relationKey, parentIds, 'created', lastPulledAt)
          ]);

          created = relatedCreated || [];

        } else {
          const fetchResult = await this.fetchInBatches(
            repository,
            baseCondition,
            tableConfig.relations,
            tableConfig.module,
            authData.id,
            customCondition,
            offset,
            limit
          );

          created = fetchResult.records || [];
          updated = [];
          deleted = [];

          totalCount = await this.getCount(repository, baseCondition, tableConfig.module, authData.id, customCondition);
        }
      }

      if (!changes[tableName]) {
        changes[tableName] = { created: [], updated: [], deleted: [] };
      }

      const changesKey = tableName === 'project_documents_users' ? 'project_documents' : tableName;
      changes[changesKey] = changes[changesKey] || { created: [], updated: [], deleted: [] };

      changes[changesKey].created.push(...created?.map?.(entity => this.sanitizeEntity(entity, tableConfig.module)));
      changes[changesKey].updated.push(...updated?.map?.(entity => this.sanitizeEntity(entity, tableConfig.module)));
      changes[changesKey].deleted.push(...deleted?.map?.(entity => entity.id));

      const syncResponse = new SyncResponseDto();
      syncResponse.changes = changes;
      syncResponse.timestamp = new Date().getTime();

      if (!parentIds || parentIds.length === 0) {
        syncResponse.pagination = {
          offset,
          limit,
          total: totalCount,
          hasMore: (offset + limit) < totalCount
        };
      }

      if (syncResponse.changes.project_documents_users) {
        delete syncResponse.changes.project_documents_users;
      }

      return syncResponse;
    } catch (error) {
      getErrorMessage(error, 'SyncService', 'getSyncData');
    }
  }

  private async fetchInBatches(
    repository: Repository<any>,
    condition: FindConditions<any>,
    relations?: string[],
    module?: string,
    userId?: number,
    customCondition: any[] = [],
    offset: number = this.DEFAULT_OFFSET,
    limit: number = this.DEFAULT_LIMIT
  ) {
    try {
      let results = [];

      if (module !== SyncModuleType.ProjectDocumentUser) {
        let appliedCondition = { ...condition };

        customCondition?.forEach(cond => {
          appliedCondition = { ...appliedCondition, ...cond };
        });

        const batch = await repository.find({
          where: appliedCondition,
          skip: offset,
          take: limit,
          order: { id: 'ASC' },
          ...(condition.deletedAt && { withDeleted: true }),
          relations: relations ?? [],
          cache: true,
        });

        results = batch;
      } else {
        const projectIds = condition.rawProjectIds
          ? Array.isArray(condition.rawProjectIds)
            ? condition.rawProjectIds
            : [condition.rawProjectIds]
          : [];

        const queryBuilder = this.createProjectDocumentUserQuery(projectIds, userId, condition);

        if (condition.deletedAt) {
          const revokedPermissions = await queryBuilder.getMany();
          const sanitizedPermissions = revokedPermissions.map(permission => ({
            id: permission.projectDocumentId,
          }));
          return {
            records: sanitizedPermissions,
          };
        }

        if (!queryBuilder) {
          return { records: [] };
        }

        const selectedFields = [
          'projectDocument.id',
          'projectDocument.fileSystemType',
          'projectDocument.type',
          'projectDocument.name',
          'projectDocument.fileSize',
          'projectDocument.fileKey',
          'projectDocument.addedBy',
          'projectDocument.updatedAt',
          'projectDocument.fileUrl',
          'projectDocument.projectId',
          'projectDocument.category',
          'projectDocument.projectDocumentId',
          'projectDocument.driveType',
        ];


        queryBuilder
          .select(selectedFields)
          .orderBy('projectDocument.id', 'ASC')
          .skip(offset)
          .take(limit)
          .cache(true);

        if (condition.updatedAt) {
          queryBuilder.andWhere('projectDocument.createdAt <= :createdAt', { createdAt: condition.createdAt });
        }

        const projectDocuments = await queryBuilder.getMany();
        results = projectDocuments;
      }

      return {
        records: results || []
      };
    } catch (error) {
      throw error;
    }
  }

  // ======================= Push Sync =======================

  async pushSyncData(input: PushSyncDataInput, authData: AuthData, projectId: number) {
    const connection = getManager().connection;

    const queryRunner = connection.createQueryRunner(); // Create a query runner
    await queryRunner.startTransaction();
    try {
      const { changes } = input;

      for (const [tableName, { created, deleted, updated }] of Object.entries(changes)) {
        const entityConfig = pushEntities.find(e => e.dbName === tableName);
        if (!entityConfig) continue;

        const repository = queryRunner.manager.getRepository(entityConfig.entity);

        if (created.length > 0) {
          const { result: createdEntities, error } = await this.bulkCreate(repository, created, authData.id, projectId);
          changes[tableName].failed = error;
          changes[tableName].created = createdEntities;
          if (changes.project_documents && changes.project_documents.created.length > 0) {
            const documentIdMap = new Map(createdEntities.map(doc => [doc.localId, doc.remoteId]));

            if (changes.request_for_signatures && changes.request_for_signatures.created.length > 0) {
              const updatedSignatures = changes.request_for_signatures.created.map(signature => {
                if (signature.localProjectDocumentId && documentIdMap.has(signature.localProjectDocumentId)) {
                  return {
                    ...signature,
                    projectDocumentId: documentIdMap.get(signature.localProjectDocumentId)
                  };
                }
                return signature;
              });
              changes.request_for_signatures.created = updatedSignatures;
            }
          }
        }

        if (updated.length > 0) {
          const updatedEntities = await this.bulkUpdateInBatches(repository, updated);
          changes[tableName].updated = updatedEntities;
          if (changes.project_documents && changes.project_documents.updated.length > 0) {
            const documentIdMap = new Map(updatedEntities.map(doc => [doc.localId, doc.remoteId]));

            if (changes.request_for_signatures && changes.request_for_signatures.updated.length > 0) {
              const updatedSignatures = changes.request_for_signatures.updated.map(signature => {
                if (signature.localProjectDocumentId && documentIdMap.has(signature.localProjectDocumentId)) {
                  return {
                    ...signature,
                    projectDocumentId: documentIdMap.get(signature.localProjectDocumentId)
                  };
                }
                return signature;
              });
              changes.request_for_signatures.updated = updatedSignatures;
            }
          }

          if (changes.request_for_signatures && changes.request_for_signatures.updated.length > 0) {
            const requestForSignatureRepository = getRepository(RequestForSignatureEntity);

            const updatedSignatures = await Promise.all(
              changes.request_for_signatures.updated.map(async sig => {
                // Fetch all signatures associated with the same projectDocumentId
                const allSignatures = await requestForSignatureRepository.find({
                  where: { projectDocumentId: sig.projectDocumentId }
                });
                // Exclude the current signature from the fetched list if needed
                const otherSignatures = allSignatures.filter(s => s.id !== sig.remoteId);
                // Build a statuses array that includes the current signature's status
                const statuses = otherSignatures.map(s => s.status);
                statuses.push(sig.status);

                // Determine the document status based on the statuses
                let documentStatus;
                if (statuses.every(s => s === 'Approved')) {
                  documentStatus = 'Approved';
                } else if (statuses.some(s => s === 'Rejected')) {
                  documentStatus = 'Rejected';
                }

                // Return the updated signature object with the new documentStatus field
                return {
                  ...sig,
                  documentStatus
                };
              })
            );
            changes.request_for_signatures.updated = updatedSignatures;
          }
        }

        if (deleted.length > 0) await this.bulkDelete(repository, deleted);
      }

      await this.configureRelations(changes, queryRunner.manager);
      await this.configureGroupRelations(changes, queryRunner.manager);
      await queryRunner.commitTransaction();

      return changes;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new Error(error);
    } finally {
      queryRunner.release();
    }
  }

  private async bulkCreate(
    repository: Repository<any>,
    datas: any[],
    userId: number,
    projectId: number
  ): Promise<{ result: any[], error: any[] }> {
    try {
      const results = [];
      const error = [];

      const localIdToEntityMap = new Map();
      let currentCount = 0;
      let groupCodeCount = 0;

      //? Handle the TaskCode and AllFormCode calculation
      if (repository.metadata.name === "ProjectDocumentEntity") {
        const count = await repository
          .createQueryBuilder('entity')
          .where('entity.projectId = :id', { id: projectId })
          .andWhere('entity.category = :category', { category: CategoryType.AllForm })
          .andWhere('entity.allFormCode IS NOT NULL')
          .withDeleted()
          .getCount();
        currentCount = count;
      } else if (repository.metadata.name === "TaskEntity") {
        const count = await repository
          .createQueryBuilder('entity')
          .where('entity.projectId = :id', { id: projectId })
          .andWhere('entity.taskCode IS NOT NULL')
          .withDeleted()
          .getCount();
        currentCount = count;
      }

      for (const data of datas) {
        try {

          const workspaceGroup = await getRepository(WorkspaceGroupEntity).findOneOrFail(data.workspaceGroupId);
          if (data.category === CategoryType.AllForm) {

            if (workspaceGroup?.name === 'Site Diary' || workspaceGroup?.name === 'Ungroup Documents') {
              const getTotalParent = await this.getParentCountAmount(data);
              groupCodeCount = getTotalParent + 1;
            } else {
              const getTotalChildren = await this.getChildrenCountAmount(data);
              groupCodeCount = getTotalChildren;
            }
          }

          const entity = repository.create({
            ...data,
            createdAt: new Date(data?.created_at ?? new Date()).toISOString(),
            updatedAt: new Date(data?.updated_at ?? new Date()).toISOString(),
            projectId: parseInt(data.projectId, 10),
            addedBy: userId,
            ...(repository.metadata.name === "ProjectDocumentEntity" &&
              data.category === CategoryType.AllForm && {
              allFormCode: currentCount + 1,
              groupCode: groupCodeCount,
            }),
            ...(repository.metadata.name === "TaskEntity" && {
              taskCode: currentCount + 1
            })
          });

          if (repository.metadata.name === "TaskEntity") {
            if (data.assignees && data.assignees.length > 0) {
              entity.assignees = data.assignees.map(id => ({ id }));
            }
            if (data.copies && data.copies.length > 0) {
              entity.copies = data.copies.map(id => ({ id }));
            }
          }

          if (repository.metadata.name === "TaskEntity") {
            if (data.assignees && data.assignees.length > 0) {
              entity.assignees = data.assignees.map(id => ({ id }));
            }
            if (data.copies && data.copies.length > 0) {
              entity.copies = data.copies.map(id => ({ id }));
            }
          }

          const savedEntity = await repository.save(entity);

          results.push({
            ...savedEntity,
            localId: data.localId,
            remoteId: savedEntity.id,
            localCurrentUserId: data.localCurrentUserId,
            localProjectDocumentId: data.localProjectDocumentId,
            localGroupId: data.localGroupId,
          });

        } catch (e) {
          error.push({ id: data.localId, type: 'created' });
          console.error(`Error creating entity with localId ${data.localId}:`, e);
        }
      }

      // if (["ProjectGroupEntity", "ProjectDocumentEntity", "WorkspaceGroupEntity"].includes(repository.metadata.name)) {
      //   const foreignKey =
      //     ["ProjectGroupEntity", "WorkspaceGroupEntity"].includes(repository.metadata.name)
      //       ? 'localGroupId'
      //       : 'localRemoteId';

      //   for (const entity of results) {
      //     const tempData = Array.from(localIdToEntityMap.entries()).find(([_, val]) => 
      //       Array.isArray(val) && val.some(item => item === entity)
      //     );
      //     console.log(tempData, 'tempData');
      //     if (tempData && tempData[0]) {
      //       const data = datas.find(d => d.localId === tempData[0]);
      //       if (data && data[foreignKey]) {
      //         const parentEntity = localIdToEntityMap.get(data[foreignKey]);
      //         if (parentEntity) {
      //           if (repository.metadata.name === "ProjectGroupEntity") {
      //             entity.projectGroupId = parentEntity.id;
      //           } else if (repository.metadata.name === "WorkspaceGroupEntity") {
      //             entity.workspaceGroupId = parentEntity.id;
      //           } else {
      //             entity.projectDocumentId = parentEntity.id;
      //           }
      //         }
      //       }
      //     }

      //     await repository.save(entity);
      //   }
      // }

      const resultWithLocalIds = results.map((entity, index) => {
        const originalData = datas[index];

        return {
          ...entity,
          id: originalData.localId,
          remoteId: entity.id,
          server_created_at: entity.createdAt,
          localId: originalData.localId,
          ...(repository.metadata.name === "ProjectDocumentEntity" && {
            localCurrentUserId: originalData.localCurrentUserId
          }),
          ...(repository.metadata.name === "RequestForSignatureEntity" && {
            localProjectDocumentId: originalData.localProjectDocumentId
          }),
          ...((repository.metadata.name === "ProjectGroupEntity" || repository.metadata.name === "WorkspaceGroupEntity") && {
            localGroupId: originalData.localGroupId
          })
        };
      });

      return {
        result: resultWithLocalIds,
        error
      };
    } catch (error) {
      console.log(error, 'Error in bulkCreate');
      throw error;
    }
  }

  async bulkUpdateInBatches(repository: Repository<any>, updates: any[]): Promise<any[]> {
    let result: any[] = [];
    for (let i = 0; i < updates.length; i += this.batchSize) {
      const batch = updates.slice(i, i + this.batchSize);
      const batchResult = await this.bulkUpdateSelectiveFields(repository, batch);
      result = result.concat(batchResult);
    }

    const resultWithLocalIds = result.map((updatedEntity, index) => ({
      ...updatedEntity,
      remoteId: updatedEntity.id,
      id: updates[index].localId
    }));

    return resultWithLocalIds;
  }

  async bulkUpdateSelectiveFields(repository: Repository<any>, updates: any[]) {
    try {
      const idsToUpdate = updates.map(update => update.remoteId);
      const entitiesToUpdate = await repository.findByIds(idsToUpdate);
      const updatesToApply = updates.filter(update => {
        const currentEntity = entitiesToUpdate.find(entity => entity.id === update.remoteId);
        if (!currentEntity) {
          return false;
        }

        const updatedAt = moment.utc(update.updated_at);
        const currentUpdatedAt = moment.utc(currentEntity.updatedAt);

        const isUpdateNewer = updatedAt.isAfter(currentUpdatedAt);
        if (isUpdateNewer) {
          return true;
        } else {
          console.log(`Update is not newer for remoteId ${update.remoteId}`);
          return false;
        }
      });

      const entitiesToSave: any[] = [];
      const resultWithLocalCurrentUserId = [];

      for (const updateData of updatesToApply) {
        const entity = entitiesToUpdate.find(entity => entity.id === updateData.remoteId);
        if (entity) {
          const fieldsToUpdate = updateData._changed.split(',');
          fieldsToUpdate.forEach(field => {
            let targetField = field;
            let fieldValue = updateData[field];

            if (field === 'updated_at' || field === 'created_at') {
              targetField = field === 'updated_at' ? 'updatedAt' : 'createdAt';
              if (fieldValue) {
                fieldValue = moment.utc(fieldValue).toISOString();
              }
            }

            if (updateData.hasOwnProperty(field)) {
              entity[targetField] = fieldValue;
            }
          });

          if (repository === getRepository(TaskEntity)) {
            const assignees = updateData.assignees;
            const copies = updateData.copies;

            if (assignees && assignees.length > 0) {
              const assigneeEntities = await getRepository(ProjectUserEntity).findByIds(assignees);
              entity.assignees = assigneeEntities;
              console.log('Assignees set for task:', assigneeEntities);
            }

            if (copies && copies.length > 0) {
              const copyEntities = await getRepository(ProjectUserEntity).findByIds(copies);
              entity.copies = copyEntities;
              console.log('Copies set for task:', copyEntities);
            }
          }

          entitiesToSave.push(entity);
          resultWithLocalCurrentUserId.push({
            ...entity,
            ...(repository.metadata.name === "ProjectDocumentEntity" && { localCurrentUserId: updateData.localCurrentUserId }),
            ...(repository.metadata.name === "RequestForSignatureEntity" && { localProjectDocumentId: updateData.localProjectDocumentId, localId: updateData.localId })
          });
        }
      }

      if (entitiesToSave.length > 0) {
        await repository.save(entitiesToSave);
      } else {
        console.log('No entities to save');
      }

      return resultWithLocalCurrentUserId;
    } catch (error) {
      console.error('Error in bulkUpdateSelectiveFields:', error);
      throw error;
    }
  }

  private async bulkDelete(repository: Repository<any>, ids: number[]) {
    try {
      const entities = await repository.findByIds(ids);

      for (const entity of entities) {
        await repository.softRemove(entity);
      }
    } catch (error) {
      console.log(error, 'Error during bulk delete');
    }
  }

  private async configureRelations(changes: any, entityManager: EntityManager): Promise<any> {
    try {

      const createdSignatures = [
        ...(changes?.request_for_signatures?.created ?? []),
        ...(changes?.request_for_signatures?.updated ?? []),
      ];
      const projectDocuments = changes?.project_documents ?? { created: [], updated: [] };
      const updatedDocuments = [...projectDocuments.created, ...projectDocuments.updated];


      if (!createdSignatures.length || !updatedDocuments.length) {
        console.log('No signatures or documents to process. Exiting early.');
        return changes;
      }

      const signatureMap = new Map(
        createdSignatures.map(sig => [sig.localId, sig.remoteId])
      );

      const updates = updatedDocuments
        .filter(doc => doc.currentUserId === null && doc.localCurrentUserId)
        .map(doc => {
          const correspondingRemoteId = signatureMap.get(doc.localCurrentUserId);
          if (correspondingRemoteId) {
            return { id: doc.remoteId, currentUserId: correspondingRemoteId };
          }
          return null;
        })
        .filter(Boolean);

      if (updates.length > 0) {
        const documentIds = updates.map(update => update.id);
        const projectDocsToUpdate = await entityManager.findByIds(ProjectDocumentEntity, documentIds);

        for (const document of projectDocsToUpdate) {
          const update = updates.find(u => u.id === document.id);
          if (update && document.currentUserId !== update.currentUserId) {
            document.currentUserId = update.currentUserId;
          } else {
          }
        }

        if (projectDocsToUpdate.length > 0) {
          await entityManager.save(projectDocsToUpdate);
        } else {
          console.log('No documents to save');
        }
      } else {
        console.log('No updates to apply');
      }

      const updateChangesArray = (docs: any[], type: 'created' | 'updated') => {
        changes.project_documents[type] = docs.map(doc => {
          const correspondingRemoteId = signatureMap.get(doc.localCurrentUserId);
          if (correspondingRemoteId) {
            return { ...doc, currentUserId: correspondingRemoteId };
          } else {
            return doc;
          }
        });
      };

      updateChangesArray(projectDocuments.created, 'created');
      updateChangesArray(projectDocuments.updated, 'updated');

      return changes;
    } catch (error) {
      console.error('Error in configureRelations:', error);
      throw error; // Propagate error to calling function
    }
  }

  private async configureGroupRelations(changes: SyncDataInput, entityManager: EntityManager): Promise<any> {
    try {
      if (!changes?.project_groups?.created?.length && !changes?.workspace_groups?.created?.length) {
        return changes;
      }

      const groupTypes = ['project_groups', 'workspace_groups'];

      for (const groupType of groupTypes) {
        const createdGroups = changes[groupType]?.created ?? [];

        if (!createdGroups.length) {
          continue;
        }

        // Create a map of localId to remoteId
        const groupMap = new Map(
          createdGroups.map(group => [group.localId, group.remoteId])
        );

        // Update localGroupId to use remoteId
        const updatedGroups = createdGroups.map(group => {
          if (group.localGroupId) {
            const correspondingRemoteId = groupMap.get(group.localGroupId);
            if (correspondingRemoteId) {
              if (groupType === 'project_groups') {
                (group as any).projectGroupId = correspondingRemoteId; // Explicitly cast to any for type safety
              } else if (groupType === 'workspace_groups') {
                (group as any).workspaceGroupId = correspondingRemoteId; // Explicitly cast to any for type safety
              }
            } else {
              console.warn(
                `No matching remoteId found for localGroupId: ${group.localGroupId} in ${groupType}`
              );
            }
          }
          return group;
        });

        changes[groupType].created = updatedGroups;

        // Retrieve and update existing entities
        const entityIds = updatedGroups.map(group => group.remoteId).filter(Boolean);

        if (entityIds.length > 0) {
          let entitiesToUpdate: (ProjectGroupEntity | WorkspaceGroupEntity)[] = [];

          if (groupType === 'project_groups') {
            entitiesToUpdate = await entityManager.findByIds(ProjectGroupEntity, entityIds);
          } else if (groupType === 'workspace_groups') {
            entitiesToUpdate = await entityManager.findByIds(WorkspaceGroupEntity, entityIds);
          }

          // Update the existing entities with the updated groups
          for (const entity of entitiesToUpdate) {
            const updatedGroup = updatedGroups.find(group => group.remoteId === entity.id);
            if (updatedGroup) {
              if (groupType === 'project_groups' && 'projectGroupId' in entity) {
                entity.projectGroupId = updatedGroup.projectGroupId || null;
              } else if (groupType === 'workspace_groups' && 'workspaceGroupId' in entity) {
                entity.workspaceGroupId = updatedGroup.workspaceGroupId || null;
              }
            }
          }

          // Save the updated entities back to the database
          await entityManager.save(entitiesToUpdate);
          console.log(`Updated ${entitiesToUpdate.length} entities for ${groupType}.`);
        }
      }

      return changes;
    } catch (error) {
      console.error('Error in configureGroupRelations:', error);
      throw new Error(`Error in configureGroupRelations: ${error.message}`);
    }
  }

  private async getParentCountAmount(data: any) {
    const projectDocuments = await getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .select(['projectDocument.id', 'projectDocument.groupCode'])
      .where('projectDocument.projectId = :projectId', { projectId: data.projectId })
      .andWhere('projectDocument.status != :status', { status: ProjectDocumentStatus.Draft })
      .andWhere('projectDocument.workspaceGroupId = :workspaceGroupId', {
        workspaceGroupId: data.workspaceGroupId
      })
      // .withDeleted()
      .getMany();

    // return projectDocuments;
    const groupCodes = projectDocuments.map((document: any) => parseInt(document.groupCode)).filter(code => !isNaN(code));
    if (groupCodes.length === 0) {
      return 0;
    }
    const highestGroupCode = Math.max(...groupCodes);
    return highestGroupCode;
  }
  private async getChildrenCountAmount(data: any) {
    const workspaceGroup = await getRepository(WorkspaceGroupEntity)
      .createQueryBuilder('workspaceGroup')
      .select(['workspaceGroup.id', 'workspaceGroup.workspaceGroupId'])
      .where('workspaceGroup.id = :id', { id: data.workspaceGroupId })
      .leftJoin('workspaceGroup.parent', 'parent')
      .addSelect(['parent.id'])
      .leftJoin('parent.children', 'children')
      .addSelect(['children.id'])
      .leftJoin('children.documents', 'documents')
      .addSelect(['documents.id', 'documents.status', 'documents.workspaceGroupId', 'documents.groupCode'])
      .andHaving('documents.status != :status', { status: ProjectDocumentStatus.Draft })
      .getOne();

    if (!workspaceGroup) return 1;

    const docs = workspaceGroup.parent?.children.map(child => child.documents);
    const flatDocs = docs.flat();
    const groupCodes = flatDocs.map((document: any) => parseInt(document.groupCode, 10)).filter(code => !isNaN(code));
    if (groupCodes.length === 0) {
      return 1;
    }
    const highestGroupCode = Math.max(...groupCodes);
    const nextGroupCode = highestGroupCode + 1;
    return nextGroupCode;
  }
}
