import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { UseUploadFilePipe } from 'src/pipes/gql-upload.pipe';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { EmailAssetEntity } from './entity/email-asset.entity';
import { EmailAssetDto, CreateEmailAssetInputDTO } from './dto/email-asset.gql.dto';
import { EmailAssetService } from './email-asset.services'
import { EmailAssetResolver } from './email-asset.resolver';
import { EmailAssetSubscriber } from './email-asset.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([EmailAssetEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: EmailAssetDto,
          EntityClass: EmailAssetEntity,
          CreateDTOClass: CreateEmailAssetInputDTO,
          decorators: [UseUploadFilePipe()],
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [EmailAssetService, EmailAssetResolver, EmailAssetSubscriber]
    }),
    IntegrationModule
  ],
  providers:[EmailAssetSubscriber]
})
export class EmailAssetModule {}
