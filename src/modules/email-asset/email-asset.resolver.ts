import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { ResolveField, Resolver, Parent } from '@nestjs/graphql';
import { EmailAssetDto } from './dto/email-asset.gql.dto';
import { EmailAssetService } from './email-asset.services';

@UseGuards(GqlAuthGuard)
@Resolver(() => EmailAssetDto)
export class EmailAssetResolver {
  constructor(private readonly emailAssetService: EmailAssetService) {}

  @ResolveField('url', () => String)
  async url(@Parent() parent: EmailAssetDto) {
    return await this.emailAssetService.getPresignedUrl(parent);
  }
}
