import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class EmailAssetService extends TypeOrmQueryService<EmailAssetEntity> {
  constructor(
    @InjectRepository(EmailAssetEntity)
    private attachmentRepo: Repository<EmailAssetEntity>,
    private tMOneService: TMOneService
  ) {
    // pass the use soft delete option to the service.
    super(attachmentRepo, { useSoftDelete: true });
  }

  async getPresignedUrlFromContentId(emailId: number, cid: string) {
    try {
      const emailAsset = await this.attachmentRepo.findOne({where: { contentId: cid, emailId }})

      return this.getPresignedUrl(emailAsset)
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }

  async getPresignedUrl(emailAsset: EmailAssetEntity) {
    try {
      const signedUrl = await this.tMOneService.getPresignedUrl(emailAsset.assetKey);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
