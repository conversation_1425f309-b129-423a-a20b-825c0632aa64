import { defaultQueryOptions } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType } from '@nestjs/graphql';
import { EmailAssetEntity } from '../entity/email-asset.entity';

@ObjectType('EmailAsset')
@QueryOptions({ ...defaultQueryOptions })
export class EmailAssetDto extends EmailAssetEntity {}

@InputType()
export class CreateEmailAssetInputDTO {
  @IDField(() => ID) emailId: number;
  assetKey: string;
  assetType: string;
  fileName: string;
}
