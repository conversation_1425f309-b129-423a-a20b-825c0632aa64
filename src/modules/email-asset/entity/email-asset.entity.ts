import { BaseEntity } from '@modules/base/base';
import { Entity, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, Field } from '@nestjs/graphql';
import { EmailEntity } from '@modules/email/entity/email.entity';
import { EmailAssetType } from '@constants';

@ObjectType()
@Entity('email_assets')
export class EmailAssetEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  emailId: number;

  @Field()
  @Column('varchar')
  assetKey: string;

  @Field()
  @Column('enum', { enum: EmailAssetType, nullable: true })
  assetType: EmailAssetType;

  @Field()
  @Column('varchar',{ nullable: true })
  contentId: string;

  @Field()
  @Column('varchar',{ nullable: true })
  rawMetadata: string;

  @Field()
  @Column('varchar')
  name: string;

  @Field()
  @Column('varchar')
  fileExtension: string;

  /* -------------------------------- Relations ------------------------------- */

  @ManyToOne(() => EmailEntity, email => email.assets)
  @JoinColumn({ name: 'emailId' })
  email: EmailEntity;
}
