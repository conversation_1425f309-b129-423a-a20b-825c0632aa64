import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber } from 'typeorm';
import { FileService } from '@modules/integration/file.service';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';

@Injectable()
@EventSubscriber()
export class EmailAssetSubscriber implements EntitySubscriberInterface<EmailAssetEntity> {
  constructor(connection: Connection,      
    private fileService: FileService,
  ) {
    
    connection.subscribers.push(this);
  }

  listenTo() {
    return EmailAssetEntity;
  }

}