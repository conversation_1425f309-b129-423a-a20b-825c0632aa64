import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber
} from 'typeorm';
import { DrawingLinkCommentEntity } from './entity/drawing-link-comment.entity';

@Injectable()
@EventSubscriber()
export class DrawingLinkCommentSubscriber implements EntitySubscriberInterface<DrawingLinkCommentEntity> {
  constructor(
    connection: Connection,
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DrawingLinkCommentEntity;
  }

}
