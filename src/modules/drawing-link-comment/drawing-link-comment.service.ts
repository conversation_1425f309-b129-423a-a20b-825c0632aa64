import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {  DrawingLinkCommentEntity } from './entity/drawing-link-comment.entity';

@Injectable()
export class DrawingLinkCommentService extends TypeOrmQueryService<DrawingLinkCommentEntity> {
  constructor(
    @InjectRepository(DrawingLinkCommentEntity)
    private drawingLinkCommentRepo: Repository<DrawingLinkCommentEntity>,    
  ) {
    super(drawingLinkCommentRepo, { useSoftDelete: true });
  }
}
