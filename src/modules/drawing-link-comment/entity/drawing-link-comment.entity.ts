import { BaseEntity } from '@modules/base/base';
import { DrawingLinksEntity } from '@modules/drawing-links/entity/drawing-links.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('drawing_link_comment')
export class DrawingLinkCommentEntity extends BaseEntity {

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  message: string;

  @FilterableField({ nullable: false })
  @Column({ unsigned: true, nullable: false })
  drawingLinkId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  // Relations
  @ManyToOne(() => DrawingLinksEntity, drawingLink => drawingLink.drawingLinkComment)
  @JoinColumn({ name: 'drawingLinkId' })
  drawingLink: DrawingLinksEntity;

  @ManyToOne(() => UserEntity, user => user.drawingLinkComments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}