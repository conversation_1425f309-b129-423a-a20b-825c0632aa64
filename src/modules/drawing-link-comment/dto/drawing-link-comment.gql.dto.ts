import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, CreateOneInputType, FilterableRelation, QueryOptions } from '@nestjs-query/query-graphql';
import { ObjectType } from '@nestjs/graphql';
import { DrawingLinkCommentEntity } from '../entity/drawing-link-comment.entity';
import { GqlContext } from '@types';
import { UserEntity } from '@modules/user/entity/user.entity';
import { relationOption } from '@constants/query.constant';


@ObjectType('DrawingLinkComment')
@FilterableRelation('user', () => UserEntity, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
@BeforeCreateOne((instance: CreateOneInputType<DrawingLinkCommentDto>, context: GqlContext) => {
    const createdBy = context.req.user.id;
    instance.input.createdBy = createdBy;
    return instance;
  })
export class DrawingLinkCommentDto extends DrawingLinkCommentEntity {}

// @InputType()
// export class CreateDrawingLinksInputDTO extends PartialType(DrawingLinkCommentDto) {}

// @InputType()
// export class UpdateDrawingLinksInputDTO extends PartialType(CreateDrawingLinksInputDTO) {}