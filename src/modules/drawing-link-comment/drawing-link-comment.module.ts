import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { DrawingLinkCommentService } from './drawing-link-comment.service';
import {  DrawingLinkCommentDto } from './dto/drawing-link-comment.gql.dto';
import { DrawingLinkCommentEntity } from './entity/drawing-link-comment.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { DrawingLinkCommentSubscriber } from './drawing-link-comment';


@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([DrawingLinkCommentEntity]), IntegrationModule],
      resolvers: [
        {          
          DTOClass: DrawingLinkCommentDto,
          EntityClass: DrawingLinkCommentEntity,
          // CreateDTOClass: CreateDrawingLinksInputDTO,          
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [DrawingLinkCommentService, DrawingLinkCommentEntity, DrawingLinkCommentSubscriber]
    })
  ]
})
export class DrawingLinkCommentModule {}
