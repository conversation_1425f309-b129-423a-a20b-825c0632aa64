import { BaseEntity } from '@modules/base/base';
import { EventEntity } from '@modules/event/entity/event.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn, OneToOne } from 'typeorm';

@ObjectType()
@Entity('event_assignee')
export class EventAssigneeEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  eventId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  assigneeId: number;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => EventEntity, event => event.eventAssignees, {
    orphanedRowAction: 'delete'
  })
  @JoinColumn({ name: 'eventId' })
  eventAssignees: EventEntity;

  @ManyToOne(() => UserEntity, user => user.eventAssignee)
  @JoinColumn({ name: 'assigneeId' })
  user: UserEntity;
}
