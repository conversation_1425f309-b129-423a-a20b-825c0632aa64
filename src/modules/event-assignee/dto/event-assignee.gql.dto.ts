import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, QueryOptions } from '@nestjs-query/query-graphql';
import { ObjectType, InputType } from '@nestjs/graphql';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { EventAssigneeEntity } from '../entity/event-assignee.entity';

@ObjectType('EventAssignee')
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class EventAssigneeDto extends EventAssigneeEntity { }

@InputType()
export class CreateEventAssigneeInputDTO {
  assigneeId: number;
  eventId: number;
}