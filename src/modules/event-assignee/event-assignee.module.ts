import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { EventAssigneeEntity } from './entity/event-assignee.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { EventAssigneeDto } from './dto/event-assignee.gql.dto';
import { EventAssigneeService } from './event-assignee.service';
import { EventAssigneeSubscriber } from './event-assignee.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([EventAssigneeEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: EventAssigneeService,
          DTOClass: EventAssigneeDto,
          EntityClass: EventAssigneeEntity,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [EventAssigneeService, EventAssigneeSubscriber]
    })
  ]
})
export class EventAssigneeModule {}
