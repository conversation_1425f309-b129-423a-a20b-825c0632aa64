import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventAssigneeEntity } from './entity/event-assignee.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class EventAssigneeService extends TypeOrmQueryService<EventAssigneeEntity> {
  constructor(
    @InjectRepository(EventAssigneeEntity)
    private eventRepo: Repository<EventAssigneeEntity>,
  ) {
    super(eventRepo, { useSoftDelete: true });
  }
}
