import { UserEntity } from '@modules/user/entity/user.entity';
import { Injectable } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, Connection, InsertEvent } from 'typeorm';
import { EventAssigneeEntity } from './entity/event-assignee.entity';
import { getErrorMessage } from '@common/error';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import moment from 'moment';
import { EventEntity } from '@modules/event/entity/event.entity';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class EventAssigneeSubscriber implements EntitySubscriberInterface<EventAssigneeEntity> {
  constructor(connection: Connection, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return EventAssigneeEntity;
  }

  async afterInsert(event: InsertEvent<EventAssigneeEntity>) {
    try {
      const { entity } = event;

      const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.assigneeId });
      
      const createdEvent = await event.manager.getRepository(EventEntity).findOne({ id: entity.eventId  });
      const owner = await event.manager.getRepository(UserEntity).findOne({ id: createdEvent.createdBy });

      const project = await event.manager
        .getRepository(ProjectEntity)
        .findOne({ id: createdEvent.projectId }, { relations: ['company'] });

      const mobileLink = 'dashboard/calendar';
      const link = `/projects/dashboard?size=10&page=1&projectId=${project?.id}&companyId=${project?.company.id}&showEvent=true&eventId=${entity?.eventId}`;
      const body = `${owner?.name} assigned you on ${createdEvent?.title}'s event`;

      return this.EventNotification(user, project, link, mobileLink, owner, body, project?.company, createdEvent?.title);
    } catch (e) {
      getErrorMessage(e, 'EventsSubscriber', 'afterInsert');
    }
  }

  EventNotification(user, project, link, mobileLink, owner, body, companyName = null, eventName) {
    const header = '📅 Calendar - New Event';
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      header,
      event: 'schedule-event-assign',
      company: companyName.name,
      title: project.title,
      body: body,
      head: project.title,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: user.name
      }
    };

    this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          android: {
            priority: 'high'
          },
          data: {
            link: mobileLink.toString(),
            projectId: project?.id.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }
}
