import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { TaskEntity } from './entity/task.entity';
import { AuditLogActionType, AuditLogModuleType, TaskStatusType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import * as _ from 'lodash';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import moment from 'moment';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import { TaskService } from './task.service';

const { APP_URI } = process.env;

@Injectable()
@EventSubscriber()
export class TaskSubscriber implements EntitySubscriberInterface<TaskEntity> {
  constructor(
    connection: Connection,
    private mailgunService: MailgunService,
    private novuService: NovuService,
    private taskService: TaskService
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TaskEntity;
  }

  async beforeInsert(event: InsertEvent<TaskEntity>) {
    const { entity } = event;
    // entity.taskCode = await nanoid(10)
    if (entity.dueDate != null) {

      if (!entity.taskCode) {
        const project = await event.manager
          .getRepository(TaskEntity)
          .createQueryBuilder('entity')
          .withDeleted()
          .where('entity.projectId = :id', { id: entity.projectId })
          .andWhere('entity.taskCode IS NOT NULL')
          .getCount();
        entity.taskCode = project + 1;
      }

      if (!entity.groupId) {
        const taskGroup = await event.manager
          .getRepository(ProjectGroupEntity)
          .findOne({ projectId: entity.projectId, title: 'Ungroup Tasks' });
        entity.groupId = taskGroup?.id;
      }
      // .leftJoinAndSelect('entity.projectId', 'projectid')
      // .loadRelationCountAndMap('entity.projectIdCount', 'entity.projectId')
      // .getCount()
    }
  }

  async afterInsert(event: InsertEvent<TaskEntity>) {
    try {
      const { entity } = event;

      const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });

      const msg = user.name + ' created ' + entity.title;

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.ownerId,
        projectId: entity.projectId,
        taskId: entity.id,
        resourceId: entity.id,
        module: AuditLogModuleType.Task,
        action: AuditLogActionType.Create,
        content: msg
      });

      await event.manager.getRepository(AuditLogEntity).save(auditLog);
    } catch (e) {
      getErrorMessage(e, 'TaskSubscriber', 'afterInsert');
    }
    // await event.manager.getRepository(TaskEntity).save(totalProjects)
  }

  async beforeUpdate(event: UpdateEvent<TaskEntity>) {
    try {
      const { entity, databaseEntity } = event;

      // AUDIT LOG WORKSPACE DESCRIPTION
      //  if(entity.description) {
      const item = await event.manager.getRepository(TaskEntity).findOne({ id: entity.id });
      if (item) {
        if (entity.description !== item.description) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });

          const msg = user.name + ' editted ' + entity.title + ' description';

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: entity.ownerId,
            projectId: entity.projectId,
            taskId: entity.id,
            resourceId: entity.id,
            module: AuditLogModuleType.Task,
            action: AuditLogActionType.Update,
            content: msg
          });
          await auditLog.save();
        }

        if (
          moment(entity.dueDate).format('YYYY-MM-DD hh:mm:ii').toString() !==
          moment(item.dueDate).format('YYYY-MM-DD hh:mm:ii').toString()
        ) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });

          const msg = user.name + ' changed due date to ' + moment(entity.dueDate).format('YYYY-MM-DD');

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: entity.ownerId,
            projectId: entity.projectId,
            taskId: entity.id,
            resourceId: entity.id,
            module: AuditLogModuleType.Task,
            action: AuditLogActionType.Update,
            content: msg
          });
          await auditLog.save();
        }

        // console.log('entity to before: ', entity);
        if (entity.status === TaskStatusType.Hold || entity.status === TaskStatusType.Completed) {
          await this.taskService.changeProposedStatus(entity.id, entity.status);
          entity.proposedStatus = entity.status;
          entity.status = databaseEntity.status;
        }
        // console.log('entity to update: ', entity);
      }
    } catch (e) {
      getErrorMessage(e, 'TaskSubscriber', 'beforeUpdate');
    }
  }

  async afterUpdate(event: UpdateEvent<TaskEntity>) {
    try {
      const { entity } = event;
      const notStatus = !!event.updatedColumns.find(column => column.propertyName !== 'status');
      const notReminder = !!event.updatedColumns.find(column => column.propertyName !== 'isSentBeforeOneDay');
      const notReminder2 = !!event.updatedColumns.find(column => column.propertyName !== 'isSentBeforeOneHour');
      const groupUpdated = !!event.updatedColumns.find(column => column.propertyName === 'groupId');
      const isProposedStatus = !!event.updatedColumns.find(column => column.propertyName === 'proposedStatus');
      
      const task = await event.manager.getRepository(TaskEntity).findOne({ id: entity.id });

      if (!task) return;
      const assignees = (await task.assignees) ?? [];
      const copies = (await task.copies) ?? [];
      const taskOwner = { userId: task.ownerId };
      const users = _.uniqBy([...assignees, ...copies, taskOwner], 'userId');
      const mobileLink = `task/1/${task.id}/task`;
      const project = await event.manager.getRepository(ProjectEntity).findOne({ id: task.projectId });
      if (!project) return;
      // const company = await event.manager.findOne(UserEntity, { id: project.companyId });
      const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });
      const link = `/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${companyName.id}`;
      const header = `📢 Task - Status Update`;

      const updatedBy = await event.manager.getRepository(UserEntity).findOne({ id: entity.updatedBy });
      if (
        notStatus &&
        event.databaseEntity.status === TaskStatusType.Open &&
        task.status === TaskStatusType.InProgress
      ) {
        await Promise.all(
          users.map(async assignee => {
            const user = await event.manager.getRepository(UserEntity).findOne({ id: assignee.userId });
            this.statusNotification(
              user,
              project,
              task,
              link,
              mobileLink,
              updatedBy,
              `updated ${task.title}'s to In Progress`,
              companyName,
              header,
              'task-status-update'
            );
          })
        );
      }

      // Send email if task was completed
      if (!notStatus && task.status !== TaskStatusType.InProgress) {
        await Promise.all(
          users.map(async assignee => {
            const user = await event.manager.getRepository(UserEntity).findOne({ id: assignee.userId });
            if (task.status === TaskStatusType.Completed) {
              this.statusNotification(
                user,
                project,
                task,
                link,
                mobileLink,
                updatedBy,
                `updated ${task.title}'s status to Completed`,
                companyName,
                header,
                'task-status-update'
              );
            } else if (task.status === TaskStatusType.Overdue) {
              this.statusNotification(
                user,
                project,
                task,
                link,
                mobileLink,
                updatedBy,
                `updated ${task.title}'s title to Overdue`,
                companyName,
                header,
                'task-status-update'
              );
            }
          })
        );
      }
      if (!notStatus && task.status !== TaskStatusType.Completed && task.status !== TaskStatusType.Overdue) {
        await Promise.all(
          users.map(async assignee => {
            const user = await event.manager.getRepository(UserEntity).findOne({ id: assignee.userId });
            this.statusNotification(
              user,
              project,
              task,
              link,
              mobileLink,
              updatedBy,
              `updated ${task.title}'s status to Hold`,
              companyName,
              header,
              'task-status-update'
            );
          })
        );
      }

      const content = (): string => {
        if (task.status === TaskStatusType.Overdue) return 'Task is overdue';
        if (task.status === TaskStatusType.Completed) return 'Task is closed';
        return null;
      };

      // const auditLog = event.manager.getRepository(AuditLogEntity).create({
      //   userId: entity.ownerId ?? task.ownerId,
      //   projectId: entity.projectId ?? task.projectId,
      //   taskId: entity.id ?? task.id,
      //   resourceId: entity.id ?? task.id,
      //   module: AuditLogModuleType.Task,
      //   action: AuditLogActionType.Update,
      //   content: content(),
      // });
      // await event.manager.getRepository(AuditLogEntity).save(auditLog);

      if (!notStatus) {
        // AUDIT LOG FOR TASK STATUS UPDATED TO IN REVIEW
        if (task.status == TaskStatusType.InProgress) {
          const msg = task.title + ' In Progress';

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: task.ownerId,
            projectId: project.id,
            // taskId: task.id,
            resourceId: task.id,
            module: AuditLogModuleType.Task,
            action: AuditLogActionType.UpdateStatus,
            content: msg
          });
          await auditLog.save();
        }
        // AUDIT LOG FOR TASK STATUS UPDATED TO HOLD / COMPLETED
        else if (task.status == TaskStatusType.Hold || task.status == TaskStatusType.Completed) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: task.ownerId });

          const msg = user.name + ' updated ' + task.title + ' status to ' + task.status;

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: task.ownerId,
            projectId: project.id,
            // taskId: task.id,
            resourceId: task.id,
            module: AuditLogModuleType.Task,
            action: AuditLogActionType.UpdateStatus,
            content: msg
          });
          await auditLog.save();
        }
      }

      if (groupUpdated) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: task.ownerId });
        const group = await event.manager.getRepository(ProjectGroupEntity).findOne({ id: task.groupId });

        const msg = user.name + ' changed ' + task.title + ' group to ' + group.title;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: task.ownerId,
          projectId: task.projectId,
          // taskId: task.id,
          resourceId: task.id,
          module: AuditLogModuleType.Task,
          action: AuditLogActionType.UpdateGroup,
          content: msg
        });
        await auditLog.save();
      }

      if (isProposedStatus) {
        // send the notification to the owner of the task
        const proposedBy = await event.manager.getRepository(UserEntity).findOne({ id: task.updatedBy });
        const owner = await event.manager.getRepository(UserEntity).findOne({ id: task.ownerId });
        const header = `📢 Task - Proposed Status`;

        if (updatedBy.id == owner.id) return;

        return this.statusNotification(
          owner,
          project,
          task,
          link,
          mobileLink,
          proposedBy,
          `propose to ${task.proposedStatus} task: ${task.title}`,
          companyName,
          header,
          'task-proposed-status'
        );
      }

    } catch (error) {
      getErrorMessage(error, 'TaskSubscriber', 'afterUpdate');
    }
  }

  statusNotification(
    user,
    project,
    task,
    link,
    mobileLink,
    owner,
    message,
    companyName = null,
    header,
    event
  ) {
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      event,
      header: header,
      company: companyName?.name,
      title: project.title,
      head: owner.name,
      body: message ?? task.status,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: user.name
      }
    };

    this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          data: {
            link: mobileLink.toString(),
            projectId: task.projectId.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }
}
