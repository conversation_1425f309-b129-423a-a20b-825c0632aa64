import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { TaskEntity } from './entity/task.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { AuditLogActionType, AuditLogModuleType, ProposeAction, TaskStatusType } from '@constants';
import moment from 'moment-timezone';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import {
  AssignTaskAttachmentInputDTO,
  AssignTaskInputDTO,
  AssignTaskMediaInputDTO,
  GenerateMemoInputDTO,
  ReceiveMemoDTO,
  TaskAssigneeAuditLogInputDTO,
  TaskDeletedQuery,
  TaskDto,
  UpdateTaskStatusInputDTO
} from './dto/task.gql.dto';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import _ from 'lodash';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { AuthData } from '@types';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import puppeteer from 'puppeteer';
import { FileService } from '@modules/integration/file.service';
import { FileUpload } from 'graphql-upload';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import axios from 'axios';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { UserService } from '@modules/user/user.service';
import { CompanyService } from '@modules/company/company.service';

const { APP_URI } = process.env;

@Injectable()
export class TaskService extends TypeOrmQueryService<TaskEntity> {
  constructor(
    @InjectRepository(TaskEntity)
    public taskRepo: Repository<TaskEntity>,
    @InjectRepository(TasksAttachmentEntity)
    public taskAttachmentRepo: Repository<TasksAttachmentEntity>,
    @InjectRepository(TasksMediaEntity)
    public taskMediaRepo: Repository<TasksMediaEntity>,
    @InjectRepository(ProjectUserEntity)
    public projectUserRepo: Repository<ProjectUserEntity>,
    private novuService: NovuService,
    private fileService: FileService,
    private tmOneService: TMOneService,
    private userService: UserService,
    private companyService: CompanyService
  ) {
    super(taskRepo, { useSoftDelete: true });
  }

  async assignTask(input: AssignTaskInputDTO, userId: number, projectId: number) {
    try {
      const { taskId, projectUserIds } = input;
      // const projectUserId = projectUserIds.map((projectUserId) => {
      //   return projectUserId?.id;
      // });
      const task = await this.taskRepo.findOne({ id: taskId });
      const assigneeBeforeAndAfter = await this.assignProjectUserToTask(task, projectUserIds, userId);
      // const assigneeBeforeAndAfter = await this.assignProjectUserToTask(task, projectUserId);
      await this.createTaskAssigneeAuditLog(userId, projectId, taskId, assigneeBeforeAndAfter);

      return task;
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignTask');
    }
  }

  async assignCcTask(input: AssignTaskInputDTO, userId: number, projectId: number) {
    try {
      const { taskId, projectUserIds } = input;
      const task = await this.taskRepo.findOne({ id: taskId });
      const assigneeBeforeAndAfter = await this.assignProjectUserCcToTask(task, projectUserIds, userId);
      await this.createTaskAssigneeCcAuditLog(userId, projectId, taskId, assigneeBeforeAndAfter);
      return task;
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignCcTask');
    }
  }

  async assignAttachments(input: AssignTaskAttachmentInputDTO, userId: number, projectId: number) {
    try {
      const { taskId, attachments } = input;
      const entity = await this.taskRepo.findOne({ where: { id: taskId }, relations: ['attachments', 'medias'] });

      const removed = entity.attachments.filter(function (obj) {
        return !attachments.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (removed.length > 0) {
        const user = await getRepository(UserEntity).findOne({ id: userId });

        let names = '';
        removed.forEach((element, index) => {
          names = names + element.name;
          if (index != removed.length - 1) names = names + ', ';
        });
        const msg = user.name + ' deleted ' + names;

        const auditLog = getRepository(AuditLogEntity).create({
          userId: entity.ownerId,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Task,
          action: AuditLogActionType.AddAttachment,
          content: msg
        });
        await auditLog.save();

        await this.taskAttachmentRepo.softRemove(removed);
      }

      const added = attachments.filter(function (obj) {
        return !entity.attachments.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (added.length > 0) await this.taskAttachmentRepo.save(added);

      return entity;
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignAttachments');
    }
  }

  async assignMedias(input: AssignTaskMediaInputDTO, userId: number, projectId: number) {
    try {
      const { taskId, medias } = input;
      const entity = await this.taskRepo.findOne({ where: { id: taskId }, relations: ['medias'] });

      const removed = entity.medias.filter(function (obj) {
        return !medias.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;


      await this.taskMediaRepo.softRemove(removed);
      // }

      const added = medias.filter(function (obj) {
        return !entity.medias.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (added.length > 0) await this.taskMediaRepo.save(added);

      return entity;
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignMedias');
    }
  }

  //Filter Task based on Drawer
  async filterTask(data: any, projectId: number) {
    data.dueDate = moment(data.dueDate).format('YYYY-MM-DD');
    if (data.dueDate == 'Invalid date') data.dueDate = '';

    const searchResult = await this.taskRepo
      .createQueryBuilder('task')
      .leftJoin('task.group', 'group')
      .addSelect('group.title')
      .leftJoin('task.assignees', 'assignees')
      .addSelect('assignees.userId')
      .leftJoin('assignees.user', 'user')
      .addSelect(['user.name', 'user.avatar'])
      .where('task.projectId = :id', { id: projectId })
      .andWhere('task.title LIKE :title', { title: `%${data.title}%` })
      .andWhere('task.taskCode LIKE :taskCode', { taskCode: `%${data.taskCode}%` })
      .andWhere('task.status LIKE :status', { status: `%${data.status}%` });
    //
    if (data.assignedTo == '') {
    } else if (data.assignedTo != '') {
      searchResult.andWhere('user.name LIKE :name', { name: `%${data.assignedTo}%` });
    }

    if (data.group == '') {
    } else if (data.group != '') {
      searchResult.andWhere('group.title LIKE :group', { group: `%${data.group}%` });
    }

    if (data.dueDate == '') {
    } else if (data.dueDate != '') {
      searchResult.andWhere('DATE(task.dueDate) LIKE :date', { date: `%${data.dueDate}%` });
    }

    return searchResult.getMany();
  }

  // Assign Task to Project user(s)
  private async assignProjectUserToTask(task: TaskEntity, projectUserIds: number[], userId: number) {
    try {
      const assigneeBefore = await task.assignees;
      const assigneeAfter = !_.isEmpty(projectUserIds)
        ? await Promise.all(
            projectUserIds.map(async projectUserId => {
              return await getRepository(ProjectUserEntity).findOne({ id: projectUserId }, { relations: ['user'] });
            })
          )
        : [];
      task.assignees = Promise.resolve([...assigneeAfter]);
      task.updatedAt = new Date();
      await this.taskRepo.save(task);

      this.assignNotification(assigneeAfter, assigneeBefore, task, userId, 'Assignee');

      return { assigneeBefore, assigneeAfter };
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignProjectUserToTask');
    }
  }

  // Carbon Copy Task to Project user(s)
  private async assignProjectUserCcToTask(task: TaskEntity, projectUserIds: number[], userId: number) {
    try {
      const assigneeBefore = await task.copies;
      const assigneeAfter = !_.isEmpty(projectUserIds)
        ? await Promise.all(
            projectUserIds.map(async projectUserId => {
              return await getRepository(ProjectUserEntity).findOne({ id: projectUserId }, { relations: ['user'] });
            })
          )
        : [];
      task.copies = Promise.resolve([...assigneeAfter]);
      task.updatedAt = new Date();
      await this.taskRepo.save(task);

      this.assignNotification(assigneeAfter, assigneeBefore, task, userId, 'CC');

      return { assigneeBefore, assigneeAfter };
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'assignProjectUserCcToTask');
    }
  }

  private assignNotification(
    assigneeAfter: ProjectUserEntity[],
    assigneeBefore: ProjectUserEntity[],
    task: TaskEntity,
    userId: number,
    type: string
  ) {
    try {
      const sanitizedAssignee = assigneeAfter.filter(assigneeAfter => {
        return !assigneeBefore.find(assigneeBefore => assigneeBefore.userId === assigneeAfter.userId);
      });

      sanitizedAssignee.forEach(async assignee => {
        try {
          const user = await getRepository(UserEntity).findOne({ id: userId });
          const mobileLink = `task/1/${task.id}/task`;
          const project = await getRepository(ProjectEntity).findOne({ id: task.projectId });
          const companyName = await getRepository(CompanyEntity).findOne({ id: project.companyId });
          const link = `/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${companyName?.id}`;

          let body;
          let template;
          let header;
          let event;

          if (type === 'Assignee') {
            body = 'assign you a new task';
            template = 'primary-workflow';
            header = '📌 New Task';
          } else {
            body = 'CC you a new task';
            template = 'secondary-workflow';
            header = '📌 New Task CC';
            event = 'task-cc';
          }

          const payload: INovuPayload = {
            user: {
              avatar: user.avatar,
              name: user.name,
              email: user.email
            },
            event,
            ...(type === 'Assignee' && { subject: 'New Task Alert', bodyContent: body }),
            header: header,
            company: companyName.name,
            title: project.title,
            head: user.name,
            body,
            tail: task.title,
            bodyColon: true,
            link: {
              mobile: mobileLink,
              web: link,
              uri: APP_URI,
              redirect: link
            },
            subscriber: {
              firstName: assignee.user.name
            }
          };
          this.novuService.trigger(template, {
            to: {
              subscriberId: assignee.user.id.toString(),
              email: assignee.user.email
            },
            //? for fcm
            overrides: {
              android: {
                priority: 'high'
              },
              fcm: {
                data: {
                  link: mobileLink.toString(),
                  projectId: task.projectId.toString(),
                  companyId: companyName?.id.toString()
                }
              }
            },
            payload
          });
        } catch (error) {
          throw new BadRequestException(error);
        }
      });
    } catch (error) {
      getErrorMessage(error, 'TaskService', 'assignNotification');
    }
  }

  private async createTaskAssigneeAuditLog(
    userId: number,
    projectId: number,
    taskId: number,
    assigneeBeforeAndAfter: TaskAssigneeAuditLogInputDTO
  ) {
    try {
      const { assigneeBefore, assigneeAfter } = assigneeBeforeAndAfter;

      const assignedToUser = _.differenceBy(assigneeAfter, assigneeBefore, 'id');
      const unassignedToUser = _.differenceBy(assigneeBefore, assigneeAfter, 'id');
      const message = 'has assigned you to task';

      if (!_.isEmpty(assignedToUser)) {
        const action = AuditLogActionType.Assigned;
        await this.createAuditLog(userId, projectId, taskId, action, assignedToUser);
        await this.taskAssignmentNotificationAndMail(assignedToUser, userId, taskId, message);
      }
      if (!_.isEmpty(unassignedToUser)) {
        // const action = AuditLogActionType.Unassigned;
        // await this.createAuditLog(userId, projectId, taskId, action);
      }
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'createTaskAssigneeAuditLog');
    }
  }

  private async createTaskAssigneeCcAuditLog(
    userId: number,
    projectId: number,
    taskId: number,
    assigneeBeforeAndAfter: TaskAssigneeAuditLogInputDTO
  ) {
    try {
      const { assigneeBefore, assigneeAfter } = assigneeBeforeAndAfter;

      const assignedToUser = _.differenceBy(assigneeAfter, assigneeBefore, 'id');
      const unassignedToUser = _.differenceBy(assigneeBefore, assigneeAfter, 'id');
      const message = "has CC'd you to task";

      if (!_.isEmpty(assignedToUser)) {
        const action = AuditLogActionType.AssignedCc;
        await this.createAuditLog(userId, projectId, taskId, action, assignedToUser);
        await this.taskAssignmentNotificationAndMail(assignedToUser, userId, taskId, message);
      }
      if (!_.isEmpty(unassignedToUser)) {
        const action = AuditLogActionType.UnassignedCc;
        // await this.createAuditLog(userId, projectId, taskId, action);
      }
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'createTaskAssigneeCcAuditLog');
    }
  }

  private async createAuditLog(
    userId: number,
    projectId: number,
    taskId: number,
    action: AuditLogActionType,
    assignees: ProjectUserEntity[]
  ) {
    const user = await getRepository(UserEntity).findOne({ id: userId });

    // AUDIT LOG FOR TASK ASSIGNEES
    if (action === AuditLogActionType.Assigned) {
      let names = '';

      assignees.forEach((element, index) => {
        names = names + element.user.name;
        if (assignees.length - 1 !== index) names = names + ', ';
      });

      const msg = user.name + ' added ' + names + ' in assignee';

      const auditLog = getRepository(AuditLogEntity).create({
        userId: userId,
        projectId: projectId,
        // taskId: entity.id,
        resourceId: taskId,
        module: AuditLogModuleType.Task,
        action: AuditLogActionType.Assigned,
        content: msg
      });
      await auditLog.save();
    } else if (action === AuditLogActionType.AssignedCc) {
      let names = '';

      assignees.forEach((element, index) => {
        names = names + element.user.name;
        if (assignees.length - 1 !== index) names = names + ', ';
      });

      const msg = user.name + ' added ' + names + ' in Cc';

      const auditLog = getRepository(AuditLogEntity).create({
        userId: userId,
        projectId: projectId,
        // taskId: entity.id,
        resourceId: taskId,
        module: AuditLogModuleType.Task,
        action: AuditLogActionType.AssignedCc,
        content: msg
      });
      await auditLog.save();
    }
  }

  private async taskAssignmentNotificationAndMail(
    newAssignees: ProjectUserEntity[],
    userId: number,
    taskId: number,
    message: string
  ) {
    try {
      const assigner = await getRepository(UserEntity).findOne({ id: userId });
      const task = await this.taskRepo.findOne({ id: taskId });

      const taskUrl = new URL(`${process.env.APP_URI}/tasks/`);
      taskUrl.searchParams.append('taskId', _.toString(task.id));
      taskUrl.searchParams.append('projectId', _.toString(task.projectId));

      await Promise.all(
        newAssignees.map(async assignee => {
          const user = await getRepository(UserEntity).findOne({ id: assignee.userId });
          await this.createTaskAssignmentNotification(taskUrl, task, user, assigner, message);
        })
      );
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'taskAssignmentNotificationAndMail');
    }
  }

  private async createTaskAssignmentNotification(
    taskUrl: URL,
    task: TaskEntity,
    user: UserEntity,
    assigner: UserEntity,
    message: string
  ) {
    try {
      const deeplink = await _.replace(taskUrl.toString(), APP_URI, '');
      const mobileDeeplink = `edit-task/:${task.id}`;
      const project = await getRepository(ProjectEntity).findOne({ id: task.projectId });
      const notification = getRepository(NotificationTransactionEntity).create({
        userId: user.id,
        actorId: assigner.id,
        title: project.title,
        actionName: task.title,
        actionType: message,
        deeplink,
        mobileDeeplink
      });
      await getRepository(NotificationTransactionEntity).save(notification);
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'createTaskAssignmentNotification');
    }
  }

  async getTaskAuthority(user: AuthData, projectId: number, body: any) {
    try {
      // check if user is in task assignee by return boolean
      const isAssignee = await this.taskRepo
        .createQueryBuilder('task')
        .leftJoinAndSelect('task.assignees', 'assignee')
        .leftJoinAndSelect('assignee.user', 'user')
        .where('task.id = :taskId', { taskId: body.taskId })
        .andWhere('user.id = :userId', { userId: user.id })
        .getOne();

      // check if user is the task owner by checking the ownerId
      const isTaskOwner = await this.taskRepo
        .createQueryBuilder('task')
        .where('task.id = :taskId', { taskId: body.taskId })
        .andWhere('task.ownerId = :userId', { userId: user.id })
        .getOne();

      // check if user is the project owner
      const isProjectOwner = await getRepository(ProjectEntity).findOne({
        where: {
          id: projectId,
          userId: user.id
        }
      });

      // check if user is the cloud coordinator
      const isCloudCoordinator = await getRepository(ProjectUserEntity).findOne({
        where: {
          projectId: projectId,
          userId: user.id,
          role: 'CloudCoordinator'
        }
      });

      // return boolean for assignee and owner
      return {
        isAssignee: !!isAssignee,
        isTaskOwner: !!isTaskOwner,
        isProjectOwner: !!isProjectOwner,
        isCloudCoordinator: !!isCloudCoordinator
      };
    } catch (e) {
      getErrorMessage(e, 'TaskService', 'getTaskAuthority');
    }
  }

  async setSubgroup(subGroup: boolean) {
    try {
      // Write a query builder to get all documents where workspacegroupId is not null
      const tasks = await this.taskRepo
        .createQueryBuilder('tasks')
        .leftJoin('tasks.group', 'group')
        .addSelect(['group.id', 'group.title', 'group.projectGroupId'])
        .where('tasks.groupId IS NOT NULL')
        .getMany();

      // return [tasks.length, tasks];
      for (const task of tasks) {
        const child = await getRepository(ProjectGroupEntity).findOne({
          where: { projectGroupId: task.groupId }
        });

        if (child) {
          await this.taskRepo.update({ groupId: task.groupId }, { groupId: child.id });
        }
      }
    } catch (error) {
      // Handle the error here
    }
  }

  async getDeletedTasks(userId: number, projectId: number, q: TaskDeletedQuery) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const builder = getRepository(TaskEntity)
        .createQueryBuilder('deletedTask')
        .where('deletedTask.projectId = :projectId', { projectId })
        .andWhere('deletedTask.deletedAt IS NOT NULL') // Only include soft-deleted records
        .andWhere('deletedTask.permanentlyDeleted = :permanentlyDeleted', { permanentlyDeleted: false }) // Add condition for permanentlyDeleted
        .andWhere('deletedTask.deletedAt >= :startDate', { startDate })
        .andWhere('deletedTask.deletedAt <= :endDate', { endDate })
        .leftJoinAndSelect('deletedTask.group', 'selectedGroup')
        .leftJoinAndSelect('deletedTask.owner', 'owner')
        .leftJoinAndSelect('deletedTask.assignees', 'assignees')
        .orderBy('deletedTask.deletedAt', 'DESC')
        .withDeleted(); // Include soft-deleted records

      if (q.filter?.title) {
        if (q.filter?.title?.like == '') {
        } else if (q.filter?.title?.like != '') {
          builder.andWhere('deletedTask.title LIKE :title', { title: `%${q.filter?.title?.like}%` });
        }
      }

      if (q.filter?.taskCode) {
        if (q.filter?.taskCode?.eq + '' == '') {
        } else if (q.filter?.taskCode.eq + '' != '') {
          builder.andWhere('deletedTask.taskCode = :code', { code: q.filter.taskCode.eq });
        }
      }

      if (q.filter?.status) {
        if (q.filter?.status?.eq + '' == '') {
        } else if (q.filter?.status.eq + '' != '') {
          builder.andWhere('deletedTask.status = :status', { status: q.filter.status.eq });
        }
      }

      if (q.filter?.groupId) {
        if (q.filter?.groupId?.eq + '' == '') {
        } else if (q.filter?.groupId.eq + '' != '') {
          builder.andWhere('group.id = :id', { id: `%${q.filter.groupId}%` });
        }
      }

      if (q.filter?.assignees) {
        if ((q.filter?.assignees as any)?.userId?.in && Array.isArray((q.filter.assignees as any)?.userId?.in)) {
          const userIds = (q.filter.assignees as any)?.userId?.in;

          if (userIds.length > 0) {
            builder.andWhere('user.id IN (:...userIds)', { userIds });
          }
        }
      }

      if (q.filter.dueDate) {
        builder.andWhere('DATE(deletedTask.dueDate) = :date', {
          date: `${moment(q.filter.dueDate.eq).format('YYYY-MM-DD')}`
        });
      }

      const res = await builder.getMany();

      const sanitizedResponse = await res.map(async task => {
        return {
          ...task,
          assignees: await task.assignees
        };
      });

      return sanitizedResponse;
    } catch (error) {
      getErrorMessage(error, 'TaskService', 'getDeletedTasks');
    }
  }

  async restoreOneTask(id) {
    const builder = await getRepository(TaskEntity)
      .createQueryBuilder('restoreTask')
      .withDeleted()
      .where('restoreTask.id = :id', { id })
      .getOne();

    if (!builder.deletedAt) throw new BadRequestException('This task cannot be restored.');

    builder.deletedAt = null;
    return builder.save();
  }

  async permanentDeleteOneTask(id) {
    const builder = await getRepository(TaskEntity)
      .createQueryBuilder('permDeleteTask')
      .withDeleted()
      .where('permDeleteTask.id = :id', { id })
      .getOne();

    if (!builder.deletedAt) throw new BadRequestException('This task cannot be permanently deleted.');

    builder.permanentlyDeleted = true;
    return builder.save();
  }

  async changeProposedStatus(id: number, status: TaskStatusType) {
    const builder = await getRepository(TaskEntity).createQueryBuilder('task').where('task.id = :id', { id }).getOne();

    builder.proposedStatus = status;
    builder.updatedAt = new Date();
    return builder.save();
  }

  async changeTaskStatus(input: UpdateTaskStatusInputDTO) {
    try {
      const { taskId: id, proposeAction, updatedBy } = input;

      const builder = await getRepository(TaskEntity)
        .createQueryBuilder('task')
        .where('task.id = :id', { id })
        .leftJoinAndSelect('task.assignees', 'assignees')
        .leftJoinAndSelect('assignees.user', 'user')
        .leftJoinAndSelect('task.project', 'project')
        .getOne();

      if (proposeAction === ProposeAction.Validate) {
        builder.status = builder.proposedStatus;
      } else {
        builder.status = TaskStatusType.InProgress;
      }

      builder.proposedStatus = null;

      const assignees = await builder.assignees;
      const project = builder.project;
      const company = await getRepository(CompanyEntity).findOne({ id: project.companyId });
      const header = `📢 Task - Proposed Status`;

      const mobileLink = `task/1/${builder.id}/task`;
      const link = `/tasks?taskId=${builder.id}&projectId=${builder.projectId}&companyId=${company.id}`;

      const validateBy = await getRepository(UserEntity).findOne({ id: updatedBy });

      assignees?.map?.(async assignee => {
        if (assignee.user.id === updatedBy) return;

        const payload: INovuPayload = {
          user: {
            avatar: validateBy.avatar,
            name: validateBy.name,
            email: validateBy.email
          },
          event: 'task-validate-status',
          header: header,
          company: company.name,
          title: project.title,
          head: validateBy.name,
          body: `has ${proposeAction?.toLowerCase?.()} the proposed status for task`,
          tail: builder.title,
          bodyColon: true,
          link: {
            mobile: mobileLink,
            web: link,
            uri: APP_URI,
            redirect: link
          },
          subscriber: {
            firstName: assignee.user.name
          }
        };
        return this.novuService.trigger('secondary-workflow', {
          to: {
            subscriberId: assignee.user.id.toString(),
            email: assignee.user.email
          },
          //? for fcm
          overrides: {
            android: {
              priority: 'high'
            },
            fcm: {
              data: {
                link: mobileLink.toString(),
                projectId: builder.projectId.toString(),
                companyId: company?.id.toString()
              }
            }
          },
          payload
        });
      });

      return builder.save();
    } catch (error) {}
  }

  async generateMemo(input: GenerateMemoInputDTO, userId: number) {
    const imageData = input.customUrl;
    let companyLogo;
    const taskId = input.taskId;
    const taskDetails = await getRepository(TaskEntity)
      .createQueryBuilder('task')
      .leftJoin('task.assignees', 'assignees')
      .leftJoin('task.project', 'project')
      .leftJoin('task.group', 'group')
      .leftJoin('task.owner', 'owner')
      .leftJoin('assignees.user', 'user')
      .leftJoin('owner.company', 'company')
      .leftJoin('task.attachments', 'attachments')
      .leftJoin('task.medias', 'medias')
      .leftJoin('task.documents', 'documents')
      .addSelect([
        'user.name',
        'user.email',
        'user.id',
        'assignees.userId',
        'assignees.id',
        'project.title',
        'group.title',
        'owner.name',
        'owner.email',
        'owner.signUrl',
        'owner.companyId',
        'company.name',
        'company.stampUrl',
        'company.logoUrl',
        'medias.id',
        'medias.fileUrl',
        'attachments.id',
        'attachments.fileUrl',
        'attachments.type',
        'documents.id',
        'documents.fileUrl'
      ])
      .where('task.id = :id', { id: taskId })
      .getOne();

    //get task memo user request
    const user = await getRepository(UserEntity)
      .createQueryBuilder('user')
      .leftJoin('user.company', 'company')
      .where('user.id = :id', { id: userId })
      .select([
        'user.name',
        'user.email',
        'user.id',
        'user.signUrl',
        'user.stampKey',
        'user.signKey',
        'company.name',
        'company.logoUrl',
        'company.stampUrl',
        'company.logoKey',
        'company.stampKey'
      ])
      .getOne();

  

    if (imageData) {
      const image = await this.fileService.uploadGqlFile(imageData, 'Memo');
      companyLogo = image.key;
    } else {
      companyLogo = user.company?.logoUrl;
    }

    const signUrl = await this.userService.getPresignedUrl(user, 'signKey');
    const companyLogoUrl = await this.companyService.getPresignedUrl(user.company, 'logoKey');
    const companyStampUrl = await this.companyService.getPresignedUrl(user.company, 'stampKey');

    const assignee = await taskDetails?.assignees;
    const name = assignee[0].user.name;
    const englishHtmlContent = `
    <html>
<head>
  <style>
    @page {
      size: A4;
      margin: 5mm 5mm; /* Adjust margin values as needed */
    }
    body {
      font-family: "Times New Roman", Times, serif;
      border: 2px solid #000;
      padding: 10px;
    }
    .header {
      text-align: center;
    }
    .section {
      margin-bottom: 20px;
    }
    .description {
      white-space: pre-line;
    }        
    .signature {
      margin-top: 40px;
    }
    /* Add additional styling as needed */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      margin-left: 0; /* Remove the default left margin */
      margin-right: 0; /* Remove the default right margin */
    }
    th,
    td {
      border: 1px solid #000;
      padding: 8px;
      text-align: left;
    }
    .company-colon {
      width: 10px;
      text-align: center;
    }
    .up-column {
      width: 100px; /* Adjust the width for the U/P column as needed */
    }
    .additional-column {
      width: calc(
        100% - 60px
      ); /* Takes up the remaining width after considering the colon and U/P column */
    }
    .company-name {
      text-align: center;
    }
    .signature-container {
      text-align: left;
      margin-top: 40px;
      margin-bottom: 20px;
    }
    .signature-container img {
      position: absolute;
      bottom: 230px;
    }
    .signature-container img:first-child {
      margin-left: -20px; /* Adjust this value to control the overlap */
      
    }
    .signature-container img:last-child {
      margin-left: 30px; /* Adjust this value to control the overlap */
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>SITE MEMO</h1>
    <img
      src="${companyLogoUrl}"
      alt="Memo Image"
      style="text-align: center; width: 75px; height: auto" />
  </div>
  <div class="company-name">
  <h2>${input?.companyName ?? 'Not Stated'}</h2>
  </div>
  <table style="margin-bottom: 100px">
    <tr>
      <td class="up-column"><strong>To</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${name}</td>
    </tr>
    <tr>
      <td><strong>From</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${user?.name}</td>
    </tr>
    <tr>
      <td><strong>Date</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">
        ${moment().tz('Asia/Kuala_Lumpur').format('DD/MM/YYYY')}
      </td>
    </tr>
    <tr>
      <td><strong>Project</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${taskDetails?.project?.title}</td>
    </tr>
    <tr>
      <td><strong>Work Scope</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${taskDetails?.group?.title}</td>
    </tr>
    <tr>
      <td><strong>Subject</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${taskDetails?.title}</td>
    </tr>
    <tr>
      <td><strong>Reference Number</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column">${input?.refNo}</td>
    </tr>
    <tr>
      <td><strong>Description</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column description">${taskDetails?.description}</td>
    </tr>
    <tr>
      <td><strong>Additional Information</strong></td>
      <td class="company-colon">:</td>
      <td class="additional-column description">${input?.additionalDetails ?? 'Not Stated'}</td>
    </tr>
  </table>
  <div style="position: absolute; bottom: 0; width: calc(100% - 40px)">
    <div class="signature-container">
      <div>
      <strong>Issued By:</strong>
      ${
        input?.hasStamp
          ? `<img
        src="${companyLogoUrl}"
        style="text-align: center; width: 70px; height: auto" />`
          : ``
      }
        ${
          input?.hasSignature
            ? `<img
            src="${signUrl}"
            style="text-align: center; width: 100px; height: auto" />`
            : ``
        }
      </div>
    </div>
    <table>
      <tr>
        <td style="text-align: center"><strong>Recipient Acknowledgement</strong></td>
      </tr>
    </table>
    <div style="margin-top:140px" class="signature-container">
      <div><strong>Received By:</strong> ________________________</div>
    </div>
  </div>
</body>
</html>

`;
    const malayHtmlContent = `
    <html>
    <head>
      <style>
        @page {
          size: A4;
          margin: 5mm 5mm; /* Adjust margin values as needed */
        }
        body {
          font-family: "Times New Roman", Times, serif;
          border: 2px solid #000;
          padding: 10px;
        }
        .header {
          text-align: center;
        }
        .section {
          margin-bottom: 20px;
        }
        .description {
          white-space: pre-line;
        }        
        .signature {
          margin-top: 40px;
        }
        /* Add additional styling as needed */
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          margin-left: 0; /* Remove the default left margin */
          margin-right: 0; /* Remove the default right margin */
        }
        th,
        td {
          border: 1px solid #000;
          padding: 8px;
          text-align: left;
        }
        .company-colon {
          width: 10px;
          text-align: center;
        }
        .up-column {
          width: 100px; /* Adjust the width for the U/P column as needed */
        }
        .additional-column {
          width: calc(
            100% - 60px
          ); /* Takes up the remaining width after considering the colon and U/P column */
        }
        .company-name {
          text-align: center;
        }
        .signature-container {
          text-align: left;
          margin-top: 40px;
          margin-bottom: 20px;
        }
        .signature-container img {
          position: absolute;
          bottom: 230px;
        }
        .signature-container img:first-child {
          margin-left: -20px; /* Adjust this value to control the overlap */
          
        }
        
        .signature-container img:last-child {
          margin-left: 30px; /* Adjust this value to control the overlap */
        }
        
      </style>
    </head>
    <body>
      <div class="header">
        <h1>MEMO TAPAK</h1>
        <img
          src="${companyLogoUrl}"
          alt="Memo Image"
          style="text-align: center; width: 75px; height: auto" />
      </div>
      <div class="company-name">
        <h2>${input?.companyName ?? 'Not Stated'}</h2>
      </div>
      <table style="margin-bottom: 100px">
        <tr>
          <td class="up-column"><strong>U/P</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${name}</td>
        </tr>
        <tr>
          <td><strong>Daripada</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${user?.name}</td>
        </tr>
        <tr>
          <td><strong>Tarikh</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">
            ${moment().tz('Asia/Kuala_Lumpur').format('DD/MM/YYYY')}
          </td>
        </tr>
        <tr>
          <td><strong>Projek</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${taskDetails?.project?.title}</td>
        </tr>
        <tr>
          <td><strong>Skop Kerja</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${taskDetails?.group?.title}</td>
        </tr>
        <tr>
          <td><strong>Perkara</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${taskDetails?.title}</td>
        </tr>
        <tr>
          <td><strong>No Rujukan</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column">${input?.refNo}</td>
        </tr>
        <tr>
          <td><strong>Penerangan</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column description">${taskDetails?.description || ''}</td>
        </tr>
        <tr>
          <td><strong>Maklumat Tambahan</strong></td>
          <td class="company-colon">:</td>
          <td class="additional-column description">${input?.additionalDetails ?? ''}</td>
        </tr>
      </table>
      <div style="position: absolute; bottom: 0; width: calc(100% - 40px)">
      <div class="signature-container">
      <div>
        <strong>Dikeluarkan Oleh:</strong>
        ${
          input?.hasStamp === true
            ? `<img
          src="${companyStampUrl}"
          style="text-align: center; width: 70px; height: auto" />`
            : ``
        }
        ${
          input?.hasSignature === true
            ? `<img
            src="${signUrl}"
            style="text-align: center; width: 100px; height: auto" />`
            : ``
        }
      </div>
    </div>
        <table>
          <tr>
            <td style="text-align: center"><strong>Pengakuan Penerimaan</strong></td>
          </tr>
        </table>
        <div style="margin-top: 140px" class="signature-container">
          <div><strong>Diterima Oleh:</strong> ________________________</div>
        </div>
      </div>
    </body>
  </html>  
`;

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true,
      ignoreHTTPSErrors: true,
      dumpio: false
    });
    const page = await browser.newPage();
    await page.setContent(input.languageType === 'BahasaMelayu' ? malayHtmlContent : englishHtmlContent);
    const pdfBuffer = await page.pdf({
      format: 'A4', // or any other format you prefer
      printBackground: true
    });
    const existingDoc = await PDFDocument.load(pdfBuffer, { ignoreEncryption: true });
    await browser.close();

    const filteredAttachments = taskDetails?.attachments.filter(attachment => {
      return input.attachmentIds.includes(String(attachment.id));
    });

    const filteredDocuments = taskDetails?.documents.filter(document => {
      return input.documentIds.includes(String(document.id));
    });

    const filteredMedia = taskDetails?.medias.filter(media => {
      return input.mediaIds.includes(String(media.id));
    });

    const totalAttachmentsLength = filteredAttachments.length;
    const totalMediasLength = filteredMedia.length;
    const totalDocumentsLength = filteredDocuments.length;

    let finalPdfBytes;
    const imagesPerPage = 4; // Set the number of images per page
    let imageCount = 0;
    let mediaPage;
    const fixedImageWidth = 220; // Set your desired fixed width
    const fixedImageHeight = 220; // Set your desired fixed height

    for (let i = 0; i < totalAttachmentsLength; i++) {
      const attachment = filteredAttachments[i];
      const response = await axios.get(attachment.fileUrl, { responseType: 'arraybuffer' });
      const pdfDoc = await PDFDocument.load(response.data, { ignoreEncryption: true });

      const indices = pdfDoc.getPageIndices();
      const copiedPages = await existingDoc.copyPages(pdfDoc, indices);

      copiedPages.forEach(page => {
        existingDoc.addPage(page);
      });

      if (i === totalAttachmentsLength - 1) {
        finalPdfBytes = await existingDoc.save({ updateFieldAppearances: false });
      }
    }

    for (let i = 0; i < totalDocumentsLength; i++) {
      const documents = filteredDocuments[i];
      const response = await axios.get(documents.fileUrl, { responseType: 'arraybuffer' });

      if (this.isImage(documents.fileUrl)) {
        if (imageCount === 0) {
          mediaPage = existingDoc.addPage();
        }

        await this.embedImageOnPage(
          existingDoc,
          documents.fileUrl,
          mediaPage,
          imageCount,
          fixedImageWidth,
          fixedImageHeight,
          imagesPerPage
        );

        imageCount++;

        if (imageCount === imagesPerPage || i === totalMediasLength - 1) {
          imageCount = 0;
          finalPdfBytes = await existingDoc.save({ updateFieldAppearances: false });
        }
      } else {
        const pdfDoc = await PDFDocument.load(response.data, { ignoreEncryption: true });

        const indices = pdfDoc.getPageIndices();
        const copiedPages = await existingDoc.copyPages(pdfDoc, indices);

        copiedPages.forEach(page => {
          existingDoc.addPage(page);
        });

        if (i === totalDocumentsLength - 1) {
          finalPdfBytes = await existingDoc.save({ updateFieldAppearances: false });
        }
      }
    }

    for (let j = 0; j < totalMediasLength; j++) {
      const media = filteredMedia[j];

      if (imageCount === 0) {
        mediaPage = existingDoc.addPage();
      }

      await this.embedImageOnPage(
        existingDoc,
        media.fileUrl,
        mediaPage,
        imageCount,
        fixedImageWidth,
        fixedImageHeight,
        imagesPerPage
      );

      imageCount++;

      if (imageCount === imagesPerPage || j === totalMediasLength - 1) {
        imageCount = 0;
        finalPdfBytes = await existingDoc.save({ updateFieldAppearances: false });
      }
    }

    const folder = 'Memo';
    const file = {
      filename: `${taskDetails.title}.pdf`,
      mimetype: 'application/pdf',
      encoding: '7bit'
    };

    const { key } = await this.fileService.uploadGqlFile(
      file as FileUpload,
      folder,
      null,
      finalPdfBytes ? finalPdfBytes : pdfBuffer
    );

    // return pdfBase64;

    // const response = await fetch(
    //   'https://bina-dev-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Memo/BINA_Memo_Template.pdf'
    // );
    // const pdfData = await response.arrayBuffer();
    // const existingPdfBytes = new Uint8Array(pdfData);
    // const pdfDoc = await PDFDocument.load(existingPdfBytes);

    // const page = pdfDoc.getPage(0); // Assuming you want to add text to the first page
    // const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

    // const text = 'Bina Cloudtech Sdn Bhd';
    // const textSize = 16; // Set your desired font size
    // const textWidth = helveticaFont.widthOfTextAtSize(text, textSize);

    // // Calculate the position for centering text
    // const centerX = (page.getWidth() - textWidth) / 2 + 25;
    // const centerY = page.getHeight() - 110;

    // page.drawText(text, {
    //   x: centerX,
    //   y: centerY,
    //   font: helveticaFont,
    //   size: textSize,
    //   color: rgb(0, 0, 0) // Black color
    // });

    // const modifiedPdfBytes = await pdfDoc.save();
    // const base64String = Buffer.from(modifiedPdfBytes).toString('base64');
    // // Now you can use modifiedPdfBytes for further processing or saving

    const assignees = await taskDetails.assignees;

    if (input?.isPreview) {
      taskDetails.previewMemoUrl = key;
    } else {
      taskDetails.previewMemoUrl = key;

      assignees?.map?.(async assignee => {
        if (assignee.user.id === user.id) return;

        return this.sendNotification(
          {
            user: user,
            header: '📌 New Memo',
            company: taskDetails.owner.company.name,
            title: taskDetails.project.title,
            head: user.name,
            subject: 'New Site Memo',
            bodyContent: 'sent you a new memo',
            body: 'sent you a new memo',
            tail: taskDetails.title,
            bodyColon: true,
            link: {
              mobile: `task/1/${taskDetails.id}/task`,
              web: `/tasks?taskId=${taskDetails.id}&projectId=${taskDetails.projectId}&companyId=${taskDetails.owner.companyId}`,
              uri: APP_URI,
              redirect: `/tasks?taskId=${taskDetails.id}&projectId=${taskDetails.projectId}&companyId=${taskDetails.owner.companyId}`
            },
            subscriber: {
              firstName: assignee.user.name
            }
          },
          assignee.user,
          taskDetails.projectId,
          taskDetails.owner.companyId,
          'primary-workflow'
        );
      });

      taskDetails.memoUrl = key;
      taskDetails.isMemoReceive = false;
      taskDetails.issuedById = userId;
    }

    return taskDetails.save();
  }

  async embedImageOnPage(pdfDoc, imageUrl, mediaPage, imageCount, fixedImageWidth, fixedImageHeight, imagesPerPage) {
    try {
      let image;
      const url = new URL(imageUrl);
      const pathname = url.pathname;
      const fileExtension = pathname?.split?.('.').pop?.().toLowerCase?.();
      const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
      if (fileExtension === 'png') {
        image = await pdfDoc.embedPng(response.data);
      } else {
        image = await pdfDoc.embedJpg(response.data);
      }
      const { width: imageWidth, height: imageHeight } = image.scaleToFit(fixedImageWidth, fixedImageHeight);
      const { width: pageWidth, height: pageHeight } = mediaPage.getSize();

      const isLandscape = imageWidth > imageHeight;
      let x, y;

      if (imageCount % 2 === 0) {
        x = pageWidth / 2 - imageWidth - 20;
      } else {
        x = isLandscape
          ? pageWidth / 2 + pageWidth / 2 - imageWidth - 20
          : pageWidth / 2 + pageWidth / 2.5 - imageWidth - 20;
      }

      if (Math.floor(imageCount / 2) === 0) {
        y = pageHeight - (pageHeight / 2 - imageHeight / 2);
      } else {
        y = pageHeight - (pageHeight / 2 + pageHeight / 4 - imageHeight / 2);
      }

      mediaPage.drawImage(image, { x, y, width: imageWidth, height: imageHeight });
    } catch (error) {
      console.error('Error embedding image:', imageUrl, error);
    }
  }

  isImage(url) {
    return /\.(jpg|jpeg|png|gif)$/.test(url?.toLowerCase?.());
  }

  async receiveMemo(input: ReceiveMemoDTO, userId: number) {
    try{
      //get task
      const task = await getRepository(TaskEntity).findOne(
        { id: input.taskId },
        { relations: ['owner', 'project', 'owner.company'] }
      );
      const user = await getRepository(UserEntity).findOne({ id: userId });

      // Fetch PDF and sign images as buffers
      const memoUrl = await this.getPresignedUrl(task, 'memoKey');
      const signUrl = await this.userService.getPresignedUrl(user, 'signKey');
      const stampUrl = await this.userService.getPresignedUrl(user, 'stampKey');

      if (!memoUrl) {
        throw new BadRequestException('Error fetching Memos');
      }

      if (!signUrl) {
        throw new BadRequestException('Error fetching Signatures');
      }

      if (!stampUrl) {
        throw new BadRequestException('Error fetching Stamps');
      }

      const response = await axios.get(memoUrl, { responseType: 'arraybuffer' });
      const signResponse = await axios.get(signUrl, { responseType: 'arraybuffer' });
      const stampResponse = await axios.get(stampUrl, { responseType: 'arraybuffer' });
      const timestamp = `Received by: ${moment().tz('Asia/Kuala_Lumpur').format('DD/MM/YYYY')}`;

      // Load PDF document
      const pdfDoc = await PDFDocument.load(response.data, { ignoreEncryption: true });
      const [firstPage] = pdfDoc.getPages();

      // Load signature image
      const signImage = await pdfDoc.embedPng(signResponse.data);
      const stampImage = await pdfDoc.embedPng(stampResponse.data);

      // Draw the signature image on the first page
      // const { width, height } = firstPage.getSize();
      const imageSize = { width: 100, height: 100 }; // Adjust as needed

      // Calculate the width based on the aspect ratio
      const aspectRatio = signImage.width / signImage.height;
      const stampAspectRatio = stampImage.width / stampImage.height;

      // Set the desired height of the image
      const targetSignHeight = 80;
      const targetWidth = targetSignHeight * aspectRatio;

      // Draw the signature
      const signPosition = { x: 110, y: targetSignHeight - 45 }; // Adjust as needed
      firstPage.drawImage(signImage, {
        x: signPosition.x,
        y: signPosition.y,
        width: targetWidth,
        height: targetSignHeight
      });

      // Calculate the width based on the aspect ratio for the stamp image
      // const targetStampHeight = stampAspectRatio > 1.8 ? 50 : 100;
      const targetStampHeight = 50;
      const stampTargetWidth = targetStampHeight * stampAspectRatio;

      // Draw the stamp below the signature
      const stampPosition = {
        // x: aspectRatio > 1.8 ? 100 : 130,
        // y: aspectRatio > 1.8 ? targetStampHeight - 10 : targetStampHeight - 60
        x: 100,
        y: targetStampHeight + 10
      };
      firstPage.drawImage(stampImage, {
        x: stampPosition.x,
        y: stampPosition.y,
        width: stampTargetWidth,
        height: targetStampHeight
      });

      const timestampPosition = { x: signPosition.x + imageSize.width + 220, y: imageSize.height - 65 }; // Adjust as needed
      firstPage.drawText(timestamp, {
        x: timestampPosition.x,
        y: timestampPosition.y,
        font: await pdfDoc.embedFont(StandardFonts.Helvetica),
        size: 12,
        color: rgb(0, 0, 0) // Black color, adjust as needed
      });

      // Save the modified PDF
      const modifiedPdfBytes = await pdfDoc.save({ updateFieldAppearances: false });

      const folder = 'Memo';
      const file = {
        filename: `${task.title}.pdf`,
        mimetype: 'application/pdf',
        encoding: '7bit'
      };

      const { key } = await this.fileService.uploadGqlFile(
        file as FileUpload,
        folder,
        null,
        modifiedPdfBytes
      );

      if (task.status === TaskStatusType.Open) {
        task.status = TaskStatusType.InProgress;
      }

      const receiver = await getRepository(UserEntity).findOne({ id: task.issuedById });

      if (user.id !== receiver.id && !task.isMemoReceive) {
        this.sendNotification(
          {
            user: {
              avatar: user.avatar,
              name: user.name,
              email: user.email
            },
            header: 'Task - Receive Memo',
            company: task.owner.company.name,
            title: task.project.title,
            head: user.name,
            body: 'receive your memo',
            tail: task.title,
            bodyColon: true,
            link: {
              mobile: `task/1/${task.id}/task`,
              web: `/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${task.owner.companyId}`,
              uri: APP_URI,
              redirect: `/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${task.owner.companyId}`
            },
            subscriber: {
              firstName: user.name
            }
          },
          receiver,
          task.projectId,
          task.owner.companyId,
          'secondary-workflow'
        );
      }

      task.isMemoReceive = true;
      task.memoKey = key;

      return task.save();
    } catch (error) {
      if (error.response?.status === 404) {
        getErrorMessage(error, 'TaskService', 'receiveMemoFileNotFound');

        return
      }
      getErrorMessage(error, 'TaskService', 'receiveMemo');
    }
  }

  async sendNotification(
    props: INovuPayload,
    receiver: UserEntity,
    projectId: number,
    companyId: number,
    template?: string
  ) {
    const payload: INovuPayload = {
      user: {
        avatar: props.user.avatar,
        name: props.user.name,
        email: props.user.email
      },
      header: props.header,
      ...(props.subject && { subject: props.subject, bodyContent: props.bodyContent }),
      company: props.company,
      title: props.title,
      head: props.head,
      body: props.body,
      tail: props.tail,
      bodyColon: true,
      link: props.link,
      subscriber: props.subscriber
    };

    return await this.novuService.trigger(template, {
      to: {
        subscriberId: receiver.id.toString(),
        email: receiver.email
      },
      //? for fcm
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          data: {
            link: props.link.toString(),
            projectId: projectId.toString(),
            companyId: companyId.toString()
          }
        }
      },
      payload
    });
  }

  async getPresignedUrl(project_document: TaskDto, type: 'memoKey' | 'previewMemoKey' ) {
    try {
      const keyAndFallback = { memoKey: 'memoUrl', previewMemoKey: 'previewMemoUrl' };
      let key = project_document[type];
      // fallback if key is missing
      if (!key){
        const fileName = project_document[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
