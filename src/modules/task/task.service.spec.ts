import { TaskService } from './task.service';
import { AssignTaskInputDTO } from './dto/task.gql.dto';
import { TaskEntity } from './entity/task.entity';
import ormconfig from '../../../ormconfig-test';
import { Repository, createConnection, getConnection } from 'typeorm';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { NovuService } from '@modules/integration/novu/novu.service';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { FileService } from '@modules/integration/file.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';

describe.skip('TaskService', () => {
  let taskService: TaskService;
  let taskRepository: Repository<TaskEntity>;
  let projectUserRepository: Repository<ProjectUserEntity>;
  let projectRepository: Repository<ProjectEntity>;

  const connectionName = 'default';

  beforeEach(async () => {
    const service: TestingModule = await Test.createTestingModule({
      controllers: [TaskService],
      providers: [
        TaskService,
        NovuService,
        WebSocketService,
        TasksAttachmentEntity,
        TasksMediaEntity,
        {
          provide: getRepositoryToken(TaskEntity, connectionName),
          useValue: TaskEntity
        },
        {
          provide: getRepositoryToken(TasksAttachmentEntity, connectionName),
          useValue: TasksAttachmentEntity
        },
        {
          provide: getRepositoryToken(TasksMediaEntity, connectionName),
          useValue: TasksMediaEntity
        },
        {
          provide: getRepositoryToken(ProjectUserEntity, connectionName),
          useValue: ProjectUserEntity
        },
        {
          provide: FileService,
          useValue: FileService
        }
        
      ]
    }).compile();

    taskService = service.get<TaskService>(TaskService);
    taskRepository = service.get<Repository<TaskEntity>>(getRepositoryToken(TaskEntity, connectionName));
    projectUserRepository = service.get<Repository<ProjectUserEntity>>(getRepositoryToken(ProjectUserEntity, connectionName));
    projectRepository = service.get<Repository<ProjectEntity>>(getRepositoryToken(ProjectEntity, connectionName));

    const connection = await createConnection(ormconfig);
    
    return connection;
  });

  afterEach(async () => {
    await getConnection(connectionName).close();
  });

  it('should be defined', () => {
    expect(taskService).toBeDefined();
  });

  it('should assign a task to a user', async () => {
    const mockTask = new TaskEntity();
    mockTask.id = 1;
    mockTask.title = 'Task 1';
    mockTask.projectId = 1;
    mockTask.save();

    const mockProjectUser = new ProjectUserEntity();
    mockProjectUser.id = 1;
    mockProjectUser.userId = 1;
    mockProjectUser.projectId = 1;
    mockProjectUser.save();

    const mockProject = new ProjectEntity();
    mockProject.id = 1;
    mockProject.save();

    jest.spyOn(taskRepository, 'findOne').mockResolvedValue(mockTask);

    const assignTaskInput: AssignTaskInputDTO = {
      taskId: 1,
      projectUserIds: [1]
    };

    const userId = 1;
    const assignedTask = await taskService.assignTask(assignTaskInput, userId, 1);

    expect(assignedTask).toBeDefined();
  });

  it('should cc a task to a user', async () => {
    const ccTaskInput: AssignTaskInputDTO = {
      taskId: 1,
      projectUserIds: [1]
    };

    const userId = 1;
    const ccTask = await taskService.assignCcTask(ccTaskInput, userId, 1);

    expect(ccTask).toBeDefined();
  }, 10000);

  // it('should assign a task attachment to a task', async () => {
  //   const attachment = await getRepository(ProjectDocumentEntity, connectionName).findOne({
  //     where: {
  //       type: 'pdf'
  //     }
  //   });

  //   const assignTaskAttachment = {
  //     taskId: 1,
  //     attachments: [
  //       {
  //         userId: 1,
  //         taskId: 1,
  //         fileUrl: attachment.fileUrl,
  //         name: attachment.name,
  //         type: attachment.type
  //       }
  //     ]
  //   } as any;

  //   const userId = 1;
  //   const assignedTaskAttachment = await taskService.assignAttachments(assignTaskAttachment, userId, 1);
  //   expect(assignedTaskAttachment).toBeDefined();
  // });

  // it('should assign a task media to a task', async () => {
  //   const media = await getRepository(ProjectDocumentEntity, connectionName).findOne({
  //     where: {
  //       type: 'jpg'
  //     }
  //   });

  //   const assignTaskMedia = {
  //     taskId: 1,
  //     medias: [
  //       {
  //         userId: 1,
  //         taskId: 1,
  //         fileUrl: media.fileUrl,
  //         name: media.name,
  //         type: media.type
  //       }
  //     ]
  //   } as any;

  //   const userId = 1;
  //   const assignedTaskMedia = await taskService.assignMedias(assignTaskMedia, userId, 1);

  //   expect(assignedTaskMedia).toBeDefined();
  // });
});
