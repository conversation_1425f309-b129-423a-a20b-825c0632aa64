import { Modu<PERSON> } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { TaskEntity } from './entity/task.entity';
import { TaskDto, CreateTaskInputDTO, UpdateTaskInputDTO } from './dto/task.gql.dto';
import { TaskService } from './task.service';
import { TaskSubscriber } from './task.subscriber';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ScheduleModule } from '@nestjs/schedule';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { TaskResolver } from './task.resolver';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { TaskController } from './task.controller';
import { IntegrationModule } from '@modules/integration/integration.module';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserService } from '@modules/user/user.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyService } from '@modules/company/company.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([
          TaskEntity,
          ProjectGroupEntity,
          TasksMediaEntity,
          TasksAttachmentEntity,
          TasksMediaEntity,
          ProjectUserEntity,
          UserEntity,
          CompanyEntity
        ]),
        ...(process.env.ENABLE_CRON === 'true' ? [ScheduleModule.forRoot()] : []),
        IntegrationModule
      ],
      resolvers: [
        {
          ServiceClass: TaskService,
          DTOClass: TaskDto,
          EntityClass: TaskEntity,
          CreateDTOClass: CreateTaskInputDTO,
          UpdateDTOClass: UpdateTaskInputDTO,
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard]
        }
      ],
      services: [TaskSubscriber, TaskResolver, TaskService, MailgunService, TasksAttachmentEntity, TasksMediaEntity, UserService, CompanyService]
    })
  ],
  controllers: [TaskController]
})
export class TaskModule {}
