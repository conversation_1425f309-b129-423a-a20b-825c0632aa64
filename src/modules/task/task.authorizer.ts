import { UnauthorizedException, Injectable } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { TaskDto } from './dto/task.gql.dto';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getRepository } from 'typeorm';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class TaskAuthorizer implements Authorizer<TaskDto> {
  async authorize(context: any): Promise<Filter<TaskDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const projectId = context?.req?.headers?.['project-id'];
    const userId = context?.req?.user?.id;
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

    if (projectUser) {
      return Promise.resolve({
        projectId: { eq: projectId }
      });
    }
    throw new UnauthorizedException('You are not involved in this project');
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    const projectId = context?.req?.headers?.['project-id'];

    if (relationName === 'project' && projectId) {
      return Promise.resolve({
        id: { eq: projectId }
      });
    }
    return Promise.resolve({});
  }
}
