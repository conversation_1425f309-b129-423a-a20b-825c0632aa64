import { defaultQueryOptions, LanguageType, ProposeAction, SourceType, TaskStatusType } from '@constants';
import { RelationIdInput, relationOption } from '@constants/query.constant';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { ProjectGroupDto } from '@modules/project-group/dto/project-group.gql.dto';
import { ProjectUserDto } from '@modules/project-user/dto/project-user.gql.dto';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectDto } from '@modules/project/dto/project.gql.dto';
import { TaskCommentDto } from '@modules/task-comment/dto/task-comment.gql.dto';
import {
  CreateTasksAttachmentInputDTO,
  TasksAttachmentDto
} from '@modules/tasks-attachment/dto/tasks-attachment.gql.dto';
import { CreateTasksMediaInputDTO, TasksMediaDto } from '@modules/tasks-media/dto/tasks-media.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import {
  Authorize,
  BeforeCreateOne,
  BeforeUpdateOne,
  CreateOneInputType,
  FilterableOffsetConnection,
  FilterableRelation,
  IDField,
  OffsetConnection,
  QueryArgsType,
  QueryOptions
} from '@nestjs-query/query-graphql';
import { ArgsType, Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GqlContext } from '@types';
import { TaskEntity } from '../entity/task.entity';
import { TaskAuthorizer } from '../task.authorizer';
import { FileUpload, GraphQLUpload } from 'graphql-upload';

@ObjectType('Task')
@Authorize(TaskAuthorizer)
@OffsetConnection('comments', () => TaskCommentDto, relationOption(true))
@FilterableOffsetConnection('assignees', () => ProjectUserDto, relationOption(true))
@FilterableOffsetConnection('copies', () => ProjectUserDto, relationOption(true))
@FilterableOffsetConnection('attachments', () => TasksAttachmentDto, relationOption(true))
@FilterableOffsetConnection('medias', () => TasksMediaDto, {
  ...relationOption(true),
  defaultResultSize: 20
})
@FilterableOffsetConnection('documents', () => ProjectDocumentDto, relationOption(true))
@FilterableOffsetConnection('group', () => ProjectGroupDto, relationOption(true))
@FilterableOffsetConnection('project', () => ProjectDto, relationOption(true))
@FilterableRelation('medias', () => TasksMediaDto, relationOption(true))
@FilterableRelation('owner', () => UserDto, relationOption(true))
@FilterableRelation('group', () => ProjectGroupDto, relationOption(true))
@FilterableRelation('project', () => ProjectDto, relationOption(true))
@BeforeCreateOne((instance: CreateOneInputType<TaskDto>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  instance.input.ownerId = createdBy;
  const projectId = Number(context.req.headers['project-id']);
  instance.input.projectId = projectId;
  return instance;
})
@BeforeUpdateOne(Hooks.UpdatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class TaskDto extends TaskEntity {
  remoteId?: number;
  localId?: string;
}

@ObjectType('TaskDeleted')
@QueryOptions({ ...defaultQueryOptions })
export class TaskDeletedDto extends TaskDto {
  group: ProjectGroupDto;
  owner: UserDto;
  assignees: Promise<ProjectUserDto[]>;
}
@ArgsType()
export class TaskDeletedQuery extends QueryArgsType(TaskDeletedDto) {}
export const TaskDeletedConnection = TaskDeletedQuery.ConnectionType;

@InputType()
export class CreateTaskInputDTO {
  title: string;
  description?: string;
  dueDate?: Date;
  status?: TaskStatusType;
  @Field(() => [CreateTasksAttachmentInputDTO]) attachments?: CreateTasksAttachmentInputDTO[];
  @Field(() => [CreateTasksMediaInputDTO]) medias?: CreateTasksMediaInputDTO[];
  groupId?: number;
  documents?: RelationIdInput[];
  annotId?: string;
  spriteId?: string;
  elementId?: string;
  pos3D?: string;
  bimId?: string;
  docType?: string;
  imageURL?: string;
  isUrgent?: boolean;
  localMemoUrl?: string;
  localId?: string;

  //? for offline-first use only
  assignees?: number[];
  copies?: number[];
  updated_at?: Date;
  created_at?: Date;
  deleted_at?: Date;
  remoteId?: number;
  _changed?: string;
  projectId?: number;
  ownerId?: number;
  updatedBy?: number;
  updatedAt?: Date;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class UpdateTaskInputDTO extends PartialType(CreateTaskInputDTO) {
  proposedStatus?: TaskStatusType;
  proposeAction?: ProposeAction;
  previewMemoUrl?: string;
  memoUrl?: string;

  // for offline-first use only
  assignees?: number[];
  copies?: number[];
  updated_at?: Date;
  created_at?: Date;
  deleted_at?: Date;
  remoteId?: number;
  _changed?: string;
  updatedBy?: number;
}

@InputType()
export class AssignTaskInputDTO {
  @IDField(() => ID) taskId: number;
  @IDField(() => [ID]) projectUserIds: number[];
}
@InputType()
export class AssignTaskAttachmentInputDTO {
  @IDField(() => ID) taskId: number;
  @Field(() => [CreateTasksAttachmentInputDTO]) attachments?: CreateTasksAttachmentInputDTO[];
}
@InputType()
export class AssignTaskMediaInputDTO {
  @IDField(() => ID) taskId: number;
  @Field(() => [CreateTasksMediaInputDTO]) medias?: CreateTasksMediaInputDTO[];
}

@InputType()
export class UpdateTaskStatusInputDTO {
  @IDField(() => ID) taskId: number;
  proposeAction?: ProposeAction;
  updatedBy?: number;
}

@InputType()
export class GenerateMemoInputDTO {
  @IDField(() => ID) taskId: number;
  @IDField(() => GraphQLUpload) customUrl?: FileUpload;
  logoUrl?: string;
  companyName?: string;
  refNo: string;
  attachmentIds?: string[];
  mediaIds?: string[];
  documentIds?: string[];
  languageType: LanguageType;
  additionalDetails?: string;
  isPreview: boolean;
  hasStamp?: boolean;
  hasSignature?: boolean;
}

export class TaskAssigneeAuditLogInputDTO {
  assigneeBefore: ProjectUserEntity[];
  assigneeAfter: ProjectUserEntity[];
}
@InputType()
export class ReceiveMemoDTO {
  taskId: number;
}
