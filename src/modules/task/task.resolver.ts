import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, ResolveField, Parent } from '@nestjs/graphql';
import {
  AssignTaskAttachmentInputDTO,
  AssignTaskInputDTO,
  AssignTaskMediaInputDTO,
  TaskDto,
  TaskDeletedQuery,
  TaskDeletedConnection,
  UpdateTaskStatusInputDTO,
  GenerateMemoInputDTO,
  ReceiveMemoDTO
} from './dto/task.gql.dto';
import { TaskService } from './task.service';
import { AuthData } from '@types';

@UseGuards(GqlAuthGuard)
@Resolver(() => TaskDto)
export class TaskResolver {
  constructor(private taskService: TaskService) {}

  @Query(() => TaskDeletedConnection)
  async getDeletedTasks(
    @Args() query: TaskDeletedQuery,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return TaskDeletedConnection.createFromPromise(
      async q => (await this.taskService.getDeletedTasks(user.id, projectId, q)) as any, // Assuming getDeletedTasks returns a promise
      { ...query, ...{ filter: { ...query.filter } } } as any
    );
  }

  @Mutation(() => TaskDto)
  async restoreTask(@Args('id') id: number) {
    return await this.taskService.restoreOneTask(id);
  }

  @Mutation(() => TaskDto)
  async permanentDeleteTask(@Args('id') id: number) {
    return await this.taskService.permanentDeleteOneTask(id);
  }

  @Mutation(() => TaskDto)
  async assignTask(
    @Args('input') input: AssignTaskInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.taskService.assignTask(input, user.id, projectId);
  }

  @Mutation(() => TaskDto)
  async assignCcTask(
    @Args('input') input: AssignTaskInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.taskService.assignCcTask(input, user.id, projectId);
  }

  @Mutation(() => TaskDto)
  async assignAttachmentTask(
    @Args('input') input: AssignTaskAttachmentInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.taskService.assignAttachments(input, user.id, projectId);
  }

  @Mutation(() => TaskDto)
  async assignMediaTask(
    @Args('input') input: AssignTaskMediaInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.taskService.assignMedias(input, user.id, projectId);
  }

  @Mutation(() => TaskDto)
  async updateOneTaskStatus(@Args('input') input: UpdateTaskStatusInputDTO) {
    return await this.taskService.changeTaskStatus(input);
  }

  @Mutation(() => TaskDto)
  async generateMemo(@Args('input') input: GenerateMemoInputDTO, @GqlGetGqlAuthData() user: AuthData) {
    return await this.taskService.generateMemo(input, user.id);
  }

  @Mutation(() => TaskDto)
  async receiveMemo(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: ReceiveMemoDTO
  ) {
    return await this.taskService.receiveMemo(input, user.id);
  }

  @ResolveField('memoUrl', () => String)
  async memoUrl(@Parent() parent: TaskDto) {
    return await this.taskService.getPresignedUrl(parent, 'memoKey');
  }

  @ResolveField('previewMemoUrl', () => String)
  async previewMemoUrl(@Parent() parent: TaskDto) {
    return await this.taskService.getPresignedUrl(parent, 'previewMemoKey');
  }
}
