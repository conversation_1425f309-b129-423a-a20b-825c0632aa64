import { TaskStatusType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { BaseEntity } from '@modules/base/base';
import { ChecklistEntity } from '@modules/checklist/entity/checklist.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { TaskCommentEntity } from '@modules/task-comment/entity/task-comment.entity';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { <PERSON>ield, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime, Field } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne, OneToMany, ManyToMany, JoinTable } from 'typeorm';

@ObjectType()
@Entity('tasks')
export class TaskEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  ownerId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  groupId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  taskCode: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  annotId: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  spriteId: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  elementId: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  pos3D: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  bimId: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  imageURL: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  description: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  dueDate: Date;

  @FilterableField(() => TaskStatusType)
  @Column('enum', { enum: TaskStatusType, default: TaskStatusType.Open })
  status: TaskStatusType;

  @Field()
  @Column('boolean', { default: false })
  isSentBeforeOneDay: boolean;

  @Field()
  @Column('boolean', { default: false })
  isSentBeforeOneHour: boolean;

  @Field()
  @Column('boolean', { default: false })
  permanentlyDeleted: boolean;

  @FilterableField({ nullable: true })
  @Column('enum', { enum: TaskStatusType, default: null })
  proposedStatus: TaskStatusType;

  @FilterableField({ nullable: true })
  @Column('boolean', { default: false })
  isUrgent: boolean;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  memoUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  previewMemoUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  memoKey: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  previewMemoKey: string;

  @FilterableField({ nullable: true })
  @Column('boolean', { nullable: true, default: false })
  isMemoReceive: boolean;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  issuedById: number;

  // @FilterableField()
  // @Column('varchar')
  // taskCode: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectEntity, project => project.tasks)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => UserEntity, user => user.tasks)
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @ManyToOne(() => ProjectGroupEntity, projectGroup => projectGroup.tasks)
  @JoinColumn({ name: 'groupId' })
  group: ProjectGroupEntity;

  @OneToMany(() => TaskCommentEntity, taskComments => taskComments.task)
  comments: TaskCommentEntity[];

  @OneToMany(() => TasksAttachmentEntity, tasksAttachments => tasksAttachments.task, { cascade: ['insert', 'update'] })
  attachments: TasksAttachmentEntity[];

  @OneToMany(() => TasksMediaEntity, tasksMedias => tasksMedias.task, { cascade: ['insert', 'update'] })
  medias: TasksMediaEntity[];

  @OneToMany(() => ChecklistEntity, checklists => checklists.task)
  checklists: ChecklistEntity[];

  @OneToMany(() => AuditLogEntity, auditLogs => auditLogs.task)
  auditLogs: AuditLogEntity[];

  @ManyToMany(() => ProjectUserEntity, projectUser => projectUser.tasks, { cascade: ['update'] })
  @JoinTable({ name: 'task_assignees' })
  assignees: Promise<ProjectUserEntity[]>;

  @ManyToMany(() => ProjectUserEntity, projectUserCopy => projectUserCopy.tasksC, { cascade: ['update'] })
  @JoinTable({ name: 'task_copies' })
  copies: Promise<ProjectUserEntity[]>;

  @ManyToMany(() => ProjectDocumentEntity, document => document.tasks, { cascade: ['update'] })
  @JoinTable({ name: 'tasks_documents' })
  documents: ProjectDocumentEntity[];
}
