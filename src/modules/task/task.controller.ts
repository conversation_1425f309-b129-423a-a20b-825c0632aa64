import { GetProjectData, GetHeaders, GetAuthData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { TaskFilterDTO, TaskAuthority } from './dto/task.api.dto';
import { TaskService } from './task.service';

@ApiTags('Tasks API')
@Controller('tasks')
export class TaskController {
  constructor(private taskService: TaskService) {}

  @Post('filter-tasks')
  async filterTasks(@Body() body: TaskFilterDTO, @GetProjectData() projectId: number) {
    return await this.taskService.filterTask(body, projectId);
  }

  @UseApiUserAuthGuard()
  @Post('task-authority')
  async getTaskAuthority(
    @GetProjectData() projectId: number,
    @GetAuthData() authData: AuthData,
    @Body() body: TaskAuthority
  ) {
    return await this.taskService.getTaskAuthority(authData, projectId, body);
  }

  @Get('tasks-sub-group')
  async setProjectDocsSubGroup(@Query('subGroup') subGroup: boolean) {
    const docsSubGroup = await this.taskService.setSubgroup(subGroup);
    return docsSubGroup;
  }
}
