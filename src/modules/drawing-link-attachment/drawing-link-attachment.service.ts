import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DrawingLinkAttachmentEntity } from './entity/drawing-links-attachment.entity';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class DrawingLinkAttachmentService extends TypeOrmQueryService<DrawingLinkAttachmentEntity> {
  constructor(
    @InjectRepository(DrawingLinkAttachmentEntity)
    private drawingLinkAttachmentRepo: Repository<DrawingLinkAttachmentEntity>,
    private tmOneService: TMOneService
  ) {
    super(drawingLinkAttachmentRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(drawing_link_attachment: DrawingLinkAttachmentEntity) {
    try {
      let key = drawing_link_attachment.fileKey;
      // fallback to fileUrl if fileKey is missing
      if (!key){
        const fileName = drawing_link_attachment.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
