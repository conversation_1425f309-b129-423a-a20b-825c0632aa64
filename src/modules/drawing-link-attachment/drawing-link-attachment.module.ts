import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { DrawingLinkAttachmentService } from './drawing-link-attachment.service';
import { CreateDrawingLinkAttachmentInputDTO, DrawingLinkAttachmentDto } from './dto/drawing-link-attachment.gql.dto';
import { DrawingLinkAttachmentEntity } from './entity/drawing-links-attachment.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { DrawingLinkAttachmentSubscriber } from './drawing-link-attachment.subscriber';
import { DrawingLinkAttachmentResolver } from './drawing-link-attachment.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([DrawingLinkAttachmentEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: DrawingLinkAttachmentDto,
          ServiceClass: DrawingLinkAttachmentService,
          EntityClass: DrawingLinkAttachmentEntity,
          CreateDTOClass: CreateDrawingLinkAttachmentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [
        DrawingLinkAttachmentService,
        DrawingLinkAttachmentEntity,
        DrawingLinkAttachmentSubscriber,
        DrawingLinkAttachmentResolver
      ]
    })
  ]
})
export class DrawingLinkAttachmentModule {}
