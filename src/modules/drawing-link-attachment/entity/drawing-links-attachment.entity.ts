import { BaseEntity } from '@modules/base/base';
import { DrawingLinksEntity } from '@modules/drawing-links/entity/drawing-links.entity';
import { FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType } from '@nestjs/graphql';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('drawing_link_attachment')
export class DrawingLinkAttachmentEntity extends BaseEntity {

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileKey: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileName: string;

  @FilterableField({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  drawingLinkId: number;

  // Relations
  @ManyToOne(() => DrawingLinksEntity, drawingLink => drawingLink.drawingLinkAttachments, { orphanedRowAction: 'delete' })
  @JoinColumn({ name: 'drawingLinkId' })
  drawingLink: DrawingLinksEntity;
}
