import { ArgsType, Field, InputType, ObjectType } from '@nestjs/graphql';
import { defaultQueryOptions } from '@constants';
import { IDField, QueryArgsType, QueryOptions } from '@nestjs-query/query-graphql';
import { DrawingLinkAttachmentEntity } from '../entity/drawing-links-attachment.entity';
import { GraphQLUpload, FileUpload } from 'graphql-upload';

@ObjectType('DrawingLinkAttachment')
@QueryOptions({ ...defaultQueryOptions })
export class DrawingLinkAttachmentDto extends DrawingLinkAttachmentEntity {}

@ArgsType()
export class DrawingLinkAttachmentQuery extends QueryArgsType(DrawingLinkAttachmentDto) {}
export const DrawingLinkAttachmentConnection = DrawingLinkAttachmentQuery.ConnectionType;

@InputType()
export class CreateDrawingLinkAttachmentInputDTO {
  @IDField(() => Number) id?: number;
  fileName?: string;
  @IDField(() => Number) drawingLinkId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;
}

// @InputType()
// export class UpdateDrawingLinksInputDTO extends PartialType(CreateDrawingLinksInputDTO) {}