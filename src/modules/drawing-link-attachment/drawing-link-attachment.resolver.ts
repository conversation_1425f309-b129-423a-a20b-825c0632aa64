import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Parent, ResolveField } from '@nestjs/graphql';
import { DrawingLinkAttachmentDto } from './dto/drawing-link-attachment.gql.dto';
import { DrawingLinkAttachmentService } from './drawing-link-attachment.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => DrawingLinkAttachmentDto)
export class DrawingLinkAttachmentResolver {
  constructor(private drawingLinkAttachmentService: DrawingLinkAttachmentService) {}

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: DrawingLinkAttachmentDto) {
    return await this.drawingLinkAttachmentService.getPresignedUrl(parent);
  }
}
