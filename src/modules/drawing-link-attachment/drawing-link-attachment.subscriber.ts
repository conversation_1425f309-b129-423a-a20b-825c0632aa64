import { FileService } from '@modules/integration/file.service';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { DrawingLinkAttachmentEntity } from './entity/drawing-links-attachment.entity';
import { FileUpload } from 'graphql-upload';
import _ from 'lodash';

@Injectable()
@EventSubscriber()
export class DrawingLinkAttachmentSubscriber implements EntitySubscriberInterface<DrawingLinkAttachmentEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }
  async beforeUpdate(event: UpdateEvent<DrawingLinkAttachmentEntity>) {
    const { entity } = event;

    const file: any = entity.fileUrl;
    if (!file) throw new BadRequestException('No Document has been upload');
    if (file && _.isObject(file)) {
      const folder = 'Drawing-Link-Attachments';
      const { key } = await this.fileService.uploadGqlFile(file as FileUpload, folder);
      entity.fileKey = key;
    }
  }

  listenTo() {
    return DrawingLinkAttachmentEntity;
  }

  async beforeInsert(event: InsertEvent<DrawingLinkAttachmentEntity>) {
    // try{
    const { entity } = event;

    if (entity.fileUrl && _.isObject(entity.fileUrl)) {
      const folder = 'Drawing-Link-Attachment';
      const { key, filename } = await this.fileService.uploadGqlFile(entity.fileUrl as FileUpload, folder);
      entity.fileKey = key;
      entity.fileName = filename;
    }
    // } catch (error) {
    //   getErrorMessage(error, 'beforeInsert', 'DrawingLinkAttachmentSubscriber');
    // }
  }
}
