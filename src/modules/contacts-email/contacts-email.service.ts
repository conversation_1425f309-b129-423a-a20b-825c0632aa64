import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ContactsEmailEntity } from './entity/contacts-email.entity';
import { AuthData } from '@types';
import { UserEntity } from '@modules/user/entity/user.entity';
import { getErrorMessage } from '@common/error';


@Injectable()
export class ContactsEmailService extends TypeOrmQueryService<ContactsEmailEntity> {
  constructor(
    @InjectRepository(ContactsEmailEntity)
    private contactsEmail: Repository<ContactsEmailEntity>,    
    private tmOneService: TMOneService,    
  ) {
    // pass the use soft delete option to the service.
    super(contactsEmail, { useSoftDelete: true });
  } 

  async getDirectUrl(key: string) {
    return this.tmOneService.getDirectUrl(key);
  }

  async getPresignedUrl(user: ContactsEmailEntity, type: 'avatarKey') {
    try {
      const keyAndFallback = { avatarKey: 'avatar', };
      let key = user[type];
      // fallback if key is missing
      if (!key){
        const fileName = user[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {      
      return null;
    }
  }

  async initContactEmailOnLogin (user: AuthData, projectId: number) {  
    try{
      const getUser = await getRepository(UserEntity).findOne({
        where: {
          id: user.id,
        },
        relations: ['company'],
      });

      // check user with same email and project exist or not
      const existingContactEmail = await this.contactsEmail.findOne({
        where: {
          email: getUser.email,
          projectId: projectId
        }
      });      

      // if contact email already exists, return it
      if (existingContactEmail) {
        return existingContactEmail;
      }
      // create contact email
      const contactEmail = new ContactsEmailEntity();
      contactEmail.email = getUser.email;
      contactEmail.projectId = projectId;
      contactEmail.userId = user.id;
      contactEmail.company = getUser.company.name;      
      contactEmail.avatarKey = getUser.avatar;
      await this.contactsEmail.save(contactEmail);
      
      return contactEmail;
    } catch(e){
      getErrorMessage(e, 'ContactsEmailService', 'initContactEmailOnLogin');
    }
  } 
}
