import { BaseEntity } from '@modules/base/base';
import { Enti<PERSON>, Column, ManyToOne, JoinColumn, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { EmailEntity } from '@modules/email/entity/email.entity';

@ObjectType()
@Entity('contacts_email')
export class ContactsEmailEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID)
  @FilterableField({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId?: number;

  @FilterableField()
  @Column('varchar')
  email: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  name: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  company: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  position: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  avatarKey: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  removedAt: Date;
 
  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectEntity, project => project.userEmails)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @OneToMany(() => EmailEntity, email => email.sender)
  sentEmails:Promise<EmailEntity[]>;

  @ManyToOne(() => UserEntity, user => user.contactsEmails)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToMany(() => EmailEntity, email => email.receivers)
  @JoinTable({
    name: 'contact_email_to_email', // Bridge table name
    joinColumn: { name: 'emailsId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'emailAccountsId', referencedColumnName: 'id' },
  })
  receivedEmails: EmailEntity[];
}
