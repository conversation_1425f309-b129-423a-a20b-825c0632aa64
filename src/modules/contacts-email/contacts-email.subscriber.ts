import { getErrorMessage } from '@common/error';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { ContactsEmailEntity } from './entity/contacts-email.entity';

@Injectable()
@EventSubscriber()
export class ContactEmailSubscriber implements EntitySubscriberInterface<ContactsEmailEntity> {
  constructor(connection: Connection,  
  ) {
    
    connection.subscribers.push(this);
  }

  listenTo() {
    return ContactsEmailEntity;
  }

  async beforeInsert(event: InsertEvent<ContactsEmailEntity>){    
    try{

      const contactEmail = await event.manager.findOne(ContactsEmailEntity, { 
        where: { 
          email: event.entity.email, 
          projectId: event.entity.projectId 
        } 
      });      

      if(contactEmail){
        throw new BadRequestException(`User with ${event.entity.email} already exists in this project`);
      }
      
    } catch(e){
      getErrorMessage(e, 'ContactEmail', 'beforeInsert');
    }
  }

}
