import { GetAuthData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { ContactsEmailService } from './contacts-email.service';
import { ContactEmailInputDTO } from './dto/contacts-email.api.dto';

@ApiTags('Contact Email API')
@Controller('contact-email')
export class ContactsEmailsController {
  constructor(private contactService: ContactsEmailService) {}
  
  @UseApiUserAuthGuard()
  @Post('create-contact-email')
  async importContacts(
    @Body() contactDto: ContactEmailInputDTO,
    @GetAuthData() user: AuthData, 
  ) {
    
    try {
      await this.contactService.initContactEmailOnLogin(user, contactDto.projectId)
    } catch (e) {
      
      return Promise.reject(e);
    }
  }
}
