import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, ResolveField, Parent, Mutation } from '@nestjs/graphql';
import { ContactsEmailService } from './contacts-email.service';
import { ContactsEmailDTO } from './dto/contacts-email.gql.dto';
import { Args } from '@nestjs/graphql';
import { AuthData } from '@types';
import { GqlGetGqlAuthData } from '@decorators/auth.decorator';

@UseGuards(GqlAuthGuard)
@Resolver(() => ContactsEmailDTO)
export class ContactsEmailResolver {
  constructor(private contactsEmailService: ContactsEmailService) {}


  @Mutation(() => ContactsEmailDTO)
  async initContactEmailOnLogin(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') projectId: number
  ) {
  return await this.contactsEmailService.initContactEmailOnLogin(user, projectId);
  }

  @ResolveField('avatar', () => String)
  async avatar(@Parent() parent: ContactsEmailDTO) {
    if (!parent.avatarKey) {
      return null;
    }

    return await this.contactsEmailService.getDirectUrl(parent.avatarKey);
  }

   @ResolveField('avatarKey', () => String)
    async avatarKey(@Parent() parent: ContactsEmailDTO) {
      return await this.contactsEmailService.getPresignedUrl(parent, 'avatarKey');
    }

}
