import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, CreateOneInputType, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType } from '@nestjs/graphql';
import { ContactsEmailEntity } from '../entity/contacts-email.entity';
import { GqlContext } from '@types';

@ObjectType('ContactsEmail')
@BeforeCreateOne(
  (instance: CreateOneInputType<ContactsEmailDTO>, context: GqlContext) => {
    
    const addedBy = context.req.user.id;
    const projectId = context.req.headers['project-id']|| context.req.headers['Project-Id'];
    
    if(addedBy){
      instance.input.createdBy = addedBy;          
    }

    if(projectId){
      instance.input.projectId = Number(projectId);
    }

    return instance;
  }
)
@QueryOptions({ ...defaultQueryOptions })
export class ContactsEmailDTO extends ContactsEmailEntity {}

@InputType()
export class CreateEmailContactInputDTO {
  @IDField(() => ID) userId?: number;
  @IDField(() => ID) projectId?: number;
  name: string;
  email: string;
  company?: string;
  position?: string;
}