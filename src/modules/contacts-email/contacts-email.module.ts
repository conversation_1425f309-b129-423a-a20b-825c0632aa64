import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { UseUploadFilePipe } from 'src/pipes/gql-upload.pipe';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ContactsEmailEntity } from './entity/contacts-email.entity';
import { ContactsEmailDTO, CreateEmailContactInputDTO } from './dto/contacts-email.gql.dto';
import { ContactsEmailService } from './contacts-email.service';
import { ContactsEmailResolver } from './contacts-email.resolver';
import { ContactEmailSubscriber } from './contacts-email.subscriber';
import { ContactsEmailsController } from './contact-email.controller';

@Module({
  imports: [    
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ContactsEmailEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: ContactsEmailDTO,
          EntityClass: ContactsEmailEntity,
          CreateDTOClass: CreateEmailContactInputDTO,
          decorators: [UseUploadFilePipe()],
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ContactsEmailService, ContactsEmailResolver]
    }),
    IntegrationModule
  ],  
  providers: [ContactEmailSubscriber],
  controllers: [ContactsEmailsController],
})
export class ContactsEmailModule {}
