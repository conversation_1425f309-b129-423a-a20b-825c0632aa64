import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { WorkspaceDocumentDto, LinkDocumentInputDTO } from './dto/workspace-document.dto';
import { WorkspaceDocumentService } from './workspace-document.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => WorkspaceDocumentDto)
export class WorkspaceDocumentResolver {
  constructor(private readonly workspaceDocumentService: WorkspaceDocumentService) {}
  
  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: WorkspaceDocumentDto) {
  
    return await this.workspaceDocumentService.getPresignedUrl(parent, 'fileKey');
  }

  @Mutation(() => WorkspaceDocumentDto)
  async linkWorkspaceDocument(
    @Args('input') input: LinkDocumentInputDTO,
  ) {
    return await this.workspaceDocumentService.linkDocument(input);
  }
  
}
