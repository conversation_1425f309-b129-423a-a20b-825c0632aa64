import { CategoryType, defaultQueryOptions, SourceType } from '@constants';
import { FilterableField, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { WorkspaceDocumentEntity } from '../entity/workspace-document.entity';

@ObjectType('WorkspaceDocument')
@QueryOptions({ ...defaultQueryOptions })
export class WorkspaceDocumentDto extends WorkspaceDocumentEntity {
  //? for offline
  @Field() remoteId?: number;
}

@InputType()
export class CreateWorkspaceDocumentInputDTO {
  @IDField(() => Number, { nullable: true }) id?: number;
  @IDField(() => Number) projectDocumentId?: number;
  @FilterableField(() => String) name?: string;
  @FilterableField(() => String) fileUrl?: string;
  @FilterableField(() => String) type?: string;
  @FilterableField(() => String) category?: CategoryType;
  @FilterableField(() => Number) documentId?: number;
  @FilterableField(() => String, { nullable: true }) fileKey?: string;

  //? offline mode
  @Field({ nullable: true }) localId?: string;
  @Field({ nullable: true }) created_at?: Date;
  @Field({ nullable: true }) updated_at?: Date;
  @Field({ nullable: true }) localProjectDocumentId?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class UpdateWorkspaceDocumentInputDTO extends PartialType(CreateWorkspaceDocumentInputDTO) {
  //? for offline
  @Field({ nullable: true }) localId?: string;
  @Field({ nullable: true }) created_at?: Date;
  @Field({ nullable: true }) _changed?: string;
  @Field({ nullable: true }) remoteId?: number;
}

@InputType()
export class LinkDocumentInputDTO extends PartialType(CreateWorkspaceDocumentInputDTO) {
  @Field(() => Number) projectDocumentId?: number;
  @Field(() => [CreateWorkspaceDocumentInputDTO]) documents?: CreateWorkspaceDocumentInputDTO[];
}
