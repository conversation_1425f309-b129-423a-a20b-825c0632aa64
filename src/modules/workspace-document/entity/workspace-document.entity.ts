import { CategoryType } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('workspace_document')
export class WorkspaceDocumentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text')
  fileUrl: string;

  @FilterableField()
  @Column('text')
  type: string;

  @FilterableField(() => CategoryType, { nullable: true })
  @Column('enum', { enum: CategoryType, nullable: true })
  category: CategoryType;

  @IDField(() => ID)
  @Column({ unsigned: true })
  documentId: number;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileKey: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, document => document.workspaceDocuments, {
    orphanedRowAction: 'soft-delete'
  })
  @JoinColumn({ name: 'projectDocumentId' })
  document: ProjectDocumentEntity;
}
