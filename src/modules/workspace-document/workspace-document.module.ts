import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { WorkspaceDocumentEntity } from './entity/workspace-document.entity';
import {
  CreateWorkspaceDocumentInputDTO,
  UpdateWorkspaceDocumentInputDTO,
  WorkspaceDocumentDto
} from './dto/workspace-document.dto';
import { WorkspaceDocumentService } from './workspace-document.service';
import { WorkspaceDocumentResolver } from './workspace-document.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([WorkspaceDocumentEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: WorkspaceDocumentDto,
          EntityClass: WorkspaceDocumentEntity,
          CreateDTOClass: CreateWorkspaceDocumentInputDTO,
          UpdateDTOClass: UpdateWorkspaceDocumentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [WorkspaceDocumentService, WorkspaceDocumentResolver]
    }),
    IntegrationModule
  ]
})
export class WorkspaceDocumentModule {}
