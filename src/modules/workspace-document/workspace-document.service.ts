import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceDocumentEntity } from './entity/workspace-document.entity';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { LinkDocumentInputDTO } from './dto/workspace-document.dto';

const { APP_URI } = process.env;

@Injectable()
export class WorkspaceDocumentService extends TypeOrmQueryService<WorkspaceDocumentEntity> {
  constructor(
    @InjectRepository(WorkspaceDocumentEntity)
    private workspaceDocumentRepo: Repository<WorkspaceDocumentEntity>,
    private tmOneService: TMOneService,
  ) {
    // pass the use soft delete option to the service.
    super(workspaceDocumentRepo, { useSoftDelete: true });
  }

  async getPresignedUrl(workspace_document: WorkspaceDocumentEntity, type: 'fileKey' ) {
    try {
      const keyAndFallback = { fileKey: 'fileUrl'};
      let key = workspace_document[type];
      // fallback if key is missing
      if (!key){
        const fileName = workspace_document[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }

  async linkDocument(data: LinkDocumentInputDTO): Promise<WorkspaceDocumentEntity> {
    const queryRunner = this.workspaceDocumentRepo.manager.connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { projectDocumentId, documents } = data;

      if (!documents || documents.length === 0) {
        return;
      }

      const existingWorkspaceDocuments = await queryRunner.manager.find(WorkspaceDocumentEntity, {
        where: { projectDocumentId },
        select: ['id', 'projectDocumentId']
      });

      const existingDocumentIds = new Set(existingWorkspaceDocuments.map(doc => doc.id));
      const inputDocumentIds = new Set(documents.map(d => d.id));

      const documentIdsToDelete = existingWorkspaceDocuments
        .filter(existingDocument => !inputDocumentIds.has(existingDocument.id))
        .map(existingDocument => existingDocument.id);

      if (documentIdsToDelete.length > 0) {
        await queryRunner.manager.softDelete(WorkspaceDocumentEntity, documentIdsToDelete);
      }

      const documentToAdd = documents.filter(d => !existingDocumentIds.has(d.id));

      if (documentToAdd.length > 0) {
        const newWorkspaceDocuments = documentToAdd.map(c =>
          queryRunner.manager.create(WorkspaceDocumentEntity, {
            projectDocumentId,
            id: c.id,
            documentId: c.documentId,
            name: c.name,
            fileUrl: c.fileUrl,
            type: c.type,
            category: c.category,
          })
        );

        await queryRunner.manager.save(WorkspaceDocumentEntity, newWorkspaceDocuments);
      }

      await queryRunner.commitTransaction();

      return queryRunner.manager.findOne(WorkspaceDocumentEntity, {
        where: { projectDocumentId },
        select: ['id', 'projectDocumentId'],
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error(`Error in assignWorkspaceCC for projectDocumentId ${data.projectDocumentId}:`, error);
      throw new Error('Failed to assign workspace CCs');
    } finally {
      await queryRunner.release();
    }
  }

}
