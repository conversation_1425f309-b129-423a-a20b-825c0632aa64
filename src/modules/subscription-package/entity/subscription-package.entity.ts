import { BaseEntity } from '@modules/base/base';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, Field } from '@nestjs/graphql';
import { Entity, Column, OneToMany } from 'typeorm';

@ObjectType()
@Entity('subscription_packages')
export class SubscriptionPackageEntity extends BaseEntity {
  @FilterableField()
  @Column('varchar', { unique: true })
  title: string;

  @FilterableField()
  @Column('text')
  description: string;

  @FilterableField()
  @Column('float')
  amount: number;

  @FilterableField()
  @Column('int')
  availableDuration: number;

  @FilterableField()
  @Column('int', { default: 9999 })
  totalProjects: number;

  @FilterableField()
  @Column('int', { default: 9999 })
  totalUsers: number;

  @FilterableField({ nullable: true })
  @Column('int', { default: 0, nullable: true })
  storage: number;

  @FilterableField()
  @Column('boolean', { default: false })
  isProjectBased: boolean;

  @Field(() => [String], { nullable: true })
  @Column('simple-array', { nullable: true })
  features: string[]; // Array of included features

  @Field(() => [String], { nullable: true })
  @Column('simple-array', { nullable: true })
  nonFeatures: string[];

  @FilterableField()
  @Column('boolean', { default: true })
  allowTask: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowProjectDocument: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowWorkProgramme: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowCorrespondence: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowWorkspaceDocument: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowWorkspaceTemplate: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowDrawing: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowBimModel: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowPhoto: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowScheduleChart: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowScheduleActivity: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowDashboard: boolean;

  @FilterableField()
  @Column('boolean', { default: true })
  allowEmailCorrespondence: boolean;

  @FilterableField()
  @Column('boolean', { default: false })
  isPublic: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @OneToMany(() => SalesOrderEntity, salesOrders => salesOrders.subscriptionPackage)
  salesOrders: SalesOrderEntity[];

  @OneToMany(() => CompanySubscriptionEntity, companySubscriptions => companySubscriptions.subscriptionPackage)
  companySubscriptions: CompanySubscriptionEntity[];
}
