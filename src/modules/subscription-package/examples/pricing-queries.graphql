# Example GraphQL queries for the Subscription Package Pricing API

# Calculate subscription pricing for a specific configuration
query CalculateSubscriptionPricing($input: PricingCalculationInput!) {
  calculateSubscriptionPricing(input: $input) {
    # First period (prorated or initial month)
    firstPeriod {
      totalAmount
      baseAmount
      sstAmount
      isProrated
      label
      formattedStartDate
      formattedEndDate
    }

    # Full month period
    fullMonthPeriod {
      totalAmount
      baseAmount
      sstAmount
      formattedStartDate
      formattedEndDate
    }

    # Combined totals
    combined {
      totalAmount
      baseAmount
      sstAmount
      totalAmountInCents
    }

    # Subscription details
    subscriptionEndDate
    formattedSubscriptionEndDate
    teamSize
    isYearly
    monthlyPricePerMember
    discountedMonthlyPricePerMember
    yearlyDiscountRate

    # Billing scenario details
    isFirstDayOfMonth
    isAfterMidMonth
    dayOfMonth
  }
}

# Variables for the above query
# {
#   "input": {
#     "subscriptionPackageId": "1",
#     "teamSize": 5,
#     "isYearly": false,
#     "billingDate": "2023-01-15T00:00:00Z"
#   }
# }

# Get pricing for multiple team sizes (make parallel requests)
# Example: Calculate pricing for different team sizes
query CalculatePricingForTeamSize1($input: PricingCalculationInput!) {
  teamSize1: calculateSubscriptionPricing(input: $input) {
    teamSize
    combined {
      totalAmount
      baseAmount
      sstAmount
    }
    firstPeriod {
      isProrated
      totalAmount
      label
    }
    monthlyPricePerMember
    discountedMonthlyPricePerMember
    yearlyDiscountRate
  }
}

# Variables for multiple team size calculations
# Make separate calls for each team size:
# Call 1: { "input": { "subscriptionPackageId": "1", "teamSize": 1, "isYearly": false } }
# Call 2: { "input": { "subscriptionPackageId": "1", "teamSize": 5, "isYearly": false } }
# Call 3: { "input": { "subscriptionPackageId": "1", "teamSize": 10, "isYearly": false } }

# Get subscription packages with pricing calculation
query GetSubscriptionPackagesWithPricing($teamSize: Int!, $isYearly: Boolean!) {
  getSubscriptionPackages {
    id
    title
    description
    amount
    features
    isPublic
  }
}

# Simplified query for frontend pricing display
query GetSimplePricing($input: PricingCalculationInput!) {
  calculateSubscriptionPricing(input: $input) {
    combined {
      totalAmount
      baseAmount
      sstAmount
    }
    firstPeriod {
      isProrated
      label
    }
    teamSize
    isYearly
    yearlyDiscountRate
  }
}

# Query for seat count selector (real-time pricing updates)
query GetPricingForSeatSelector($input: PricingCalculationInput!) {
  calculateSubscriptionPricing(input: $input) {
    teamSize
    combined {
      totalAmount
    }
    firstPeriod {
      isProrated
    }
    yearlyDiscountRate
  }
}

# Query for billing summary page
query GetBillingSummary($input: PricingCalculationInput!) {
  calculateSubscriptionPricing(input: $input) {
    firstPeriod {
      totalAmount
      baseAmount
      sstAmount
      isProrated
      label
      formattedStartDate
      formattedEndDate
    }
    fullMonthPeriod {
      totalAmount
      baseAmount
      sstAmount
      formattedStartDate
      formattedEndDate
    }
    combined {
      totalAmount
      baseAmount
      sstAmount
    }
    subscriptionEndDate
    formattedSubscriptionEndDate
    teamSize
    isYearly
    monthlyPricePerMember
    discountedMonthlyPricePerMember
  }
}

# Query for yearly vs monthly comparison
query GetYearlyVsMonthlyComparison(
  $subscriptionPackageId: ID!
  $teamSize: Int!
  $billingDate: DateTime
) {
  monthly: calculateSubscriptionPricing(input: {
    subscriptionPackageId: $subscriptionPackageId
    teamSize: $teamSize
    isYearly: false
    billingDate: $billingDate
  }) {
    combined {
      totalAmount
    }
    monthlyPricePerMember
    yearlyDiscountRate
  }

  yearly: calculateSubscriptionPricing(input: {
    subscriptionPackageId: $subscriptionPackageId
    teamSize: $teamSize
    isYearly: true
    billingDate: $billingDate
  }) {
    combined {
      totalAmount
    }
    discountedMonthlyPricePerMember
    yearlyDiscountRate
  }
}
