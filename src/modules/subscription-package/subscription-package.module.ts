import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { SubscriptionPackageEntity } from './entity/subscription-package.entity';
import {
  SubscriptionPackageDto,
  CreateSubscriptionPackageInputDTO,
  UpdateSubscriptionPackageInputDTO
} from './dto/subscription-package.gql.dto';
import { SubscriptionPackageResolver } from './subscription-package.resolver';
import { SubscriptionPackageService } from './subscription-package.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([SubscriptionPackageEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: SubscriptionPackageDto,
          EntityClass: SubscriptionPackageEntity,
          CreateDTOClass: CreateSubscriptionPackageInputDTO,
          UpdateDTOClass: UpdateSubscriptionPackageInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [SubscriptionPackageResolver, SubscriptionPackageService]
    })
  ],
  providers: [SubscriptionPackageService],
  exports: [SubscriptionPackageService]
})
export class SubscriptionPackageModule {}
