import { Args, Query, Resolver } from '@nestjs/graphql';
import { SubscriptionPackageService } from './subscription-package.service';
import { SubscriptionPackageDto, PricingCalculationInputDto, PricingCalculationResultDto } from './dto/subscription-package.gql.dto';
import { UseGuards, Logger, BadRequestException } from '@nestjs/common';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { AuthData } from '@types';

@UseGuards(GqlAuthGuard)
@Resolver()
export class SubscriptionPackageResolver {
  private readonly logger = new Logger(SubscriptionPackageResolver.name);

  constructor(private subscriptionPackageService: SubscriptionPackageService) {}

  @Query(() => [SubscriptionPackageDto])
  async getSubscriptionPackages(@GqlGetGqlAuthData() user: AuthData): Promise<any> {
    return await this.subscriptionPackageService.getSubscriptionPackages(user.type);
  }

  /**
   * Calculate subscription pricing based on package, team size, and billing preferences
   * This resolver centralizes all pricing calculations on the backend
   */
  @Query(() => PricingCalculationResultDto, {
    name: 'calculateSubscriptionPricing',
    description: 'Calculate subscription pricing including prorated amounts, full subscription costs, and billing dates'
  })
  async calculateSubscriptionPricing(
    @Args('input') input: PricingCalculationInputDto
  ): Promise<PricingCalculationResultDto> {
    this.logger.log(`Calculating pricing for package ${input.subscriptionPackageId}, team size ${input.teamSize}, yearly: ${input.isYearly}`);

    try {
      // Validate input
      if (!input.subscriptionPackageId) {
        throw new BadRequestException('Subscription package ID is required');
      }

      if (!input.teamSize || input.teamSize < 1) {
        throw new BadRequestException('Team size must be at least 1');
      }

      if (typeof input.isYearly !== 'boolean') {
        throw new BadRequestException('isYearly must be a boolean value');
      }

      // Calculate pricing using the service
      const result = await this.subscriptionPackageService.calculateSubscriptionPricing(input);

      this.logger.log(`Pricing calculation completed for package ${input.subscriptionPackageId}. Total: RM ${result.combined.totalAmount}`);

      return result;
    } catch (error) {
      this.logger.error(`Error calculating pricing: ${error.message}`, error.stack);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException(`Failed to calculate pricing: ${error.message}`);
    }
  }


}
