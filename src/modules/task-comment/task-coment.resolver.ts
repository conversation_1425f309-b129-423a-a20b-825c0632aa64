import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { AuthData } from '@types';
import { TaskCommentDto, CreateTaskCommentInputDTO } from './dto/task-comment.gql.dto';
import { TaskCommentService } from './task-comment.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => TaskCommentDto)
export class TaskCommentResolver {
  constructor(private readonly commentService: TaskCommentService) {}

  @Mutation(() => TaskCommentDto)
  async createComment(@Args('input') input: CreateTaskCommentInputDTO, @GqlGetGqlAuthData() user: AuthData) {
    const { message, mentions, taskId } = input;
    const { id: userId } = user;
    return await this.commentService.createComment(message, mentions, taskId, userId);
  }
}
