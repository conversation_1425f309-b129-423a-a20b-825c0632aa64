import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, In, InsertEvent } from 'typeorm';
import { TaskCommentEntity } from './entity/task-comment.entity';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { getErrorMessage } from '@common/error';
import { uniqBy } from 'lodash';
import { getEllipsisText } from '@constants/function';
import { SoftRemoveEvent } from 'typeorm/subscriber/event/SoftRemoveEvent';

const { APP_URI } = process.env;

@Injectable()
@EventSubscriber()
export class TaskCommentSubscriber implements EntitySubscriberInterface<TaskCommentEntity> {
  constructor(connection: Connection, private webSocketService: WebSocketService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TaskCommentEntity;
  }

  // async beforeInsert(event: InsertEvent<TaskCommentEntity>) {
  //   const { entity } = event;
  // }

  async afterInsert(event: InsertEvent<TaskCommentEntity>) {
    try {
      const { entity } = event;
      const { message } = entity;

      const task = await event.manager.getRepository(TaskEntity).findOne({ id: entity.taskId });
      const project = await event.manager.findOne(ProjectEntity, { id: task.projectId });
      const mobileLink = `task/1/${task.id}/comment`;
      const commentMessage = this.CommentMessage(entity.message);
      const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });
      const link = `/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${companyName.id}`;

      task.updatedAt = new Date();
      await event.manager.save(task);

      const mentionRegex = /\B@\[([^\]]+)\]\((\d+)\)/g;
      const mentionMatches = message.matchAll(mentionRegex);

      const mentionIds = [];

      for (const match of mentionMatches) {
        const [, , id] = match;
        mentionIds.push(id);
      }

      if (mentionIds?.length === 0) {
        if (!task) {
          throw new Error(`Task with ID ${entity.taskId} not found`);
        }

        if (!project) {
          throw new Error(`Project with ID ${task.projectId} not found`);
        }

        const assignees = (await task.assignees) ?? [];
        const copies = (await task.copies) ?? [];
        const taskOwner = { userId: task.ownerId };
        const users = uniqBy([...assignees, ...copies, taskOwner], 'userId');

        const commentedUser = await event.manager.findOne(UserEntity, { id: entity.userId });

        const sanitizedUsers = await event.manager.find(UserEntity, {
          where: {
            id: In(users.map(user => user.userId))
          }
        });

        return sanitizedUsers
          ?.filter(user => user?.id !== entity?.userId)
          ?.forEach?.(async user => {
            try {
              const body = `commented on ${task.title}: ${getEllipsisText(commentMessage)}`;
              const header = `💬 Task - New Comment`;

              return this.statusNotification(
                user,
                project,
                task,
                link,
                mobileLink,
                commentedUser,
                body,
                companyName,
                header
              );
            } catch (error) {
              getErrorMessage(error, 'TaskSubscriber', 'afterInsert');
            }
          });
      }

      if (mentionIds.length > 0) {
        for (const userId of mentionIds) {
          try {
            const commentedUser = await event.manager.findOne(UserEntity, { id: entity.userId });
            const user = await event.manager.findOne(UserEntity, { id: userId });
            const task = await event.manager.findOne(TaskEntity, { id: entity.taskId });
            if (!task) {
              throw new Error(`Task with ID ${entity.taskId} not found`);
            }

            if (!project) {
              throw new Error(`Project with ID ${task.projectId} not found`);
            }

            const body = `commented on ${task.title}: ${getEllipsisText(commentMessage)}`;
            const header = `💬 Task - New Comment`;

            return this.statusNotification(
              user,
              project,
              task,
              link,
              mobileLink,
              commentedUser,
              body,
              companyName,
              header
            );
          } catch (error) {
            throw new error(`Error processing mention for user ID ${userId}: ${error}`);
          }
        }
      }

      const socketEvent = 'event:new-task-comment';
      const room = `task-comment-room-${entity.taskId}`;
      await this.webSocketService.socket.to(room).emit(socketEvent, entity);

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.userId,
        projectId: task.projectId,
        taskId: entity.taskId,
        module: AuditLogModuleType.TaskComment,
        action: AuditLogActionType.Create,
        content: entity.message
      });
      await event.manager.getRepository(AuditLogEntity).save(auditLog);

    } catch (error) {
      console.log(error);
      getErrorMessage(error, 'TaskSubscriber', 'afterInsert');
    }
  }

  statusNotification(user, project, task, link, mobileLink, owner, body, companyName = null, header) {
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      event: 'task-comment',
      header: header,
      company: companyName.name,
      title: project.title,
      head: owner?.name,
      body: body,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: owner.name
      }
    };

    this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          android: {
            priority: 'high'
          },
          data: {
            link: mobileLink.toString(),
            projectId: task.projectId.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }

  CommentMessage = (message: string) => {
    const mentionRegex = /\B@\[([^\]]+)\]\(\d+\)/g;
    const replacedContent = message.replace(mentionRegex, '@$1');

    return replacedContent;
  };


  async afterSoftRemove(event: SoftRemoveEvent<TaskCommentEntity>): Promise<any> {
    try {
      const { entity } = event;

      // update the task's updatedAt
      const task = await event.manager.findOne(TaskEntity, { id: entity.taskId });
      task.updatedAt = new Date();
      return await event.manager.save(task);

    } catch (error) {
      console.log(error);
      getErrorMessage(error, 'TaskCommentSubscriber', 'afterSoftRemove');
    }
  }
}
