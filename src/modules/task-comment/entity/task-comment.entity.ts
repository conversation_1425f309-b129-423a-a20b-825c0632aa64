import { BaseEntity } from '@modules/base/base';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('task_comments')
export class TaskCommentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  taskId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  message: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => TaskEntity, task => task.comments)
  @JoinColumn({ name: 'taskId' })
  task: TaskEntity;

  @ManyToOne(() => UserEntity, user => user.taskComments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
