import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { TaskCommentEntity } from './entity/task-comment.entity';
import { Injectable } from '@nestjs/common';
import _ from 'lodash';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';

const { APP_URI } = process.env;

@Injectable()
export class TaskCommentService extends TypeOrmQueryService<TaskCommentEntity> {
  constructor(
    @InjectRepository(TaskCommentEntity)
    private taskCommentRepo: Repository<TaskCommentEntity>
  ) {
    // pass the use soft delete option to the service.
    super(taskCommentRepo, { useSoftDelete: true });
  }

  async createComment(message: string, mentions: string[], taskId: number, userId: number) {
    const newComment = getRepository(TaskCommentEntity).create({ userId, message, taskId });
    const comment = await getRepository(TaskCommentEntity).save({ ...newComment });

    return comment;
  }
}
