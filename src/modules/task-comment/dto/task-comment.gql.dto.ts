import { defaultQueryOptions, SourceType } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserEntity } from '@modules/user/entity/user.entity';
import { BeforeCreateOne, FilterableRelation, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { TaskCommentEntity } from '../entity/task-comment.entity';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { TaskEntity } from '@modules/task/entity/task.entity';

@ObjectType('TaskComment')
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
@FilterableRelation('user', () => UserEntity, relationOption())
@FilterableRelation('task', () => TaskEntity, relationOption())
export class TaskCommentDto extends TaskCommentEntity {
  remoteId?: number;
}

@InputType()
export class CreateTaskCommentInputDTO {
  @IDField(() => ID) taskId: number;
  message: string;
  mentions?: string[];
  userId?: number;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  @Field({ nullable: true }) recordSource?: SourceType;
}
@InputType()
export class UpdateTaskCommentInputDTO extends PartialType(CreateTaskCommentInputDTO) {}

@InputType()
export class FilterTaskCommentByTaskIdDTO {
  taskId: number;
}
