import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { TaskCommentEntity } from './entity/task-comment.entity';
import { TaskCommentDto, CreateTaskCommentInputDTO, UpdateTaskCommentInputDTO } from './dto/task-comment.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { TaskCommentSubscriber } from './task-comment.subscriber';
import { TaskCommentService } from './task-comment.service';
import { TaskCommentResolver } from './task-coment.resolver';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([TaskCommentEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: TaskCommentService,
          DTOClass: TaskCommentDto,
          EntityClass: TaskCommentEntity,
          CreateDTOClass: CreateTaskCommentInputDTO,
          UpdateDTOClass: UpdateTaskCommentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          enableSubscriptions: true
        }
      ],
      services: [TaskCommentSubscriber, TaskCommentService, TaskCommentResolver]
    })
  ]
})
export class TaskCommentModule {}
