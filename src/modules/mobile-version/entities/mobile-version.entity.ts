import { BaseEntity } from "@modules/base/base";
import { FilterableField } from "@nestjs-query/query-graphql";
import { ObjectType } from "@nestjs/graphql";
import { Column, Entity } from "typeorm";

@ObjectType()
@Entity('mobile_versions')
export class MobileVersionEntity extends BaseEntity {

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: false })
  platformName: string;

  @FilterableField()
  @Column('varchar')
  buildCode: string;

  @FilterableField()
  @Column('varchar')
  versionCode: string;
}
