import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { MobileVersionEntity } from '../entities/mobile-version.entity';

@ObjectType('MobileVersion')
@QueryOptions({ ...defaultQueryOptions })
export class MobileVersionDto extends MobileVersionEntity {}

@InputType()
export class CreateMobileVersionInputDTO {
  platformName: string;
  buildCode: string;
  versionCode: string;
}
@InputType()
export class UpdateMobileVersionInputDTO extends PartialType(CreateMobileVersionInputDTO) {}
