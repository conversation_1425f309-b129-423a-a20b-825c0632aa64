import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { MobileVersionDto } from './dto/mobile-version.gql.dto';
import { MobileVersionEntity } from './entities/mobile-version.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([MobileVersionEntity])],
      resolvers: [
        {
          DTOClass: MobileVersionDto,
          EntityClass: MobileVersionEntity,
          create: {
            disabled: true
          },
          update: {
            disabled: true
          },
          delete: {
            disabled: true
          },
          // guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ]
    })
  ]
})
export class MobileVersionModule {}
