import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { UserFcmTokenEntity } from './entity/user-fcm-token.entity';
import { UserFcmTokenDto, CreateUserFcmTokenInputDTO, UpdateUserFcmTokenInputDTO } from './dto/user-fcm-token.gql.dto';
import { UserFcmTokenService } from './user-fcm-token.service';
import { UserFcmTokenSubscriber } from './user-fcm-token.subscriber';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([UserFcmTokenEntity])],
      resolvers: [
        {
          ServiceClass: UserFcmTokenService,
          DTOClass: UserFcmTokenDto,
          EntityClass: UserFcmTokenEntity,
          CreateDTOClass: CreateUserFcmTokenInputDTO,
          UpdateDTOClass: UpdateUserFcmTokenInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [UserFcmTokenSubscriber, UserFcmTokenService]
    })
  ]
})
export class UserFcmTokenModule {}
