import { BaseEntity } from '@modules/base/base';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';

@ObjectType()
@Entity('user_fcm_tokens')
export class UserFcmTokenEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @FilterableField()
  @Column('text')
  fcmToken: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.fcmTokens)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
