import { defaultQueryOptions } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Authorize, BeforeCreateOne, FilterableRelation, QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { UserFcmTokenEntity } from '../entity/user-fcm-token.entity';
import { UserFcmTokenAuthorizer } from '../user-fcm-token.authorizer';

@ObjectType('UserFcmToken')
@Authorize(UserFcmTokenAuthorizer)
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
@FilterableRelation('user', () => UserEntity, relationOption())
export class UserFcmTokenDto extends UserFcmTokenEntity {}

@InputType()
export class CreateUserFcmTokenInputDTO {
  fcmToken: string;
}

@InputType()
export class UpdateUserFcmTokenInputDTO extends PartialType(CreateUserFcmTokenInputDTO) {}
