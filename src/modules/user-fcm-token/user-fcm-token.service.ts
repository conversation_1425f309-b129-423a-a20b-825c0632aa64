import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserFcmTokenEntity } from './entity/user-fcm-token.entity';
import { Injectable } from '@nestjs/common';

@Injectable()
export class UserFcmTokenService extends TypeOrmQueryService<UserFcmTokenEntity> {
  constructor(
    @InjectRepository(UserFcmTokenEntity)
    private userFcmTokenRepo: Repository<UserFcmTokenEntity>
  ) {
    // pass the use soft delete option to the service.
    super(userFcmTokenRepo, { useSoftDelete: true });
  }
}
