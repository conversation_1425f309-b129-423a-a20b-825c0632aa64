import { Injectable } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, Connection, InsertEvent } from 'typeorm';
import { UserFcmTokenEntity } from './entity/user-fcm-token.entity';

@Injectable()
@EventSubscriber()
export class UserFcmTokenSubscriber implements EntitySubscriberInterface<UserFcmTokenEntity> {
  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return UserFcmTokenEntity;
  }

  async beforeInsert(event: InsertEvent<UserFcmTokenEntity>) {
    const { entity } = event;

    entity.userId = entity.createdBy;
    entity.createdBy = null;
  }
}
