import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { RoleTypeEnum } from '@constants';
import { UserFcmTokenDto } from './dto/user-fcm-token.gql.dto';

@Injectable()
export class UserFcmTokenAuthorizer implements Authorizer<UserFcmTokenDto> {
  authorize(context: any): Promise<Filter<UserFcmTokenDto>> {
    // User authorizer
    const userId = context?.req?.user?.id;
    if (!userId) throw new UnauthorizedException('User not found');

    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({
        id: { eq: context.req.user.id }
      });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
