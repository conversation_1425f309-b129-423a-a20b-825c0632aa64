import { Injectable } from '@nestjs/common';

import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { FileService } from '@modules/integration/file.service';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { getErrorMessage } from '@common/error';

import { FileUpload } from 'graphql-upload';
import * as _ from 'lodash';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
const { APP_URI } = process.env;

@Injectable()
@EventSubscriber()
export class ProjectScheduleSubscriber implements EntitySubscriberInterface<ProjectScheduleEntity> {
  constructor(connection: Connection, private novuService: NovuService, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectScheduleEntity;
  }

  async beforeInsert(event: InsertEvent<ProjectScheduleEntity>) {
    try {
      const { entity } = event;
      const { fileKey } = entity;

      if (fileKey && _.isObject(fileKey)) {
        const { key } = await this.fileService.uploadGqlFile(fileKey as FileUpload, '/project-schedules');
        entity.fileKey = key;
      }
    } catch (error) {
      getErrorMessage(error, 'ProjectScheduleSubscriber', 'beforeInsert');
    }
  }

  async afterUpdate(event: UpdateEvent<ProjectScheduleEntity>) {
    try {
      const { entity } = event;

      const { revision } = entity;

      if (
        !entity ||
        !entity.updatedBy ||
        event.databaseEntity?.updatedAt === event.entity?.updatedAt ||
        event.databaseEntity.revision === event.entity.revision
      )
        return;

      const schedule = await event.manager.findOne(ProjectScheduleEntity, { id: entity.id });
      const project = await event.manager.findOne(ProjectEntity, { id: schedule.projectId });
      const companyName = await event.manager.findOne(CompanyEntity, { id: project.companyId });
      const user = await event.manager.findOne(UserEntity, { id: entity.updatedBy });
      const collaborators = await event.manager.find(ProjectUserEntity, {
        where: { projectId: project.id, scheduleRole: 'Collaborator' },
        relations: ['user']
      });

      const link = `/schedules/schedule?projectScheduleId=${schedule?.id}&projectId=${project?.id}&companyId=${companyName.id}`;

      collaborators.forEach(async collaborator => {
        const payload: INovuPayload = {
          user: {
            avatar: collaborator.user?.avatar,
            name: collaborator.user?.name,
            email: collaborator.user?.email
          },
          company: companyName.name,
          title: project.title,
          head: user?.name,
          body:
            revision <= 1
              ? `has imported a new schedule file ‘${schedule.name}’ in Schedules.`
              : `has updated the schedule file ‘${schedule.name}’ by uploading a new version in Schedules`,
          header: revision <= 1 ? `📁 Schedule - New Schedule` : `📁 Schedule - File Update`,
          headColon: true,
          link: {
            mobile: '',
            web: link,
            uri: APP_URI,
            redirect: link
          }
        };

        await this.novuService.trigger('secondary-workflow', {
          to: {
            subscriberId: collaborator.userId.toString()
          },
          payload
        });
      });

    } catch (error) {
      getErrorMessage(error, 'ProjectScheduleSubscriber', 'afterUpdate');
    }
  }

  async beforeUpdate(event: UpdateEvent<ProjectScheduleEntity>): Promise<void | Promise<any>> {
    try {
      const { entity } = event;
      const { fileKey } = entity;

      if (fileKey && _.isObject(fileKey)) {
        const { key } = await this.fileService.uploadGqlFile(fileKey as FileUpload, '/project-schedules');
        entity.fileKey = key;
      }
    } catch (error) {
      getErrorMessage(error, 'ProjectScheduleSubscriber', 'beforeInsert');
    }
  }
}
