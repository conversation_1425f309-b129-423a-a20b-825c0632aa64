import { InputType, ObjectType, PartialType, ID, Field } from '@nestjs/graphql';
import { defaultQueryOptions } from '@constants';
import {
  BeforeCreateOne,
  BeforeUpdateOne,
  CreateOneInputType,
  IDField,
  QueryArgsType,
  QueryOptions,
  UpdateOneInputType,
} from '@nestjs-query/query-graphql';
import { ProjectScheduleEntity } from '../entity/project-schedule.entity';
import { GqlContext } from '@types';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { GraphQLUpload, FileUpload } from "graphql-upload";

@ObjectType('ProjectSchedule')
@BeforeCreateOne((instance: CreateOneInputType<CreateProjectScheduleInputDTO>, context: GqlContext) => {
  const projectId = Number(context.req.headers['project-id']);  
  instance.input.createdBy = context.req.user.id;

  instance.input.projectId = projectId;
  return instance;
})
@BeforeUpdateOne((instance: UpdateOneInputType<UpdateProjectScheduleInputDTO>, context: GqlContext) => {
  const updatedBy = context.req.user.id;
  instance.update.updatedBy = updatedBy;

  return instance;
})
@QueryOptions({ ...defaultQueryOptions })
export class ProjectScheduleDTO extends ProjectScheduleEntity {
  totalCount: number;
  delayCount: number;
  onHoldCount: number;
  inProgressCount: number;
  completedCount: number;
  userCount: number;
  updatedByUser?: UserDto;
  startVarianceCount?: number;
  finishVariance?: number;
  holdStatusCount?: number;
  fileKey?: string;
  isNotify?: boolean;
}
export class ProjectScheduleQuery extends QueryArgsType(ProjectScheduleDTO) {}
export const ProjectScheduleConnection = ProjectScheduleQuery.ConnectionType;
@InputType()
export class CreateProjectScheduleInputDTO {
  @IDField(() => ID) projectId?: number;
  name: string;
  description: string;
  isScheduleTracked: boolean;
  holidays?: string;
  revision?: number;

  createdBy?: number;

  errorLog?: string;
  @Field(() => GraphQLUpload) fileKey?: FileUpload;

}

@InputType()
export class UpdateProjectScheduleInputDTO extends PartialType(CreateProjectScheduleInputDTO) {
  id?: string;
  updatedBy?: number;
  isNotify?: boolean;
  @Field(() => GraphQLUpload) fileKey?: FileUpload;
}

@InputType()
export class DeleteProjectScheduleInputDTO {
  @IDField(() => ID) id: number;
}
