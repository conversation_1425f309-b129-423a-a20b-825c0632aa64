import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { ProjectSchedulesService } from './project-schedule.service';
import { CreateProjectScheduleInputDTO, DeleteProjectScheduleInputDTO, ProjectScheduleConnection, ProjectScheduleDTO, ProjectScheduleQuery } from './dto/project-schedule.gql.dto'; 
import { UpdateProjectScheduleInputDTO } from './dto/project-schedule.gql.dto'; 
import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { AuthData } from '@types';

@Resolver('ProjectSchedule')
export class ProjectSchedulesResolver {
  constructor(private readonly projectSchedulesService: ProjectSchedulesService) {}

  @Query(() => [ProjectScheduleDTO])
  async getProjectSchedules(
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  )  {
    return await this.projectSchedulesService.getProjectSchedules(projectId)
  }

  // Delete Project Schedule
  @Mutation(() => ProjectScheduleDTO)
  async deleteProjectSchedule(
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number,
    @Args('input') input: DeleteProjectScheduleInputDTO
  ) {
    return await this.projectSchedulesService.deleteProjectSchedule(
      input.id, 
      user?.id, 
      projectId
    );
  }
}
