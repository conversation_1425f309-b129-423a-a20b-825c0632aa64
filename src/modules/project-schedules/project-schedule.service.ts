import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProjectScheduleEntity } from './entity/project-schedule.entity';
import { getRepository, Repository } from 'typeorm';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { ScheduleStatus } from '@constants';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ScheduleService } from '@modules/schedule/schedule.service';
import { UserEntity } from '@modules/user/entity/user.entity';

@Injectable()
export class ProjectSchedulesService extends TypeOrmQueryService<ProjectScheduleEntity> {
  constructor(
    @InjectRepository(ProjectScheduleEntity)
    private projectScheduleRepo: Repository<ProjectScheduleEntity>,
    private scheduleService: ScheduleService
    // ScheduleService
  ) {
    super(projectScheduleRepo, { useSoftDelete: true });
  }

  async getProjectSchedules(projectId: number) {

    const proScheds = await getRepository(ProjectScheduleEntity)
    .createQueryBuilder("sched")
    .where("sched.projectId = :id", { id: projectId })
    .getMany()
        
    const countOfProScheds = await this.countForSchedule(proScheds);
    return countOfProScheds;
  }

  private async countForSchedule(proScheds: ProjectScheduleEntity[]) {
    return await Promise.all(
      proScheds.map(async proSched => {

        const total = await getRepository(ScheduleEntity)
                                  .createQueryBuilder("sched")  
                                  .where("sched.projectScheduleId = :id", { id: proSched.id })
        
        const delay = total
        const delayCount = delay.andWhere("sched.status = :status", { status: ScheduleStatus.Delay }).getCount()

        const onHold = total
        const onHoldCount = onHold.andWhere("sched.status = :status", { status: ScheduleStatus.Hold }).getCount()

        const inProgress = total
        const inProgressCount = inProgress.andWhere("sched.status = :status", { status: ScheduleStatus.InProgress }).getCount()

        const completed = total
        const completedCount = completed.andWhere("sched.status = :status", { status: ScheduleStatus.Completed }).getCount()

        const users = await getRepository(ProjectUserEntity)
                      .createQueryBuilder("proUser")  
                      .where("proUser.projectId = :id", { id: proSched.projectId })
          .andWhere('proUser.scheduleRole IS NOT NULL')
        
        const updatedByUser = await getRepository(UserEntity)
          .createQueryBuilder("user")
          .where("user.id = :id", { id: proSched.updatedBy })
          .getOne()

        // get summary of project schedule
        const summary = await this.scheduleService.getScheduleSummary(proSched.projectId, proSched?.id);        

        const param = {
          ...proSched,
          totalCount: total.getCount(),
          delayCount,
          onHoldCount,
          inProgressCount,
          completedCount,
          userCount: users.getCount(),
          updatedByUser: updatedByUser ?? null,
          ...summary
          
        };
        return param
      })
    );
  }

  async deleteProjectSchedule(data: any, user: any, projectId) {
    try {
      const exist = await getRepository(ProjectScheduleEntity).findOne({
        id: data,
        projectId,
      });

      if (!exist) throw new BadRequestException('Project Schedule not found');

      // TODO: CHECK IF USER IS AUTHORIZED TO DELETE

      return await this.projectScheduleRepo.softRemove({ id: exist.id, projectId });
    } catch (e) {
      
      throw new BadRequestException('Failed to delete Schedule');
    }
  }
}
