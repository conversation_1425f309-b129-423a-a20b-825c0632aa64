import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { CreateProjectScheduleInputDTO, ProjectScheduleDTO, UpdateProjectScheduleInputDTO } from './dto/project-schedule.gql.dto';
import { ProjectScheduleEntity } from './entity/project-schedule.entity';
import { ProjectSchedulesService } from './project-schedule.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ProjectSchedulesResolver } from './project-schedule.resolver';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { ScheduleService } from '@modules/schedule/schedule.service';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { ProjectScheduleSubscriber } from './project-schedule.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectScheduleEntity]), 
      NestjsQueryTypeOrmModule.forFeature([ScheduleEntity]),
      NestjsQueryTypeOrmModule.forFeature([ProjectScheduleEntity]),
      NestjsQueryTypeOrmModule.forFeature([SchedulesMediaEntity]),
       IntegrationModule],
      resolvers: [
        {
          DTOClass: ProjectScheduleDTO,
          EntityClass: ProjectScheduleEntity,
          CreateDTOClass: CreateProjectScheduleInputDTO,
          UpdateDTOClass: UpdateProjectScheduleInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      
      services: [ProjectSchedulesService, ProjectSchedulesResolver, ScheduleService, ProjectScheduleSubscriber]
      
    }),
    
    IntegrationModule,
  ],
  // providers:[ScheduleService]
})
export class ProjectScheduleModule {}
