import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ScheduleLinksEntity } from '@modules/schedule-links/entity/schedule-links.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

@ObjectType()
@Entity('project_schedules')
export class ProjectScheduleEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField()
  @Column('varchar')
  name: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  description: string;

  @FilterableField()
  @Column('boolean', { default: true })
  isScheduleTracked: boolean;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  holidays: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  errorLog: string;

  @FilterableField()
  @Column('int', { default: 0 })
  revision: number;

  @Column('text', { nullable: true })
  fileKey?: string;
  
  @Column('boolean', { nullable: true , default:false})
  isNotify?: boolean;

  /* -------------------------------- Relations ------------------------------- */

  @ManyToOne(() => ProjectEntity, project => project.schedules)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @OneToMany(() => ScheduleLinksEntity, project => project.projectSchedule)
  scheduleLinks: ProjectScheduleEntity[];

  @OneToMany(() => ScheduleEntity, project => project.projectSchedule)
  scheduleTasks: ProjectScheduleEntity[];
}
