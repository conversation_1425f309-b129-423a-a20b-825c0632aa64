import { BaseEntity } from '@modules/base/base';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('schedules_medias')
export class SchedulesMediaEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  scheduleId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text')
  fileUrl: string;

  @FilterableField()
  @Column('text')
  type: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  videoThumbnail: string;

  @Column('text', { nullable: true })
  fileKey: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ScheduleEntity, schedule => schedule.medias, {
    orphanedRowAction: 'soft-delete',
    onDelete: 'CASCADE'
  })
  @JoinColumn({ name: 'scheduleId' })
  schedule: ScheduleEntity;

  @ManyToOne(() => UserEntity, user => user.scheduleMedias)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
