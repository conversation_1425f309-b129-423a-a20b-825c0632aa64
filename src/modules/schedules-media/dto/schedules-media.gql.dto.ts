import { defaultQueryOptions } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { SchedulesMediaEntity } from '../entity/schedules-media.entity';

@ObjectType('SchedulesMedia')
@QueryOptions({ ...defaultQueryOptions })
export class SchedulesMediaDto extends SchedulesMediaEntity {}

@InputType()
export class CreateSchedulesMediaInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) userId?: number;
  @IDField(() => Number) scheduleId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;
  @Field(() => GraphQLUpload) videoThumbnail?: FileUpload;
}
@InputType()
export class UpdateSchedulesMediaInputDTO extends PartialType(CreateSchedulesMediaInputDTO) {}
