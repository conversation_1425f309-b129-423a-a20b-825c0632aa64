import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, getRepository } from 'typeorm';
import { SchedulesMediaEntity } from './entity/schedules-media.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '@modules/integration/file.service';
import * as _ from 'lodash';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { getErrorMessage } from '@common/error';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class SchedulesMediaSubscriber implements EntitySubscriberInterface<SchedulesMediaEntity> {
  constructor(connection: Connection, private fileService: FileService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return SchedulesMediaEntity;
  }

  async beforeInsert(event: InsertEvent<SchedulesMediaEntity>) {
    try {
      const { entity } = event;

      const file: any = entity.fileUrl;
      const thumbnail: any = entity.videoThumbnail;

      if (!file) throw new BadRequestException('No Document has been upload');

      if (thumbnail && _.isObject(thumbnail)) {
        const folder = 'Schedule-Medias';
        const { key } = await this.fileService.uploadGqlFile(thumbnail as FileUpload, folder);
        entity.videoThumbnail = key;
      }

      if (file && _.isObject(file)) {
        const folder = 'Schedule-Medias';
        const { filename, key, type } = await this.fileService.uploadGqlFile(file as FileUpload, folder);
        entity.fileKey = key;
        entity.name = filename;
        entity.type = type;
      }
    } catch (error) {
      getErrorMessage(error, 'SchedulesMediaSubscriber', 'beforeInsert');
    }
  }

  async afterInsert(event: InsertEvent<SchedulesMediaEntity>): Promise<any> {
    try {
      const { entity } = event;

      const currentUser = await getRepository(UserEntity).findOne({ where: { id: entity?.userId } });
      const schedule = await getRepository(ScheduleEntity).findOne({ where: { id: entity?.scheduleId } });
      const project = await getRepository(ProjectEntity).findOne({
        where: { id: schedule.projectId },
        relations: ['company']
      });

      const projectId = project?.id;
      const userId = currentUser?.id;

      const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&suid=${schedule?.suid}&projectId=${schedule?.projectId}&companyId=${project?.companyId}&status=${schedule?.status}`;

      const validatorsAndManagers = await this.findValidatorsAndManagers(projectId, userId, schedule?.id);

      const payload: INovuPayload = {
        user: {
          avatar: currentUser?.avatar,
          name: currentUser?.name,
          email: currentUser?.email
        },
        company: project?.company?.name,
        head: currentUser?.name,
        headColon: true,
        body: `has updated the photos`,
        tail: schedule?.name,
        title: project?.title,
        pushMessageTitle: `Schedule Updated`,
        link: {
          web: link,
          uri: APP_URI,
          mobile: '',
          redirect: link
        }
      };

      validatorsAndManagers
        ?.filter((user: any) => user?.user?.id !== userId)
        .forEach((user: any) => {
          return this.sendNotification(payload, user?.user);
        });

      // AUDIT LOG SCHEDULE (ADD MEDIA)
      if (entity.userId) {
        const schedule = await event.manager.getRepository(ScheduleEntity).findOne({ id: entity.scheduleId });
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

        const msg = user.name + ' added a media in schedule ' + schedule.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: schedule.projectId,
          // taskId: schedule.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Schedule,
          action: AuditLogActionType.AddPhoto,
          content: msg
        });
        await auditLog.save();
      }
    } catch (error) {
      getErrorMessage(error, 'SchedulesMediaSubscriber', 'afterInsert');
    }
  }

  async findValidatorsAndManagers(projectId: number, userId: number, scheduleId: number) {
    try {
      const validatorsAndManagers = await getRepository(ProjectUserEntity)
        .createQueryBuilder('ProjectUser')
        .leftJoinAndSelect('ProjectUser.user', 'User')
        .where('(projectUser.scheduleRole IS NOT NULL) AND (projectUser.projectId = :projectId)', { projectId })
        .andWhere('User.id != :userId', { userId })
        .getMany();

      const assigneesAndCCs = await getRepository(ScheduleEntity)
        .createQueryBuilder('Schedule')
        .leftJoinAndSelect('Schedule.assignees', 'Assignee')
        .leftJoinAndSelect('Schedule.copies', 'CC')
        .leftJoinAndSelect('Assignee.user', 'AssigneeUser')
        .leftJoinAndSelect('CC.user', 'CCUser')
        .where('Schedule.id = :scheduleId', { scheduleId: scheduleId })
        .getOne();

      return [...validatorsAndManagers, assigneesAndCCs?.assignees, assigneesAndCCs?.copies];
    } catch (error) {
      getErrorMessage(error, 'SchedulesMediaSubscriber', 'findValidatorsAndManagers');
    }
  }

  async sendNotification(payload: INovuPayload, user: UserEntity) {
    if (!user) return;

    return this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user?.id.toString(),
        email: user?.email
      },
      payload
    });
  }
}
