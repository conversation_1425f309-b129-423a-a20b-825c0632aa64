import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { SchedulesMediaEntity } from './entity/schedules-media.entity';
import { SchedulesMediaDto, CreateSchedulesMediaInputDTO, UpdateSchedulesMediaInputDTO } from './dto/schedules-media.gql.dto';
import { SchedulesMediaService } from './schedules-media.service';
import { SchedulesMediaSubscriber } from './schedules-media.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([SchedulesMediaEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: SchedulesMediaService,
          DTOClass: SchedulesMediaDto,
          EntityClass: SchedulesMediaEntity,
          CreateDTOClass: CreateSchedulesMediaInputDTO,
          UpdateDTOClass: UpdateSchedulesMediaInputDTO,
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard]
        }
      ],
      services: [SchedulesMediaSubscriber, SchedulesMediaService]
    })
  ]
})
export class SchedulesMediaModule {}
