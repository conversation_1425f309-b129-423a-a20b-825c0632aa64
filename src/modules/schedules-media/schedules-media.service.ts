import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SchedulesMediaEntity } from './entity/schedules-media.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class SchedulesMediaService extends TypeOrmQueryService<SchedulesMediaEntity> {
  constructor(
    @InjectRepository(SchedulesMediaEntity)
    private schedulesMediaRepo: Repository<SchedulesMediaEntity>
  ) {
    super(schedulesMediaRepo, { useSoftDelete: true });
  }
}
