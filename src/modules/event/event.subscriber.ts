import { AuditLogActionType, AuditLogModuleType, ProjectUserRoleType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Injectable } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, Connection, InsertEvent, Not, In } from 'typeorm';
import { SoftRemoveEvent } from 'typeorm/subscriber/event/SoftRemoveEvent';
import { EventEntity } from './entity/event.entity';
import { getErrorMessage } from '@common/error';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import moment from 'moment';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class EventsSubscriber implements EntitySubscriberInterface<EventEntity> {
  constructor(connection: Connection, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return EventEntity;
  }

  async beforeInsert(event: InsertEvent<EventEntity>) {
    const { entity } = event;

    // entity.userId = entity.createdBy;
    // entity.createdBy = null;
  }

  async afterInsert(event: InsertEvent<EventEntity>) {
    try {
      const { entity } = event;
      const owner = await event.manager.getRepository(UserEntity).findOne({ id: entity.createdBy });

      const projectUsers = await event.manager.getRepository(ProjectUserEntity).find({
        where: {
          projectId: entity.projectId,
          role: In([
            ProjectUserRoleType.ProjectOwner,
            ProjectUserRoleType.CloudCoordinator,
            ProjectUserRoleType.CanEdit
          ])
        },
        relations: ['user'],
        cache: true
      });

      const project = await event.manager
        .getRepository(ProjectEntity)
        .findOne({ id: entity.projectId }, { relations: ['company'] });

      const mobileLink = 'dashboard/calendar';
      const link = `/projects/dashboard?size=10&page=1&projectId=${entity?.projectId}&companyId=${project?.company.id}&showEvent=true&eventId=${entity?.id}`;
      const body = `${owner?.name} has create ${entity?.title}'s event on ${moment(entity?.startAt)
        .tz('Asia/Kuala_Lumpur')
        .format('DD-MM-YYYY')}`;

      if (entity?.scheduleForAll) {
        projectUsers.forEach(projectUser => {
          if (projectUser.user.id === entity.createdBy) return;
          return this.EventNotification(
            projectUser.user,
            project,
            link,
            mobileLink,
            owner,
            body,
            project?.company,
            entity?.title
          );
        });
      }

      const msg = owner.name + ' created ' + entity.title;
      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: entity.userId,
        projectId: entity.projectId,
        resourceId: entity.id,
        module: AuditLogModuleType.Calendar,
        action: AuditLogActionType.Create,
        content: msg
      });
      await event.manager.getRepository(AuditLogEntity).save(auditLog);
    } catch (e) {
      getErrorMessage(e, 'EventsSubscriber', 'afterInsert');
    }
  }

  async beforeSoftRemove(event: SoftRemoveEvent<EventEntity>) {
    try {
      const { entity } = event;
      if (entity) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

        const msg = user.name + ' deleted the event ' + entity.title;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Calendar,
          action: AuditLogActionType.Delete,
          content: msg
        });
        await auditLog.save();
      }
    } catch (e) {
      getErrorMessage(e, 'EventsSubscriber', 'beforeSoftRemove');
    }
  }

  EventNotification(user, project, link, mobileLink, owner, body, companyName = null, eventName) {
    const header = '📅 Calendar - New Event';
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      header,
      event: 'schedule-event-for-all',
      company: companyName.name,
      title: project.title,
      body: body,
      head: project.title,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: user.name
      }
    };

    this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          android: {
            priority: 'high'
          },
          data: {
            link: mobileLink.toString(),
            projectId: project?.id.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }
}
