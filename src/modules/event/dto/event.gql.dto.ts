import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, FilterableOffsetConnection, QueryArgsType, QueryOptions } from '@nestjs-query/query-graphql';
import { ArgsType, Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { EventEntity } from '../entity/event.entity';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { CreateEventAssigneeInputDTO, EventAssigneeDto } from '@modules/event-assignee/dto/event-assignee.gql.dto';
import { relationOption } from '@constants/query.constant';

@ObjectType('Event')
@FilterableOffsetConnection('event', () => EventAssigneeDto, relationOption(true))
@BeforeCreateOne(Hooks.CreatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class EventDto extends EventEntity {}

@InputType()
export class CreateEventInputDTO {
  title: string;
  startAt: Date;
  endAt: Date;
  startTime?: string;
  endTime?: string;
  isAllDay?: boolean;
  scheduleForAll?: boolean;
  @Field(() => [CreateEventAssigneeInputDTO]) assignees?: CreateEventAssigneeInputDTO[];
}
@InputType()
export class UpdateEventInputDTO extends PartialType(CreateEventInputDTO) { }

@InputType()
export class deleteManyEvent {
  title: string;
  createdBy: string;
  startAt: Date;
  endAt: Date;
}

@ArgsType()
export class getEventsQuery extends QueryArgsType(EventDto) {}
export const getEventsConnection = getEventsQuery.ConnectionType;