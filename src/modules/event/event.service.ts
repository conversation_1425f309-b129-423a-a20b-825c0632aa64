import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, getRepository } from 'typeorm';
import { EventEntity } from './entity/event.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { CreateEventInputDTO, deleteManyEvent, getEventsQuery } from './dto/event.gql.dto';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getErrorMessage } from '@common/error';
import moment from 'moment';

@Injectable()
export class EventService extends TypeOrmQueryService<EventEntity> {
  constructor(
    @InjectRepository(EventEntity)
    private eventRepo: Repository<EventEntity>,
    @InjectRepository(ProjectUserEntity)
    private projectUser: Repository<ProjectUserEntity>
  ) {
    super(eventRepo, { useSoftDelete: true });
  }

  async getEvents(projectId: number, userId: number, q: getEventsQuery) { 
    try {
      const events = await getRepository(EventEntity)
        .createQueryBuilder('event')
        .leftJoinAndSelect('event.eventAssignees', 'eventAssignees')
        .leftJoinAndSelect('eventAssignees.user', 'user')
        .where('event.projectId = :projectId', { projectId })
        .andWhere('(event.userId = :userId OR user.id = :userId OR event.scheduleForAll = :scheduleForAll)', {
          userId,
          scheduleForAll: true
        })
        .getMany();
      
      return events;
    } catch (error) {
      getErrorMessage(error, 'EventService', 'getEvents');
    }
  }

  async createEvent(event: CreateEventInputDTO, projectId: number, userId: number) {
    try {
        return await this.eventRepo.save({
          title: event.title,
          startAt: event.startAt,
          endAt: event.endAt,
          startTime: event.startTime,
          endTime: event.endTime,
          isAllDay: event.isAllDay,
          userId: userId,
          projectId: projectId,
          createdBy: userId,
          scheduleForAll: event.scheduleForAll ?? false
        });
    } catch (error) {
      getErrorMessage(error, 'EventService', 'createEvent');
    }
  }

  async deleteManyEvents(deleteManyEvent: deleteManyEvent, projectId: number) {
    try {
      const { title, createdBy, startAt, endAt } = deleteManyEvent;

      const events = await getRepository(EventEntity)
        .createQueryBuilder('event')
        .where('event.title = :title', { title })
        .andWhere('event.createdBy = :createdBy', { createdBy })
        .andWhere('DATE(event.endAt) = :endAt', { endAt: moment(endAt).format('YYYY-MM-DD') })
        .andWhere('event.projectId = :projectId', { projectId })
        .andWhere('event.scheduleForAll = :scheduleForAll', { scheduleForAll: true })
        .getMany();
      
      if (events.length === 0) throw Error('Event not found')
      
      const result = await getRepository(EventEntity).softRemove(events);
      return result[0];
    } catch (error) {
      throw new Error(error);
    }
  }
}
