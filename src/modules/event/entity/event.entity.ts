import { BaseEntity } from '@modules/base/base';
import { EventAssigneeEntity } from '@modules/event-assignee/entity/event-assignee.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime, Field } from '@nestjs/graphql';
import { GraphQLBoolean } from 'graphql';
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm';

@ObjectType()
@Entity('events')
export class EventEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @FilterableField()
  @Column('varchar')
  title: string;

  @FilterableField(() => GraphQLISODateTime)
  @Column('datetime')
  startAt: Date;

  @FilterableField(() => GraphQLISODateTime)
  @Column('datetime')
  endAt: Date;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  startTime: string;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  endTime: string;

  @Field()
  @Column('boolean', { default: false })
  isAllDay: boolean;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField(() => GraphQLBoolean)
  @Column('boolean', { default: false })
  scheduleForAll: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.events)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @OneToMany(() => EventAssigneeEntity, eventAssignee => eventAssignee.eventAssignees, {
    cascade: true
  })
  eventAssignees: EventAssigneeEntity[];
}
