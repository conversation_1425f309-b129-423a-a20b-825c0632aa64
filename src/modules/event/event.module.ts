import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { EventEntity } from './entity/event.entity';
import { EventDto, CreateEventInputDTO, UpdateEventInputDTO } from './dto/event.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { EventService } from './event.service';
import { EventsSubscriber } from './event.subscriber';
import { EventResolver } from './event.resolver'
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { IntegrationModule } from '@modules/integration/integration.module';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([EventEntity, ProjectUserEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: EventService,
          DTOClass: EventDto,
          EntityClass: EventEntity,
          CreateDTOClass: CreateEventInputDTO,
          UpdateDTOClass: UpdateEventInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [EventsSubscriber, EventService, EventResolver]
    })
  ]
})
export class EventModule {}
