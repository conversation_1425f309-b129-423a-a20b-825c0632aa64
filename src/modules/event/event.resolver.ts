import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver, Query } from '@nestjs/graphql';
import { AuthData } from '@types';
import { EventDto, CreateEventInputDTO, deleteManyEvent, getEventsQuery, getEventsConnection } from './dto/event.gql.dto';
import { EventService } from './event.service';
import { Filter } from '@nestjs-query/core';

@UseGuards(GqlAuthGuard)
@Resolver(() => EventDto)
export class EventResolver {
  constructor(private eventService: EventService) { }
  
  @Query(() => getEventsConnection)
  async getEventsByAssignee(
    @Args() query: getEventsQuery,
    @GqlGetGqlAuthData() authData: AuthData,
    @GqlGetGqlProjectData() projectId: any,
  ) {
    const filter: Filter<getEventsQuery> = {
      ...query.filter
    };

    return await getEventsConnection.createFromPromise(
      async q => await this.eventService.getEvents(projectId, authData.id, q),
      { ...query, ...{ filter } }
    );
  }

  @Mutation(() => EventDto)
  async createEvent(
    @Args('createEventInput') createEventInput: CreateEventInputDTO,
    @GqlGetGqlAuthData() authData: AuthData,
    @GqlGetGqlProjectData() projectData: any,
  ) {
    const { id } = authData;
    const event = await this.eventService.createEvent(createEventInput, projectData, id);
    return event;
  } 

  // mutation for delete many events
  @Mutation(() => EventDto)
  async deleteManyEvent(
    @Args('deleteManyEvent') deleteManyEvent: deleteManyEvent,
    @GqlGetGqlAuthData() authData: AuthData,
    @GqlGetGqlProjectData() projectId: any,
  ) {
    const events = await this.eventService.deleteManyEvents(deleteManyEvent, projectId);
    return events;
  }
} 
