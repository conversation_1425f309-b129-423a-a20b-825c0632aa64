import { WorkerHostProcessor } from '@modules/bull-mq/bullmq-process';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { RequestForSignatureService } from '../request-for-signature.service';

@Processor('request-for-signature-notification-process')
export class RequestForSignatureNotificationQueProcessor extends WorkerHostProcessor {
  constructor(private readonly requestForSignatureService: RequestForSignatureService) {
    super();
  }

  async process(job: Job<any>): Promise<void> {
    if (job.name === 'requestForSignatureStatusSent') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusSent(
        job.data.requestForSignatureEntityId,
        job.data.isResubmit
      );
    } else if (job.name === 'requestForSignatureStatusAmend') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusAmend(job.data.requestForSignatureEntityId);
    } else if (job.name === 'requestForSignatureStatusInReview') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusInReview(job.data.requestForSignatureEntityId);
    } else if (job.name === 'requestForSignatureStatusReviewProgress') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusReviewProgress(job.data.requestForSignatureEntityId);
    } else if (job.name === 'requestForSignatureStatusReviewApproveReject') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusReviewApproveReject(job.data.requestForSignatureEntityId);
    } else if (job.name === 'requestForSignatureStatusApproveProceed') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusApproveProceed(job.data.requestForSignatureEntityId);
    }  else if (job.name === 'requestForSignatureStatusProceed') {
      await this.requestForSignatureService.notifyRequestForSignatureStatusProceed(job.data.requestForSignatureEntityId);
    }
  }
}
