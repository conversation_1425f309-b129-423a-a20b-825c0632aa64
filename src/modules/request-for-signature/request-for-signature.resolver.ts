import { ProjectUserRoleType } from '@constants';
import { GqlGetGqlAuthData, GqlGetGqlProjectData, ProjectRoles } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { AuthData } from '@types';
import {
  ApproveFormInputDTO,
  CreateManyRequestForSignaturesInput,
  ApproveFormOrUpdateStatusInputDTO,
  RequestForSignatureDto,
  CheckInvolvedUserInputDTO,
  UpdateDynamicWorkspaceInputDTO,
  ApproveOrRejectWorkspaceInputDTO,
  AssignWorkspaceAssigneeInputDTO
} from './dto/request-for-signature.gql.dto';
import { RequestForSignatureService } from './request-for-signature.service';

@UseGuards(GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard)
@Resolver(() => RequestForSignatureDto)
export class RequestForSignatureResolver {
  constructor(private requestForSignatureService: RequestForSignatureService) {}

  @ProjectRoles(ProjectUserRoleType.ProjectOwner, ProjectUserRoleType.CloudCoordinator, ProjectUserRoleType.CanEdit)
  @Mutation(() => [RequestForSignatureDto])
  async createManyRequestForSignatures(
    @Args('input') input: CreateManyRequestForSignaturesInput,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.requestForSignatureService.createManyRequestForSignatures(input, user.id, projectId);
  }

  @ProjectRoles(ProjectUserRoleType.ProjectOwner, ProjectUserRoleType.CloudCoordinator, ProjectUserRoleType.CanEdit)
  @Mutation(() => RequestForSignatureDto)
  async approveForm(@GqlGetGqlAuthData() user: AuthData, @Args('input') input: ApproveFormInputDTO) {
    return await this.requestForSignatureService.approveForm(user.id, input.id);
  }

  @Mutation(() => RequestForSignatureDto)
  async approveFormOrUpdateStatus(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: ApproveFormOrUpdateStatusInputDTO
  ) {
    const data =  await this.requestForSignatureService.approveFormOrUpdateStatus(user.id, input);

    await this.requestForSignatureService.addOwnerAsNewAssignee(input.id, user?.id);
    
    return data
  }

  @Mutation(() => CheckInvolvedUserInputDTO)
  async checkInvolvedUser(@GqlGetGqlAuthData() user: AuthData, @Args('id') projectDocumentId: number) {
    return await this.requestForSignatureService.checkInvolvedUser(user.id, projectDocumentId);
  }

  @Mutation(() => RequestForSignatureDto)
  async updateDynamicWorkspace(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: UpdateDynamicWorkspaceInputDTO
  ) {
    return await this.requestForSignatureService.updateDynamicWorkspace(user.id, input);
  }

  @Mutation(() => RequestForSignatureDto)
  async approveOrRejectWorkspace(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: ApproveOrRejectWorkspaceInputDTO
  ) {
    return await this.requestForSignatureService.approveOrRejectWorkspace(user.id, input);
  }

  @Mutation(() => RequestForSignatureDto)
  async amendToOwner(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('id') id: number,
  ) {
    return await this.requestForSignatureService.amendToOwner(user.id, id);
  } 

  @Mutation(() => RequestForSignatureDto)
  async assignRequestForSignature(
    @Args('input') input: AssignWorkspaceAssigneeInputDTO,
  ) {
    return await this.requestForSignatureService.assignRequestForSignature(
        input
    );
  }
}
