import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { RequestForSignatureEntity } from './entity/request-for-signature.entity';
import {
  RequestForSignatureDto,
  CreateRequestForSignatureInputDTO,
  UpdateRequestForSignatureInputDTO
} from './dto/request-for-signature.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { RequestForSignatureService } from './request-for-signature.service';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { RequestForSignatureSubscriber } from './request-for-signature.subscriber';
import { RequestForSignatureResolver } from './request-for-signature.resolver';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { BullMQModule } from '@modules/bull-mq/bullmq-module';
import { RequestForSignatureNotificationQueProcessor } from './processor/request-for-signature-notification.process';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([
          RequestForSignatureEntity,
          ProjectDocumentEntity,
          WorkspaceGroupEntity,
          UserEntity,
          ProjectEntity,
          CompanyEntity,
          WorkspaceCCEntity
        ]),
        IntegrationModule,
        BullMQModule.register({
          queues: ['request-for-signature-notification-process']
        })
      ],
      resolvers: [
        {
          ServiceClass: RequestForSignatureService,
          DTOClass: RequestForSignatureDto,
          EntityClass: RequestForSignatureEntity,
          CreateDTOClass: CreateRequestForSignatureInputDTO,
          UpdateDTOClass: UpdateRequestForSignatureInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard],
          create: {
            many: { disabled: true },
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          }
        }
      ],
      services: [RequestForSignatureResolver, RequestForSignatureSubscriber, RequestForSignatureService, MailgunService]
    }),
    IntegrationModule
  ],
  providers: [RequestForSignatureNotificationQueProcessor],
})
export class RequestForSignatureModule {}
