import { defaultQueryOptions, RequestForSignatureStatus, SourceType } from '@constants';
import { relationOption } from '@constants/query.constant';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { CreateManyInputType, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { RequestForSignatureEntity } from '../entity/request-for-signature.entity';
@ObjectType('RequestForSignature')
@QueryOptions({ ...defaultQueryOptions })
@Relation('projectDocument', () => ProjectDocumentDto, relationOption())
@Relation('owner', () => UserDto, relationOption())
@Relation('signBy', () => UserDto, relationOption())
export class RequestForSignatureDto extends RequestForSignatureEntity {
  //? offline mode
  remoteId?: string;
  documentStatus?: string;
}

@InputType()
export class CreateRequestForSignatureInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => ID) projectDocumentId: number;
  @IDField(() => Number) ownerId?: number;
  @IDField(() => Number) signById?: number;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  status?: RequestForSignatureStatus;
  assigneeNo?: number;
  updated_at?: Date;
  localProjectDocumentId?: string;
  @Field({ nullable: true }) recordSource?: SourceType;

}

@InputType()
export class CreateManyRequestForSignaturesInput extends CreateManyInputType(
  'requestForSignatures',
  CreateRequestForSignatureInputDTO
) {}

@InputType()
export class UpdateRequestForSignatureInputDTO extends PartialType(CreateRequestForSignatureInputDTO) {
  status: RequestForSignatureStatus;

  //? for offline
  @Field() localId?: string;
  documentStatus?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
}

@InputType()
export class ApproveFormInputDTO {
  @IDField(() => ID) id: number;
}
@InputType()
export class ApproveFormOrUpdateStatusInputDTO {
  @IDField(() => ID) id: number;
  status: RequestForSignatureStatus;
}

@InputType()
export class AssignWorkspaceAssigneeInputDTO {
  @IDField(() => ID) projectDocumentId: number;
  @Field(() => [CreateRequestForSignatureInputDTO]) assignees?: CreateRequestForSignatureInputDTO[];
}

@ObjectType()
export class CheckInvolvedUserInputDTO {
  @Field(() => Boolean) isInvolved: boolean;
}

@InputType()
export class UpdateDynamicWorkspaceInputDTO {
  currentAssigneeId: number;
  updatedUserStatus: RequestForSignatureStatus;
  newAssigneeId?: number;
}

@InputType()
export class ApproveOrRejectWorkspaceInputDTO { 
  @IDField(() => ID) id: number;
  status: RequestForSignatureStatus;
  inProgress?: boolean;
}
