import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { RequestForSignatureEntity } from './entity/request-for-signature.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import * as _ from 'lodash';
import {
  AuditLogActionType,
  AuditLogModuleType,
  ProjectDocumentStatus,
  RequestForSignatureStatus,
  workflowType
} from '@constants';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { getErrorMessage } from '@common/error';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
@Injectable()
@EventSubscriber()
export class RequestForSignatureSubscriber implements EntitySubscriberInterface<RequestForSignatureEntity> {
  constructor(
    connection: Connection,
    @InjectQueue('request-for-signature-notification-process')
    private requestForSignatureNotificationQueue: Queue
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return RequestForSignatureEntity;
  }

  async beforeInsert(event: InsertEvent<RequestForSignatureEntity>) {
    const { entity } = event;

    // check if the record is ady exist in the table then don't insert it
    

    // const requestForSignature = await event.manager
    //   .getRepository(RequestForSignatureEntity)
    //   .findOne({ projectDocumentId: entity.projectDocumentId, signById: entity.signById });

    // if (requestForSignature) {
    //   requestForSignature.status = RequestForSignatureStatus.Unsent;
    //   requestForSignature.save();
    //   // const signBy = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });
    //   // throw new BadRequestException(`${signBy.name} had been requested to sign this form.`);
    // }
  }
  /************************************ On Edit Workspace Drawer sends notification ********************************************** */
  // async afterInsert(event: InsertEvent<RequestForSignatureEntity>) {
  //   const { entity } = event;

  //   const signBy = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });
  //   const owner = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });
  //   const form = await event.manager
  //     .getRepository(ProjectDocumentEntity)
  //     .findOne({ id: entity.projectDocumentId });

  //   const link = `/viewer/all-form-viewer?documentId=${form.id}&projectId=${form.projectId}`;
  //   const mobileLink = `digital-form/0/:${form.id}`;
  //   const project = await event.manager.getRepository(ProjectEntity).findOne({ id: form.projectId });
  //   const redirect = `/digital-form/all-form?documentDetailsId=${form.id}`;

  //   const payload: INovuPayload = {
  //     user: {
  //       avatar: owner.avatar,
  //       name: owner.name,
  //       email: owner.email,
  //     },
  //     title: project.title,
  //     head: owner.name,
  //     body: 'is requesting for your signature',
  //     tail: form.name,
  //     link: {
  //       mobile: mobileLink,
  //       web: link,
  //       uri: APP_URI,
  //       redirect: redirect,
  //     },
  //     subscriber: {
  //       firstName: signBy.name,
  //     },
  //   };

  //   this.novuService.trigger('workspace-approval', {
  //     to: {
  //       subscriberId: signBy.id.toString(),
  //       email: signBy.email,
  //     },
  //     payload,
  //   });
  // }
  /************************************ On Edit Workspace Drawer sends notification ********************************************** */

  async afterInsert(event: InsertEvent<RequestForSignatureEntity>) {
    try {
      const { entity } = event;

      const document = await event.manager
        .getRepository(ProjectDocumentEntity)
        .findOne({ id: entity.projectDocumentId });

      if (entity?.status === RequestForSignatureStatus?.Sent && document?.status !== ProjectDocumentStatus.Draft) {
        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusApproveProceed',
          { requestForSignatureEntityId: entity.id },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      const signBy = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });
      const owner = await event.manager.getRepository(UserEntity).findOne({ id: entity.ownerId });
      const form = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.projectDocumentId });
      const project = await event.manager.getRepository(ProjectEntity).findOne({ id: form.projectId });
      const msg = `${owner?.name} added ${signBy?.name} in Assignee`;

      //? find the if the cc is already in the audit log
      const isAuditLogExist = await event.manager.getRepository(AuditLogEntity).findOne({
        where: {
          userId: owner.id,
          projectId: project.id,
          content: msg,
          resourceId: entity.projectDocumentId
        }
      });

      if (isAuditLogExist) return;

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: owner.id,
        projectId: project.id,
        resourceId: entity.projectDocumentId,
        module: AuditLogModuleType.Workspace,
        action: AuditLogActionType.Assigned,
        content: msg
      });
      await auditLog.save();
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureSubscriber', 'afterInsert');
    }
  }

  async afterUpdate(event: UpdateEvent<RequestForSignatureEntity>) {
    try {
      const { entity, databaseEntity } = event;
      const allForm = await event.manager
        .getRepository(ProjectDocumentEntity)
        .findOne({ id: entity.projectDocumentId });

      const document = await event.manager
        .getRepository(ProjectDocumentEntity)
        .findOne({ id: entity.projectDocumentId });

      if (!document) {
        throw new BadRequestException(`Document not found`);
      }

      const groupName = await event.manager.findOne(WorkspaceGroupEntity, {
        where: { id: document.workspaceGroupId },
        relations: ['parent', 'parent.workspaceGroupUsers']
      });

      if (entity.status === RequestForSignatureStatus.Sent && databaseEntity?.status !== RequestForSignatureStatus.Amend) {
        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusSent',
          { requestForSignatureEntityId: entity.id, },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      // when owner resubmit document after request for amendment
      if (entity.status === RequestForSignatureStatus.Proceed && databaseEntity?.status !== RequestForSignatureStatus.Sent) {
        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusProceed',
          { requestForSignatureEntityId: entity.id},
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      // Sent notification when assignee click amend
      if (entity.status === RequestForSignatureStatus.Amend) {
        if (document?.currentUserId === entity?.id) return;

        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusAmend',
          { requestForSignatureEntityId: entity.id },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      // Sent notification when assignee click submit
      if (
        entity.status === RequestForSignatureStatus.InReview &&
        document?.status === ProjectDocumentStatus.InProgress
      ) {
        if (document?.currentUserId === entity?.id) return;

        if (groupName?.parent?.code !== 'NCR' && groupName?.parent?.code !== 'TQ') return;

        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusReviewProgress',
          { requestForSignatureEntityId: entity.id },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      // Change Status of All Form to "In Review" once a assignee clicked "In Review" button
      if (entity.status === RequestForSignatureStatus.InReview && allForm.status === ProjectDocumentStatus.Submitted) {
        event.manager.getRepository(ProjectDocumentEntity).merge(allForm, { status: ProjectDocumentStatus.InReview });
        await event.manager.getRepository(ProjectDocumentEntity).save(allForm);

        const signBy = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });
        const form = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.projectDocumentId });
        const project = await event.manager.getRepository(ProjectEntity).findOne({ id: form.projectId });
        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusInReview',
          { requestForSignatureEntityId: entity.id },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );

        // AUDIT LOG FOR WORKSPACE STATUS UPDATED TO IN REVIEW
        const msg = signBy.name + ' updated ' + form.name + ' status to ' + 'In Review';

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: signBy.id,
          projectId: project.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.UpdateStatus,
          content: msg
        });
        await auditLog.save();
      }

      if (
        entity.status === RequestForSignatureStatus.Approved ||
        entity.status === RequestForSignatureStatus.Rejected
      ) {
        this.requestForSignatureNotificationQueue.add('requestForSignatureStatusReviewApproveReject', 
          { requestForSignatureEntityId: entity.id },
          { delay: 5000 } // delay 5 seconds to make sure the document status is updated first
        );
      }

      const requestForSignatures = await event.manager
        .getRepository(RequestForSignatureEntity)
        .find({ projectDocumentId: entity.projectDocumentId });

      // Change Status of All Form to "Rejected"
      const rejected = _.find(requestForSignatures, ['status', RequestForSignatureStatus.Rejected]);
      if (rejected) {
        event.manager.getRepository(ProjectDocumentEntity).merge(allForm, { status: ProjectDocumentStatus.Rejected });
        await event.manager.getRepository(ProjectDocumentEntity).save(allForm);

        // AUDIT LOG FOR WORKSPACE STATUS UPDATED TO REJECTED
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });

        const msg = user.name + ' updated ' + allForm.name + ' status to ' + 'Rejected';

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: allForm.projectId,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.UpdateStatus,
          content: msg
        });
        await auditLog.save();
      }

      // Change Status of All Form to "Approved"
      const allApproved = _.every(requestForSignatures, ['status', RequestForSignatureStatus.Approved]);
      if (allApproved && document?.workflow === workflowType.Linear) {
        event.manager.getRepository(ProjectDocumentEntity).merge(allForm, { status: ProjectDocumentStatus.Approved });
        await event.manager.getRepository(ProjectDocumentEntity).save(allForm);

        // AUDIT LOG FOR WORKSPACE STATUS UPDATED TO REJECTED
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });

        const msg = user.name + ' updated ' + allForm.name + ' status to ' + 'Approved';

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: allForm.projectId,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.UpdateStatus,
          content: msg
        });
        await auditLog.save();
      } else if (entity.status === RequestForSignatureStatus.Approved && document?.workflow === workflowType.Dynamic) {
        event.manager.getRepository(ProjectDocumentEntity).merge(allForm, { status: ProjectDocumentStatus.Approved });
        await event.manager.getRepository(ProjectDocumentEntity).save(allForm);

        // AUDIT LOG FOR WORKSPACE STATUS UPDATED TO REJECTED
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.signById });

        const msg = user.name + ' updated ' + allForm.name + ' status to ' + 'Approved';

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: allForm.projectId,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.UpdateStatus,
          content: msg
        });
        await auditLog.save();
      }
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureSubscriber', 'afterUpdate');
    }
  }
}
