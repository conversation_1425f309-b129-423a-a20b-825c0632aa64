import { RequestForSignatureStatus } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn, OneToOne } from 'typeorm';

@ObjectType()
// @Unique(['projectDocumentId', 'signById'])
@Entity('request_for_signatures')
export class RequestForSignatureEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectDocumentId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  ownerId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  signById: number;

  @FilterableField(() => RequestForSignatureStatus, { nullable: true })
  @Column('enum', {
    enum: RequestForSignatureStatus,
    nullable: true,
    default: RequestForSignatureStatus.Pending
  })
  status: RequestForSignatureStatus;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  assigneeNo: number;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.requestForSignatures, {
    orphanedRowAction: 'delete'
  })
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;

  @ManyToOne(() => UserEntity, user => user.requestForSignatures)
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @ManyToOne(() => UserEntity, user => user.signForRequests, { nullable: true })
  @JoinColumn({ name: 'signById' })
  signBy: UserEntity;

  @OneToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.currentUser)
  currentUser: ProjectDocumentEntity;
}
