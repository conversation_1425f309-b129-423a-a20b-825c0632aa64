import { ProjectDocumentStatus, ProjectUserRoleType, RequestForSignatureStatus, workflowType } from '@constants';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { BadRequestException, ForbiddenException, Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getManager, getRepository, Repository, Not } from 'typeorm';
import {
  ApproveOrRejectWorkspaceInputDTO,
  AssignWorkspaceAssigneeInputDTO,
  CreateManyRequestForSignaturesInput,
  UpdateDynamicWorkspaceInputDTO
} from './dto/request-for-signature.gql.dto';
import { RequestForSignatureEntity } from './entity/request-for-signature.entity';
import { ApproveFormOrUpdateStatusInputDTO } from './dto/request-for-signature.gql.dto';
import * as _ from 'lodash';
import { getErrorMessage } from '@common/error';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { getDocumentNotificationPayload, getWorkflowNotificationPath } from '@constants/function';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';

const { APP_URI } = process.env;
@Injectable()
export class RequestForSignatureService extends TypeOrmQueryService<RequestForSignatureEntity> {
  constructor(
    private readonly novuService: NovuService,
    @InjectRepository(RequestForSignatureEntity)
    private readonly requestForSignatureRepo: Repository<RequestForSignatureEntity>,
    @InjectRepository(ProjectDocumentEntity)
    private projectDocumentRepo: Repository<ProjectDocumentEntity>,
    @InjectRepository(WorkspaceGroupEntity)
    private workspaceGroupRepo: Repository<WorkspaceGroupEntity>,
    @InjectRepository(UserEntity)
    private userEntityRepo: Repository<UserEntity>,
    @InjectRepository(ProjectEntity)
    private projectEntityRepo: Repository<ProjectEntity>,
    @InjectRepository(CompanyEntity)
    private companyEntityRepo: Repository<CompanyEntity>,
    @InjectRepository(WorkspaceCCEntity)
    private workspaceCCEntityRepo: Repository<WorkspaceCCEntity>,
  ) {
    super(requestForSignatureRepo, { useSoftDelete: true });
  }

  async notifyRequestForSignatureStatusSent(id: number, isResubmit?: boolean) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });
    const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);
    const owner = await this.userEntityRepo.findOne({ id: requestForSignature.ownerId });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });

    let previousSignBy;
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const notRestricted = groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(signBy.id);
    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;
    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&status=${requestForSignature.status}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;

    if (isResubmit) {
      previousSignBy = await this.requestForSignatureRepo.findOne(
        {
          projectDocumentId: requestForSignature.projectDocumentId,
          assigneeNo: requestForSignature.assigneeNo - 1
        },
        { relations: ['signBy'] }
      );
    }

    const { header, head, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      isResubmit ? 'Resubmit' : 'Request Approval',
      isResubmit ? '✍️ Amendment Request Update' : '✍️ New Document Approval',
      isResubmit ? previousSignBy?.signBy?.name : owner.name,
      isResubmit ? 'has resubmitted a file for your amendment request' : 'requested you to sign',
      document.name
    );

    //Mobile Notifications
    const notification = getRepository(NotificationTransactionEntity).create({
      userId: owner.id,
      actorId: signBy.id,
      title: project.title,
      actionName: document.name,
      actionType: isResubmit ? 'has resubmitted a file for your amendment request' : 'requested you to sign',
      deeplink: link,
      mobileDeeplink: mobileLink
    });

    await getRepository(NotificationTransactionEntity).save(notification);

    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      header,
      event: 'workspace-approval',
      bodyContent: 'requested you to sign',
      subject: 'You have a new approval request',
      company: companyName.name,
      title: project.title,
      head,
      body: message,
      tail,
      bodyColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: redirect
      },
      subscriber: {
        firstName: signBy.name
      }
    };

    if (notRestricted) {
      this.novuService.trigger('primary-workflow', {
        to: {
          subscriberId: signBy.id.toString(),
          email: signBy.email
        },
        payload,
        overrides: {
          android: {
            priority: 'high'
          },
          fcm: {
            data: {
              link: mobileLink.toString(),
              projectId: project.id.toString(),
              companyId: companyName?.id.toString()
            }
          }
        }
      });
    }
  }

  async notifyRequestForSignatureStatusProceed(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });
    const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);
    const owner = await this.userEntityRepo.findOne({ id: requestForSignature.ownerId });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });

    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const notRestricted = groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(signBy.id);
    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;
    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&status=${requestForSignature.status}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;

    const nextSignBy = await this.requestForSignatureRepo.findOne(
      {
        projectDocumentId: requestForSignature.projectDocumentId,
        assigneeNo: requestForSignature.assigneeNo + 1
      },
      { relations: ['signBy'] }
    );

    const { header, head, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      'Resubmit',
      '✍️ Amendment Request Update',
      signBy?.name,
      'has resubmitted a file for your amendment request',
      document.name
    );

    //Mobile Notifications
    const notification = getRepository(NotificationTransactionEntity).create({
      userId: owner.id,
      actorId: signBy.id,
      title: project.title,
      actionName: document.name,
      actionType: 'has resubmitted a file for your amendment request',
      deeplink: link,
      mobileDeeplink: mobileLink
    });

    await getRepository(NotificationTransactionEntity).save(notification);

    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      header,
      event: 'workspace-approval',
      bodyContent: 'requested you to sign',
      subject: 'You have a new approval request',
      company: companyName.name,
      title: project.title,
      head,
      body: message,
      tail,
      bodyColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: redirect
      },
      subscriber: {
        firstName: nextSignBy?.signBy.name
      }
    };

    if (notRestricted) {
      this.novuService.trigger('primary-workflow', {
        to: {
          subscriberId: nextSignBy?.signBy.id.toString(),
          email: nextSignBy?.signBy.email
        },
        payload,
        overrides: {
          android: {
            priority: 'high'
          },
          fcm: {
            data: {
              link: mobileLink.toString(),
              projectId: project.id.toString(),
              companyId: companyName?.id.toString()
            }
          }
        }
      });
    }
  }

  async notifyRequestForSignatureStatusAmend(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });
    const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);
    const notRestricted = groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(signBy.id);
    const amendFrom = await this.requestForSignatureRepo.findOne(
      {
        projectDocumentId: requestForSignature.projectDocumentId,
        assigneeNo: requestForSignature.assigneeNo + 1
      },
      { relations: ['signBy'] }
    );
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });
    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;
    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&status=${requestForSignature.status}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;

    const { header, head, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      'Amend',
      '🔙 New Document Amendment',
      amendFrom?.signBy.name,
      'requesting amendment for',
      document.name
    );

    const payload: INovuPayload = {
      user: {
        avatar: amendFrom?.signBy?.avatar ?? '',
        name: amendFrom?.signBy?.name ?? '',
        email: amendFrom?.signBy?.email
      },
      header,
      event: 'workspace-amend',
      bodyContent: 'requesting amendment for',
      subject: 'New Request for Amendment',
      company: companyName.name,
      title: project.title,
      head,
      body: message,
      tail,
      bodyColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: redirect
      },
      subscriber: {
        firstName: signBy?.name
      }
    };

    if (notRestricted) {
      this.novuService.trigger('primary-workflow', {
        to: {
          subscriberId: signBy.id.toString(),
          email: signBy.email
        },
        payload,
        overrides: {
          android: {
            priority: 'high'
          },
          fcm: {
            data: {
              link: mobileLink.toString(),
              projectId: project.id.toString(),
              companyId: companyName?.id.toString()
            }
          }
        }
      });
    }
  }

  async notifyRequestForSignatureStatusReviewProgress(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });
    const amendFrom = await this.requestForSignatureRepo.findOne(
      {
        projectDocumentId: requestForSignature.projectDocumentId,
        assigneeNo: requestForSignature.assigneeNo + 1
      },
      { relations: ['signBy'] }
    );

    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;
    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&status=${requestForSignature.status}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;

    const { header, head, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      'Submit',
      'NCR Report Update',
      signBy.name,
      'has submitted a reply to your Non Conformance Report,',
      document.name
    );

    const payload: INovuPayload = {
      user: {
        avatar: amendFrom.signBy?.avatar,
        name: amendFrom.signBy?.name,
        email: amendFrom.signBy?.email
      },
      header,
      event: 'workspace-ncr-submit',
      bodyContent: message,
      subject: 'NCR Report Update',
      company: companyName.name,
      title: project.title,
      head,
      body: message,
      tail,
      bodyColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: redirect
      },
      subscriber: {
        firstName: signBy?.name
      }
    };

    this.novuService.trigger('primary-workflow', {
      to: {
        subscriberId: signBy.id.toString(),
        email: signBy.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          data: {
            link: mobileLink.toString(),
            projectId: project.id.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }

  async notifyRequestForSignatureStatusReviewApproveReject(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const owner = await this.userEntityRepo.findOne({ id: requestForSignature.ownerId });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });
    const groupUsersId = groupName?.parent?.workspaceGroupUsers?.map(data => data.userId);
    const notRestricted = groupUsersId === undefined || groupUsersId.length === 0 || groupUsersId.includes(signBy.id);

    const assignees = await this.requestForSignatureRepo.find({
      where: {
        projectDocumentId: requestForSignature.projectDocumentId,
        signById: Not(requestForSignature.signById)
      },
      relations: ['signBy']
    });
    const ccsDocument = await this.workspaceCCEntityRepo.find({
      where: { projectDocumentId: requestForSignature.projectDocumentId },
      relations: ['ccUser']
    });

    const sanitizedCCs = ccsDocument.map(cc => cc.ccUser);
    const sanitizedAssignees = assignees.map(sign => sign.signBy);

    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;

    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;

    const { head, header, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      'Answer',
      'Document status updated',
      signBy.name,
      `has ${requestForSignature.status} `,
      document.name
    );

    const users = _.uniqBy([...sanitizedCCs, ...sanitizedAssignees, owner], 'id');

    users?.forEach(async user => {
      const payload: INovuPayload = {
        user: {
          avatar: signBy.avatar,
          name: signBy.name,
          email: signBy.email
        },
        header,
        subject: 'Document status updated',
        bodyContent: `has been ${requestForSignature.status} by ${signBy.name}`,
        event: 'workspace-document-status',
        company: companyName.name,
        title: project.title,
        head,
        body: message,
        tail,
        headColon: false,
        link: {
          mobile: mobileLink,
          web: link,
          uri: APP_URI,
          redirect: redirect
        },
        subscriber: {
          firstName: user.name
        }
      };

      if (notRestricted) {
        this.novuService.trigger('primary-workflow', {
          to: {
            subscriberId: user.id.toString(),
            email: user.email
          },
          payload,
          overrides: {
            fcm: {
              android: {
                priority: 'high'
              },
              data: {
                link: mobileLink.toString(),
                projectId: project.id.toString(),
                companyId: companyName?.id.toString()
              }
            }
          }
        });
      }
    });
  }

  async notifyRequestForSignatureStatusInReview(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const owner = await this.userEntityRepo.findOne({ id: requestForSignature.ownerId });
    const assignees = await this.requestForSignatureRepo.find({
      where: {
        projectDocumentId: requestForSignature.projectDocumentId,
        signById: Not(requestForSignature.signById)
      },
      relations: ['signBy']
    });
    const ccsDocument = await this.workspaceCCEntityRepo.find({
      where: { projectDocumentId: requestForSignature.projectDocumentId },
      relations: ['ccUser']
    });

    const sanitizedCCs = ccsDocument.map(cc => cc.ccUser);
    const sanitizedAssignees = assignees.map(sign => sign.signBy);

    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;
    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&projectId=${document.projectId}&companyId=${companyName.id}`;
    const link = `/viewer/all-form-viewer?documentId=${document.id}&projectId=${document.projectId}&companyId=${companyName.id}`;

    const users = _.uniqBy([...sanitizedCCs, ...sanitizedAssignees, owner], 'id');

    users?.forEach(async user => {
      const payload: INovuPayload = {
        user: {
          avatar: signBy.avatar,
          name: signBy.name,
          email: signBy.email
        },
        company: companyName.name,
        title: project.title,
        head: document.name,
        body: 'status updated to',
        tail: 'In Review',
        headColon: true,
        link: {
          mobile: mobileLink,
          web: link,
          uri: APP_URI,
          redirect: redirect
        },
        subscriber: {
          firstName: user.name
        }
      };

      this.novuService.trigger('primary-workflow', {
        to: {
          subscriberId: user.id.toString(),
          email: user.email
        },
        payload,
        overrides: {
          fcm: {
            android: {
              priority: 'high'
            },
            data: {
              link: mobileLink.toString(),
              projectId: project.id.toString(),
              companyId: companyName?.id.toString()
            }
          }
        }
      });
    });
  }

  async notifyRequestForSignatureStatusApproveProceed(id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    const document = await this.projectDocumentRepo.findOne({ id: requestForSignature.projectDocumentId });
    const project = await this.projectEntityRepo.findOne({ id: document.projectId });
    const companyName = await this.companyEntityRepo.findOne({ id: project.companyId });
    const signBy = await this.userEntityRepo.findOne({ id: requestForSignature.signById });
    const groupName = await this.workspaceGroupRepo.findOne({
      where: { id: document.workspaceGroupId },
      relations: ['parent', 'parent.workspaceGroupUsers']
    });

    const previousSignBy = await this.requestForSignatureRepo.findOne(
      {
        projectDocumentId: requestForSignature.projectDocumentId,
        assigneeNo: requestForSignature?.assigneeNo ? requestForSignature?.assigneeNo - 1 : 0
      },
      { relations: ['signBy'] }
    );

    const mobileLink = `${getWorkflowNotificationPath(document?.workflow)}/${document.id}/document`;

    const redirect = `/digital-form/all-form?documentDetailsId=${document.id}&status=${requestForSignature.status}&projectId=${document.projectId}&companyId=${companyName.id}&workflowType=${document.workflow}`;
    const link = redirect;


    const { header, head, message, tail } = getDocumentNotificationPayload(
      groupName?.parent?.code,
      'Request Approval',
      '✍️ New Document Approval',
      previousSignBy?.signBy?.name,
      'requested you to sign',
      document.name
    );

    const payload: INovuPayload = {
      user: {
        avatar: previousSignBy?.signBy.avatar,
        name: previousSignBy?.signBy.name,
        email: previousSignBy?.signBy.email
      },
      header,
      event: 'workspace-approval',
      bodyContent: 'requested you to sign',
      subject: 'You have a new approval request',
      company: companyName.name,
      title: project.title,
      head,
      body: message,
      tail,
      bodyColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: redirect
      },
      subscriber: {
        firstName: signBy?.name
      }
    };

    this.novuService.trigger('primary-workflow', {
      to: {
        subscriberId: signBy?.id.toString(),
        email: signBy?.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          data: {
            link: mobileLink.toString(),
            projectId: project.id.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }

  async createManyRequestForSignatures(input: CreateManyRequestForSignaturesInput, userId: number, projectId: number) {
    try {
      const projectDocumentId: number = input['requestForSignatures'][0]?.projectDocumentId;
      const requestForSignature = await this.requestForSignatureRepo.find({ projectDocumentId });

      const ownerInProject = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

      if (ownerInProject.role === ProjectUserRoleType.CanEdit && !_.isEmpty(requestForSignature))
        throw new ForbiddenException('Editor are only allowed to request signatures for one time.');

      const requestForSignatures: RequestForSignatureEntity[] = await Promise.all(
        input['requestForSignatures'].map(i => {
          return this.requestForSignatureRepo.create({
            ownerId: userId,
            signById: i.signById,
            projectDocumentId: i.projectDocumentId
          });
        })
      );

      return await this.requestForSignatureRepo.save(requestForSignatures);
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'createManyRequestForSignatures');
    }
  }

  async approveForm(userId: number, id: number) {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
    if (requestForSignature.signById !== userId)
      throw new UnauthorizedException('You have not the authorization to approve this form');

    this.requestForSignatureRepo.merge(requestForSignature, { status: RequestForSignatureStatus.Approved });
  }

  async approveFormOrUpdateStatus(userId: number, input: ApproveFormOrUpdateStatusInputDTO) {
    try {
      const { id, status } = input;

      const requestForSignature = await this.requestForSignatureRepo.find({
        projectDocumentId: id,
        signById: userId
      });
      if (!requestForSignature) throw new BadRequestException('This request is not found');

      if (
        status !== RequestForSignatureStatus.Approved &&
        status !== RequestForSignatureStatus.Rejected &&
        status !== RequestForSignatureStatus.InReview
      )
        throw new BadRequestException('Status is invalid');

      // changed to a usecase where a document is transfered,
      // and a single user has 2 request for signature for the same document, so we update both
      for (const signature of requestForSignature) {
        this.requestForSignatureRepo.merge(signature, { status });
        await this.requestForSignatureRepo.save(signature);
      }

      return requestForSignature[0]
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'approveFormOrUpdateStatus');
    }
  }

  async addOwnerAsNewAssignee(projectDocumentId: number, userId: number) {
    try {
      // get document with workflow type and workspace group use query builder
      const document = await this.projectDocumentRepo
        .createQueryBuilder('project_document')
        .leftJoinAndSelect('project_document.workspaceGroup', 'workspace_group')
        .select('project_document.workflow', 'workflow')
        .addSelect('workspace_group.name', 'workspaceGroupName')
        .addSelect('workspace_group.parent', 'parentId')
        .addSelect('project_document.addedBy', 'addedBy')
        .where('project_document.id = :id', { id: projectDocumentId })
        .getRawOne();

      //  get workspace group name
      const workspaceGroup = await this.workspaceGroupRepo
        .createQueryBuilder('workspaceGroup')
        .select('workspaceGroup.name', 'name')
        .where('workspaceGroup.id = :id', { id: document.parentId })
        .getRawOne();

      if (document?.workflow === workflowType.Linear && workspaceGroup?.name === 'Non Conformance Report') {
        // check if this is the last submitter
        const submitted = await this.requestForSignatureRepo
          .createQueryBuilder('request_for_signature')
          .where('request_for_signature.projectDocumentId = :id', { id: projectDocumentId })
          .getMany();

        // check if all approve
        const isAllApprove = submitted.every(submitter => submitter.status === RequestForSignatureStatus.Approved);

        // check if current request for signature is owner of document
        const isOwner = userId === document?.addedBy;

        if (isOwner) return;

        if (isAllApprove) {
          // add owner of document as new assignee
          const projectDocument = await this.projectDocumentRepo.findOne({ id: projectDocumentId });

          await this.requestForSignatureRepo.insert({
            projectDocumentId: projectDocumentId,
            signById: projectDocument.addedBy,
            status: RequestForSignatureStatus.Sent,
            ownerId: projectDocument.addedBy
          });

          //  change documents status to in review
          await this.projectDocumentRepo.update({ id: projectDocumentId }, { status: ProjectDocumentStatus.InReview });
        }
      }
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'addOwnerAsNewAssignee');
    }
  }

  async checkInvolvedUser(userId: number, id: number) {
    try {
      const requestForSignature = await this.requestForSignatureRepo
        .createQueryBuilder('request_for_signature')
        .leftJoinAndSelect('request_for_signature.projectDocument', 'project_document')
        .where('request_for_signature.projectDocumentId = :id', { id })
        .andWhere('request_for_signature.signById = :userId', { userId })
        .andWhere('(request_for_signature.status <> :approve) AND (request_for_signature.status <> :reject)', {
          approve: RequestForSignatureStatus.Approved,
          reject: RequestForSignatureStatus.Rejected
        })
        .getOne();

      if (!requestForSignature) return { isInvolved: false };

      return { isInvolved: true };
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'checkInvolvedUser');
    }
  }

  async updateDynamicWorkspace(userId: number, input: UpdateDynamicWorkspaceInputDTO) {
    try {
      const { updatedUserStatus, newAssigneeId, currentAssigneeId } = input;

      if (updatedUserStatus === RequestForSignatureStatus.Amend) {
        return await this.handleAmendRequest(currentAssigneeId);
      }

      if (updatedUserStatus === RequestForSignatureStatus.Proceed) {
        return await this.updateProjectDocument(currentAssigneeId, updatedUserStatus, newAssigneeId);
      }

      if (updatedUserStatus === RequestForSignatureStatus.Sent) {
        return await this.handleResubmitRequest(currentAssigneeId);
      }
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'updateDynamicWorkspace');
    }
  }

  private async handleAmendRequest(currentAssigneeId: number) {
    // find the current request for signature
    const requestForSignature = await this.fetchRequestForSignature(currentAssigneeId, 'Amend');

    if (requestForSignature.assigneeNo === 1) {
      throw new BadRequestException('Cannot amend the first request for signature');
    }

    // find the previous request for signature
    const previousRequestForSignature = await this.requestForSignatureRepo.findOne({
      projectDocumentId: requestForSignature.projectDocumentId,
      assigneeNo: requestForSignature.assigneeNo - 1
    });
    if (!previousRequestForSignature) {
      throw new BadRequestException('Previous request for signature not found');
    }

    const updatedPreviousRequestForSignature = this?.requestForSignatureRepo?.merge(previousRequestForSignature, {
      status: RequestForSignatureStatus.Amend
    });
    const updatedRequestForSignature = this?.requestForSignatureRepo?.merge(requestForSignature, {
      status: RequestForSignatureStatus.Amend
    });

    await this?.requestForSignatureRepo?.save(updatedRequestForSignature);
    await this?.requestForSignatureRepo?.save(updatedPreviousRequestForSignature);

    await getRepository(ProjectDocumentEntity).update(
      { id: requestForSignature.projectDocumentId },
      { currentUserId: previousRequestForSignature.id, status: ProjectDocumentStatus.Amend }
    );

    return previousRequestForSignature;
  }

  private async fetchRequestForSignature(assigneeId: number, scenario: string): Promise<RequestForSignatureEntity> {
    const requestForSignature = await this.requestForSignatureRepo.findOne({ id: assigneeId });
    if (!requestForSignature) {
      throw new BadRequestException(`Cannot found request for signature with id ${assigneeId} in ${scenario} scenario`);
    }
    return requestForSignature;
  }

  private async updateProjectDocument(
    currentAssigneeId: number,
    updatedUserStatus: RequestForSignatureStatus,
    newAssigneeId: number
  ) {
    // find the current request for signature
    const requestForSignature = await this.fetchRequestForSignature(currentAssigneeId, 'Update');
    const projectDocument = await getRepository(ProjectDocumentEntity).findOne({
      id: requestForSignature.projectDocumentId
    });

    // update the current request for signature status
    this.requestForSignatureRepo.merge(requestForSignature, { status: updatedUserStatus });
    await this.requestForSignatureRepo.save(requestForSignature);

    // create new request for signature
    const newRequestForSignature = await getRepository(RequestForSignatureEntity).save({
      projectDocumentId: requestForSignature.projectDocumentId,
      assigneeNo: requestForSignature.assigneeNo + 1,
      signById: newAssigneeId,
      status: RequestForSignatureStatus.Sent,
      ownerId: requestForSignature.ownerId
    });

    // update the project document status to pending and current user id to new request for signature id
    getRepository(ProjectDocumentEntity).merge(projectDocument, {
      status: ProjectDocumentStatus.Pending,
      currentUserId: newRequestForSignature.id
    });
    await getRepository(ProjectDocumentEntity).save(projectDocument);

    return newRequestForSignature;
  }

  async handleResubmitRequest(currentAssigneeId: number) {
    // find the current request for signature
    const requestForSignature = await this.fetchRequestForSignature(currentAssigneeId, 'Update');
    // update the current request for signature status
    this.requestForSignatureRepo.merge(requestForSignature, { status: RequestForSignatureStatus.Proceed });
    await this.requestForSignatureRepo.save(requestForSignature);

    const nextRequestForSignature = await this.requestForSignatureRepo.findOne({
      projectDocumentId: requestForSignature.projectDocumentId,
      assigneeNo: requestForSignature.assigneeNo + 1
    });

    //find latest request for signature
    const latestSignature = await this.requestForSignatureRepo
      .createQueryBuilder('requestForSignature')
      .select('MAX(requestForSignature.assigneeNo)', 'assigneeNo')
      // .addSelect('requestForSignature.signById', 'signById')
      .where('requestForSignature.projectDocumentId = :projectDocumentId', {
        projectDocumentId: requestForSignature.projectDocumentId
      })
      // .groupBy('requestForSignature.signById')
      .getRawOne();

    const status =
      latestSignature.assigneeNo - 1 === requestForSignature.assigneeNo
        ? RequestForSignatureStatus.Sent
        : RequestForSignatureStatus.Amend;

    if (!nextRequestForSignature) {
      throw new BadRequestException('Next request for signature not found');
    }

    const updatedNextRequestForSignature = this?.requestForSignatureRepo?.merge(nextRequestForSignature, {
      status: status
    });

    await this?.requestForSignatureRepo?.save(updatedNextRequestForSignature);

    await getRepository(ProjectDocumentEntity).update(
      { id: requestForSignature.projectDocumentId },
      { currentUserId: nextRequestForSignature.id, status: ProjectDocumentStatus.Pending }
    );

    return nextRequestForSignature;
  }

  async approveOrRejectWorkspace(userId: number, input: ApproveOrRejectWorkspaceInputDTO) {
    try {
      const { id, status, inProgress } = input;

      const validStatuses = [
        RequestForSignatureStatus.Approved,
        RequestForSignatureStatus.Rejected,
        RequestForSignatureStatus.InReview,
        RequestForSignatureStatus.Pending
      ];
      
      if (!validStatuses.includes(status)) {
        throw new BadRequestException('Status is invalid');
      }

      const requestForSignature = await this.requestForSignatureRepo.findOne({
        id
      });

      if (!requestForSignature) throw new BadRequestException('This request is not found');

      const projectDocument = await getRepository(ProjectDocumentEntity).findOne({
        id: requestForSignature.projectDocumentId
      });

      if (status !== RequestForSignatureStatus.InReview) {
        this.requestForSignatureRepo.merge(requestForSignature, {
          status
        });
      }

      let projectDocumentStatus: ProjectDocumentStatus;
      switch (status) {
        case RequestForSignatureStatus.Approved:
          projectDocumentStatus = ProjectDocumentStatus.Approved;
          break;
        case RequestForSignatureStatus.Rejected:
          projectDocumentStatus = ProjectDocumentStatus.Rejected;
          break;
        case RequestForSignatureStatus.Pending:
          if (inProgress === true) {
            projectDocumentStatus = ProjectDocumentStatus.InProgress;
          } else {
            projectDocumentStatus = ProjectDocumentStatus.Pending;
          }
          break;
        default:
          projectDocumentStatus = ProjectDocumentStatus.InReview;
          break;
      }

      getRepository(ProjectDocumentEntity).merge(projectDocument, {
        ...projectDocument,
        status: projectDocumentStatus
      });

      if (status === RequestForSignatureStatus.Pending && inProgress === true) {
        //update signature
        this.requestForSignatureRepo.merge(requestForSignature, {
          status: RequestForSignatureStatus.Sent
        });
      }

      await Promise.all([
        this.requestForSignatureRepo.save(requestForSignature),
        getRepository(ProjectDocumentEntity).save(projectDocument)
      ]);

      return requestForSignature;
    } catch (e) {
      getErrorMessage(e, 'RequestForSignatureService', 'approveOrRejectWorkspace');
    }
  }

  async amendToOwner(userId: number, id: number) {
    try {
      // Fetch the request for signature
      const requestForSignature = await this.requestForSignatureRepo.findOne({ id });
      if (!requestForSignature) {
        throw new BadRequestException('Request for signature not found');
      }

      // Fetch the owner request for signature and project document concurrently
      const projectDocument = await getRepository(ProjectDocumentEntity).findOne({
        id: requestForSignature.projectDocumentId
      });

      if (!projectDocument) {
        throw new BadRequestException('document not found');
      }

      // Create a new request for signature for the owner
      const newRequestForSignature = await this.requestForSignatureRepo.save({
        projectDocumentId: requestForSignature.projectDocumentId,
        assigneeNo: requestForSignature.assigneeNo + 1,
        signById: projectDocument.addedBy,
        status: RequestForSignatureStatus.Sent,
        ownerId: projectDocument.addedBy
      });

      // Update entities
      requestForSignature.status = RequestForSignatureStatus.InProgress;
      projectDocument.status = ProjectDocumentStatus.InProgress;
      projectDocument.currentUserId = newRequestForSignature.id;

      this.requestForSignatureRepo.merge(requestForSignature, {
        status: RequestForSignatureStatus.InProgress
      });

      // Save the updated entities within a transaction
      await getManager().transaction(async transactionalEntityManager => {
        await transactionalEntityManager.save(requestForSignature);
        await transactionalEntityManager.save(projectDocument);
      });

      return requestForSignature;
    } catch (error) {
      getErrorMessage(error, 'RequestForSignatureService', 'amendToOwner');
    }
  }

  async assignRequestForSignature(data: AssignWorkspaceAssigneeInputDTO): Promise<RequestForSignatureEntity> {
    const queryRunner = this.requestForSignatureRepo.manager.connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { projectDocumentId, assignees } = data;

      if (!assignees || assignees.length === 0) {
        return;
      }

      const existingSignatures = await queryRunner.manager.find(RequestForSignatureEntity, {
        where: { projectDocumentId },
        select: ['id']
      });

      const existingAssigneeIds = new Set(existingSignatures.map(signature => signature.id));
      const inputAssigneeIds = new Set(assignees.map(a => a.id));

      const assigneeIdsToDelete = existingSignatures
        .filter(existingSignature => !inputAssigneeIds.has(existingSignature.id))
        .map(existingSignature => existingSignature.id);

      if (assigneeIdsToDelete.length > 0) {
        await queryRunner.manager.softDelete(RequestForSignatureEntity, assigneeIdsToDelete);
      }

      const assigneesToAdd = assignees.filter(a => !existingAssigneeIds.has(a.id));

      if (assigneesToAdd.length > 0) {
        const newSignatures = assigneesToAdd.map(a =>
          queryRunner.manager.create(RequestForSignatureEntity, {
            projectDocumentId,
            ownerId: a.ownerId,
            signById: a.signById
          })
        );

        await queryRunner.manager.save(RequestForSignatureEntity, newSignatures);
      }

      await queryRunner.commitTransaction();

      return queryRunner.manager.findOne(RequestForSignatureEntity, {
        where: { projectDocumentId },
        select: ['id']
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error(`Error in requestForSignature for projectDocumentId ${data.projectDocumentId}:`, error);
      throw new Error('Failed to request signature');
    } finally {
      await queryRunner.release();
    }
  }
}
