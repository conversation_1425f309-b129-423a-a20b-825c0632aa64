import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
  getRepository,
  InsertEvent
} from 'typeorm';
import { ScheduleEntity } from './entity/schedule.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { AuditLogActionType, AuditLogModuleType, ScheduleStatus } from '@constants';
import { getErrorMessage } from '@common/error';
import moment from 'moment';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class ScheduleSubscriber implements EntitySubscriberInterface<ScheduleEntity> {
  constructor(connection: Connection, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ScheduleEntity;
  }

  async beforeInsert(event: InsertEvent<ScheduleEntity>): Promise<void | Promise<any>> {
    try {
      const { entity } = event;

      const baselineStart = moment(entity.baselineStart, 'YYYY-MM-DD');
      const currentDate = moment().startOf('day');
      const sevenDaysFromNow = moment().add(6, 'days').endOf('day');

      if (baselineStart.isBetween(currentDate, sevenDaysFromNow, null, '[]')) {
        entity.status = ScheduleStatus.Upcoming;
      }
    } catch (error) {
      getErrorMessage(error, 'ScheduleSubscriber', 'beforeInsert');
    }
  }

  async afterInsert(event: InsertEvent<ScheduleEntity>): Promise<void | Promise<any>> {
    try {
      const { entity } = event;

      // change status to delay if start variace is positive
      if (
        entity.startVariance <= 0 &&
        entity.finishVariance > 0 &&
        entity.percentComplete !== '1' &&
        entity.status !== ScheduleStatus.Hold &&
        entity.children.length === 2 // empty string consists of -> []
      ) {
        entity.status = ScheduleStatus.FinishDelay;
        await event.manager.save(entity);
      } else if (
        (entity.startVariance > 0 &&
          entity.finishVariance <= 0 &&
          entity.percentComplete !== '1' &&
          entity.status !== ScheduleStatus.Hold &&
          entity.children.length === 2) ||
        (entity.startVariance > 0 &&
          entity.finishVariance > 0 &&
          entity.percentComplete !== '1' &&
          entity.status !== ScheduleStatus.Hold &&
          entity.children.length === 2)
      ) {
        entity.status = ScheduleStatus.StartDelay;
        await event.manager.save(entity);
      }

      // update project schedule revision
      // const projectSchedule = await event.manager
      //   .getRepository(ProjectScheduleEntity)
      //   .findOne({ where: { id: entity.projectScheduleId } });
      // if (projectSchedule) {
      //   projectSchedule.revision += 1;
      //   await event.manager.save(projectSchedule);
      // }
    } catch (error) {
      getErrorMessage(error, 'ScheduleSubscriber', 'afterInsert');
    }
  }

  async afterUpdate(event: UpdateEvent<ScheduleEntity>): Promise<void | Promise<any>> {
    try {
      const { entity, databaseEntity } = event;
      // Update createdBy project schedule
      await event.manager
        .getRepository(ProjectScheduleEntity)
        .update({ id: entity.projectScheduleId }, { updatedBy: entity.updatedBy });

      if (entity && databaseEntity && entity.notes !== databaseEntity.notes && entity?.isNotified) {
        const currentUser = await getRepository(UserEntity).findOne(entity.updatedBy);
        const project = await getRepository(ProjectEntity).findOne(entity.projectId, { relations: ['company'] });
        const schedule = await getRepository(ScheduleEntity).findOne(entity.id);

        const link = `/schedules/activity?projectScheduleId=${schedule.projectScheduleId}&projectId=${schedule.projectId}&suid=${schedule.suid}&companyId=${project.companyId}&status=${schedule.status}`;

        const validatorsAndManagers = await this.findValidatorsAndManagers(project.id, currentUser.id);

        await Promise.all(
          validatorsAndManagers
            .filter(user => user.user.id !== currentUser.id)
            .map(async user => {
              const payload: INovuPayload = {
                user: {
                  avatar: currentUser.avatar,
                  name: currentUser.name,
                  email: currentUser.email
                },
                company: project.company?.name,
                head: currentUser.name,
                event: 'update-schedule-notes',
                headColon: true,
                body: 'has updated the notes',
                tail: schedule.name,
                title: project.title,
                pushMessageTitle: 'Schedule Updated',
                link: {
                  web: link,
                  uri: APP_URI,
                  mobile: '',
                  redirect: link
                }
              };

              await this.sendNotification(payload, user.user);
            })
        );
      }

      // AUDIT LOG SCHEDULE (ADD / DELETE NOTE)
      if (event.updatedColumns.find(column => column.propertyName === 'notes')) {
        if (!databaseEntity?.notes && entity.notes) {
          const msg = `New note added in ${entity.name}; ${entity.notes}`;
          await this.createAuditLog(
            entity.createdBy,
            entity.projectId,
            entity.id,
            AuditLogModuleType.Schedule,
            AuditLogActionType.AddNote,
            msg
          );
        } else if (databaseEntity?.notes && !entity.notes) {
          const msg = `The notes was removed from ${entity.name}`;
          await this.createAuditLog(
            entity.createdBy,
            entity.projectId,
            entity.id,
            AuditLogModuleType.Schedule,
            AuditLogActionType.DeleteNote,
            msg
          );
        }
      }
    } catch (e) {
      getErrorMessage(e, 'ScheduleSubscriber', 'afterUpdate');
    }
  }

  async createAuditLog(
    userId: number,
    projectId: number,
    resourceId: number,
    module: AuditLogModuleType,
    action: AuditLogActionType,
    content: string
  ): Promise<void> {
    const auditLog = getRepository(AuditLogEntity).create({
      userId: userId,
      projectId: projectId,
      resourceId: resourceId,
      module: module,
      action: action,
      content: content
    });
    await auditLog.save();
  }

  //find the validator, manager, assignees and ccs of the schedule;
  async findValidatorsAndManagers(projectId: number, userId: number) {
    try {
      return await getRepository(ProjectUserEntity)
        .createQueryBuilder('ProjectUser')
        .leftJoinAndSelect('ProjectUser.user', 'User')
        .where('(projectUser.scheduleRole IS NOT NULL) AND (projectUser.projectId = :projectId)', { projectId })
        // .andWhere('User.id != :userId', { userId })
        .andWhere('projectUser.userId != :userId', { userId })
        .getMany();

      // const assigneesAndCCs = await getRepository(ScheduleEntity)
      //   .createQueryBuilder('Schedule')
      //   .leftJoinAndSelect('Schedule.assignees', 'Assignee')
      //   .leftJoinAndSelect('Schedule.copies', 'CC')
      //   .leftJoinAndSelect('Assignee.user', 'AssigneeUser')
      //   .leftJoinAndSelect('CC.user', 'CCUser')
      //   .where('Schedule.id = :scheduleId', { scheduleId: scheduleId })
      //   .getOne();

      // return [...validatorsAndManagers, assigneesAndCCs?.assignees, assigneesAndCCs?.copies];
    } catch (error) {
      getErrorMessage(error, 'ScheduleSubscriber', 'findValidatorsAndManagers');
    }
  }

  async sendNotification(payload: INovuPayload, user: UserEntity) {
    if (!user) return;

    return this.novuService.trigger('secondary-workflow', {
      to: {
        subscriberId: user?.id.toString(),
        email: user?.email
      },
      payload
    });
  }
}
