import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { CreateScheduleInputDTO, ScheduleDto, UpdateScheduleInputDTO } from './dto/schedule.gql.dto';
import { ScheduleEntity } from './entity/schedule.entity';
import { ScheduleService } from './schedule.service';
import { IntegrationModule } from '@modules/integration/integration.module';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { ScheduleResolver } from './schedule.resolver';
import { ScheduleSubscriber } from './schedule.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ScheduleEntity, SchedulesMediaEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: ScheduleDto,
          EntityClass: ScheduleEntity,
          CreateDTOClass: CreateScheduleInputDTO,
          UpdateDTOClass: UpdateScheduleInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ScheduleService, SchedulesMediaEntity, ScheduleResolver, ScheduleSubscriber]
    }),
    IntegrationModule
  ]
})
export class ScheduleModule {}
