import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import {
  AssignScheduleInputDTO,
  AssignSchedulesMediaInputDTO,
  ScheduleConnection,
  ScheduleDto,
  ScheduleQuery,
  ScheduleSummaryResponseType,
  ScheduleVectorizationRequestDTO,
  ScheduleVectorizationResponseDTO,
  SchedulesRespondProposedPercentageInputDto,
  SchedulesRespondProposedStatusInputDto,
  UpdateManyScheduleStatusInputDTO,
  UpdateRetainValueDTO,
  UpdateScheduleBySuidInputDTO
} from './dto/schedule.gql.dto';
import { ScheduleService } from './schedule.service';
import { AuthData } from '@types';
import { Filter } from '@nestjs-query/core';
@UseGuards(GqlAuthGuard)
@Resolver(() => ScheduleDto)
export class ScheduleResolver {
  constructor(private scheduleService: ScheduleService) {}

  @Mutation(() => ScheduleDto)
  async assignSchedulesTask(
    @Args('input') input: AssignSchedulesMediaInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.assignSchedulesMedias(input, user.id, projectId);
  }

  @Query(() => ScheduleConnection)
  async getSchedules(@Args() query: ScheduleQuery, @GqlGetGqlProjectData() projectId: number) {
    const filter: Filter<ScheduleDto> = {
      ...query.filter
    };
    return ScheduleConnection.createFromPromise(async q => await this.scheduleService.getSchedules(projectId, q), {
      ...query,
      ...{ filter }
    });
  }

  @Query(() => ScheduleSummaryResponseType)
  async getScheduleSummaryData(
    @GqlGetGqlProjectData('projectId') projectId: number,
    @Args('projectScheduleId', { nullable: true }) projectScheduleId?: number
  ) {
    return this.scheduleService.getScheduleSummary(projectId, projectScheduleId);
  }

  // Do a query to get the schedule by suid
  @Query(() => ScheduleDto)
  async getScheduleBySuid(
    @Args('suid') suid: string,
    @Args('projectScheduleId') projectScheduleId: number,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.getScheduleBySuid(suid, projectScheduleId, projectId);
  }

  @Query(() => ScheduleConnection)
  async getRetainValueSchedule(@Args() query: ScheduleQuery, @GqlGetGqlProjectData() projectId: number) {
    const filter: Filter<ScheduleDto> = {
      ...query.filter
    };
    return ScheduleConnection.createFromPromise(async q => await this.scheduleService.getRetainValueSchedule(q), {
      ...query,
      ...{ filter }
    });
  }

  @Query(() => ScheduleVectorizationResponseDTO)
  async processVectorizationSchedule(@Args('input') query: ScheduleVectorizationRequestDTO) {
    return await this.scheduleService.processVectorizationSchedule(query);
  }

  @Mutation(() => ScheduleDto)
  async updateManyScheduleStatus(
    @Args('input') input: UpdateManyScheduleStatusInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.updateManySchedules(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async updateOneScheduleStatusResponse(
    @Args('input') input: SchedulesRespondProposedStatusInputDto,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.updateOneScheduleStatusResponse(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async updateOneSchedulePercentageResponse(
    @Args('input') input: SchedulesRespondProposedPercentageInputDto,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.updateOneSchedulePercentageResponse(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async updateScheduleBySuid(
    @Args('input') input: UpdateScheduleBySuidInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.updateScheduleBySuid(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async assignSchedule(
    @Args('input') input: AssignScheduleInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.assignSchedule(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async assignCcSchedule(
    @Args('input') input: AssignScheduleInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.assignCcSchedule(input, user.id, projectId);
  }

  @Mutation(() => ScheduleDto)
  async updateRetainValue(
    @Args('input') input: UpdateRetainValueDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.scheduleService.updateRetainValue(input, user.id, projectId);
  }
}
