import { BaseEntity } from '@modules/base/base';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Entity, Column, JoinColumn, ManyToOne, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { ScheduleStatus, ScheduleType } from '@constants';
import { ScheduleCommentEntity } from '@modules/schedule-comment/entity/schedule-comment.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';

@ObjectType()
@Entity('schedules')
export class ScheduleEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  ownerId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectScheduleId: number;

  @FilterableField({ nullable: true })
  @Column('text')
  suid: string; // SCHEDULE UNIQUE ID

  @FilterableField({ nullable: true })
  @Column('text')
  taskMode: string;

  @FilterableField({ nullable: true })
  @Column('text')
  name: string;

  @FilterableField({ nullable: true })
  @Column('text')
  baselineDuration: string;

  @FilterableField({ nullable: true })
  @Column('text')
  baselineStart: string;

  @FilterableField({ nullable: true })
  @Column('text')
  actualStart: string;

  @FilterableField({ nullable: true })
  @Column('text')
  baselineFinish: string;

  @FilterableField({ nullable: true })
  @Column('text')
  actualFinish: string;

  @FilterableField({ nullable: true })
  @Column('text')
  outlineLevel: string;

  @FilterableField({ nullable: true })
  @Column('text')
  outlineNumber: string;

  @FilterableField({ nullable: true })
  @Column('text')
  percentComplete: string;

  @FilterableField({ nullable: true })
  @Column('text')
  proposedPercentComplete: string;

  @FilterableField({ nullable: true })
  @Column('text')
  percentWorkComplete: string;

  @FilterableField({ nullable: true })
  @Column('text')
  predecessors: string;

  @FilterableField({ nullable: true })
  @Column('text')
  wbs: string;

  @FilterableField()
  @Column('enum', { enum: ScheduleStatus, default: ScheduleStatus.Pending })
  status: ScheduleStatus;

  @FilterableField({ nullable: true })
  @Column('enum', { enum: ScheduleStatus, default: null })
  proposedStatus: ScheduleStatus;

  @FilterableField({ nullable: true })
  @Column('timestamp', { default: null })
  proposedStatusDate: Date;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  proposedUserId: number;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  daysDelayed: number;

  @FilterableField({ nullable: true })
  @Column('text', { default: null })
  notes: string;

  @FilterableField({ nullable: true })
  @Column('text', { default: null })
  children: string;

  @FilterableField({ nullable: true })
  @Column('enum', { enum: ScheduleType, default: null })
  type: ScheduleType;

  @FilterableField({ nullable: true })
  @Column('int', { default: null })
  completedDelay: number;

  @FilterableField()
  @Column('boolean', { default: false })
  isTaskPushed: boolean;

  @FilterableField()
  @Column('boolean', { default: false })
  isCritical: boolean;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  startVariance: number;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  finishVariance: number;

  @FilterableField({ nullable: true })
  @Column('boolean', { nullable: true, default: false })
  isPriority: boolean;

  @Column('boolean', { default: true })
  isNotified: boolean;
  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, owner => owner.schedules)
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @ManyToOne(() => ProjectEntity, project => project.tasks)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => ProjectScheduleEntity, project => project.scheduleTasks)
  @JoinColumn({ name: 'projectScheduleId' })
  projectSchedule: ProjectScheduleEntity;

  @ManyToMany(() => ProjectDocumentEntity, document => document.schedules, { cascade: ['update'] })
  @JoinTable({ name: 'schedules_documents' })
  documents: ProjectDocumentEntity[];

  @OneToMany(() => SchedulesMediaEntity, schedulesMedia => schedulesMedia.schedule, { cascade: ['insert', 'update'] })
  medias: SchedulesMediaEntity[];

  @OneToMany(() => ScheduleCommentEntity, schedulesComment => schedulesComment.schedule, {
    cascade: ['insert', 'update']
  })
  comments: ScheduleCommentEntity[];

  @ManyToMany(() => ProjectUserEntity, projectUser => projectUser.schedules, { cascade: ['update'] })
  @JoinTable({ name: 'schedule_assignees' })
  assignees: ProjectUserEntity[];

  @ManyToMany(() => ProjectUserEntity, projectUserCopy => projectUserCopy.schedulesC, { cascade: ['update'] })
  @JoinTable({ name: 'schedule_copies' })
  copies: ProjectUserEntity[];
}
