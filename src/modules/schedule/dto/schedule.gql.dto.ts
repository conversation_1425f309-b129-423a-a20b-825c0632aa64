import { defaultQueryOptions, ProposeAction, ScheduleRespondStatus, ScheduleStatus, ScheduleType } from '@constants';
import {
  BeforeCreateOne,
  BeforeCreateMany,
  CreateOneInputType,
  CreateManyInputType,
  FilterableOffsetConnection,
  IDField,
  QueryArgsType,
  QueryOptions,
  UnPagedRelation,
  BeforeUpdateOne,
  UpdateOneInputType
} from '@nestjs-query/query-graphql';
import { ArgsType, Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { RelationIdInput, relationOption } from '@constants/query.constant';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { ScheduleEntity } from '../entity/schedule.entity';
import { GqlContext } from '@types';
import { CreateSchedulesMediaInputDTO, SchedulesMediaDto } from '@modules/schedules-media/dto/schedules-media.gql.dto';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { ProjectUserDto } from '@modules/project-user/dto/project-user.gql.dto';
import { ProjectScheduleDTO } from '@modules/project-schedules/dto/project-schedule.gql.dto';
import * as Hooks from '@hooks/nest-graphql.hooks';
import { ScheduleCommentDto } from '@modules/schedule-comment/dto/schedule-comment.gql.dto';

@ObjectType('Schedule')
@FilterableOffsetConnection('owner', () => UserDto, relationOption(true))
@UnPagedRelation('documents', () => ProjectDocumentDto, relationOption(true))
@FilterableOffsetConnection('comments', () => ScheduleCommentDto, relationOption(true))
@FilterableOffsetConnection('assignees', () => ProjectUserDto, relationOption(true))
@FilterableOffsetConnection('copies', () => ProjectUserDto, relationOption(true))
@FilterableOffsetConnection('projectSchedules', () => ProjectScheduleDTO, relationOption(true))
@FilterableOffsetConnection('medias', () => SchedulesMediaDto, {
  ...relationOption(true),
  defaultResultSize: 20
})
@BeforeCreateOne((instance: CreateOneInputType<CreateScheduleInputDTO>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  instance.input.ownerId = createdBy;
  const projectId = Number(context.req.headers['project-id']);
  instance.input.projectId = projectId;
  return instance;
})
@BeforeUpdateOne((instance: UpdateOneInputType<UpdateScheduleInputDTO>, context: GqlContext) => {
  const updatedBy = context.req.user.id;
  instance.update.updatedBy = updatedBy;

  return instance;
})
@BeforeCreateMany((instance: CreateManyInputType<CreateScheduleInputDTO>, context: GqlContext) => {
  const createdBy = context.req.user.id;
  const projectId = Number(context.req.headers['project-id']);

  instance.input = instance.input.map(c => ({ ...c, createdBy, ownerId: createdBy, projectId }));
  return instance;
})
@BeforeUpdateOne(Hooks.UpdatedByOneHook)
@QueryOptions({ ...defaultQueryOptions })
export class ScheduleDto extends ScheduleEntity {
  predecessorName?: string;
  updatedByUser?: UserDto;
  assignees: ProjectUserDto[];
  commentCount?: number;
}

@ArgsType()
export class ScheduleQuery extends QueryArgsType(ScheduleDto) {}
export const ScheduleConnection = ScheduleQuery.ConnectionType;

@InputType()
export class CreateScheduleInputDTO {
  @IDField(() => ID) ownerId?: number;
  @IDField(() => ID) projectId?: number;
  @IDField(() => ID) projectScheduleId?: number;
  @Field(() => [CreateSchedulesMediaInputDTO]) medias?: CreateSchedulesMediaInputDTO[];
  suid?: string;
  taskMode?: string;
  name?: string;
  baselineDuration?: string;
  baselineStart?: string;
  actualStart?: string;
  baselineFinish?: string;
  actualFinish?: string;
  outlineLevel?: string;
  outlineNumber?: string;
  percentComplete?: string;
  proposedPercentComplete?: string;
  percentWorkComplete?: string;
  predecessors?: string;
  status?: ScheduleStatus;
  proposedStatus?: ScheduleStatus;
  proposedStatusDate?: Date;
  proposedUserId?: number;
  daysDelayed?: number;
  notes?: string;
  documents?: RelationIdInput[];
  wbs?: string;
  type?: ScheduleType;
  isTaskPushed?: boolean;
  isCritical?: boolean;
  children?: string;
  startVariance: number;
  finishVariance: number;
}

@InputType()
export class UpdateScheduleInputDTO extends PartialType(CreateScheduleInputDTO) {
  //? enum type
  proposeAction?: ProposeAction;
  isPriority?: boolean;
  updatedBy?: number;
}

@InputType()
export class DeleteScheduleInputDTO {
  @IDField(() => ID) projectId?: number;
}

@InputType()
export class AssignSchedulesMediaInputDTO {
  @IDField(() => ID) id: number;
  @Field(() => [CreateSchedulesMediaInputDTO]) medias?: CreateSchedulesMediaInputDTO[];
}

@InputType()
export class AssignScheduleInputDTO {
  @IDField(() => ID) scheduleId: number;
  @IDField(() => [ID]) projectUserIds: number[];
}

@InputType()
export class SchedulesStatusInputDto {
  suids: string;
  status: ScheduleStatus;
}

@InputType()
export class UpdateManyScheduleStatusInputDTO {
  status: SchedulesStatusInputDto[];
  projectScheduleId: string;
}

@InputType()
export class SchedulesRespondProposedStatusInputDto {
  @IDField(() => ID) projectScheduleId?: number;
  suid: string;
  status: ScheduleStatus;
  respondStatus: ScheduleRespondStatus;
}

@InputType()
export class SchedulesRespondProposedPercentageInputDto {
  @IDField(() => ID) projectScheduleId?: number;
  suid: string;
  respondStatus: ScheduleRespondStatus;
}

@InputType()
export class UpdateScheduleBySuidInputDTO extends PartialType(CreateScheduleInputDTO) {
  suid: string;
  updatedBy?: number;
}

@InputType()
export class UpdateRetainValueDTO {
  id?: number;
  suid?: string;
  status?: ScheduleStatus;
  isPriority?: boolean;
  notes?: string;
  projectScheduleId?: number;
  comments?: string;
  userId?: number;
  proposedStatus?: ScheduleStatus;
}

@InputType()
export class ScheduleVectorizationRequestDTO {
  scheduleId: number;
}

@ObjectType()
export class ScheduleVectorizationResponseDTO {
  status: string;
}

@ObjectType()
export class ScheduleSummaryResponseType {
  revision: number;
  startVarianceCount: number;
  finishVariance: number;
  holdStatusCount: number;
}
