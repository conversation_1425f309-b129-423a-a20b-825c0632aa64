import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, getRepository, In, Repository } from 'typeorm';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Cron } from '@nestjs/schedule';
import moment from 'moment';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ScheduleEntity } from './entity/schedule.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import {
  AssignScheduleInputDTO,
  AssignSchedulesMediaInputDTO,
  ScheduleQuery,
  ScheduleSummaryResponseType,
  ScheduleVectorizationRequestDTO,
  ScheduleVectorizationResponseDTO,
  SchedulesRespondProposedPercentageInputDto,
  SchedulesRespondProposedStatusInputDto,
  UpdateManyScheduleStatusInputDTO,
  UpdateRetainValueDTO,
  UpdateScheduleBySuidInputDTO
} from './dto/schedule.gql.dto';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { ScheduleRespondStatus, ScheduleStatus, ScheduleType } from '@constants';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import _ from 'lodash';
import { getErrorMessage } from '@common/error';
import { ProjectScheduleEntity } from '@modules/project-schedules/entity/project-schedule.entity';
import { ScheduleCommentEntity } from '@modules/schedule-comment/entity/schedule-comment.entity';
import { LangflowService } from '@modules/integration/langflow/langflow.service';
const { APP_URI } = process.env;
interface groupedSchedules {
  projectId: number;
  projectName: string;
  cpmTask: {
    name: string;
    projectScheduleId: string;
    tasks: {
      start: string[];
      finish: string[];
    }
  }[];
  companyId: number;
  projectUsers: ProjectUserEntity[];
}

@Injectable()
export class ScheduleService extends TypeOrmQueryService<ScheduleEntity> {
  constructor(
    @InjectRepository(ScheduleEntity)
    private ScheduleRepo: Repository<ScheduleEntity>,
    private novuService: NovuService,
    private langflowService: LangflowService,
    @InjectRepository(SchedulesMediaEntity)
    public schedulesMediaRepo: Repository<SchedulesMediaEntity>,

  ) {
    super(ScheduleRepo, { useSoftDelete: true });
  }
  @Cron('0 30 8 * * *', {
    timeZone: 'Asia/Kuala_Lumpur'
  })
  async updateUpcomingSchedule() {
    try {
      // TOM TODO: CONSIDER INCLUDING COMPANY ACTIVE/GRACE SUBSCRIPTION FOR ALLOWED SCHEDULE ACTIVITY ONLY
      const startDate = moment().startOf('day').toDate(); // Convert to JavaScript Date
      const endDate = moment().add(6, 'days').endOf('day').toDate();

      const schedulesWithProjectUser = await getRepository(ScheduleEntity)
        .createQueryBuilder('schedule')
        .select([
          'schedule.id',
          'schedule.name',
          'schedule.baselineStart',
          'schedule.status',
          'schedule.children',
          'schedule.type'
        ])
        .andHaving('status = :status', { status: ScheduleStatus.Pending })
        .where('DATE(schedule.baselineStart) BETWEEN :startDate AND :endDate', {
          startDate,
          endDate
        })
        .getMany();

      if (!schedulesWithProjectUser || schedulesWithProjectUser?.length === 0) return;

      const scheduleIdsToUpdate = schedulesWithProjectUser
        .filter(schedule => schedule.children === '[]' && schedule.type === 'task')
        .map(schedule => schedule.id);

      if (scheduleIdsToUpdate.length === 0) return;

      await getRepository(ScheduleEntity).update(scheduleIdsToUpdate, { status: ScheduleStatus.Upcoming });

      return;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateUpcomingSchedule');
    }
  }

  @Cron('0 50 8 * * *', {
    timeZone: 'Asia/Kuala_Lumpur'
  })
  async handleCron() {
    const processName = process.env.name || 'bina-be-primary';
    if (processName.search(/bina-be-primary/) !== -1) {
      try {
        const companyIds = await this.companiesHasActiveActivity();

        if (!companyIds || companyIds?.length === 0) return;

        const sevenDaysFromNow = moment().add(7, 'days').format('YYYY-MM-DD');

        const schedulesWithProjectUser = await getRepository(ScheduleEntity)
          .createQueryBuilder('schedule')
          .select(['schedule.name', 'schedule.projectScheduleId', 'schedule.baselineStart', 'schedule.baselineFinish'])
          .where(new Brackets(qb => {
            qb.where('DATE(schedule.baselineStart) = :date', { date: sevenDaysFromNow })
              .orWhere('DATE(schedule.baselineFinish) = :date', { date: sevenDaysFromNow });
          }))  
          .andWhere('schedule.isCritical = :isCritical', { isCritical: true })
          .leftJoin('schedule.project', 'project')
          //? only get schedules from companies with active subscription
          .andWhere('project.companyId IN (:...companyIds)', { companyIds })
          .leftJoinAndSelect('schedule.projectSchedule', 'projectSchedule')
          .andWhere('projectSchedule.isScheduleTracked = :isScheduleTracked', { isScheduleTracked: true })
          .andWhere('projectSchedule.isNotify = :isNotify', { isNotify: true })
          //? load project users with schedule role
          .leftJoinAndMapMany(
            'schedule.project_users',
            'project_users',
            'projectUser',
            '(projectUser.projectId = project.id) AND (projectUser.scheduleRole IS NOT NULL)'
          )
          .addSelect(['project.title', 'project.id', 'project.companyId'])
          .getMany();

        if (!schedulesWithProjectUser || schedulesWithProjectUser?.length === 0) return;

        const groupedSchedules: groupedSchedules[] = schedulesWithProjectUser?.reduce?.((result, schedule: any) => {
          const projectUsers = schedule?.project_users ?? [];
          const project = schedule.project;
          const projectName = project.title;
          const cpmTask = schedule.name;
          
          const isStarting = moment(schedule.baselineStart).isSame(sevenDaysFromNow, 'day');
          const isFinishing = moment(schedule.baselineFinish).isSame(sevenDaysFromNow, 'day');
  
          const existingProjectIndex = result.findIndex(group => group.projectId === project.id);
  
          if (existingProjectIndex === -1) {
            // If the project doesn't exist in the result, create a new project grouping.
            result.push({
              projectId: project.id,
              projectName: projectName,
              cpmTask: [
                {
                  name: schedule.projectSchedule.name,
                  projectScheduleId: schedule.projectScheduleId,
                  tasks: {
                    starting: isStarting ? [{ name: cpmTask }] : [],
                    finishing: isFinishing ? [{ name: cpmTask }] : []
                  }
                }
              ],
              companyId: project.companyId,
              projectUsers: [...projectUsers]
            });
          } else {
            // If the project already exists in the result, check if the schedule exists.
            const existingScheduleIndex = result[existingProjectIndex].cpmTask.findIndex(
              task => task.projectScheduleId === schedule.projectScheduleId
            );
  
            if (existingScheduleIndex === -1) {
              // If the schedule doesn't exist, create a new schedule under the project.
              result[existingProjectIndex].cpmTask.push({
                name: schedule.projectSchedule.name,
                projectScheduleId: schedule.projectScheduleId,
                tasks: {
                  starting: isStarting ? [{ name: cpmTask }] : [],
                  finishing: isFinishing ? [{ name: cpmTask }] : []
                }
              });
            } else {
              // If the schedule already exists, add the task to the existing schedule.
              if (isStarting) {
                result[existingProjectIndex].cpmTask[existingScheduleIndex].tasks.starting.push({ name: cpmTask });
              }
              if (isFinishing) {
                result[existingProjectIndex].cpmTask[existingScheduleIndex].tasks.finishing.push({ name: cpmTask });
              }
            }
          }
  
          return result;
        }, []);

        return groupedSchedules?.forEach?.(async (schedule: groupedSchedules) => {
          const projectUsers = schedule?.projectUsers ?? [];
          const projectName = schedule.projectName;
          const cpmTask = schedule?.cpmTask ?? [];
          const projectId = schedule.projectId;


          const company = await getRepository(CompanyEntity).findOne({
            where: {
              id: schedule.companyId
            },
            select: ['name', 'id']
          });

          projectUsers?.forEach?.(async (projectUser: ProjectUserEntity) => {
            const user = await getRepository(UserEntity).findOne({
              where: {
                id: projectUser?.userId
              },
              select: ['name', 'email', 'id']
            });

            this.cpmEmail(user, cpmTask as any, projectName);
            this.cpmNotification(
              user,
              cpmTask as any,
              projectName,
              company?.name,
              projectId?.toString?.(),
              company?.id?.toString?.()
            );
          });

          return;
        });
      } catch (error) {
        getErrorMessage(error, 'ScheduleService', 'handleCron');
      }
    }
  }

  @Cron('0 1 * * 1-6', { timeZone: 'Asia/Kuala_Lumpur' })
  async cpmDelayStatus() {
    const processName = process.env.name || 'bina-be-primary';
    if (processName.search(/bina-be-primary/) !== -1) {
      try {
        const companyIds = await this.companiesHasActiveActivity();

        if (!companyIds || companyIds?.length === 0) return;

        await new Promise<void>(resolve => {
          const yesterday = moment().tz('Asia/Kuala_Lumpur').subtract(1, 'days').format('YYYY-MM-DD');

          getRepository(ScheduleEntity)
            .createQueryBuilder('schedule')
            .select([
              'schedule.id',
              'schedule.baselineStart',
              'schedule.baselineFinish',
              'schedule.status',
              'schedule.daysDelayed',
              'schedule.completedDelay',
              'schedule.percentComplete'
            ])
            .leftJoin('schedule.project', 'project')
            //? only get schedules from companies with active subscription
            .where('project.companyId IN (:...companyIds)', { companyIds })
            .leftJoin('schedule.projectSchedule', 'projectSchedule')
            .andWhere('projectSchedule.isScheduleTracked = :isScheduleTracked', { isScheduleTracked: true })
            .andWhere('schedule.percentComplete <> :completePercent', { completePercent: 1 })
            .andWhere(
              '(((DATE(schedule.baselineStart) = :yesterday) OR (DATE(schedule.baselineFinish) = :yesterday)) AND (schedule.status IN (:statuses))) OR ((schedule.daysDelayed != 0) OR (schedule.completedDelay != 0))',
              {
                yesterday,
                statuses: [
                  ScheduleStatus.Upcoming,
                  ScheduleStatus.Delay,
                  ScheduleStatus.InProgress,
                  ScheduleStatus.Hold
                ]
              }
            )
            .getMany()
            .then(async schedules => {
              if (schedules.length === 0) resolve();

              const startDelaySchedules = schedules.filter(
                schedule =>
                  (schedule?.daysDelayed > 0 || moment(schedule.baselineStart).format('YYYY-MM-DD') === yesterday) &&
                  (schedule.status === ScheduleStatus.Upcoming ||
                    schedule.status === ScheduleStatus.Delay ||
                    schedule.status === ScheduleStatus.Hold)
              );
              const completedDelaySchedules = schedules.filter(
                schedule =>
                  (schedule?.completedDelay > 0 ||
                    moment(schedule.baselineFinish).format('YYYY-MM-DD') === yesterday) &&
                  (schedule.status === ScheduleStatus.Upcoming ||
                    schedule.status === ScheduleStatus.InProgress ||
                    schedule.status === ScheduleStatus.Delay ||
                    schedule.status === ScheduleStatus.Hold)
              );

              await Promise.all([
                this.updateDelaySchedule(startDelaySchedules, 'start'),
                this.updateDelaySchedule(completedDelaySchedules, 'complete')
              ]);

              return resolve();
            });
        });
      } catch (error) {
        getErrorMessage(error, 'ScheduleService', 'cpmDelayStatus');
      }
    }
  }

  async assignSchedule(input: AssignScheduleInputDTO, userId: number, projectId: number) {
    const { scheduleId, projectUserIds } = input;
    const schedule = await this.ScheduleRepo.findOne({ id: scheduleId }, { relations: ['assignees'] });
    const assigneeBeforeAndAfter = await this.assignProjectUserToSchedule(schedule, projectUserIds, userId);

    // TODO: AISHAH TO COMPLETE AUDIT LOG SPECS FIRST
    // await this.createTaskAssigneeAuditLog(userId, projectId, scheduleId, assigneeBeforeAndAfter);

    return schedule;
  }

  // Assign Schedule to Project user(s)
  private async assignProjectUserToSchedule(schedule: ScheduleEntity, projectUserIds: number[], userId: number) {
    try {
      const assigneeBefore = await schedule.assignees;
      const assigneeAfter = !_.isEmpty(projectUserIds)
        ? await Promise.all(
            projectUserIds.map(async projectUserId => {
              return await getRepository(ProjectUserEntity).findOne({ id: projectUserId }, { relations: ['user'] });
            })
          )
        : [];
      schedule.assignees = assigneeAfter;
      await this.ScheduleRepo.save(schedule);

      const currentUser = await getRepository(UserEntity).findOne({ where: { id: userId } });

      await this.assignNotification(
        assigneeBefore,
        assigneeAfter,
        'Assignee',
        schedule,
        schedule.projectId,
        currentUser
      );

      return { assigneeBefore, assigneeAfter };
    } catch (e) {
      getErrorMessage(e, 'ScheduleService', 'assignProjectUserToSchedule');
    }
  }

  async assignCcSchedule(input: AssignScheduleInputDTO, userId: number, projectId: number) {
    try {
      const { scheduleId, projectUserIds } = input;
      const schedule = await this.ScheduleRepo.findOne({ id: scheduleId }, { relations: ['copies'] });
      const assigneeBeforeAndAfter = await this.assignProjectUserCcToSchedule(schedule, projectUserIds, userId);

      // TODO: AISHAH TO COMPLETE AUDIT LOG SPECS FIRST
      // await this.createTaskAssigneeCcAuditLog(userId, projectId, scheduleId, assigneeBeforeAndAfter);

      return schedule;
    } catch (e) {
      getErrorMessage(e, 'ScheduleService', 'assignCcSchedule');
    }
  }

  // Carbon Copy Schedule to Project user(s)
  private async assignProjectUserCcToSchedule(schedule: ScheduleEntity, projectUserIds: number[], userId: number) {
    try {
      const assigneeBefore = await schedule.copies;
      const assigneeAfter = !_.isEmpty(projectUserIds)
        ? await Promise.all(
            projectUserIds.map(async projectUserId => {
              return await getRepository(ProjectUserEntity).findOne({ id: projectUserId }, { relations: ['user'] });
            })
          )
        : [];
      schedule.copies = assigneeAfter;
      await this.ScheduleRepo.save(schedule);

      const currentUser = await getRepository(UserEntity).findOne({ where: { id: userId } });

      this.assignNotification(assigneeBefore, assigneeAfter, 'CC', schedule, schedule.projectId, currentUser);

      return { assigneeBefore, assigneeAfter };
    } catch (e) {
      getErrorMessage(e, 'ScheduleService', 'assignProjectUserCcToSchedule');
    }
  }

  async updateDelaySchedule(schedules: ScheduleEntity[], update: 'start' | 'complete') {
    try {
      const scheduleIds = schedules.map(schedule => schedule.id);
      const updateData = {};

      if (update === 'start') {
        updateData['daysDelayed'] = () => 'COALESCE(daysDelayed, 0) + 1';
      } else {
        updateData['completedDelay'] = () => 'COALESCE(completedDelay, 0) + 1';
      }

      return await getRepository(ScheduleEntity)
        .createQueryBuilder('schedule')
        .update()
        .set({
          ...updateData,
          ...{
            status: () =>
              `CASE WHEN status = '${ScheduleStatus.Upcoming}' THEN '${ScheduleStatus.Delay}' ELSE status END`
          }
        })
        .where({ id: In(scheduleIds) })
        .execute();
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateDelaySchedule');
    }
  }

  async cpmEmail(
    user: UserEntity,
    cpmTask: { name: string; projectScheduleId: string; task: string[] }[],
    projectName: string
  ) {
    return await this.sendNotification(user, cpmTask, 'cpm-summary', null, null, null, null, null, null, projectName);
  }

  async cpmNotification(
    user: UserEntity,
    cpmTask: { name: string; projectScheduleId: string; tasks: string[] }[],
    projectName: string,
    companyName: string,
    projectId?: string,
    companyId?: string
  ) {
    try {

      cpmTask?.forEach?.(async schedule => {
        if (schedule?.tasks?.length > 7) {
          const body = `${schedule?.name}: You have total of ${schedule?.tasks?.length} upcoming CPM tasks in 7 days`;
          return await this.sendNotification(
            user,
            [],
            'secondary-workflow',
            body,
            projectName,
            companyName,
            undefined,
            'Schedule Reminder'
          );
        }

        const tasks = schedule?.tasks
          ?.map?.((task, index) => {
            if (schedule?.tasks?.length === 1) return task;
            if (index === schedule?.tasks?.length - 1) return `and ${task}`;
            else if (index === schedule?.tasks?.length - 2) return `${task} `;
            else {
              return `${task}, `;
            }
          })
          .join('');

        const body = `${schedule?.name}:\n${tasks} will start in next 7 days`;
        const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&projectId=${projectId}&companyId=${companyId}&status=${schedule}`;

        return await this.sendNotification(
          user,
          [],
          'secondary-workflow',
          body,
          projectName,
          companyName,
          undefined,
          'Schedule Reminder',
          link
        );
      });
    } catch (e) {
      getErrorMessage(e, 'ScheduleService', 'cpmNotification');
    }
  }

  async sendNotification(
    user: UserEntity,
    cpmTask?: { name: string; projectScheduleId: string; task: string[] }[],
    template?: 'cpm-summary' | 'secondary-workflow',
    body?: string | null,
    title?: string | null,
    companyName?: string | null,
    fromUser?: UserEntity | null,
    pushMessageTitle?: string | null,
    link?: string | null,
    projectName?: string | null
  ) {
    try {
      const payload: INovuPayload = {
        user: {
          avatar: fromUser?.avatar ?? user.avatar,
          name: fromUser?.name ?? user.name,
          email: fromUser?.email ?? user.email
        },
        ...(template === 'secondary-workflow' && {
          title
        }),
        header: '🗂️ CPM Reminder',
        company: companyName ?? '',
        body: body ?? `Hi ${user.name} here's your upcoming tasks in the next 7 days`,
        subscriber: {
          firstName: user.name
        },
        ...(cpmTask && {
          list: {
            cpmTask
          }
        }),
        head: title,
        date: moment().format('D MMM YYYY'),
        pushMessageTitle: pushMessageTitle,
        link: {
          web: link,
          mobile: link,
          redirect: link,
          uri: ''
        },
        title: title,
        projectName: projectName ?? ''
      };

      return await this.novuService.trigger(template, {
        to: {
          subscriberId: user.id.toString(),
          email: user.email
        },
        payload,
        ...(template === 'secondary-workflow' && {
          overrides: {
            fcm: {
              android: {
                priority: 'high'
              }
            }
          }
        })
      });
    } catch (e) {
      getErrorMessage(e, 'ScheduleService', 'sendNotification');
    }
  }

  async assignSchedulesMedias(input: AssignSchedulesMediaInputDTO, userId: number, projectId: number) {
    try {
      const { id, medias } = input;
      const entity = await this.ScheduleRepo.findOne({ where: { suid: id }, relations: ['medias'] });

      if (medias?.length > 20) throw new BadRequestException('Maximum 20 photos can be uploaded');

      const removed = entity.medias.filter(function (obj) {
        return !medias.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (removed.length > 0) {
        const user = await getRepository(UserEntity).findOne({ id: userId });

        let names = '';
        removed.forEach((element, index) => {
          names = names + element.name;
          if (index != removed.length - 1) names = names + ', ';
        });
        const msg = user.name + ' deleted schedule media(s)' + names;

        // const auditLog = getRepository(AuditLogEntity).create({
        //   userId: entity.ownerId,
        //   projectId: entity.projectId,
        //   // taskId: entity.id,
        //   resourceId: entity.id,
        //   module: AuditLogModuleType.Schedule,
        //   action: AuditLogActionType.RemovePhoto,
        //   content: msg
        // });
        // await auditLog.save();

        await this.schedulesMediaRepo.softRemove(removed);
      }

      const added = medias.filter(function (obj) {
        return !entity.medias.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (added.length > 0) await this.schedulesMediaRepo.save(added);

      return entity;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'assignSchedulesMedias');
    }
  }

  async updateManySchedules(input: UpdateManyScheduleStatusInputDTO, userId: number, projectId: number): Promise<void> {
    try {
      const { status, projectScheduleId } = input;

      const suids = status?.map(schedule => schedule.suids);

      const schedules = await getRepository(ScheduleEntity).find({
        where: {
          suid: In(suids),
          projectId,
          projectScheduleId
        }
      });

      const updatedSchedules = schedules.map(schedule => {
        const status = input.status.find(status => status.suids === schedule.suid);
        return {
          ...schedule,
          proposedStatus: status?.status,
          proposedStatusDate: new Date(),
          proposedUserId: userId
        };
      });

      await getRepository(ScheduleEntity).save(updatedSchedules);

      const predecessorIds = updatedSchedules.map(schedule => schedule.predecessors);

      const predecessors = await getRepository(ScheduleEntity).find({
        where: {
          suid: In(predecessorIds)
        }
      });

      const predecessorMap = new Map<number, string>();
      predecessors.forEach(predecessor => predecessorMap.set(parseInt(predecessor.suid), predecessor.name));

      const sanitizedSchedule = updatedSchedules?.map(schedule => ({
        ...schedule,
        predecessorName: predecessorMap.get(parseInt(schedule.predecessors)) || 'No Predecessor'
      }));

      const validators = await getRepository(ProjectUserEntity)
        .createQueryBuilder('ProjectUser')
        .leftJoinAndSelect('ProjectUser.user', 'User')
        .where('(projectUser.scheduleRole IS NOT NULL) AND (projectUser.projectId = :projectId)', { projectId })
        .andWhere('User.id != :userId', { userId })
        .getMany();

      const project = await getRepository(ProjectEntity)
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.company', 'company')
        .where('project.id = :projectId', { projectId })
        .getOne();

      const user = await getRepository(UserEntity).findOne({ where: { id: userId } });

      sanitizedSchedule?.forEach(async schedule => {
        validators?.forEach(async validator => {
          const body = `${user.name} has proposed ${schedule?.name} to ${this.getStatusName(schedule.proposedStatus)} ${
            schedule.predecessorName ? `- ${schedule.predecessorName}` : ''
          }`;
          const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&suid=${schedule?.suid}&projectId=${projectId}&companyId=${project?.companyId}&status=${schedule?.status}`;
          this.sendNotification(
            validator?.user,
            null,
            'secondary-workflow',
            body,
            project?.title,
            project?.company?.name,
            user,
            'Schedule Proposed',
            link
          );
        });
      });

      return updatedSchedules[0] as any;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateManySchedules');
    }
  }

  async updateOneSchedulePercentageResponse(
    input: SchedulesRespondProposedPercentageInputDto,
    userId: number,
    projectId: number
  ) {
    try {
      let schedule = await getRepository(ScheduleEntity).findOneOrFail({
        where: {
          suid: input.suid,
          projectScheduleId: input.projectScheduleId,
          projectId
        }
      });
      if (!schedule) throw new BadRequestException('No such schedule task found.');

      if (schedule.proposedPercentComplete == null)
        throw new BadRequestException('Task must be proposed with a new progress percentage first before responding.');

      const newPercentComplete = (parseFloat(schedule.proposedPercentComplete) / 100).toFixed(4);
      if (input.respondStatus === ScheduleRespondStatus.Validated) {
        const res = {
          ...schedule,
          percentComplete: newPercentComplete.toString(),
          proposedPercentComplete: '',
          proposedStatusDate: null,
          proposedUserId: null
        };
        await getRepository(ScheduleEntity).save(res);
        while (schedule.predecessors != '0') {
          const parent = await getRepository(ScheduleEntity).findOne({
            where: { suid: schedule.predecessors, projectId, projectScheduleId: input.projectScheduleId }
          });
          const children = JSON.parse(parent.children);
          const childrenPercentComplete = await Promise.all(
            children.map(async (child: any) => {
              const childSchedule = await getRepository(ScheduleEntity).findOne({
                where: { suid: child, projectId, projectScheduleId: input.projectScheduleId }
              });
              return parseFloat(childSchedule.percentComplete);
            })
          );
          const sum = childrenPercentComplete.reduce((a, b) => a + b, 0);
          const average = sum / childrenPercentComplete.length;
          const parentSchedule = {
            ...parent,
            percentComplete: average.toString()
          };
          await getRepository(ScheduleEntity).save(parentSchedule);
          schedule = parent;
        }
        return res;
      } else if (input.respondStatus === ScheduleRespondStatus.Rejected) {
        const res = {
          ...schedule,
          proposedPercentComplete: '',
          proposedStatusDate: null,
          proposedUserId: null
        };
        await getRepository(ScheduleEntity).save(res);
        return res;
      }
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateOneSchedulePercentageResponse');
    }
  }

  async updateOneScheduleStatusResponse(
    input: SchedulesRespondProposedStatusInputDto,
    userId: number,
    projectId: number
  ) {
    const currentData = moment().tz('Asia/Kuala_Lumpur').format('YYYY-MM-DD HH:mm:ss');

    try {
      const user = await getRepository(UserEntity).findOne({ where: { id: userId } });
      const project = await getRepository(ProjectEntity)
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.company', 'company')
        .where('project.id = :projectId', { projectId })
        .getOne();

      let schedule = await getRepository(ScheduleEntity).findOneOrFail({
        where: {
          suid: input.suid,
          projectScheduleId: input.projectScheduleId,
          projectId
        }
      });

      if (!schedule) throw new BadRequestException('No such schedule task found.');

      if (schedule.proposedStatus == null)
        throw new BadRequestException('Task must be proposed with a new status first before responding.');

      const validators = await getRepository(ProjectUserEntity)
        .createQueryBuilder('ProjectUser')
        .leftJoinAndSelect('ProjectUser.user', 'User')
        .where('(projectUser.scheduleRole IS NOT NULL) AND (projectUser.projectId = :projectId)', { projectId })
        .andWhere('User.id != :userId', { userId })
        .getMany();

      if (input.respondStatus === ScheduleRespondStatus.Validated) {
        const res = {
          ...schedule,
          status: schedule.proposedStatus,
          proposedStatus: null as ScheduleStatus,
          proposedStatusDate: null,
          proposedUserId: null,
          ...(input?.status === ScheduleStatus.InProgress &&
            !schedule.actualStart && {
              actualStart: currentData
            }),
          ...(input?.status === ScheduleStatus.Completed && {
            percentComplete: '1',
            ...(!schedule.actualStart && {
              actualStart: currentData
            }),
            actualFinish: currentData
          })
        };
        await getRepository(ScheduleEntity).save(res);

        const body = `${user.name} has validated ${schedule.name} to ${this.getStatusName(schedule?.proposedStatus)}`;
        const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&suid=${schedule?.suid}&projectId=${projectId}&companyId=${project?.companyId}&status=${input?.status}`;

        validators?.forEach(async validator => {
          this.sendNotification(
            validator?.user,
            null,
            'secondary-workflow',
            body,
            project.title,
            project.company.name,
            user,
            'Schedule Validated',
            link
          );
        });
        if (input?.status === ScheduleStatus.Completed) {
          while (schedule.predecessors != '0') {
            const parent = await getRepository(ScheduleEntity).findOne({
              where: { suid: schedule.predecessors, projectId, projectScheduleId: input.projectScheduleId }
            });

            const children = JSON.parse(parent.children);

            const childrenPercentComplete = await Promise.all(
              children.map(async (child: any) => {
                const childSchedule = await getRepository(ScheduleEntity).findOne({
                  where: { suid: child, projectId, projectScheduleId: input.projectScheduleId }
                });
                return parseFloat(childSchedule.percentComplete);
              })
            );

            const sum = childrenPercentComplete.reduce((a, b) => a + b, 0);

            const average = sum / childrenPercentComplete.length;

            const parentSchedule = {
              ...parent,
              percentComplete: average.toString()
            };
            await getRepository(ScheduleEntity).save(parentSchedule);
            schedule = parent;
          }
        }

        return res;
      } else if (input.respondStatus === ScheduleRespondStatus.Rejected) {
        const res = {
          ...schedule,
          proposedStatus: null as ScheduleStatus,
          proposedStatusDate: null,
          proposedUserId: null
        };
        await getRepository(ScheduleEntity).save(res);

        const body = `${user.name} has rejected ${schedule.name} to ${this.getStatusName(schedule?.proposedStatus)}`;

        validators?.forEach(async validator => {
          this.sendNotification(
            validator?.user,
            null,
            'secondary-workflow',
            body,
            project.title,
            project.company.name,
            user,
            'Schedule Rejected'
          );
        });

        return res;
      }
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateOneScheduleStatusResponse');
    }
  }

  async getSchedules(projectId: number, query: ScheduleQuery) {
    try {
      const { filter, sorting } = query;

      const schedules = await getRepository(ScheduleEntity)
        .createQueryBuilder('schedule')
        .where('schedule.projectScheduleId = :projectScheduleId', {
          projectScheduleId: filter?.projectScheduleId?.eq
        })
        .leftJoin('schedule.copies', 'copies')
        .addSelect('copies.id')
        .leftJoin('copies.user', 'copyUser')
        .addSelect(['copyUser.name', 'copyUser.avatar'])
        .leftJoin('schedule.assignees', 'assignees')
        .addSelect('assignees.id')
        .leftJoin('assignees.user', 'user')
        .addSelect(['user.name', 'user.avatar', 'user.id'])
        .leftJoinAndSelect('schedule.comments', 'comments')
        .addSelect(subQuery => {
          return subQuery
            .from(ScheduleCommentEntity, 'comment')
            .select('CASE WHEN COUNT(comment.id) > 0 THEN 0 ELSE 1 END', 'commentPresenceFlag')
            .where('comment.scheduleId = schedule.id')
            .groupBy('comment.scheduleId');
        }, 'commentPresenceFlag')
        .orderBy('schedule.isPriority', 'DESC')
        .addOrderBy('commentPresenceFlag', 'DESC');

      schedules.andWhere('schedule.type != :type', { type: ScheduleType.Project });

      if (filter?.status?.eq) {
        schedules.andWhere('schedule.status = :status', { status: filter.status.eq });
      }

      if (filter?.wbs?.like) {
        schedules.andHaving('schedule.wbs LIKE :wbs', {
          wbs: !_.isEmpty(filter?.wbs?.like) ? `%${filter?.wbs?.like}%` : '%%'
        });
      }

      if (filter?.suid?.eq) {
        schedules.andWhere('schedule.suid = :suid', { suid: filter.suid.eq });
      }

      // isCritical
      if (filter?.isCritical?.is) {
        schedules.andWhere('schedule.isCritical = :isCritical', { isCritical: filter.isCritical.is });
      }

      if (filter?.percentComplete?.gte) {
        schedules.andWhere('schedule.percentComplete >= :percentComplete', {
          percentComplete: filter.percentComplete.gte
        });
      }

      if (filter?.percentComplete?.lt) {
        schedules.andWhere('schedule.percentComplete < :percentComplete', {
          percentComplete: filter?.percentComplete.lt
        });
      }

      // if (filter?.baselineStart?.gte && filter?.baselineStart?.lte) {
      //   schedules.andWhere('DATE(schedule.baselineStart) BETWEEN :gte AND :lte', {
      //     gte: filter.baselineStart.gte,
      //     lte: filter.baselineStart.lte
      //   });
      // }
      if (filter?.startVariance?.gte) {
        schedules.andWhere('schedule.startVariance >= :gte', { gte: filter.startVariance.gte });
      }

      if (filter?.finishVariance?.gte) {
        schedules.andWhere(
          '(schedule.finishVariance >= :gte) AND (schedule.startVariance = :startVariance OR schedule.startVariance IS NULL)',
          { gte: filter.finishVariance.gte, startVariance: 0 }
        );
      }

      if (filter?.baselineStart?.gte) {
        //Filter status eq to pending and baselineStart gte not more than 7 days, starting from today
        schedules.andWhere('DATE(schedule.baselineStart) >= :gte', {
          gte: filter.baselineStart.gte
        });
      }

      if (filter?.baselineStart?.lte) {
        schedules.andWhere('DATE(schedule.baselineStart) <= :lte', {
          lte: filter.baselineStart.lte
        });
      }

      if (filter?.baselineFinish?.gte) {
        schedules.andWhere('DATE(schedule.baselineFinish) >= :gte', {
          gte: filter.baselineFinish.gte
        });
      }

      if (filter?.baselineFinish?.lte) {
        schedules.andWhere('DATE(schedule.baselineFinish) <= :lte', {
          lte: filter.baselineFinish.lte
        });
      }

      if (filter?.actualStart?.gte) {
        schedules.andWhere('DATE(schedule.actualStart) >= :gte', {
          gte: filter.actualStart.gte
        });
      }

      if (filter?.actualStart?.lte) {
        schedules.andWhere('DATE(schedule.actualStart) <= :lte', {
          lte: filter.actualStart.lte
        });
      }

      if (filter?.actualFinish?.gte) {
        schedules.andWhere('DATE(schedule.actualFinish) >= :gte', {
          gte: filter.actualFinish.gte
        });
      }

      if (filter?.actualFinish?.lte) {
        schedules.andWhere('DATE(schedule.actualStart) <= :lte', {
          lte: filter.actualFinish.lte
        });
      }

      if (filter?.suid?.in?.length > 0) {
        schedules.andWhere('schedule.suid IN (:...suid)', { suid: filter.suid.in });
      }

      if (filter?.type?.neq) {
        schedules.andWhere('schedule.type != :type', { type: filter.type.neq });
      }

      if (filter?.status?.eq === ScheduleStatus.Pending) {
        schedules
          .orWhere('schedule.isTaskPushed = :isTaskPushed', { isTaskPushed: 1 })
          .andHaving('schedule.projectScheduleId = :projectScheduleId', {
            projectScheduleId: filter?.projectScheduleId?.eq
          })
          .andHaving('schedule.status = :status', { status: ScheduleStatus.Pending });
      }

      if (filter?.name?.like) {
        schedules.andHaving('schedule.name LIKE :name', {
          name: !_.isEmpty(filter?.name?.like) ? `%${filter?.name?.like}%` : '%%'
        });
      }

      if ((filter?.assignees as any)?.userId?.eq) {
        schedules.andHaving('user.id = :id', { id: (filter?.assignees as any).userId.eq });
      }

      if ((filter?.assignees as any)?.id?.in?.length > 0) {
        schedules.andHaving('assignees.id IN (:...id)', { id: (filter?.assignees as any).id.in });
      }

      if ((filter?.copies as any)?.id?.in?.length > 0) {
        schedules.andHaving('copies.id IN (:...id)', { id: (filter?.copies as any).id.in });
      }

      if (filter?.proposedStatus?.eq) {
        schedules.andHaving('schedule.proposedStatus = :proposedStatus', {
          proposedStatus: filter?.proposedStatus?.eq
        });
      }

      if (sorting[0]?.direction === 'ASC' && sorting[0]?.field === 'baselineStart') {
        schedules.addOrderBy('schedule.baselineStart', 'ASC');
      }

      if (sorting[0]?.direction === 'DESC' && sorting[0]?.field === 'baselineStart') {
        schedules.addOrderBy('schedule.baselineStart', 'DESC');
      }

      if (sorting[0]?.direction === 'ASC' && sorting[0]?.field === 'actualStart') {
        schedules.addOrderBy('schedule.actualStart', 'ASC');
      }

      if (sorting[0]?.direction === 'DESC' && sorting[0]?.field === 'actualStart') {
        schedules.addOrderBy('schedule.actualStart', 'DESC');
      }

      if (sorting[0]?.direction === 'ASC' && sorting[0]?.field === 'wbs') {
        schedules.addOrderBy('schedule.wbs', 'ASC');
      }

      if (sorting[0]?.direction === 'DESC' && sorting[0]?.field === 'wbs') {
        schedules.addOrderBy('schedule.wbs', 'DESC');
      }

      if (sorting[0]?.direction === 'ASC' && sorting[0]?.field === 'percentComplete') {
        schedules.addOrderBy('schedule.percentComplete', 'ASC');
      }

      if (sorting[0]?.direction === 'DESC' && sorting[0]?.field === 'percentComplete') {
        schedules.addOrderBy('schedule.percentComplete', 'DESC');
      }

      schedules?.skip(query?.paging?.offset).take(query?.paging?.limit);

      const result = await schedules.getMany();

      const predecessorIds = result
        .filter(schedule => parseInt(schedule.predecessors) !== 0)
        .map(schedule => parseInt(schedule.predecessors));

      const predecessors = await getRepository(ScheduleEntity).find({
        where: {
          suid: In(predecessorIds)
        }
      });

      const predecessorMap = new Map<number, string>();
      predecessors.forEach(predecessor => predecessorMap.set(parseInt(predecessor.suid), predecessor.name));

      const sanitizedSchedule: any = result.map(schedule => {
        return {
          ...schedule,
          commentCount: schedule.comments.length ?? 0,
          predecessorName: predecessorMap.get(parseInt(schedule.predecessors))
        };
      });

      return sanitizedSchedule;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'getSchedules');
    }
  }

  async getScheduleSummary(projectId: number, projectScheduleId: number): Promise<ScheduleSummaryResponseType> {
    try {
      const schedule = await getRepository(ProjectScheduleEntity).findOne({
        where: {
          id: projectScheduleId
        }
      });

      const schedules = await getRepository(ScheduleEntity)
        .createQueryBuilder('schedule')
        .where('schedule.projectId = :projectId', { projectId })
        .andWhere('schedule.projectScheduleId = :projectScheduleId', { projectScheduleId })
        .andWhere('schedule.status IN (:...statuses)', {
          statuses: [ScheduleStatus.Hold, ScheduleStatus.StartDelay, ScheduleStatus.FinishDelay]
        })
        //exclude children length > 0
        .andWhere('schedule.children = :children', { children: '[]' })
        // exclude percentComplete = 1
        .andWhere('schedule.percentComplete != :percentComplete', { percentComplete: '1' })
        .andWhere('schedule.type != :type', { type: ScheduleType.Milestone })
        // .andWhere(
        //   new Brackets(qb => {
        //     qb.where('schedule.startVariance > :startVariance', { startVariance: 0 }).orWhere(
        //       'schedule.finishVariance > :finishVariance',
        //       { finishVariance: 0 }
        //     );
        //   })
        // )
        .getMany();

      const finishVariance =
        schedules.filter(
          schedule =>
            schedule.startVariance <= 0 &&
            schedule.finishVariance > 0 &&
            schedule.percentComplete !== '1' &&
            schedule.status !== ScheduleStatus.Hold
        ).length ?? 0;

      const startVariance =
        schedules.filter(
          schedule =>
            (schedule.startVariance > 0 &&
              schedule.finishVariance <= 0 &&
              schedule.percentComplete !== '1' &&
              schedule.status !== ScheduleStatus.Hold) ||
            (schedule.startVariance > 0 &&
              schedule.finishVariance > 0 &&
              schedule.percentComplete !== '1' &&
              schedule.status !== ScheduleStatus.Hold)
        ).length ?? 0;
      const holdStatus = schedules.filter(schedule => schedule.status === ScheduleStatus.Hold).length ?? 0;

      return {
        startVarianceCount: startVariance,
        finishVariance: finishVariance,
        holdStatusCount: holdStatus,
        revision: schedule?.revision ?? 0
      };
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'getScheduleSummary');
    }
  }

  async getScheduleBySuid(suid: string, projectScheduleId: number, projectId: number) {
    try {
      const schedule = await getRepository(ScheduleEntity).findOne({ where: { suid, projectId, projectScheduleId } });

      const updatedByUser = await getRepository(UserEntity).findOne({ where: { id: schedule.updatedBy } });

      const sanitizedSchedule = {
        ...schedule,
        updatedByUser
      };

      return sanitizedSchedule;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'getScheduleBySuid');
    }
  }

  async updateScheduleBySuid(input: UpdateScheduleBySuidInputDTO, userId: number, projectId: number) {
    try {
      const { suid, projectScheduleId, ...rest } = input;

      const schedule = await getRepository(ScheduleEntity).findOne({ where: { suid, projectId, projectScheduleId } });

      if (!schedule) throw new BadRequestException('Schedule not found');

      this.ScheduleRepo.merge(schedule, {
        ...rest,
        medias: schedule.medias
      });

      return await this.ScheduleRepo.save(schedule);
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateScheduleBySuid');
    }
  }

  async updateRetainValue(input: UpdateRetainValueDTO, userId: number, projectId: number) {
    try {
      const { suid, isPriority, notes, status, projectScheduleId, comments, id, proposedStatus } = input;
      const schedule = await getRepository(ScheduleEntity).findOne({ where: { suid, projectId, projectScheduleId } });
      if (!schedule) throw new BadRequestException('Schedule not found');
      const comments2 = JSON.parse(comments);

      comments2.forEach(async (comment: any) => {
        {
          getRepository(ScheduleCommentEntity).insert({
            scheduleId: schedule.id,
            userId: comment.userId,
            message: comment.message,
            isNotified: false
          });
        }
      });

      schedule.isPriority = isPriority;
      schedule.notes = notes;
      schedule.status = status;
      schedule.isNotified = false;
      schedule.proposedStatus = proposedStatus ?? null;

      return await this.ScheduleRepo.save(schedule);
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'updateRetainValue');
    }
  }

  getStatusName(status: ScheduleStatus) {
    switch (status) {
      case ScheduleStatus.Pending:
        return 'Pending';
      case ScheduleStatus.InProgress:
        return 'In Progress';
      case ScheduleStatus.Completed:
        return 'Complete';
      case ScheduleStatus.Delay:
        return 'Delay';
      case ScheduleStatus.Hold:
        return 'Hold';
      case ScheduleStatus.Upcoming:
        return 'Upcoming';
      default:
        return status;
    }
  }

  async assignNotification(
    assigneeBefore: ProjectUserEntity[],
    assigneeAfter: ProjectUserEntity[],
    type: 'Assignee' | 'CC',
    schedule: ScheduleEntity,
    projectId: number,
    currentUser: UserEntity
  ) {
    try {
      //find the project details
      const projects = await getRepository(ProjectEntity)
        .createQueryBuilder('project')
        .leftJoinAndSelect('project.company', 'company')
        .where('project.id = :projectId', { projectId })
        .getOne();

      const link = `/schedules/activity?projectScheduleId=${schedule?.projectScheduleId}&suid=${schedule?.suid}&projectId=${projectId}&companyId=${projects?.company?.id}&status=${schedule?.status}`;

      const sanitizedAssignee = assigneeAfter?.filter(assigneeAfter => {
        return !assigneeBefore?.find(assigneeBefore => assigneeBefore.userId === assigneeAfter.userId);
      });

      sanitizedAssignee?.forEach(async assignee => {
        const payload: INovuPayload = {
          user: {
            avatar: currentUser.avatar,
            name: currentUser.name,
            email: currentUser.email
          },
          event: `schedule-${type === 'Assignee' ? 'assign' : 'cc'}`,
          company: projects?.company?.name,
          head: currentUser.name,
          headColon: true,
          body: `${currentUser.name} has assigned you as ${type?.toLowerCase?.()}`,
          tail: schedule?.name,
          bodyColon: true,
          header: `Schedule ${type}`,
          title: projects?.title,
          link: {
            web: link,
            uri: APP_URI,
            mobile: '',
            redirect: link
          }
        };

        return await this.novuService.trigger('secondary-workflow', {
          to: {
            subscriberId: assignee?.user?.id.toString(),
            email: assignee?.user?.email
          },
          payload,
          //TODO: complete the schedule on mobile
          overrides: {
            fcm: {
              android: {
                priority: 'high'
              },
              data: {
                link: '',
                projectId: '',
                companyId: ''
              }
            }
          }
        });
      });
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'assignNotification');
    }
  }


  // CONSIDER ADDING 10 DAYS GRACE PERIOD AFTER CURRENT SUBSCRIPTION EXPIRES
  async companiesHasActiveActivity() {
    const companies = await getRepository(CompanyEntity)
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.companySubscriptions', 'companySubscriptions')
      .leftJoinAndSelect('companySubscriptions.subscriptionPackage', 'subscriptionPackage')
      .andWhere('subscriptionPackage.allowScheduleActivity = :allowScheduleActivity', { allowScheduleActivity: true })
      .getMany();

    const activeCompanies = companies
      ?.map?.(company => {
        if (company?.companySubscriptions?.[0]?.subscriptionEndDate < moment().subtract(10, 'days').toDate())
          return null;
        return company;
      })
      ?.filter?.(company => company !== null);

    return activeCompanies?.map?.(company => company?.id);
  }

  async getRetainValueSchedule(query: ScheduleQuery) {
    try {
      const schedulesWithConditions = await getRepository(ScheduleEntity)
        .createQueryBuilder('schedule')
        .leftJoinAndSelect('schedule.comments', 'comment')
        .where('schedule.projectScheduleId = :projectScheduleId', {
          projectScheduleId: query?.filter?.projectScheduleId?.eq
        })
        .andWhere(
          new Brackets(qb => {
            qb.orWhere('comment.id IS NOT NULL')
              .orWhere('schedule.isPriority = 1')
              .orWhere('schedule.notes IS NOT NULL')
              .orWhere('schedule.status = :hold', { hold: ScheduleStatus.Hold });
          })
        )
        .getMany();

      return schedulesWithConditions;
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'getRetainValueSchedule');
    }
  }

  async processVectorizationSchedule(query: ScheduleVectorizationRequestDTO): Promise<ScheduleVectorizationResponseDTO> {
    try {
      const { scheduleId } = query;
      const collectionName = `schedule_${scheduleId}`;
      
      const schedules = await this.ScheduleRepo.find({
        where: {
          projectScheduleId: scheduleId
        }
      });

      await this.langflowService.uploadAndProcessSchedules(schedules, collectionName);

      return {
        status: 'OK'
      }

    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'processVectorizationSchedule');
    }
  }
}
