import { defaultQueryOptions } from '@constants';
import { CursorConnection, IDField, OffsetConnection, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { CompanyEntity } from '../entity/company.entity';
import { CompanySubscriptionDto } from '@modules/company-subscription/dto/company-subscription.gql.dto';
import { relationOption } from '@constants/query.constant';
import { UserDto } from '@modules/user/dto/user.gql.dto';

@ObjectType('Company')
@QueryOptions({ ...defaultQueryOptions })
@OffsetConnection('companySubscriptions', () => CompanySubscriptionDto, relationOption(true))
@CursorConnection('_users', () => UserDto, { disableRemove: true, disableUpdate: true })
export class CompanyDto extends CompanyEntity {}

@InputType()
export class CreateCompanyInputDTO {
  @IDField(() => ID) ownerId?: number;
  name: string;
  @Field(() => GraphQLUpload) logoUrl?: FileUpload;
}
@InputType()
export class UpdateCompanyInputDTO extends PartialType(CreateCompanyInputDTO) {
  @Field(() => GraphQLUpload) stampUrl?: FileUpload;
}
@InputType()
export class UpdateCompanyStamp {  
  @Field() stampKey?: string;
  @Field(() => GraphQLUpload) stampFile?: FileUpload;
}
