import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getManager, getRepository, Repository } from 'typeorm';
import { CompanyEntity } from './entity/company.entity';
import { BadRequestException, ConflictException, Injectable } from '@nestjs/common';
import _ from 'lodash';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { getErrorMessage } from '@common/error';
import { CreateCompanyInputDTO, UpdateCompanyInputDTO, UpdateCompanyStamp } from './dto/company.gql.dto';
import { FileService } from '@modules/integration/file.service';
import { FileUpload } from 'graphql-upload';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { AuthData } from '@types';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';

@Injectable()
export class CompanyService extends TypeOrmQueryService<CompanyEntity> {
  constructor(
    @InjectRepository(CompanyEntity)
    private companyRepo: Repository<CompanyEntity>,
    private fileService: FileService,
    private tMOneService: TMOneService
  ) {
    // pass the use soft delete option to the service.
    super(companyRepo, { useSoftDelete: true });
  }
  // Update UserMe
  async updateCompany(userId: number, input: any) {
    // const user = await this.getUserMe(userId);
    // if (!user) throw new BadRequestException('Account not found');
    try {
      const company = await getRepository(CompanyEntity).findOne({ ownerId: userId });
      if (!company) throw new BadRequestException('Company does not exist');

      getRepository(CompanyEntity).merge(company, {
        stampUrl: input.stampUrl
      });

      return await getRepository(CompanyEntity).save(company);
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'updateCompany');
    }
  }

  async switchCompany(userId: number, companyId: number) {
    try {
      const company = await getRepository(CompanyEntity).findOne({ id: companyId });
      if (!company) throw new BadRequestException('Company does not exist');

      const companies = await getRepository(UserEntity)
        .findOne({ id: userId }, { relations: ['companies'] })
        .then(user => user.companies);
      if (!_.find(companies, { id: companyId })) throw new BadRequestException('Company does not exist');

      return await getRepository(UserEntity).save({ id: userId, companyId });
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'switchCompany');
    }
  }

  async getCompanyUsers(userId: number) {
    try {
      const user = await getRepository(UserEntity).findOne(
        { id: userId },
        { relations: ['company', 'company._users'] }
      );

      if (user.company.ownerId !== userId) throw new BadRequestException('You are not the owner of this company');

      return user.company._users;
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'getCompanyUsers');
    }
  }

  async checkCompanyUserLimit(companyId: number): Promise<boolean> {
    try {
      const company = await getRepository(CompanyEntity).findOne(
        { id: companyId },
        { relations: ['_users'] }
      );

      if (!company) {
        throw new BadRequestException('Company does not exist');
      }

      const currentUserCount = company._users?.length || 0;

      return currentUserCount < company.maxUsers;
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'checkCompanyUserLimit');
      return false;
    }
  }

  async removeCompanyUser(userId: number, removeUserId: number) {
    const owner = await getRepository(UserEntity).findOne({ id: userId }, { relations: ['company'] });
    const company = owner.company;
    if (!company) throw new BadRequestException('Company does not exist');

    const user = await getRepository(UserEntity).findOne({ id: removeUserId }, { relations: ['companies'] });
    if (!user) throw new BadRequestException('User does not exist');

    if (!_.find(user.companies, { id: company.id }))
      throw new BadRequestException('User does not belong to this company');

    const projects = await getRepository(CompanyEntity)
      .findOne({ id: company.id }, { relations: ['projects'] })
      .then(company => company.projects);

    try {
      await getManager().transaction(async transactionalEntityManager => {
        await transactionalEntityManager
          .createQueryBuilder()
          .relation(CompanyEntity, '_users')
          .of(user)
          .remove(company);

        await transactionalEntityManager
          .createQueryBuilder()
          .delete()
          .from(ProjectUserEntity)
          .where('userId = :userId', { userId: removeUserId })
          .andWhere('projectId IN (:...projectIds)', { projectIds: projects.map(project => project.id) })
          .execute();

        if (user.companyId === company.id) {
          await transactionalEntityManager.save(UserEntity, { id: removeUserId, companyId: null });
        }
      });
      return true;
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'removeCompanyUser');
    }
  }

  async createCompany(userId: number, input: CreateCompanyInputDTO) {
    const user = await getRepository(UserEntity).findOne({ id: userId }, { relations: ['companies'] });
    if (!user) throw new BadRequestException('User does not exist');

    let logoUrl;

    try {
      // check user is owner or not
      const isOwner = await this.companyRepo.findOne({ ownerId: userId });
      if (isOwner) throw new ConflictException('You already have a company.');

      if (input.logoUrl && _.isObject(input.logoUrl)) {
        const folder = 'Company-Logo';
        const { key } = await this.fileService.uploadGqlFile(input.logoUrl as FileUpload, folder);

        logoUrl = key;
      }

      return await getManager().transaction(async transactionalEntityManager => {
        // Create the company
        const company = await transactionalEntityManager.save(CompanyEntity, {
          name: input.name,
          ownerId: userId,
          logoUrl: logoUrl
        });

        // Update user with company info
        await transactionalEntityManager.save(UserEntity, {
          id: userId,
          companyId: company.id,
          companies: [...user.companies, company]
        });

        // Check if the user has already used a free trial
        const userCompanies = await transactionalEntityManager.find(CompanyEntity, {
          where: { ownerId: userId, hasUsedFreeTrial: true }
        });

        // Only offer free trial if the user hasn't used one before
        if (userCompanies.length === 0) {
          // Find the free trial subscription package
          const freeTrialPackage = await transactionalEntityManager.findOne(SubscriptionPackageEntity, {
            where: { title: '30-Day Free Trial' }
          });

          if (freeTrialPackage) {
            // Calculate end date (14 days from now)
            const trialEndDate = new Date();
            trialEndDate.setDate(trialEndDate.getDate() + 14);

            // Calculate the first billing date (27th of the month)
            const nextBillingDate = new Date(trialEndDate);
            nextBillingDate.setDate(27);
            if (nextBillingDate <= trialEndDate) {
              nextBillingDate.setMonth(nextBillingDate.getMonth() + 1);
            }

            // Set the maxUsers based on the subscription package and mark as using free trial
            await transactionalEntityManager.save(CompanyEntity, {
              id: company.id,
              maxUsers: freeTrialPackage.totalUsers,
              hasUsedFreeTrial: true
            });

            // Create a free trial subscription
            await transactionalEntityManager.save(CompanySubscriptionEntity, {
              companyId: company.id,
              subscriptionPackageId: freeTrialPackage.id,
              paymentMethod: 'Free Trial',
              subscriptionEndDate: trialEndDate,
              nextBillingDate: nextBillingDate, // Next billing date is the 27th
              seatCount: freeTrialPackage.totalUsers,
              isYearly: false
            });
          }
        }

        return company;
      });
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'createCompany');
    }
  }

  async editCompany(userId: number, input: UpdateCompanyInputDTO) {
    try {
      const user = await getRepository(UserEntity).findOne({ id: userId }, { relations: ['company'] });
      const company = user.company;
      if (!company) throw new BadRequestException('Company does not exist');

      const duplicateName = await getRepository(CompanyEntity).findOne({ name: input?.name });
      if (duplicateName) throw new BadRequestException('Company name already exists');

      let logoUrl;
      if (input.logoUrl && _.isObject(input.logoUrl)) {
        const folder = 'Company-Logo';
        const { key } = await this.fileService.uploadGqlFile(input.logoUrl as FileUpload, folder);
        logoUrl = key;
      }

      return await getRepository(CompanyEntity).save({ id: company.id, name: input.name, logoUrl });
    } catch (e) {
      getErrorMessage(e, 'CompanyService', 'renameCompany');
    }
  }

  async uploadCompanyStamp(input: UpdateCompanyStamp, user: AuthData) {

    try {
      const getUser = await getRepository(UserEntity).findOne({ id: user.id });
      const company = await getRepository(CompanyEntity).findOne({ id : getUser.companyId });
      if (!company) throw new BadRequestException('Company does not exist');

      let stampKey = input.stampKey;
      if (input.stampFile && _.isObject(input.stampFile)) {
        const folder = 'Company-Stamp';
        const { key } = await this.fileService.uploadGqlFile(input.stampFile as FileUpload, folder);
        stampKey = key;
      }

      return await getRepository(CompanyEntity).save({ id: company.id, stampKey });
    } catch (error) {
      getErrorMessage(error, 'CompanyService', 'uploadCompanyStamp');
    }
  }

  async getPresignedUrl(company: CompanyEntity, type: 'avatarKey' | 'stampKey' | 'logoKey') {
    try {
      const keyAndFallback = { avatarKey: 'avatar', stampKey: 'stampUrl', logoKey: 'logoUrl' };
      let key = company[type];
      // fallback if key is missing
      if (!key){
        const fileName = company[keyAndFallback[type]];

        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tMOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
