import { RoleTypeEnum } from '@constants';
import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { ForbiddenException, UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { AuthData } from '@types';
import { CompanyDto, CreateCompanyInputDTO, UpdateCompanyInputDTO, UpdateCompanyStamp } from './dto/company.gql.dto';
import { CompanyService } from './company.service';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { getErrorMessage } from '@common/error';

@UseGuards(GqlAuthGuard)
@Resolver(() => CompanyDto)
export class CompanyResolver {
  constructor(private companyService: CompanyService) {}

  @Query(() => [UserDto])
  async getCompanyUsers(@GqlGetGqlAuthData() user: AuthData): Promise<UserDto[]> {
    try {
      return await this.companyService.getCompanyUsers(user.id);
    } catch (e) {
      throw new Error('Something went wrong in company resolver! getCompanyUsers function');
    }
  }

  // Update UserMe
  @Mutation(() => CompanyDto)
  async updateCompany(@GqlGetGqlAuthData() user: AuthData, @Args('input') input: UpdateCompanyInputDTO): Promise<any> {
    try {
      if (user.type !== RoleTypeEnum.User)
        throw new ForbiddenException(`This action only for Role ${RoleTypeEnum.User}`);

      return await this.companyService.updateCompany(user.id, input);
    } catch (e) {
      throw new Error('Something went wrong in company resolver! updateCompany function');
    }
  }

  @Mutation(() => UserDto)
  async switchCompany(@GqlGetGqlAuthData() user: AuthData, @Args('companyId') companyId: number): Promise<UserDto> {
    return await this.companyService.switchCompany(user.id, companyId);
  }

  @Mutation(() => Boolean)
  async removeCompanyUser(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('removeuserId') removeUserId: number
  ): Promise<boolean> {
    try {
      return await this.companyService.removeCompanyUser(user.id, removeUserId);
    } catch (e) {
      throw new Error('Something went wrong in company resolver! removeCompanyUser function');
    }
  }

  @Mutation(() => CompanyDto)
  async createCompany(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: CreateCompanyInputDTO
  ): Promise<CompanyDto> {
    try {
      return await this.companyService.createCompany(user.id, input);
    } catch (e) {
      getErrorMessage(e, 'CompanyResolver', 'createCompany');
    }
  }

  @Mutation(() => CompanyDto)
  async editCompany(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: UpdateCompanyInputDTO
  ): Promise<CompanyDto> {
    try {
      return await this.companyService.editCompany(user.id, input);
    } catch (e) {
      throw new Error('Something went wrong in company resolver! renameCompany function');
    }
  }

  @Mutation(() => CompanyDto)
  async updateCompanyStamp( @GqlGetGqlAuthData() user: AuthData,@Args('input') input: UpdateCompanyStamp): Promise<any> {
    return await this.companyService.uploadCompanyStamp(input, user);
  }

  @ResolveField('stampUrl', () => String)
  async stampUrl(@Parent() parent: CompanyDto) {    
    return await this.companyService.getPresignedUrl(parent, 'stampKey');
  }

  @ResolveField('avatar', () => String)
  async avatar(@Parent() parent: CompanyDto) {
    return await this.companyService.getPresignedUrl(parent, 'avatarKey');
  }

  @ResolveField('logoUrl', () => String)
  async logoUrl(@Parent() parent: CompanyDto) {
    return await this.companyService.getPresignedUrl(parent, 'logoKey');
  }
}
