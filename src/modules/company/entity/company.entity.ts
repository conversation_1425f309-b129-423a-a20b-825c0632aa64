import { BaseEntity } from '@modules/base/base';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Entity, Column, OneToOne, JoinColumn, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';

@ObjectType()
@Entity('companies')
export class CompanyEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true, nullable: true })
  ownerId: number;

  @FilterableField({ nullable: true })
  @Column('varchar')
  name: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  logoUrl: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  stampUrl: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  avatar: string;

  @Column('varchar', { nullable: true })
  logoKey: string;

  @Column('varchar', { nullable: true })
  stampKey: string;

  @Column('varchar', { nullable: true })
  avatarKey: string;

  @FilterableField()
  @Column('int', { default: 9999 })
  maxUsers: number;

  @FilterableField()
  @Column('boolean', { default: false })
  hasUsedFreeTrial: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @OneToOne(() => UserEntity, user => user.myCompany, {
    onDelete: 'SET NULL'
  })
  @JoinColumn({ name: 'ownerId' })
  owner: UserEntity;

  @OneToMany(() => UserEntity, users => users.company)
  users: UserEntity[];

  @ManyToMany(() => UserEntity, user => user.companies, { cascade: true })
  @JoinTable({ name: 'user_to_company' })
  _users: UserEntity[];

  @OneToMany(() => CompanySubscriptionEntity, companySubscriptions => companySubscriptions.company)
  companySubscriptions: CompanySubscriptionEntity[];

  @OneToMany(() => ProjectEntity, projects => projects.company)
  projects: ProjectEntity[];

  @OneToMany(() => SalesOrderEntity, salesOrders => salesOrders.company)
  salesOrders: SalesOrderEntity[];

  @ManyToMany(() => SalesOrderEntity, salesOrder => salesOrder.companies, { cascade: true })
  @JoinTable({ name: 'company_subscription_transactions' })
  orderTransactions: SalesOrderEntity[];
}
