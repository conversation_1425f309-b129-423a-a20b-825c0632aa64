import { FileService } from '@modules/integration/file.service';
import { Injectable } from '@nestjs/common';
import { FileUpload } from 'graphql-upload';
import _ from 'lodash';
import { EventSubscriber, EntitySubscriberInterface, Connection, UpdateEvent, InsertEvent } from 'typeorm';
import { CompanyEntity } from './entity/company.entity';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class CompanySubscriber implements EntitySubscriberInterface<CompanyEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return CompanyEntity;
  }

  async beforeInsert(event: InsertEvent<CompanyEntity>) {
    try {
      const { entity } = event;

      // upload logo
      if (entity.logoUrl && _.isObject(entity.logoUrl)) {
        const folder = 'Company-Logo';
        const { key } = await this.fileService.uploadGqlFile(entity.logoUrl as FileUpload, folder);
        entity.logoUrl = key ?? 'hjjj';
      }
    } catch (e) {
      getErrorMessage(e, 'CompanySubscriber', 'beforeInsert');
    }
  }

  async beforeUpdate(event: UpdateEvent<CompanyEntity>) {
    try {
      const { entity } = event;

      if (entity.stampUrl && _.isObject(entity.stampUrl)) {
        const folder = 'Company-Stamp';
        const { key } = await this.fileService.uploadGqlFile(entity.stampUrl as FileUpload, folder);
        entity.stampUrl = key ?? 'hjjj';
      }

      // upload logo
      if (entity.logoUrl && _.isObject(entity.logoUrl)) {
        const folder = 'Company-Logo';
        const { key } = await this.fileService.uploadGqlFile(entity.logoUrl as FileUpload, folder);
        entity.logoUrl = key ?? 'hjjj';
      }
    } catch (e) {
      getErrorMessage(e, 'CompanySubscriber', 'beforeUpdate');
    }
  }
}
