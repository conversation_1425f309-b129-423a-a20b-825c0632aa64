import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { CompanyEntity } from './entity/company.entity';
import { CompanyDto, CreateCompanyInputDTO, UpdateCompanyInputDTO } from './dto/company.gql.dto';
import { UseUploadFilePipe } from 'src/pipes/gql-upload.pipe';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { CompanySubscriber } from './company.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { CompanyService } from './company.service';
import { CompanyResolver } from './company.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([CompanyEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: CompanyDto,
          EntityClass: CompanyEntity,
          CreateDTOClass: CreateCompanyInputDTO,
          UpdateDTOClass: UpdateCompanyInputDTO,
          decorators: [UseUploadFilePipe()],
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [CompanySubscriber, CompanyService, CompanyResolver]
    }),
    IntegrationModule
  ]
})
export class CompanyModule {}
