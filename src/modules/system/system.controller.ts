import { Controller, Get, Res, Query } from '@nestjs/common';
import { SystemService } from './system.service';
import { Response } from 'express';
import { ApiTags } from '@nestjs/swagger';
import ObsClient from 'esdk-obs-nodejs';
import { UseApiUserAuthGuard } from '@decorators/auth.decorator';
@ApiTags('System')
@Controller('system')
export class SystemController {
  constructor(private readonly systemService: SystemService) {}

  @Get('system/version')
  getVersion(@Res() res: Response) {
    const { version, lastUpdated } = this.systemService.getVersion();
    return res.status(200).send(version);
  }

  @Get('system/alive')
  getAlive(@Res() res: Response) {
    res.status(200).send('System is alive');
  }

  @Get('system/migration')
  getLatestMigrationFileName(@Res() res: Response) {
    res.status(200).send(this.systemService.getLatestMigrationFileName());
  }

  @Get('system/calculation')
  performComplexCalculation(@Res() res: Response) {
    let result = 0;
    const operationCount = this.getRandomInt(500000, 1500000);

    for (let i = 0; i < operationCount; i++) {
      const randomOperation = this.getRandomInt(1, 4);

      switch (randomOperation) {
        case 1:
          result += Math.random() * Math.random();
          break;
        case 2:
          result -= Math.random() * Math.random();
          break;
        case 3:
          result *= Math.random();
          break;
        case 4:
          const divisor = Math.random();
          result = divisor !== 0 ? result / divisor : result;
          break;
      }
    }

    res.status(200).send(result.toString());
  }

  private getRandomInt(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  @UseApiUserAuthGuard()
  @Get('/presigned-upload')
  async getPresignedUrl(
    @Query('key') key: string,
    @Query('size') size: number,
    @Query('mimeType') mimeType: string,
    @Res() res: Response
  ) {
    try {
      const { TMONE_BUCKET, TMONE_ACCESS_KEY_ID, TMONE_SECRET_ACCESS_KEY, TMONE_SERVER } = process.env;

      // INITIALIZE OBSCLIENT
      const obsClient = new ObsClient({
        access_key_id: TMONE_ACCESS_KEY_ID,
        secret_access_key: TMONE_SECRET_ACCESS_KEY,
        server: TMONE_SERVER,
        timeout: 60 * 5,
        signature: 'obs'
      });

      // PREPARE UPLOAD CONFIGS
      const method = 'PUT';
      const headers = {} as any;
      headers['Content-Length'] = size;
      if (mimeType === 'pdf') {
        headers['Content-Type'] = 'application/pdf';
      } else if (mimeType === 'jpeg') {
        headers['Content-Type'] = 'image/jpeg';
      } else if (mimeType === 'android') {
        headers['Content-Type'] = 'application/vnd.android.package-archive';
      } else {
        headers['Content-Type'] = 'text/plain';
      }

      // GENERATE PRESIGNED URL FOR UPLOAD
      const signedUrl = await obsClient.createSignedUrlSync({
        Method: method,
        Bucket: TMONE_BUCKET,
        Key: key,
        Headers: headers
      });

      res.status(200).json({ uploadUrl: signedUrl });
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      res.status(500).json({ error: 'Internal Server Error' });
    }
  }
}
