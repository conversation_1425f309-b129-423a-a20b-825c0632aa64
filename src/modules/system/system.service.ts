import { Injectable } from '@nestjs/common';
import * as fs from 'fs';

@Injectable()
export class SystemService {
  getVersion() {
    try {
      const packageJson = fs.readFileSync('package.json', 'utf-8');
      const version = JSON.parse(packageJson).version;
      const stat = fs.statSync('package.json');
      const lastUpdated = stat.mtime;

      return { version, lastUpdated };
    } catch (error) {
      throw new Error('Unable to read package.json or find version.');
    }
  }

  getLatestMigrationFileName() {
    const filePath = process.env.NODE_ENV === 'production' ? 'dist/database/migrations' : 'database/migrations';
    const migrations = fs.readdirSync(filePath);
    const migrationFileNames = migrations.map(migration => migration.split('-')[0]);
    return migrationFileNames.sort().reverse()[0];
  }
}
