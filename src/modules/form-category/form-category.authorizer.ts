import { UnauthorizedException, Injectable } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { FormCategoryDto } from './dto/form-category.gql.dto';
import { RoleTypeEnum } from '@constants';
import { UserEntity } from '@modules/user/entity/user.entity';
import { getRepository } from 'typeorm';

@Injectable()
export class FormCategoryAuthorizer implements Authorizer<FormCategoryDto> {
  async authorize(context: any): Promise<Filter<FormCategoryDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const userId = context?.req?.user?.id;
    const user = await getRepository(UserEntity).findOne({ id: userId });
    if (!user) throw new UnauthorizedException('User not found');

    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({
        owner: { companyId: { eq: user.companyId } }
      });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
