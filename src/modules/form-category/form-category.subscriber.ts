import { Injectable } from '@nestjs/common';
import { EventSubscriber, EntitySubscriberInterface, Connection, InsertEvent } from 'typeorm';
import { FormCategoryEntity } from './entity/form-category.entity';

@Injectable()
@EventSubscriber()
export class FormCategorySubscriber implements EntitySubscriberInterface<FormCategoryEntity> {
  constructor(connection: Connection) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return FormCategoryEntity;
  }

  async beforeInsert(event: InsertEvent<FormCategoryEntity>) {
    const { entity } = event;

    entity.userId = entity.createdBy;
    entity.createdBy = null;
  }
}
