import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { FormCategoryEntity } from './entity/form-category.entity';
import { FormCategoryDto, CreateFormCategoryInputDTO, UpdateFormCategoryInputDTO } from './dto/form-category.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { FormCategoryService } from './form-category.service';
import { FormCategorySubscriber } from './form-category.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([FormCategoryEntity])],
      resolvers: [
        {
          ServiceClass: FormCategoryService,
          DTOClass: FormCategoryDto,
          EntityClass: FormCategoryEntity,
          CreateDTOClass: CreateFormCategoryInputDTO,
          UpdateDTOClass: UpdateFormCategoryInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [FormCategorySubscriber, FormCategoryService]
    })
  ]
})
export class FormCategoryModule {}
