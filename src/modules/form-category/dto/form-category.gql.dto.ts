import { defaultQueryOptions } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Authorize, BeforeCreateOne, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { FormCategoryEntity } from '../entity/form-category.entity';
import { FormCategoryAuthorizer } from '../form-category.authorizer';
import * as Hooks from '@hooks/nest-graphql.hooks';

@ObjectType('FormCategory')
@Authorize(FormCategoryAuthorizer)
@BeforeCreateOne(Hooks.CreatedByOneHook)
@Relation('owner', () => UserEntity, relationOption())
@QueryOptions({ ...defaultQueryOptions })
export class FormCategoryDto extends FormCategoryEntity {}

@InputType()
export class CreateFormCategoryInputDTO {
  name: string;
}
@InputType()
export class UpdateFormCategoryInputDTO extends PartialType(CreateFormCategoryInputDTO) {}
