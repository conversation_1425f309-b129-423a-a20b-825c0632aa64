import { BaseEntity } from '@modules/base/base';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm';

@ObjectType()
@Entity('form_categories')
export class FormCategoryEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { unique: true })
  name: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.formCategories)
  @JoinColumn({ name: 'userId' })
  owner: UserEntity;

  @OneToMany(() => ProjectDocumentEntity, projectDocuments => projectDocuments.formCategory)
  projectDocuments: ProjectDocumentEntity[];
}
