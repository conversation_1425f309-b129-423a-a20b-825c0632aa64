import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FormCategoryEntity } from './entity/form-category.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';

@Injectable()
export class FormCategoryService extends TypeOrmQueryService<FormCategoryEntity> {
  constructor(
    @InjectRepository(FormCategoryEntity)
    private formCategoryRepo: Repository<FormCategoryEntity>
  ) {
    super(formCategoryRepo, { useSoftDelete: true });
  }
}
