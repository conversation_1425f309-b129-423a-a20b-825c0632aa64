import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DrawingLinksEntity } from './entity/drawing-links.entity';
import { GetDrawingLinksInput } from './dto/drawing-links.gql.dto';
import { DrawingLinkAttachmentEntity } from '@modules/drawing-link-attachment/entity/drawing-links-attachment.entity';

@Injectable()
export class DrawingLinksService extends TypeOrmQueryService<DrawingLinksEntity> {
  constructor(
    @InjectRepository(DrawingLinksEntity)
    private drawingLinksRepo: Repository<DrawingLinksEntity>,
    @InjectRepository(DrawingLinkAttachmentEntity)
    private drawingLinksAttachmentRepo: Repository<DrawingLinkAttachmentEntity>
  ) {
    super(drawingLinksRepo, { useSoftDelete: true });
  }

  async getDrawingLinksByAnnotId(input?: GetDrawingLinksInput) {
    const link = await this.drawingLinksRepo.findOne({
      where: { annotationId: input.annotationId }
    });
    if (!link) {
      throw new NotFoundException(`No drawing link found for annotation ID: ${input.annotationId}`);
    }
    return link;
  }

  async getDrawingLinksAttachments(drawingLinkId: number): Promise<DrawingLinkAttachmentEntity[]> {
    return this.drawingLinksAttachmentRepo.find({
      where: { drawingLinkId: drawingLinkId }
    });
  }
}
