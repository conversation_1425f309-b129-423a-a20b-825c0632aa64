import { Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber
} from 'typeorm';
import { DrawingLinksEntity } from './entity/drawing-links.entity';

@Injectable()
@EventSubscriber()
export class DrawingLinksSubscriber implements EntitySubscriberInterface<DrawingLinksEntity> {
  constructor(
    connection: Connection,
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return DrawingLinksEntity;
  }

}
