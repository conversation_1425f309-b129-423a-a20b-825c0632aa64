import { OffsetConnection, QueryArgsType, QueryOptions, UnPagedRelation } from '@nestjs-query/query-graphql';
import { ArgsType, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { DrawingLinksEntity } from '../entity/drawing-links.entity';
import { RelationIdInput, relationOption } from '@constants/query.constant';
import { ProjectDocumentDto } from '@modules/project-document/dto/project-document.gql.dto';
import { DrawingLinkAttachmentDto } from '@modules/drawing-link-attachment/dto/drawing-link-attachment.gql.dto';
import { CategoryType, defaultQueryOptions } from '@constants';

@ObjectType('DrawingLink')
@OffsetConnection('drawingLinkAttachments', () => DrawingLinkAttachmentDto, relationOption(true))
@UnPagedRelation('drawingLinkDocuments', () => ProjectDocumentDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class DrawingLinksDto extends DrawingLinksEntity {}

@ArgsType()
export class DrawingLinksQuery extends QueryArgsType(DrawingLinksDto) {}
export const DrawingLinksConnection = DrawingLinksQuery.ConnectionType;

@InputType()
export class GetDrawingLinksInput {
  annotationId: string;
}

@InputType()
export class CreateDrawingLinksInputDTO {
  annotationId: string;
  name: string;
  projectDocumentId: number;
  description?: string;
  category: CategoryType;
  drawingLinkDocuments?: RelationIdInput[];
}

@InputType()
export class UpdateDrawingLinksInputDTO extends PartialType(CreateDrawingLinksInputDTO) {}
