import { BaseEntity } from '@modules/base/base';
import { Enti<PERSON>, Column, JoinColumn, ManyToOne, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { ObjectType } from '@nestjs/graphql';
import { FilterableField } from '@nestjs-query/query-graphql';
import { CategoryType } from '@constants';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { DrawingLinkAttachmentEntity } from '@modules/drawing-link-attachment/entity/drawing-links-attachment.entity';
import { DrawingLinkCommentEntity } from '@modules/drawing-link-comment/entity/drawing-link-comment.entity';

@ObjectType()
@Entity('drawing_links')
export class DrawingLinksEntity extends BaseEntity {
  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  annotationId: string;

  @FilterableField({ nullable: false })
  @Column('text', { nullable: false })
  name: string;

  @FilterableField({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectDocumentId: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  description: string;

  @FilterableField(() => CategoryType, { nullable: false })
  @Column('enum', { enum: CategoryType, nullable: false })
  category: CategoryType;

  // Relations
  @ManyToOne(() => ProjectDocumentEntity, projectDocument => projectDocument.drawingLinks)
  @JoinColumn({ name: 'projectDocumentId' })
  projectDocument: ProjectDocumentEntity;

  @OneToMany(() => DrawingLinkAttachmentEntity, drawingLinkAttachment => drawingLinkAttachment.drawingLink, {
    cascade: true
  })
  drawingLinkAttachments: DrawingLinkAttachmentEntity[];

  @OneToMany(() => DrawingLinkCommentEntity, drawingLinkComment => drawingLinkComment.drawingLink, {
    cascade: true
  })
  drawingLinkComment: DrawingLinkCommentEntity[];

  @ManyToMany(() => ProjectDocumentEntity, document => document.drawingLinkDocuments, { cascade: ['update'] })
  @JoinTable({ name: 'drawing_link_documents' })
  drawingLinkDocuments: ProjectDocumentEntity[];
}
