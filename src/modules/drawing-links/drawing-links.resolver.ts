import { GqlAuthGuard } from '@guards/auth.guard';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { Args, Query, Resolver } from '@nestjs/graphql';
import {
  DrawingLinksDto,
  GetDrawingLinksInput
} from './dto/drawing-links.gql.dto';
import { DrawingLinksService } from './drawing-links.service';

@UseGuards(GqlAuthGuard)
@Resolver(() => DrawingLinksDto)
export class DrawingLinksResolver {
  constructor(private drawingLinksService: DrawingLinksService) {}

  @Query(() => DrawingLinksDto)
  async getDrawingLinksByAnnotId(@Args('input') input: GetDrawingLinksInput) {
    const links = await this.drawingLinksService.getDrawingLinksByAnnotId(input);
    if (!links) {
      throw new NotFoundException('No drawing link found with the given annotation ID');
    }
    return links;
  }
}
