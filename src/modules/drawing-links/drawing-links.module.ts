import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { DrawingLinksService } from './drawing-links.service';
import { DrawingLinksEntity } from './entity/drawing-links.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { DrawingLinksSubscriber } from './drawing-links.subscriber';
import { CreateDrawingLinksInputDTO, DrawingLinksDto, UpdateDrawingLinksInputDTO } from './dto/drawing-links.gql.dto';
import { DrawingLinksResolver } from './drawing-links.resolver';
import { DrawingLinkAttachmentEntity } from '@modules/drawing-link-attachment/entity/drawing-links-attachment.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([DrawingLinksEntity, DrawingLinkAttachmentEntity]),
        IntegrationModule
      ],

      resolvers: [
        {
          ServiceClass: DrawingLinksService,
          DTOClass: DrawingLinksDto,
          EntityClass: DrawingLinksEntity,
          CreateDTOClass: CreateDrawingLinksInputDTO,
          UpdateDTOClass: UpdateDrawingLinksInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [DrawingLinksService, DrawingLinksEntity, DrawingLinksSubscriber, DrawingLinksResolver]
    })
  ]
})
export class DrawingLinksModule {}
