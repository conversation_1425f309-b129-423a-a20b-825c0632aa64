import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { AuthData } from '@types';
import { ContactService } from './contact.service';
import { ContactDto, CreateContactInputDTO, UpdateOneContactDTO } from './dto/contact.gql.dto';

@UseGuards(GqlAuthGuard)
@Resolver(() => ContactDto)
export class ContactResolver {
  constructor(private contactService: ContactService) {}

  @Mutation(() => ContactDto)
  async createNewContact(
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number,
    @Args('input') input: CreateContactInputDTO
  ) {
    return this.contactService.createNewContact(user.id, projectId, input);
  }

  @Mutation(() => ContactDto)
  async updateContact(@GqlGetGqlProjectData() projectId: number, @Args('input') input: UpdateOneContactDTO) {
    return this.contactService.updateExistContact(projectId, input);
  }
}
