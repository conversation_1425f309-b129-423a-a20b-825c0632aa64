import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ContactEntity } from './entity/contact.entity';
import { ContactDto, CreateContactInputDTO, UpdateContactInputDTO } from './dto/contact.gql.dto';
import {
  ContactCompanyDto,
  CreateContactCompanyInputDTO,
  UpdateContactCompanyInputDTO
} from './dto/contact-company.gql.dto';
import { ContactCompanyEntity } from './entity/contact-company.entity';
import { ContactResolver } from './contact.resolver';
import { ContactService } from './contact.service';
import { ContactImportController } from './contact-import.controller';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ContactEntity, ContactCompanyEntity])],
      resolvers: [
        {
          ServiceClass: ContactService,
          DTOClass: ContactDto,
          EntityClass: ContactEntity,
          CreateDTOClass: CreateContactInputDTO,
          UpdateDTOClass: UpdateContactInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        },
        {
          DTOClass: ContactCompanyDto,
          EntityClass: ContactCompanyEntity,
          CreateDTOClass: CreateContactCompanyInputDTO,
          UpdateDTOClass: UpdateContactCompanyInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [ContactResolver, ContactService]
    })
  ],
  controllers: [ContactImportController]
})
export class ContactModule {}
