import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { ContactCompanyEntity } from './contact-company.entity';

@ObjectType()
@Entity('contacts')
export class ContactEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  contactCompanyId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @FilterableField()
  @Column('varchar')
  name: string;

  @FilterableField()
  @Column('varchar')
  phoneNo: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  email?: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => ContactCompanyEntity, contactCompany => contactCompany.contacts)
  @JoinColumn({ name: 'contactCompanyId' })
  contactCompany: ContactCompanyEntity;

  @ManyToOne(() => ProjectEntity, project => project.contacts)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => UserEntity, user => user.contacts)
  @JoinColumn({ name: 'userId' })
  owner: UserEntity;
}
