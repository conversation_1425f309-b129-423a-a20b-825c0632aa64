import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { ContactEntity } from './contact.entity';

@ObjectType()
@Entity('contact_companies')
export class ContactCompanyEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @FilterableField({ nullable: true })
  @Column('varchar')
  name: string;

  /* -------------------------------- Relations ------------------------------- */
  @OneToMany(() => ContactEntity, contacts => contacts.contactCompany)
  contacts: ContactEntity[];

  @ManyToOne(() => ProjectEntity, project => project.contactCompanies)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;
}
