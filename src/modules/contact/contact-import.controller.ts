import { GetAuthD<PERSON>, Get<PERSON><PERSON>jectD<PERSON>, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { BadRequestException, Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { ContactService } from './contact.service';
import * as Dto from './dto/contact.api.dto';

@ApiTags('Contact Import API')
@Controller('contact')
export class ContactImportController {
  constructor(private contactService: ContactService) {}

  @UseApiUserAuthGuard()
  @Post('import')
  async importContacts(
    @Body() contactDto: Dto.ContactImportDto,
    @GetAuthData() user: AuthData,
    @GetProjectData() projectId: number
  ) {
    try {
      const contacts = contactDto.contacts;
      if (!contacts) throw new BadRequestException(`Input body is invalid`);
      if (!projectId) throw new BadRequestException('Project Id not found');

      await this.contactService.importNewContacts(contacts, user.id, projectId);
      return { message: 'Import Contact Successfully' };
    } catch (e) {
      throw new Error('Something went wrong in contact import controller! importContacts function');
      return Promise.reject(e);
    }
  }
}
