import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ContactCompanyEntity } from '../entity/contact-company.entity';

@ObjectType('ContactCompany')
@QueryOptions({ ...defaultQueryOptions })
export class ContactCompanyDto extends ContactCompanyEntity {}

@InputType()
export class CreateContactCompanyInputDTO {
  name: string;
}
@InputType()
export class UpdateContactCompanyInputDTO extends PartialType(CreateContactCompanyInputDTO) {}
