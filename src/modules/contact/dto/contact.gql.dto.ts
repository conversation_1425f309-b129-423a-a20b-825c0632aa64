import { defaultQueryOptions } from '@constants';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import { Authorize, FilterableRelation, IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { IsEmail, IsOptional } from 'class-validator';
import { ContactAuthorizer } from '../contact.authorizer';
import { ContactEntity } from '../entity/contact.entity';
import { ContactCompanyDto } from './contact-company.gql.dto';
import { relationOption } from '@constants/query.constant';

@ObjectType('Contact')
@Authorize(ContactAuthorizer)
@FilterableRelation('contactCompany', () => ContactCompanyDto, relationOption(true))
@FilterableRelation('owner', () => UserDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class ContactDto extends ContactEntity {}

@InputType()
export class CreateContactInputDTO {
  contactCompanyId?: number;
  projectId?: number;
  name: string;
  companyName?: string;
  // @IsMobilePhone() phoneNo: string;
  phoneNo: string;

  @IsOptional()
  email?: string;
}
@InputType()
export class UpdateContactInputDTO extends PartialType(CreateContactInputDTO) {}

@InputType()
export class UpdateOneContactDTO {
  @IDField(() => ID) id: number;
  update: UpdateContactInputDTO;
}
