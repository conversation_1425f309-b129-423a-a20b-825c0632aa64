import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { CreateContactInputDTO, UpdateOneContactDTO } from './dto/contact.gql.dto';
import { ContactCompanyEntity } from './entity/contact-company.entity';
import { ContactEntity } from './entity/contact.entity';
import * as _ from 'lodash';
import * as Dto from './dto/contact.api.dto';
import { getErrorMessage } from '@common/error';
import validator from 'validator';
import { emit } from 'process';

@Injectable()
export class ContactService extends TypeOrmQueryService<ContactEntity> {
  constructor(
    @InjectRepository(ContactEntity)
    private contactRepo: Repository<ContactEntity>
  ) {
    // pass the use soft delete option to the service.
    super(contactRepo, { useSoftDelete: true });
  }

  async createNewContact(userId: number, projectId: number, input: CreateContactInputDTO) {
    try {
      const { phoneNo, email, companyName } = input;
      // To verify the contact input
      await this.contactInputValidation(phoneNo, email, projectId);

      const contactCompany = await getRepository(ContactCompanyEntity).findOne({
        name: companyName,
        projectId
      });

      // Create new contact company if it doesn't exist
      if (!contactCompany) {
        const newContactCompany = await this.createContactCompany(companyName, projectId);
        return await this.createContact(newContactCompany.id, userId, projectId, input);
      }
      return await this.createContact(contactCompany.id, userId, projectId, input);
    } catch (e) {
      
      getErrorMessage(e, 'ContactService', 'createNewContact');
    }
  }

  async updateExistContact(projectId: number, input: UpdateOneContactDTO) {
    try {
      const { phoneNo, email, companyName } = input.update;

      // To verify the contact input
      await this.contactInputValidation(phoneNo, email, projectId, input.id);

      const contactCompany = await getRepository(ContactCompanyEntity).findOne({
        name: companyName,
        projectId
      });
      // Create new contact company if it doesn't exist
      if (!contactCompany) {
        const newContactCompany = await this.createContactCompany(companyName, projectId);
        return await this.updateContact(newContactCompany.id, input);
      }
      return await this.updateContact(contactCompany.id, input);
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'updateExistContact');
    }
  }

  async importNewContacts(contacts: Dto.ContactImportType[], userId: number, projectId: number) {
    try {
      await this.verifyImportContactWithDb(contacts, projectId);
      const newImportContacts = await this.uniqByWithinImportContact(contacts);

      // return newImportContacts;
      return await _.forEach(newImportContacts, async newImportContact => {
        const contactCompany = await getRepository(ContactCompanyEntity).findOne({
          name: newImportContact.company,
          projectId
        });

        // Create new contact company if it doesn't exist
        if (!contactCompany) {
          const newContactCompany = await this.createContactCompany(newImportContact.company, projectId);
          return await this.createContact(newContactCompany.id, userId, projectId, newImportContact);
        }
        return await this.createContact(contactCompany.id, userId, projectId, newImportContact);
      });
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'importNewContacts');
    }
  }

  // Custom Create Contact
  private async createContact(
    contactCompanyId: number,
    userId: number,
    projectId: number,
    input: CreateContactInputDTO
  ) {
    try {
      const { name, phoneNo, email } = input;

      const param = {
        contactCompanyId,
        projectId,
        name,
        phoneNo,
        email,
        userId
      };

      if (!email) delete param['email'];

      const newContact = await this.contactRepo.create(param);
      return await this.contactRepo.save(newContact);
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'createContact');
    }
  }

  // Custom Update Contact Information
  private async updateContact(contactCompanyId: number, input: UpdateOneContactDTO) {
    try {
      const { name, phoneNo, email } = input.update;

      const contact = await this.contactRepo.findOne({ id: input.id });
      if (!contact) throw new BadRequestException('Contact not found');

      this.contactRepo.merge(contact, { contactCompanyId, name, phoneNo, email });
      return await this.contactRepo.save(contact);
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'updateContact');
    }
  }

  // Create Contact Company
  private async createContactCompany(companyName: string, projectId: number) {
    try {
      const newContactCompany = getRepository(ContactCompanyEntity).create({
        projectId,
        name: companyName
      });
      return await getRepository(ContactCompanyEntity).save(newContactCompany);
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'createContactCompany');
    }
  }

  /** ------------------ To verify the contact input (phoneNo and email) ------------------ */
  private async contactInputValidation(phoneNo: string, email: string, projectId: number, contactId?: number) {
    try {
      const currentContact = await this.contactRepo.findOne({ id: contactId });
      const isEmailValid = email === '' ? true : validator.isEmail(email);
      if (!isEmailValid) throw new BadRequestException(`Email Address is not valid`);

      // Check duplicate phoneNo
      const duplicatePhoneNo =
        currentContact && currentContact.phoneNo === phoneNo ? null : await this.contactRepo.find({ phoneNo });

      // Check duplicate email
      const duplicateEmail =
        currentContact && currentContact.email === email ? null : await this.contactRepo.find({ email });

      if (duplicatePhoneNo || duplicateEmail) {
        const isSameProject = await this.isSameProject(projectId, duplicatePhoneNo, duplicateEmail);
        if (isSameProject)
          throw new BadRequestException(`The entered Email or Phone No. is exist in this project's contact`);
      }
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'contactInputValidation');
    }
  }

  /** ------------------ To verify the imported contact (phoneNo and email) ------------------ */
  private async verifyImportContactWithDb(contacts: Dto.ContactImportType[], projectId: number) {
    try {
      const duplicatePhoneNoOrEmailRows = await Promise.all(
        contacts.map(async (contact, index) => {
          // Check duplicate phoneNo
          const duplicatePhoneNo =
            contact && contact.phoneNo ? await this.contactRepo.find({ phoneNo: contact.phoneNo }) : null;
          // Check duplicate email
          const duplicateEmail =
            contact && contact.email ? await this.contactRepo.find({ email: contact.email }) : null;

          if (duplicatePhoneNo || duplicateEmail) {
            const isSameProject = await this.isSameProject(projectId, duplicatePhoneNo, duplicateEmail);
            if (isSameProject) return index + 1;
          }
          return null;
        })
      );

      // Splice null from the array of error
      const errorRows = duplicatePhoneNoOrEmailRows.filter(errorIndex => {
        return errorIndex !== null;
      });

      // Return the number of row by (index + 1)
      if (errorRows.length !== 0) {
        const errors = _.join(errorRows, ', ');

        throw new BadRequestException(`The Email or Phone No. on row(s) ${errors} are exist in this project's contact`);
      }
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'verifyImportContactWithDb');
    }
  }

  // Check whether is same project or not
  private async isSameProject(projectId: number, duplicatePhoneNo: ContactEntity[], duplicateEmail: ContactEntity[]) {
    try {
      const isSameProject1 = await _.find(duplicatePhoneNo, ['projectId', projectId]);
      const isSameProject2 = await _.find(duplicateEmail, ['projectId', projectId]);
      if (isSameProject1 || isSameProject2) return true;
      return false;
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'isSameProject');
    }
  }

  // Uniq the imported contacts
  private async uniqByWithinImportContact(contacts: Dto.ContactImportType[]) {
    // const contactUniqByEmail = await _.uniqBy(contacts, (contact) => {
    //   return contact.email;
    // });
    try {
      const contactUniqByPhoneNo = await _.uniqBy(contacts, contact => {
        return contact.phoneNo;
      });

      return contactUniqByPhoneNo;
    } catch (e) {
      getErrorMessage(e, 'ContactService', 'uniqByWithinImportContact');
    }
  }
}
