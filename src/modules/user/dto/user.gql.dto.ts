import { defaultQueryOptions } from '@constants';
import { CompanyDto } from '@modules/company/dto/company.gql.dto';
import {
  Authorize,
  CursorConnection,
  FilterableRelation,
  IDField,
  QueryOptions,
  Relation
} from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { IsEmail } from 'class-validator';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { UserEntity } from '../entity/user.entity';
import { relationOption } from '@constants/query.constant';
import { UserAuthorizer } from '../user.authorizer';
import { TimezoneDto } from '@modules/timezone/dto/timezone.gql.dto';
import { ScheduleDto } from '@modules/schedule/dto/schedule.gql.dto';
import { ScheduleLinksDto } from '@modules/schedule-links/dto/schedule-links.gql.dto';

@ObjectType('User')
@Authorize(UserAuthorizer)
@FilterableRelation('company', () => CompanyDto, relationOption(true))
@FilterableRelation('schedules', () => ScheduleDto, relationOption(true))
@FilterableRelation('scheduleslinks', () => ScheduleLinksDto, relationOption(true))
@Relation('myCompany', () => CompanyDto, relationOption(true))
@Relation('timezone', () => TimezoneDto, relationOption(true))
@CursorConnection('companies', () => CompanyDto, { disableRemove: true, disableUpdate: true, defaultResultSize: 9999999 })
@QueryOptions({ ...defaultQueryOptions })
export class UserDto extends UserEntity {}

@InputType()
export class CreateUserInputDTO {
  name: string;
  @IsEmail() email: string;
  // @IsMobilePhone() phoneNo: string;
  phoneNo: string;
  @Field(() => GraphQLUpload) avatar?: FileUpload;
  reportTo?: string;
  position?: string;
  companyOrigin?: string;
  receiveWeeklyEmails?: boolean;
  isReadChangeLog?: boolean;
  isReadChangeLogMobile?: boolean;
}
@InputType()
export class UpdateUserInputDTO extends PartialType(CreateUserInputDTO) {
  timezoneId?: number;
  fontSize?: number;
  signKey?: string;
  stampKey?: string;
  enableTwoFA?: boolean;
  @Field(() => GraphQLUpload) stampUrl?: FileUpload;
  @Field(() => GraphQLUpload) signUrl?: FileUpload;
  @Field(() => GraphQLUpload) stampAndSignUrl?: FileUpload;  
  changeSignatureToken?: string;
  isFirstTimeLogin?: boolean;
}

@InputType()
export class UpdateUserPasswordDTO {
  password: string;
}

@InputType()
export class DeleteOneUserInput {
  @IDField(() => ID) id: number;
}

@InputType()
export class IsReadChangeLogDTO {
  isReadChangeLog: boolean;
}

@InputType()
export class UpdateUserSignature {
  changeSignatureToken: string;
  signature: string;
}

@ObjectType()
export class Channels {
  @Field(() => Boolean) email: boolean;
  @Field(() => Boolean) sms: boolean;
  @Field(() => Boolean) in_app: boolean;
  @Field(() => Boolean) chat: boolean;
  @Field(() => Boolean) push: boolean;
}

@ObjectType()
export class GetUserPreferencesResponseType {
  @Field(() => Boolean) enabled: boolean;
  @Field(() => Channels, { nullable: true }) channels: Channels | null;
}

@InputType()
export class UpdateUserStamp {
  id: number;
  stamp: string;
}

@InputType()
export class GenerateOtpInput {
  email: string; 
}

@InputType()
export class VerifyOtpInput {
  email: string; 
  otp: string;
}
