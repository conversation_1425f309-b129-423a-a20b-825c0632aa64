import { BaseEntity } from '@modules/base/base';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { ContactEntity } from '@modules/contact/entity/contact.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { ProjectTeamEntity } from '@modules/project-team/entity/project-team.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { TaskCommentEntity } from '@modules/task-comment/entity/task-comment.entity';
import { hashPassword } from '@providers/bcrypt.service';
import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  OneToOne,
  ManyToMany,
  BeforeInsert,
  BeforeUpdate,
  getRepository,
  JoinTable
} from 'typeorm';
import { <PERSON>ield, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { FormCategoryEntity } from '@modules/form-category/entity/form-category.entity';
import { EventEntity } from '@modules/event/entity/event.entity';
import { RoleTypeEnum } from '@constants';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { ProjectDocumentCommentEntity } from '@modules/project-document-comment/entity/project-document-comment.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { UserFcmTokenEntity } from '@modules/user-fcm-token/entity/user-fcm-token.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';
import { TimezoneEntity } from '@modules/timezone/entity/timezone.entity';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { FileLogEntity } from '@modules/file-log/entity/file-log.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { ScheduleCommentEntity } from '@modules/schedule-comment/entity/schedule-comment.entity';
import { ScheduleLinksEntity } from '@modules/schedule-links/entity/schedule-links.entity';
import { EventAssigneeEntity } from '@modules/event-assignee/entity/event-assignee.entity';
import { DrawingLinkCommentEntity } from '@modules/drawing-link-comment/entity/drawing-link-comment.entity';
import { UserSettingsEntity } from '@modules/user-settings/entity/user-settings.entity';
import { WorkspaceGroupUserEntity } from '@modules/workspace-group-user/entity/workspace-group-user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';

@ObjectType()
@Entity('users')
export class UserEntity extends BaseEntity {
  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  companyId: number;

  @FilterableField({ nullable: true })
  @Column({ charset: 'utf8mb4', nullable: true })
  name: string;

  @FilterableField()
  @Column('varchar', { unique: true })
  email: string;

  @Column('varchar', { nullable: true })
  password: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  facebookId?: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  googleId?: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  appleId?: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  phoneNo: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  avatar: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  reportTo: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  position: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  lastLogin: Date;

  @FilterableField()
  @Column('boolean', { default: false })
  isEmailVerified: boolean;

  @FilterableField()
  @Column('varchar', { length: 20, default: RoleTypeEnum.User })
  type: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  fcmToken: string;

  @FilterableField()
  @Column({ unsigned: true, nullable: true })
  timezoneId: number;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  companyOrigin: string;

  @FilterableField()
  @Column('boolean', { default: false })
  isReadChangeLog: boolean;

  @FilterableField()
  @Column('boolean', { default: false })
  isReadChangeLogMobile: boolean;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  signUrl: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  stampUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  stampAndSignUrl: string;

  @FilterableField({ nullable: true })
  @Column('int', { nullable: true, default: 12 })
  fontSize: number;

  @Column('text', { nullable: true })
  avatarKey: string;

  @Column('text', { nullable: true })
  stampKey: string;

  @Column('text', { nullable: true })
  signKey: string;

  @Column('text', { nullable: true })
  stampAndSignKey: string;

  @Column('varchar', { nullable: true })
  otpToken: string;

  @Column('timestamp', { nullable: true })
  otpExpiredAt: Date;

  @FilterableField({ nullable: true })
  @Column('bool', { nullable: true, default: false })
  enableTwoFA: boolean;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  removedAt: Date;
  
  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  color: string;

  /* -------------------------------- Token ------------------------------- */
  @FilterableField()
  @Column('boolean', { default: true })
  isFirstTimeLogin: boolean;

  @Column('text', { nullable: true })
  refreshToken: string;

  @Column('text', { nullable: true })
  resetPasswordToken: string;

  @Column('text', { nullable: true })
  signUpToken: string;

  @FilterableField()
  @Column('boolean', { default: true })
  receiveWeeklyEmails: boolean;

  @Column('json', { nullable: true })
  clientDeviceInfo: string;

  @Column('text', { nullable: true })
  changeSignatureToken: string;

  /* -------------------------------- Hash Password ------------------------------- */
  @BeforeInsert()
  hashPassword() {
    if (this.password) {
      this.password = hashPassword(this.password);
    }
  }

  @BeforeUpdate()
  async hashPasswordUpdate() {
    const userRepo = getRepository(UserEntity);
    const user = await userRepo.findOne({ id: this.id });
    if (this.password !== user.password) {
      this.password = hashPassword(this.password);
    }
  }
  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => TimezoneEntity, timezone => timezone.users, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'timezoneId' })
  timezone: TimezoneEntity;

  @OneToOne(() => CompanyEntity, company => company.owner)
  myCompany: CompanyEntity;

  @ManyToOne(() => CompanyEntity, company => company.users, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'companyId' })
  company: CompanyEntity;

  @ManyToMany(() => CompanyEntity, company => company._users)
  @JoinTable({ name: 'user_to_company' })
  companies: CompanyEntity[];

  @OneToMany(() => ContactEntity, contacts => contacts.owner)
  contacts: ContactEntity[];

  @OneToMany(() => ProjectEntity, projects => projects.owner)
  projects: ProjectEntity[];

  @OneToMany(() => ProjectUserEntity, projectUsers => projectUsers.user)
  projectUsers: ProjectUserEntity[];

  @OneToMany(() => ProjectDocumentUserEntity, projectDocumentUser => projectDocumentUser.user)
  projectDocumentUsers: ProjectDocumentUserEntity[];

  @OneToMany(() => WorkspaceGroupUserEntity, workspaceGroupUser => workspaceGroupUser.user)
  workspaceGroupUsers: WorkspaceGroupUserEntity[];

  @OneToMany(() => TaskEntity, tasks => tasks.owner)
  tasks: TaskEntity[];

  @OneToMany(() => ProjectUserEntity, projectUsers => projectUsers.owner)
  addedProjectUsers: ProjectUserEntity[];

  @OneToMany(() => TaskCommentEntity, taskComments => taskComments.user)
  taskComments: TaskCommentEntity[];

  @OneToMany(() => TasksAttachmentEntity, taskAttachments => taskAttachments.user)
  taskAttachments: TasksAttachmentEntity[];

  @OneToMany(() => TasksMediaEntity, taskMedias => taskMedias.user)
  taskMedias: TasksMediaEntity[];

  @OneToMany(() => WorkspaceAttachmentEntity, workspaceAttachments => workspaceAttachments.user)
  workspaceAttachments: WorkspaceAttachmentEntity[];

  @OneToMany(() => WorkspacePhotoEntity, workspacePhotos => workspacePhotos.user)
  workspacePhotos: WorkspacePhotoEntity[];

  @OneToMany(() => NotificationTransactionEntity, notificationTransactions => notificationTransactions.user)
  notifications: NotificationTransactionEntity[];

  @OneToMany(() => NotificationTransactionEntity, notificationTransactions => notificationTransactions.actor)
  sentNotifications: NotificationTransactionEntity[];

  @OneToMany(() => EventEntity, events => events.user)
  events: EventEntity[];

  @OneToMany(() => FormCategoryEntity, formCategories => formCategories.owner)
  formCategories: FormCategoryEntity[];

  @OneToMany(() => AuditLogEntity, auditLogs => auditLogs.user)
  auditLogs: AuditLogEntity[];

  @OneToMany(() => FileLogEntity, fileLog => fileLog.user)
  fileLog: FileLogEntity[];

  @OneToMany(() => SalesOrderEntity, salesOrders => salesOrders.user)
  salesOrders: SalesOrderEntity[];

  @OneToMany(() => ProjectDocumentEntity, projectDocuments => projectDocuments.owner)
  projectDocuments: ProjectDocumentEntity[];

  @OneToMany(() => RequestForSignatureEntity, requestForSignatures => requestForSignatures.owner)
  requestForSignatures: RequestForSignatureEntity[];

  @OneToMany(() => RequestForSignatureEntity, requestForSignatures => requestForSignatures.signBy)
  signForRequests: RequestForSignatureEntity[];

  @OneToMany(() => ProjectDocumentCommentEntity, projectDocumentComments => projectDocumentComments.user)
  projectDocumentComments: ProjectDocumentCommentEntity[];

  @OneToMany(() => WorkspaceCCEntity, ccs => ccs.ccOwner)
  sendCCs: WorkspaceCCEntity[];

  @OneToMany(() => WorkspaceCCEntity, ccs => ccs.ccUser)
  receiveCCs: WorkspaceCCEntity[];

  @OneToMany(() => UserFcmTokenEntity, fcmTokens => fcmTokens.user)
  fcmTokens: UserFcmTokenEntity[];

  @ManyToMany(() => ProjectTeamEntity, projectTeam => projectTeam.users)
  projectTeams: ProjectTeamEntity[];

  @OneToMany(() => ScheduleEntity, schedules => schedules.owner)
  schedules: ScheduleEntity[];

  @OneToMany(() => ScheduleLinksEntity, schedulesLinks => schedulesLinks.owner)
  schedulesLinks: ScheduleLinksEntity[];

  @OneToMany(() => SchedulesMediaEntity, scheduleMedias => scheduleMedias.user)
  scheduleMedias: SchedulesMediaEntity[];

  @OneToMany(() => ScheduleCommentEntity, scheduleComments => scheduleComments.user)
  scheduleComments: ScheduleCommentEntity[];

  @OneToMany(() => WorkspaceGroupEntity, workspaceGroups => workspaceGroups.creator)
  workspaceGroups: WorkspaceGroupEntity[];

  @OneToOne(() => EventAssigneeEntity, eventAssignee => eventAssignee.user)
  eventAssignee: EventAssigneeEntity;

  @OneToOne(() => UserSettingsEntity, userSettings => userSettings.user)
  userSettings: UserSettingsEntity;

  @OneToMany(() => DrawingLinkCommentEntity, drawingLinkComment => drawingLinkComment.user)
  drawingLinkComments: DrawingLinkCommentEntity[];

  @OneToMany(() => ContactsEmailEntity, contactsEmails => contactsEmails.user)
  contactsEmails: ContactsEmailEntity[];
}