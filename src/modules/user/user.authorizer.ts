import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { UserDto } from './dto/user.gql.dto';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class UserAuthorizer implements Authorizer<UserDto> {
  authorize(context: any): Promise<Filter<UserDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const userId = context?.req?.user?.id;
    if (!userId) throw new UnauthorizedException('User not found');

    if (context?.req?.user?.type === RoleTypeEnum.User) {
      return Promise.resolve({
        id: { eq: context.req.user.id }
      });
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
