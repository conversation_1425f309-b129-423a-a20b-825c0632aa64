import { FileService } from '@modules/integration/file.service';
import { Injectable } from '@nestjs/common';
import { FileUpload } from 'graphql-upload';
import _ from 'lodash';
import { EventSubscriber, EntitySubscriberInterface, Connection, UpdateEvent, InsertEvent } from 'typeorm';
import { UserEntity } from './entity/user.entity';
import { NovuService } from '@modules/integration/novu/novu.service';
import { getErrorMessage } from '@common/error';
import sharp from 'sharp';
import axios from 'axios';
import { UserService } from './user.service';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { UserSettingsService } from '@modules/user-settings/user-settings.service';
import randomColor from 'randomcolor'; // Import randomcolor library

@Injectable()
@EventSubscriber()
export class UserSubscriber implements EntitySubscriberInterface<UserEntity> {
  constructor(
    connection: Connection,
    private fileService: FileService,
    private novuService: NovuService,
    private tmObsService: TMOneService,
    private userService: UserService,
    private userSettingsService: UserSettingsService
  ) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return UserEntity;
  }

  async afterInsert(event: InsertEvent<UserEntity>) {
    const { entity } = event;
    await this.novuService.createSubscriber(entity);

    // Generate a random color using randomcolor
    const darkColor = randomColor({ luminosity: 'dark' });

    // Save the random color to the database
    await event.manager.getRepository(UserEntity).update(entity.id, {
      color: darkColor
    });

    // removed for this hotfix as its not used anywhere yet
    // await this.userSettingsService.createOne({
    //   userId: entity.id,
    // });

    return entity;
  }

  async afterUpdate(event: UpdateEvent<UserEntity>) {
    try {
      const { entity } = event;
      if (entity.signKey && entity.stampKey && (entity.stampAndSignKey === null || entity.stampAndSignKey === '')) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        const resizeConfig = { fit: sharp.fit.contain, background: { r: 0, g: 0, b: 0, alpha: 0 } };

        const stampUrl = await this.userService.getPresignedUrl(entity as UserEntity, 'stampKey');
        const signUrl = await this.userService.getPresignedUrl(entity as UserEntity, 'signKey');

        const stampBuffer = (await axios({ url: stampUrl, responseType: 'arraybuffer' })).data as Buffer;
        let stampMetadata = await sharp(stampBuffer).metadata();
        let resizedStampBuffer = stampBuffer;
        if (stampMetadata.width > 600 || stampMetadata.height > 600) {
          resizedStampBuffer = await sharp(stampBuffer)
            .resize({ width: 600, height: 600, ...resizeConfig })
            .toBuffer();
          stampMetadata = await sharp(resizedStampBuffer).metadata();
        }

        const signBuffer = (await axios({ url: signUrl, responseType: 'arraybuffer' })).data as Buffer;
        let signatureMetadata = await sharp(signBuffer).metadata();
        let resizedSignBuffer = signBuffer;
        if (stampMetadata.width > 0.5 * stampMetadata.width || stampMetadata.height > 0.5 * stampMetadata.height) {
          resizedSignBuffer = await sharp(signBuffer)
            .resize({
              width: Math.round(0.5 * stampMetadata.width),
              height: Math.round(0.5 * stampMetadata.width),
              ...resizeConfig
            })
            .toBuffer();
          signatureMetadata = await sharp(resizedSignBuffer).metadata();
        }

        const signatureOffset = {
          top: Math.round(stampMetadata.height),
          left: Math.round((stampMetadata.width - signatureMetadata.width) / 2)
        };

        const stampOffset = {
          top: 10,
          left: Math.round((stampMetadata.width - signatureMetadata.width) / 2)
        };

        const output = await sharp('templates/template.png', { animated: true })
          .composite([
            { input: resizedStampBuffer, top: stampOffset.top, left: 0, blend: 'over' },
            { input: resizedSignBuffer, top: 0, left: signatureOffset.left, blend: 'over' }
          ])
          .resize({
            height: Math.round(stampMetadata.height + stampOffset.top),
            width: Math.round(stampMetadata.width),
            ...resizeConfig
          })
          .toBuffer();

        const file = {
          filename: `combined-stamp-${entity.id}.png`,
          mimetype: 'application/pdf',
          encoding: '7bit'
        };
        const folder = 'User-Signature';

        const { key } = await this.fileService.uploadGqlFile(file as FileUpload, folder, null, output);

        await event.manager.getRepository(UserEntity).update(entity.id, {
          stampAndSignKey: key
        });
      }

      const email = event.updatedColumns.find(column => column.propertyName === 'email');
      const name = event.updatedColumns.find(column => column.propertyName === 'name');
      const phoneNo = event.updatedColumns.find(column => column.propertyName === 'phoneNo');
      const avatar = event.updatedColumns.find(column => column.propertyName === 'avatar');

      if (email || name || phoneNo || avatar) this.novuService.updateSubscriber(entity as UserEntity);

      return entity;
    } catch (e) {
      getErrorMessage(e, 'UserSubscriber', 'afterUpdate');
    }
  }

  async beforeUpdate(event: UpdateEvent<UserEntity>) {
    try {
      const { entity } = event;

      if (entity.avatar && _.isObject(entity.avatar)) {
        const folder = 'User-Avatar';
        const { key } = await this.fileService.uploadGqlFile(entity.avatar as FileUpload, folder);
        entity.avatarKey = key;
      }

      if (entity.stampUrl && _.isObject(entity.stampUrl)) {
        const folder = 'User-Stamp';
        const { key } = await this.fileService.uploadGqlFile(entity.stampUrl as FileUpload, folder);
        entity.stampKey = key;
      }

      if (entity.signUrl && _.isObject(entity.signUrl)) {
        const folder = 'User-Signature';
        const { key } = await this.fileService.uploadGqlFile(entity.signUrl as FileUpload, folder);
        entity.signKey = key;
      }
    } catch (error) {
      getErrorMessage(error, 'UserSubscriber', 'beforeUpdate');
    }
  }
}
