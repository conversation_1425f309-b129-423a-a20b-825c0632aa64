import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from './entity/user.entity';
import { BadRequestException, Injectable } from '@nestjs/common';
import { GenerateOtpInput, UpdateUserInputDTO, UpdateUserSignature, UpdateUserStamp, VerifyOtpInput } from './dto/user.gql.dto';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectDocumentStatus, ProjectUserRoleType } from '@constants';
import _ from 'lodash';
import moment from 'moment';
import { Cron } from '@nestjs/schedule';
import { getErrorMessage } from '@common/error';
import { FileService } from '@modules/integration/file.service';
import mime from 'mime';
import { Readable } from 'stream';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { FileUpload } from 'graphql-upload';
import * as jwt from 'jsonwebtoken';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';

@Injectable()
export class UserService extends TypeOrmQueryService<UserEntity> {
  constructor(
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    private novuService: NovuService,
    private fileService: FileService,
    private tmOneService: TMOneService,
    private mailgunService: MailgunService,
  ) {
    // pass the use soft delete option to the service.
    super(userRepo, { useSoftDelete: true });
  }

  // Get UserMe
  async getUserMe(userId: number): Promise<UserEntity> {
    return await this.userRepo.findOne({ where: { id: userId } });
  }

  // Update UserMe
  async updateUserMe(userId: number, input: UpdateUserInputDTO) {
    const user = await this.getUserMe(userId);
    if (!user) throw new BadRequestException('Account not found');    

    return this.updateUser(user, input);
  }

  // Update My Password
  async updateMyPassword(userId: number, input: any) {
    const user = await this.getUserMe(userId);
    if (!user) throw new BadRequestException('Account not found');

    this.userRepo.merge(user, { password: input.password });
    return await this.userRepo.save(user);
  }

  private async updateUser(user: UserEntity, input: any) {
    // Condition if user have login eod
    if (!user.isFirstTimeLogin) {
      this.userRepo.merge(user, { ...input });
      return await this.userRepo.save(user);
    }

    this.userRepo.merge(user, {
      ...(input as any),
      isFirstTimeLogin: false,
      signUpToken: null
    });
    return await this.userRepo.save(user);
  }

  async deleteOneUser(id: number, userId: number) {
    const user = await this.userRepo.findOne({ id: userId });

    if (id === user.id) return await this.userRepo.softRemove(user);

    throw new BadRequestException('Not authorized to delete.');
  }

  async isCompanySubscriptionActive(userId: number): Promise<boolean> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['company', 'company.companySubscriptions']
    });
    const subscription = user?.company?.companySubscriptions?.find(sub => sub.subscriptionEndDate > new Date());
    return !!subscription;
  }

  async updateUserByEmail(email: string, input: UpdateUserInputDTO) {
    const users = await this.userRepo.findOne({
      where: { email }
    });

    this.userRepo.merge(users, {
      ...(input as any)
    });

    return await this.userRepo.save(users);
  }

  async updateIsReadChangeLog(body: boolean) {
    try {
      const users = await this.userRepo.find();

      users.forEach(user => {
        this.userRepo.merge(user, {
          isReadChangeLog: body,
          isReadChangeLogMobile: body
        });
      });

      return await this.userRepo.save(users);
    } catch (e) {
      getErrorMessage(e, 'UserService', 'updateIsReadChangeLog');
    }
  }

  // create a new cron job that will run every monday and friday at 9:00 AM and will send summary
  @Cron('0 9 * * 1,4', {
    timeZone: 'Asia/Kuala_Lumpur'
  })
  async notifyUserWeeklyTasks() {
    try {
      const currentDate = moment().format('YYYY-MM-DD');
      const processName = process.env.name || 'bina-be-primary';
      if (processName.search(/bina-be-primary/) !== -1) {
        const users = await this.userRepo
          .createQueryBuilder('user')
          .leftJoinAndMapMany(
            'user.requestForSignatures',
            'request_for_signatures',
            'requestForSignatures',
            'requestForSignatures.signById = user.id'
          )
          .leftJoinAndSelect('user.projectUsers', 'projectUsers')
          .leftJoinAndSelect('projectUsers.tasks', 'tasksA')
          .leftJoinAndSelect('requestForSignatures.projectDocument', 'projectDocument')
          .where('requestForSignatures.status <> :status', { status: ProjectDocumentStatus.Approved })
          .andWhere('user.receiveWeeklyEmails = :receiveWeeklyEmails', { receiveWeeklyEmails: true })
          .andWhere('projectUsers.role IN (:roles)', {
            roles: [ProjectUserRoleType.ProjectOwner, ProjectUserRoleType.CloudCoordinator, ProjectUserRoleType.CanEdit]
          })
          .andWhere('tasksA.dueDate IS NOT NULL')
          .orWhere('(projectDocument.category = :category) AND (projectDocument.status IN (:documentStatus))', {
            category: 'AllForm',
            documentStatus: [
              ProjectDocumentStatus.Submitted,
              ProjectDocumentStatus.InReview,
              ProjectDocumentStatus.Rejected
            ]
          })
          .getMany();

        users.forEach(async user => {
          const tasksA = (await Promise.all(user.projectUsers.map(pu => pu.tasks))).flat();
          const tasksC = (await Promise.all(user.projectUsers.map(pu => pu.tasksC))).flat();
          const sanitizedTasks = _.uniqBy([...tasksA, ...tasksC], 'id');

          const upcomingDueDateTasks = sanitizedTasks.filter(task =>
            moment().isSameOrBefore(moment(task.dueDate), 'day')
          );

          const overdueTasks = sanitizedTasks.filter(task => moment().isAfter(moment(task.dueDate), 'day'));

          const submittedDocuments = user.requestForSignatures
            ? user?.requestForSignatures?.filter(
                requestForSignature => requestForSignature?.projectDocument?.status === ProjectDocumentStatus.Submitted
              )
            : [];

          const inReviewDocuments = user.requestForSignatures
            ? user?.requestForSignatures?.filter(
                requestForSignature => requestForSignature?.projectDocument?.status === ProjectDocumentStatus.InReview
              )
            : [];

          // get the rejected documents that updated at before two weeks from the current date
          const rejectedDocuments = user.requestForSignatures
            ? user?.requestForSignatures?.filter(
                requestForSignature =>
                  requestForSignature?.projectDocument?.status === ProjectDocumentStatus.Rejected &&
                  moment(requestForSignature?.projectDocument?.updatedAt).isBetween(
                    moment().subtract(2, 'weeks'),
                    moment(),
                    'day'
                  )
              )
            : [];

          // don't send email if there are no tasks or documents
          if (
            upcomingDueDateTasks.length === 0 &&
            overdueTasks.length === 0 &&
            submittedDocuments.length === 0 &&
            inReviewDocuments.length === 0 &&
            rejectedDocuments.length === 0
          )
            return;

          const payload: INovuPayload = {
            user: {
              avatar: user.avatar,
              name: user.name,
              email: user.email
            },
            title: 'System',
            body: `Hi ${user.name} here's list of your upcoming Task due this week`,
            subscriber: {
              firstName: user.name
            },
            list: {
              upcomingDueDateTask: upcomingDueDateTasks.map(task => ({
                title: `${task.title} - ${moment(task.dueDate).format('D MMM YYYY')}`,
                link: `${process.env.APP_URI}/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${user.companyId}`
              })),
              overdueTask: overdueTasks.map(task => ({
                title: `${task.title} - ${moment(task.dueDate).format('D MMM YYYY')}`,
                link: `${process.env.APP_URI}/tasks?taskId=${task.id}&projectId=${task.projectId}&companyId=${user.companyId}`
              })),
              submittedWorkspace: submittedDocuments.map(document => ({
                title: document.projectDocument.name,
                link: `${process.env.APP_URI}/digital-form/all-form?documentDetailsId=${document.projectDocumentId}&projectId=${document?.projectDocument?.projectId}&companyId=${user?.companyId}`
              })),
              inReviewWorkspace: inReviewDocuments.map(document => ({
                title: document.projectDocument.name,
                link: `${process.env.APP_URI}/digital-form/all-form?documentDetailsId=${document.projectDocumentId}&projectId=${document?.projectDocument?.projectId}&companyId=${user?.companyId}`
              })),
              rejectedWorkspace: rejectedDocuments.map(document => ({
                title: document.projectDocument.name,
                link: `${process.env.APP_URI}/digital-form/all-form?documentDetailsId=${document.projectDocumentId}&projectId=${document?.projectDocument?.projectId}&companyId=${user?.companyId}`
              }))
            },
            date: moment().format('D MMM YYYY'),
            unsubscribeWeeklyLink: `${process.env.APP_URI}/unsubscribe-weekly-email?email=${user.email}`
          };
          this.novuService.trigger('task-summary', {
            to: {
              subscriberId: user.id.toString(),
              email: user.email
            },
            payload
          });
        });
      }
    } catch (e) {
      throw new Error(`Something went wrong in user subscriber. afterInsert function. Error: ${e.message}`);
    }
  }

  async updateUserSignature(input: UpdateUserSignature) {  
    //check expiry of token
    const {id, isExp, isInvalid, isTokenChanged} = await this.checkTokenExpiry(input?.changeSignatureToken);
    
    if(isTokenChanged) throw new BadRequestException('Token is invalid: The token is changed from outside of our system changed.');
    else if(isInvalid) throw new BadRequestException('Signature already exists. To make changes, please request a new signature.');
    else if(!isExp) throw new BadRequestException('Oops! Your QR code has expired. Please try again.');          
        
    const regex = /^data:(.+);base64,(.+)/;
    const matches = input?.signature.match(regex);

    const mimetype = matches[1];
    const base64Data = matches[2];

    // Decode base64-encoded data to a buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Extract filename from the Data URL
    const extension = mime.getExtension(mimetype);
    const filename = `image.${extension}`;

    // Create a function that returns a ReadStream from the image buffer
    const createReadStream = () => {
      const readableStream = new Readable();
      readableStream.push(imageBuffer);
      readableStream.push(null);
      return readableStream;
    };

    const file = {
      filename,
      mimetype,
      encoding: '7bit',
      createReadStream
    };

    //get a user
    const user = await this.userRepo.findOne({ where: { id } });    

    if (input?.signature) {
      const folder = 'User-Signature';
      const { key } = await this.fileService.uploadGqlFile(file as any, folder);
      
      user.signKey = key;
    }

    //clear token after submission
    user.changeSignatureToken = null;

    return user.save();
  }

  async updateUserStamp(input: UpdateUserStamp) {
    const user = await this.userRepo.findOne({ id: input.id });
    if (!user) throw new BadRequestException('User does not exist');

    const folder = 'User-Stamp';
    const file = {
      filename: `test.pdf`,
      mimetype: 'application/pdf',
      encoding: '7bit'
    };

    const { filename, fileSize, type, channel } = await this.fileService.uploadGqlFile(
      file as FileUpload,
      folder,
      null,
      input.stamp
    );

    // return await getRepository(UserEntity).save({ id: input.id, stampUrl: url });
  }

  async getDirectUrl(key: string) {
    return this.tmOneService.getDirectUrl(key);
  }

  async getPresignedUrl(user: UserEntity, type: 'avatarKey' | 'stampKey' | 'signKey' | 'stampAndSignKey') {
    try {
      const keyAndFallback = { avatarKey: 'avatar', stampKey: 'stampUrl', signKey: 'signUrl', stampAndSignKey: 'stampAndSignUrl' };
      let key = user[type];
      // fallback if key is missing
      if (!key){
        const fileName = user[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {      
      return null;
    }
  }

  async generateOtp(user:GenerateOtpInput) {

    try{
   
    // update user with otp token and expired time
    const getUser = await this.userRepo.findOne({ where: { email: user.email } });
    if(!getUser.enableTwoFA) return 'Two Factor Authentication is not enabled';
    if (!getUser) throw new BadRequestException('User not found');
    
    // generate otp with 6 digits
    const otp = Math.floor(100000 + Math.random() * 900000);  
    getUser.otpToken = otp.toString();
    getUser.otpExpiredAt = moment().add(10, 'minutes').toDate();
    await this.userRepo.save(getUser);
    
    // send email with otp
    await this.mailgunService.sendMail({
      to: user?.email,
      subject: 'Login OTP',              
      template: 'send_otp',
      message: otp.toString(),     
    });

    return 'OTP sent successfully';
  } catch (error) {
    getErrorMessage(error, 'UserService', 'generateOtp');
  }}

  async verifyOtp(input:VerifyOtpInput) {    
    
    const user = await this.userRepo.findOne({ where: { email:input?.email } });
    if (!user) throw new BadRequestException('User not found');
    if (user.otpToken !== input?.otp) throw new BadRequestException('Invalid OTP');
    if (moment().isAfter(user.otpExpiredAt)) throw new BadRequestException('OTP expired');
    
    return true;
  }

  async checkTokenExpiry(token: string): Promise<any> {
    try {
      // Decode the token without verification
      const decoded: any = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        throw new Error('Token does not contain expiration.');
      }

      // Get user timezone
      const user = await this.userRepo.findOne({ where: { id: decoded.id } });      
      const userTimezone = typeof user?.timezone === 'string' ? user.timezone : 'UTC';
      const isTokenChanged = user?.changeSignatureToken !== token;

      // Convert token expiration time to user's timezone
      const tokenExpiryTime = moment.unix(decoded.exp);      
      const currentTime = moment().tz(userTimezone);      

      return{ id:user?.id, isExp: tokenExpiryTime.isAfter(currentTime), isInvalid:user?.changeSignatureToken === null, isTokenChanged }; 
    } catch (error) {      
      return false; 
    }
  }
}
