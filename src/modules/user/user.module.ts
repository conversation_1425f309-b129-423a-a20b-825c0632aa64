import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { UserEntity } from './entity/user.entity';
import { UserDto, CreateUserInputDTO, UpdateUserInputDTO } from './dto/user.gql.dto';
import { UseUploadFilePipe } from 'src/pipes/gql-upload.pipe';
import { UserResolver } from './user.resolver';
import { UserService } from './user.service';
import { UserSubscriber } from './user.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { UserController } from './user.controller';
import { UserSettingsService } from '@modules/user-settings/user-settings.service';
import { UserSettingsEntity } from '@modules/user-settings/entity/user-settings.entity';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        NestjsQueryTypeOrmModule.forFeature([
          UserEntity,
          ScheduleEntity,
          UserSettingsEntity
        ]),
        IntegrationModule,
      ],
      resolvers: [
        {
          ServiceClass: UserService,
          DTOClass: UserDto,
          EntityClass: UserEntity,
          CreateDTOClass: CreateUserInputDTO,
          UpdateDTOClass: UpdateUserInputDTO,
          decorators: [UseUploadFilePipe()],
          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],
      services: [
        UserSubscriber,
        UserService,
        UserResolver,
        UserSettingsService
      ]
    }),
    
  ],
  controllers: [UserController]
})
export class UserModule {}
