import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { IsReadChangeLogDTO } from './dto/user.gql.dto';

@ApiTags('User API')
@Controller('users')
export class UserController {
  constructor(private userService: UserService) {}

  @Post('change-isReadChangeLog')
  async filterTasks(
    @Body() body: IsReadChangeLogDTO
  ) {
    return await this.userService.updateIsReadChangeLog(body.isReadChangeLog);
  }
}
