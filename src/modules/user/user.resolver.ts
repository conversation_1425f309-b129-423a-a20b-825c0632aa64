import { RoleTypeEnum } from '@constants';
import { GqlGetGqlAuthData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { ForbiddenException, UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { AuthData } from '@types';
import {
  UserDto,
  UpdateUserInputDTO,
  UpdateUserPasswordDTO,
  DeleteOneUserInput,
  UpdateUserSignature,
  GetUserPreferencesResponseType,
  UpdateUserStamp,
  GenerateOtpInput,
  VerifyOtpInput
} from './dto/user.gql.dto';
import { UserService } from './user.service';
import { NovuService } from '@modules/integration/novu/novu.service';


@UseGuards(GqlAuthGuard)
@Resolver(() => UserDto)
export class UserResolver {
  constructor(
    private userService: UserService,
    private novuService: NovuService,    
  ) {}

  // Get UserMe
  @Query(() => UserDto)
  async getUserMe(@GqlGetGqlAuthData() user: AuthData): Promise<UserDto> {
    return await this.userService.getUserMe(user.id);
  }

  // Update UserMe
  @Mutation(() => UserDto)
  async updateUserMe(@GqlGetGqlAuthData() user: AuthData, @Args('input') input: UpdateUserInputDTO): Promise<any> {
    if (user.type !== RoleTypeEnum.User) throw new ForbiddenException(`This action only for Role ${RoleTypeEnum.User}`);

    return await this.userService.updateUserMe(user.id, input);
  }

  @Mutation(() => UserDto)
  async updateMyPassword(
    @GqlGetGqlAuthData() user: AuthData,
    @Args('input') input: UpdateUserPasswordDTO
  ): Promise<any> {
    if (user.type !== RoleTypeEnum.User) throw new ForbiddenException(`This action only for Role ${RoleTypeEnum.User}`);

    return await this.userService.updateMyPassword(user.id, input);
  }

  @Query(() => Boolean)
  async isCompanySubscriptionActive(@GqlGetGqlAuthData() user: AuthData): Promise<boolean> {
    return await this.userService.isCompanySubscriptionActive(user.id);
  }

  @Query(() => String)
  async userHmacHash(@GqlGetGqlAuthData() user: AuthData) {
    return this.novuService.hmacHash(user.id.toString());
  }

  @Query(() => GetUserPreferencesResponseType)
  async getUserPreferences(@GqlGetGqlAuthData() user: AuthData) {
    return await this.novuService.getSubscriberPreference(user.id.toLocaleString());
  }

  @Mutation(() => GetUserPreferencesResponseType)
  async updateUserPreferences(@GqlGetGqlAuthData() user: AuthData, @Args('input') preference: boolean) {
    return await this.novuService.updateNotificationPreference(user.id.toLocaleString(), preference);
  }

  @Mutation(() => UserDto)
  async deleteOneUser(@GqlGetGqlAuthData() user: AuthData, @Args('input') input: DeleteOneUserInput): Promise<any> {
    return await this.userService.deleteOneUser(input.id, user.id);
  }

  //? update user by email
  @Mutation(() => UserDto)
  async updateUserByEmail(@Args('email') email: string, @Args('input') input: UpdateUserInputDTO): Promise<any> {
    return await this.userService.updateUserByEmail(email, input);
  }

  @Mutation(() => UserDto)
  async updateUserSignature(@Args('input') input: UpdateUserSignature): Promise<any> {
    return await this.userService.updateUserSignature(input);
  }

  @ResolveField('avatar', () => String)
  async avatar(@Parent() parent: UserDto) {
    if (!parent.avatarKey) {
      return null;
    }

    return await this.userService.getDirectUrl(parent.avatarKey);
  }

  @ResolveField('stampUrl', () => String)
  async stampUrl(@Parent() parent: UserDto) {
    return await this.userService.getPresignedUrl(parent, 'stampKey');
  }

  @ResolveField('signUrl', () => String)
  async signUrl(@Parent() parent: UserDto) {
    return await this.userService.getPresignedUrl(parent, 'signKey');
  }

  @ResolveField('stampAndSignUrl', () => String)
  async stampAndSignUrl(@Parent() parent: UserDto) {
    return await this.userService.getPresignedUrl(parent, 'stampAndSignKey');
  }

  @Mutation(() => UserDto)
  async updateUserStamp(@Args('input') input: UpdateUserStamp): Promise<any> {
    return await this.userService.updateUserStamp(input);
  }

  @Mutation(() => String)
  async generateOtp(@Args('input') input: GenerateOtpInput): Promise<any> {
    return await this.userService.generateOtp(input);
  }

  @Mutation(() => Boolean)
  async verifyOtp(@Args('input') input: VerifyOtpInput): Promise<any> {
    return await this.userService.verifyOtp(input);
  }
}
