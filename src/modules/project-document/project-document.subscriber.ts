import { BadRequestException, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  getRepository,
  InsertEvent,
  UpdateEvent
} from 'typeorm';
import { ProjectDocumentEntity } from './entity/project-document.entity';
import * as _ from 'lodash';
import { FileUpload } from 'graphql-upload';
import {
  AuditLogActionType,
  AuditLogModuleType,
  CategoryType,
  FileChannelTypes,
  FileSystemType,
  ProjectDocumentDriveType,
  ProjectDocumentStatus,
  RequestForSignatureStatus,
  SourceType,
  WorkspaceCCStatus,
  workflowType
} from '@constants';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { FileService } from '@modules/integration/file.service';
import { replaceSpaceWithDash } from '@constants/function';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { customAlphabet } from 'nanoid/async';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';
import moment from 'moment';
import { getErrorMessage } from '@common/error';
import { DrawingRevisionEntity } from '@modules/drawing-revision/entity/drawing-revision.entity';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
@EventSubscriber()
export class ProjectDocumentSubscriber implements EntitySubscriberInterface<ProjectDocumentEntity> {
  constructor(
    connection: Connection,
    private fileService: FileService,
    private webSocketService: WebSocketService,
    private novuService: NovuService,
    private tMOneService: TMOneService
  ) {
    connection.subscribers.push(this);
  }

  // TODO: RECHECK IF THIS IS REQUIRED
  listenTo() {
    return ProjectDocumentEntity;
  }

  async beforeInsert(event: InsertEvent<ProjectDocumentEntity>) {
    
    try {
      const { entity } = event;      
      entity.versionName = moment().format('DD-MM-YYYY');

      if(entity.isBimPhoto){
        const folder = "Project-Document";
        await this.fileService.uploadGqlFile(
          entity.fileUrl as any,
          folder,
          null,          
        );        
        
      }

      if (!_.isNull(entity.projectDocumentId)) {
        const parent = await event.manager
          .getRepository(ProjectDocumentEntity)
          .findOne({ id: entity.projectDocumentId });
        entity.parentFolder = parent;
      }

      const fileKey: any = entity.fileKey; //to be verified, might contain the file object itself
      const fileObject = entity.fileUrl; // on insert, we actually send the file object to be uploaded      

      const thumbnail: any = entity.videoThumbnail;
      if (entity?.fileSystemType === FileSystemType.Document && !(fileKey || fileObject) && !entity.obsUrl) // obsUrl is for photo is actually the fileKey, might need to change the name
        throw new BadRequestException('No Document has been uploaded');

      if (thumbnail && _.isObject(thumbnail)) {
        const folder = replaceSpaceWithDash(_.startCase(entity.category));
        const { key } = await this.fileService.uploadGqlFile(thumbnail as FileUpload, folder);
        entity.videoThumbnail = key;
        entity.videoThumbnailKey = key;
      }

      if (fileObject && _.isObject(fileObject)) {
        const folder = replaceSpaceWithDash(_.startCase(entity.category));
        const user = await getRepository(UserEntity).findOne({ id: entity.addedBy });

        const logParams = {
          before_file_name: entity.name,
          category: entity.category,
          createdBy: entity.addedBy,
          user_id: entity.addedBy,
          user_name: user?.name
        };

        try {
          const { filename, key, fileSize, type, channel } = await this.fileService.uploadGqlFile(
            fileObject as FileUpload,
            folder,
            logParams
          );
          entity.name = filename;
          entity.fileKey = key;
          entity.fileUrl = key;
          if (type === 'PDF') {
            entity.type = 'pdf';
          } else {
            entity.type = type;
          }
          entity.fileSize = +fileSize;
          entity.fileChannel = channel;
          entity.status = entity.category === CategoryType.AllForm ? ProjectDocumentStatus.Pending : null;
        } catch (err) {
          getErrorMessage(err, 'ProjectDocumentSubscriber', 'beforeInsert');
        }
      } else {
        if (entity.category == CategoryType.Photo && entity.obsUrl && entity.obsFileSize) {
          entity.fileKey = entity.obsUrl;
          entity.fileUrl = entity.obsUrl
          entity.type = _.last(_.split(entity.name, '.')).toUpperCase();
          entity.fileSize = +(entity.obsFileSize / (1024 * 1024)).toFixed(2);
        }
        if (entity.category == CategoryType.BIMDrawings && entity.obsUrl && entity.obsFileSize) {
          // workaround for sending autodesk urn due to graphql type limitation
          // urn in base 64 is sent as a string in xfdf field
          entity.fileUrl = entity.xfdf;
          entity.xfdf = null;
          entity.obsKey = entity.obsUrl;
          entity.obsUrl = process.env.TMONE_OBS_STORAGE + entity.obsUrl;
          entity.type = _.last(_.split(entity.name, '.'));
          entity.fileSize = +(entity.obsFileSize / (1024 * 1024)).toFixed(2);
          entity.fileChannel = FileChannelTypes.FORGE;
        }
        if (entity.category === CategoryType.TwoDDrawings && entity.obsUrl && entity.obsFileSize) {
          entity.fileKey = entity.obsUrl;
          entity.fileUrl = entity.obsUrl
          entity.type = _.last(_.split(entity.name, '.'))
          entity.fileSize = +(entity.obsFileSize / (1024 * 1024)).toFixed(2);
        }
      }

      if (entity.category === CategoryType.AllForm) {
        const workspaceGroup = await event.manager
          .getRepository(WorkspaceGroupEntity)
          .findOne({ projectId: entity.projectId, name: 'Ungroup Documents' });
        
        if (entity.recordSource !== SourceType.OfflineApp) {
          entity.status = ProjectDocumentStatus.Draft;
          entity.workspaceGroupId = workspaceGroup?.id ?? null;
        }
      }

      if (entity.name) {
        const name = this.validateFileName(entity.name, entity.fileSystemType, entity.type);
        entity.name = name;
      }

      // Defaulted to Personal. Clouds Docs WP and Corr may carry Shared or Personal
      if (!entity.driveType) entity.driveType = ProjectDocumentDriveType.Personal;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentSubscriber', 'beforeInsert');
    }
  }

  async afterInsert(event: InsertEvent<ProjectDocumentEntity>) {
    try {
      const { entity } = event;      
      
      // create drawing revision
      if (entity.category === CategoryType.TwoDDrawings && entity.fileSystemType === FileSystemType.Document) {
        const drawingRevisionEntity = new DrawingRevisionEntity();
        drawingRevisionEntity.projectDocumentId = entity.id;
        drawingRevisionEntity.fileKey = entity.fileKey;
        drawingRevisionEntity.fileUrl = entity.fileKey;
        drawingRevisionEntity.fileName = entity.name;
        drawingRevisionEntity.version = 1;
        drawingRevisionEntity.versionName = moment().format('DD-MM-YYYY');

        await event.manager
          .createQueryBuilder()
          .insert()
          .into(DrawingRevisionEntity)
          .values(drawingRevisionEntity)
          .execute();
      }

      if (entity.category === CategoryType.AllForm && !entity.allFormCode && !entity.autosavedAt) {
        await event.manager.transaction(async transManager => {
          const docs = await transManager
            .getRepository(ProjectDocumentEntity)
            .createQueryBuilder('entity')
            .where('entity.projectId = :id', { id: entity.projectId })
            .andWhere('entity.category = :category', { category: CategoryType.AllForm })
            .andWhere('entity.allFormCode IS NOT NULL')
            .withDeleted()
            .getCount();

          entity.allFormCode = entity.category === CategoryType.AllForm ? docs + 1 : null;
          await transManager.save(entity);
        });
      }

      // if file/document uploaded, send notification
      if (entity.fileKey && entity.fileSystemType === FileSystemType.Document) {
        const projectUsers = await event.manager.getRepository(ProjectUserEntity).find({ projectId: entity.projectId });
        const project = await event.manager.getRepository(ProjectEntity).findOne({ id: entity.projectId });
        const categoryLink = () => {
          switch (entity.category) {
            case CategoryType.ProjectDocument:
              return { deeplink: '/cloud-docs/project-document?', mobileDeeplink: 'projectDocument/:' };
            case CategoryType.WorkProgramme:
              return { deeplink: '/cloud-docs/work-programme?', mobileDeeplink: 'workProgramme/:' };
            case CategoryType.Correspondence:
              return { deeplink: '/cloud-docs/correspondence?', mobileDeeplink: 'correspondence/:' };
            case CategoryType.StandardForm:
              return { deeplink: '/digital-form/standard-form?', mobileDeeplink: 'standardForm/:' };
            case CategoryType.AllForm:
              return { deeplink: '/digital-form/all-form?', mobileDeeplink: 'allForm/:' };
            case CategoryType.Photo:
              return { deeplink: '/photos?', mobileDeeplink: 'photos/:' };
            case CategoryType.TwoDDrawings:
              return { deeplink: '/drawings/2D-drawing?', mobileDeeplink: '2DDrawing/:' };
            case CategoryType.BIMDrawings:
              return { deeplink: '/drawings/BIM-drawing?', mobileDeeplink: 'BIMDrawing/:' };
            case CategoryType.WorkProgramme:
              return { deeplink: '/cloud-docs/work-programme?', mobileDeeplink: 'cloud-doc/1/:' };
            case CategoryType.Correspondence:
              return { deeplink: '/cloud-docs/correspondence?', mobileDeeplink: 'cloud-doc/2/:' };
            case CategoryType.StandardForm:
              return { deeplink: '/digital-form/standard-form?', mobileDeeplink: 'digital-form/1/:' };
            case CategoryType.AllForm:
              return { deeplink: '/digital-form/all-form?', mobileDeeplink: 'digital-form/0/:' };
            case CategoryType.Photo:
              return { deeplink: '/photos?', mobileDeeplink: 'photo/1/:' };
            case CategoryType.TwoDDrawings:
              return { deeplink: '/drawings/2D-drawing?', mobileDeeplink: 'drawing/0/:' };
            case CategoryType.BIMDrawings:
              return { deeplink: '/drawings/BIM-drawing?', mobileDeeplink: 'drawing/1/:' };
            case CategoryType.SCurveGraph:
              return { deeplink: '/graphs/scurve?', mobileDeeplink: 'graph/0/:' };
            case CategoryType.GanttChart:
              return { deeplink: '/schedule/gantt-chart?', mobileDeeplink: 'schedule/0/:' };
          }
        };
        const dirId = entity.projectDocumentId ? `dirId=${entity.projectDocumentId}&` : '';
        const deeplink = categoryLink().deeplink + dirId + `documentId=${entity.id}`;
        const mobileDeeplink = categoryLink().mobileDeeplink + entity.id;

        await Promise.all(
          projectUsers.map(async projectUser => {
            if (projectUser.userId !== entity.addedBy) {
              const notification = await event.manager.getRepository(NotificationTransactionEntity).create({
                userId: projectUser.userId,
                actorId: entity.addedBy,
                title: project.title,
                actionName: entity.name,
                actionType: 'uploaded a document',
                deeplink,
                mobileDeeplink
              });
              await event.manager.getRepository(NotificationTransactionEntity).save(notification);
            }
          })
        );
      }

      // AUDIT LOG PHOTO
      if (entity.category === CategoryType.Photo) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });

        const msg =
          entity.fileSystemType === FileSystemType.Document
            ? user.name + ' added ' + entity.name
            : user.name + ' created ' + entity.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Photo,
          action: AuditLogActionType.Create,
          content: msg
        });
        await event.manager.getRepository(AuditLogEntity).save(auditLog);
      }

      // AUDIT LOG DRAWING
      else if (entity.category === CategoryType.BIMDrawings || entity.category === CategoryType.TwoDDrawings) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });

        const msg =
          entity.fileSystemType === FileSystemType.Document
            ? user.name + ' added ' + entity.name
            : user.name + ' created ' + entity.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Drawing,
          action: AuditLogActionType.Create,
          content: msg
        });
        await auditLog.save();
        // await event.manager.getRepository(AuditLogEntity).save(auditLog);
      }

      // AUDIT LOG CLOUD DOCS (ADD DOCUMENT / FOLDER)
      else if (
        ((entity.category === CategoryType.ProjectDocument || entity.category === CategoryType.Correspondence) &&
          entity.driveType === ProjectDocumentDriveType.Shared) ||
        entity.category === CategoryType.WorkProgramme
      ) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });

        const msg =
          entity.fileSystemType === FileSystemType.Document
            ? user.name +
              ' added document ' +
              entity.name +
              ' in ' +
              entity.category.replace(/([a-z])([A-Z])/g, '$1 $2')
            : user.name +
              ' created folder ' +
              entity.name +
              ' in ' +
              entity.category.replace(/([a-z])([A-Z])/g, '$1 $2');

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.CloudDocument,
          action: AuditLogActionType.Add,
          content: msg
        });
        await auditLog.save();
      }
    } catch (e) {
      throw new Error(`Something went wrong at project document subscriber! afterInsert function. Error: ${e.message}`);
    }
  }

  async beforeUpdate(event: UpdateEvent<ProjectDocumentEntity>) {
    try {
      const { entity, databaseEntity } = event;

      if (!databaseEntity) return;
      // utk document yg autosaved
      if (entity.category === CategoryType.AllForm && !entity.allFormCode && !entity.autosavedAt) {
        await event.manager.transaction(async transManager => {
          const docs = await transManager
            .getRepository(ProjectDocumentEntity)
            .createQueryBuilder('entity')
            .where('entity.projectId = :id', { id: entity.projectId })
            .andWhere('entity.category = :category', { category: CategoryType.AllForm })
            .andWhere('entity.allFormCode IS NOT NULL')
            .withDeleted()
            .getCount();

          entity.allFormCode = entity.category === CategoryType.AllForm ? docs + 1 : null;
        });
      }
      // utk document yg autosaved
      if (entity.category === CategoryType.AllForm && !entity.workspaceGroupId && !entity.autosavedAt) {
        const workspaceGroup = await event.manager
          .getRepository(WorkspaceGroupEntity)
          .findOne({ projectId: entity.projectId, name: 'Ungroup Documents' });
        
        entity.workspaceGroupId = workspaceGroup?.id ?? null;
      }

      if (entity?.name) {
        const name = this.validateFileName(entity.name, entity.fileSystemType, entity.type);
        entity.name = name;
      }

      if (entity.projectDocumentId) {
        const parent = await event.manager
          .getRepository(ProjectDocumentEntity)
          .createQueryBuilder()
          .select('*')
          .where({ id: entity.projectDocumentId })
          .getRawOne();

        // there might be a case where its parent is deleted, but the parent id is still there
        // if (parent) {
        //   await event.manager.query(
        //     `UPDATE project_documents SET mpath = '${
        //       parent.mpath ? parent.mpath + entity.id : entity.id
        //     }.' WHERE id = '${entity.id}'`
        //   );
        // }
      }

      const fileKey: any = entity.fileKey;
      if (entity.fileSystemType === FileSystemType.Document && !fileKey && entity.category !==  CategoryType.BIMDrawings)
        throw new BadRequestException('No Document has been uploaded');

      if (
        (entity?.status === ProjectDocumentStatus.Submitted &&
          entity?.groupCode === null &&
          entity?.category === CategoryType.AllForm) ||
        (parseInt(entity?.workspaceGroupId) !== databaseEntity?.workspaceGroupId &&
          entity?.status !== ProjectDocumentStatus.Draft &&
          entity?.category === CategoryType.AllForm)
      ) {
        const groupName = await event.manager
          .getRepository(WorkspaceGroupEntity)
          .findOne({ where: { id: entity.workspaceGroupId } });
        if (groupName?.name === 'Site Diary' || groupName?.name === 'Ungroup Documents') {
          const getTotalParent = await this.getParentCountAmount(entity, event);
          entity.groupCode = getTotalParent + 1;
        } else {
          const getTotalChildren = await this.getChildrenCountAmount(entity, event);
          entity.groupCode = getTotalChildren;
        }
      }

      if (
        entity?.workflow === workflowType.Dynamic &&
        entity?.status === ProjectDocumentStatus.Submitted &&
        event?.databaseEntity?.status === ProjectDocumentStatus.Draft &&
        // only create new request for signature if the document is submitted from the web
        entity?.recordSource === SourceType.Web
      ) {
        const requestForSignatures = await event.manager.getRepository(RequestForSignatureEntity).find({
          projectDocumentId: entity.id
        });

        // check if the request for signature is already more than 1, do not create new request for signature.
        if (requestForSignatures.length > 1) return;

        const newRequestForSignature = {
          projectDocumentId: entity.id,
          signById: entity.addedBy,
          ownerId: entity.addedBy
        };

        requestForSignatures.unshift(newRequestForSignature as any);

        await Promise.all(
          requestForSignatures.map((requestForSignature, index) => {
            return event.manager.getRepository(RequestForSignatureEntity).merge(requestForSignature, {
              status: RequestForSignatureStatus.Sent,
              assigneeNo: index + 1
            });
          })
        );

        const nextSignee = requestForSignatures?.[1];
        entity.currentUserId = nextSignee?.id ?? null;
        entity.updatedAt = new Date();

        await event.manager.getRepository(RequestForSignatureEntity).save(requestForSignatures);
      }

      const fileObject = entity.fileUrl; // on update, we actually send the file object to be uploaded
      if (entity.status !== ProjectDocumentStatus.Approved && !entity.watermarkId) {
        if (fileObject && _.isObject(fileObject)) {
          const folder = replaceSpaceWithDash(_.startCase(entity.category));
          const { filename, key, fileSize, type, channel } = await this.fileService.uploadGqlFile(
            fileObject as FileUpload,
            folder
          );
          entity.fileKey = key;
          entity.name = filename;
          if (type === 'PDF') {
            entity.type = 'pdf';
          } else {
            entity.type = type;
          }
          entity.fileSize = fileSize;
          entity.fileChannel = channel;
        }
      } else if (
        (entity.status === ProjectDocumentStatus.Approved || entity.status === ProjectDocumentStatus.Rejected) &&
        !entity.watermarkId
      ) {
        const charSet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const generateId = customAlphabet(charSet, 16);
        const generatedId = `BC-${await generateId()}`;

        // Watermark happens here
        const url = await this.tMOneService.getPresignedUrl(entity.fileKey)
        const response = await fetch(url.SignedUrl);
        const pdfData = await response.arrayBuffer();
        const existingPdfBytes = new Uint8Array(pdfData);
        const pdfDoc = await PDFDocument.load(existingPdfBytes);

        const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);

        const pages = pdfDoc.getPages();

        for (const page of pages) {
          const { width } = page.getSize();

          const bottomMargin = 10; // Adjust the margin as needed
          const leftText = `ID : ${generatedId} - ${moment().tz('Asia/Kuala_Lumpur').format('DD-MM-YYYY h:mm A')}`;
          const rightText = 'Processed with Bina Cloud';
          const rightTextWidth = helveticaFont.widthOfTextAtSize(rightText, 5);

          // Add a white background rectangle for the left text
          page.drawRectangle({
            x: 30,
            y: bottomMargin - 5, // Adjust the vertical position as needed
            width: 200, // Adjust the width as needed
            height: 10, // Adjust the height as needed
            color: rgb(1, 1, 1) // White color
          });

          // Add the left text on top of the white background
          page.drawText(leftText, {
            x: 30,
            y: bottomMargin,
            size: 5,
            font: helveticaFont,
            color: rgb(0.2118, 0.2118, 0.2118)
          });

          // Add a white background rectangle for the right text
          page.drawRectangle({
            x: width - rightTextWidth - 40, // Adjust the position as needed
            y: bottomMargin - 5, // Adjust the vertical position as needed
            width: rightTextWidth + 20, // Adjust the width as needed
            height: 10, // Adjust the height as needed
            color: rgb(1, 1, 1) // White color
          });

          // Add the right text on top of the white background
          page.drawText(rightText, {
            x: width - rightTextWidth - 30,
            y: bottomMargin,
            size: 5,
            font: helveticaFont,
            color: rgb(0.2118, 0.2118, 0.2118)
          });
        }

        const pdfBytes = await pdfDoc.save();

        const file = {
          filename: entity.name,
          mimetype: 'application/pdf',
          encoding: '7bit'
        };

        const folder = replaceSpaceWithDash(_.startCase(entity.category));
        const { filename, key, type, channel } = await this.fileService.uploadGqlFile(
          file as FileUpload,
          folder,
          null,
          pdfBytes
        );
        entity.fileKey = key;
        entity.name = filename;
        if (type === 'PDF') {
          entity.type = 'pdf';
        } else {
          entity.type = type;
        }
        entity.fileChannel = channel;
        entity.watermarkId = generatedId;
      }

      // AUDIT LOG WORKSPACE DESCRIPTION
      if (entity.description) {
        const doc = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.id });
        if (doc && doc.category === CategoryType.AllForm) {
          if (entity.description !== doc.description) {
            const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });

            const msg = user.name + ' editted ' + entity.name + ' description';

            const auditLog = event.manager.getRepository(AuditLogEntity).create({
              userId: entity.addedBy,
              projectId: entity.projectId,
              // taskId: entity.id,
              resourceId: entity.id,
              module: AuditLogModuleType.Workspace,
              action: AuditLogActionType.Update,
              content: msg
            });
            await auditLog.save();
          }
        }
      }

      if (entity.status === ProjectDocumentStatus.Submitted && !entity.submittedAt) {
        entity.submittedAt = new Date();
      }

      // AUDIT LOG WORKSPACE GROUP
      if (entity.workspaceGroupId) {
        const doc = await event.manager.getRepository(ProjectDocumentEntity).findOne({ id: entity.id });
        if (doc && doc.category === CategoryType.AllForm) {
          if (+entity.workspaceGroupId !== +doc.workspaceGroupId) {
            const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });
            const group = await event.manager
              .getRepository(WorkspaceGroupEntity)
              .findOne({ id: entity.workspaceGroupId });

            const msg = user.name + ' changed ' + entity.name + ' group to ' + group.name;

            const auditLog = event.manager.getRepository(AuditLogEntity).create({
              userId: entity.addedBy,
              projectId: entity.projectId,
              // taskId: entity.id,
              resourceId: entity.id,
              module: AuditLogModuleType.Workspace,
              action: AuditLogActionType.UpdateGroup,
              content: msg
            });
            await auditLog.save();
          }
        }
      }
    } catch (e) {
      throw new Error(
        `Something went wrong at project document subscriber! beforeUpdate function. Error: ${e.message}`
      );
    }
  }

  async afterUpdate(event: UpdateEvent<ProjectDocumentEntity>) {
    const { entity, databaseEntity } = event;

    try {
      if (entity.category === CategoryType.TwoDDrawings && entity.fileSystemType === FileSystemType.Document) {
        const drawingRevisionRepo = await event.manager.findOne(DrawingRevisionEntity, {
          where: { projectDocumentId: entity.id, fileKey: entity.fileKey }
        });

        if (drawingRevisionRepo?.fileKey === entity?.fileKey) {
          await event.manager
            .createQueryBuilder()
            .update(DrawingRevisionEntity)
            .set({
              xfdf: entity.xfdf
            })
            .where('projectDocumentId = :projectDocumentId', { projectDocumentId: entity.id })
            .execute();
        }
      }

      if (entity.xfdf && entity.category === CategoryType.AllForm) {
        const socketEvent = 'event:all-form-signature-xfdf';
        const room = `all-form-signature-xfdf-room-${entity.id}`;
        await this.webSocketService.socket.to(room).emit(socketEvent, entity);
      }

      if (entity.xfdf && entity.category === CategoryType.TwoDDrawings) {
        const socketEvent = 'event:drawings-annotation-xfdf';
        const room = `drawings-annotation--room-${entity.id}`;
        await this.webSocketService.socket.to(room).emit(socketEvent, entity);
      }

      if (entity.status === ProjectDocumentStatus.Submitted) {
        const siteDiaryGroup = await getRepository(WorkspaceGroupEntity).findOne({
          where: {
            projectId: entity?.projectId,
            name: 'Site Diary'
          }
        });

        if (entity?.workspaceGroupId === siteDiaryGroup?.id) {
          this.siteDrawingNotification(entity.projectId, entity);
        }

        const requestForSignatures = await event.manager.getRepository(RequestForSignatureEntity).find({
          projectDocumentId: entity.id
        });

        await Promise.all(
          requestForSignatures.map(requestForSignature => {
            return event.manager.getRepository(RequestForSignatureEntity).merge(requestForSignature, {
              status: RequestForSignatureStatus.Sent
            });
          })
        );

        await event.manager.getRepository(RequestForSignatureEntity).save(requestForSignatures);

        // Change Status of Workspace CC
        const workspaceCCs = await event.manager.getRepository(WorkspaceCCEntity).find({
          projectDocumentId: entity.id
        });

        await Promise.all(
          workspaceCCs.map(workspaceCC => {
            return event.manager.getRepository(WorkspaceCCEntity).merge(workspaceCC, {
              status: WorkspaceCCStatus.Sent
            });
          })
        );

        await event.manager.getRepository(WorkspaceCCEntity).save(workspaceCCs);

        // AUDIT LOG REQUEST APPROVAL
        if (
          entity.category === CategoryType.AllForm &&
          event.updatedColumns.find(column => column.propertyName === 'status')
        ) {
          const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });

          const array = [];

          requestForSignatures.forEach(element => {
            array.push(element.signById);
          });

          const signs = await event.manager.getRepository(UserEntity).findByIds(array);

          let names = '';

          signs.forEach((element, index) => {
            names = names + element.name;
            if (signs.length - 1 !== index) names = names + ', ';
          });

          const msg = user.name + ' requested approval for ' + entity.name + ' from ' + names;

          const auditLog = event.manager.getRepository(AuditLogEntity).create({
            userId: entity.addedBy,
            projectId: entity.projectId,
            // taskId: entity.id,
            resourceId: entity.id,
            module: AuditLogModuleType.Workspace,
            action: AuditLogActionType.RequestApproval,
            content: msg
          });
          await auditLog.save();
        }
      }

      // handle change wofkflow
      // if workflow is different from the previous one, and the status is draft, delete all request for signature
      if ( entity.workflow !== databaseEntity?.workflow && entity.status === ProjectDocumentStatus.Draft) {
        const requestForSignatures = await event.manager.getRepository(RequestForSignatureEntity).find({
          projectDocumentId: entity.id
        });

        await Promise.all(
          requestForSignatures.map(requestForSignature => {
            return event.manager.getRepository(RequestForSignatureEntity).remove(requestForSignature);
          })
        );
      }

      // AUDIT LOG FOR CLOUD DOCS (RENAME)
      if (
        ((entity.category === CategoryType.ProjectDocument || entity.category === CategoryType.Correspondence) &&
          entity.driveType === ProjectDocumentDriveType.Shared) ||
        (entity.category === CategoryType.WorkProgramme &&
          !!event.updatedColumns.find(column => column.propertyName === 'name'))
      ) {
        // UPDATEDBY COLUMN IN DATABASE TO BE RESOLVED
        const user = await getRepository(UserEntity).findOne({ where: { id: entity.addedBy } });
        const msg =
          user.name +
          ' renamed ' +
          (entity.fileSystemType == FileSystemType.Document ? 'a document' : 'a folder') +
          ' to ' +
          entity.name;

        const auditLog = getRepository(AuditLogEntity).create({
          userId: user.id,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.CloudDocument,
          action: AuditLogActionType.Update,
          content: msg
        });
        await auditLog.save();
      }
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentSubscriber', 'afterUpdate');
    }
  }

  private async getChildrenCountAmount(entity: any, event: any) {
    const workspaceGroup = await event.manager
      .getRepository(WorkspaceGroupEntity)
      .createQueryBuilder('workspaceGroup')
      .select(['workspaceGroup.id', 'workspaceGroup.workspaceGroupId'])
      .where('workspaceGroup.id = :id', { id: entity.workspaceGroupId })
      .leftJoin('workspaceGroup.parent', 'parent')
      .addSelect(['parent.id'])
      .leftJoin('parent.children', 'children')
      .addSelect(['children.id'])
      .leftJoin('children.documents', 'documents')
      .addSelect(['documents.id', 'documents.status', 'documents.workspaceGroupId', 'documents.groupCode'])
      .andHaving('documents.status != :status', { status: ProjectDocumentStatus.Draft })
      .getOne();

    if (!workspaceGroup) return 1;

    const docs = workspaceGroup.parent?.children.map(child => child.documents);
    const flatDocs = docs.flat();
    const groupCodes = flatDocs.map(document => parseInt(document.groupCode, 10)).filter(code => !isNaN(code));
    if (groupCodes.length === 0) {
      return 1;
    }
    const highestGroupCode = Math.max(...groupCodes);
    const nextGroupCode = highestGroupCode + 1;
    return nextGroupCode;
  }

  private async getParentCountAmount(entity: any, event: any) {
    const projectDocuments = await event.manager
      .getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .select(['projectDocument.id', 'projectDocument.groupCode'])
      .where('projectDocument.projectId = :projectId', { projectId: entity.projectId })
      .andWhere('projectDocument.status != :status', { status: ProjectDocumentStatus.Draft })
      .andWhere('projectDocument.workspaceGroupId = :workspaceGroupId', {
        workspaceGroupId: entity.workspaceGroupId
      })
      // .withDeleted()
      .getMany();

    // return projectDocuments;
    const groupCodes = projectDocuments.map(document => parseInt(document.groupCode)).filter(code => !isNaN(code));
    if (groupCodes.length === 0) {
      return 0;
    }
    const highestGroupCode = Math.max(...groupCodes);
    return highestGroupCode;
  }

  private async siteDrawingNotification(projectId: string, document: any) {
    try {
      const project = await getRepository(ProjectEntity).findOne({ where: { id: projectId } });
      const companyName = await getRepository(CompanyEntity).findOne({ id: project.companyId });
      const ownerId = document.addedBy;
      const mobileLink = 'dashboard/site-diary';
      const link = `/projects/dashboard?size=10&page=1&projectId=${projectId}&companyId=${project?.companyId}`;

      const projectUsers = await getRepository(ProjectUserEntity)
        .createQueryBuilder('projectUser')
        .leftJoinAndSelect('projectUser.user', 'user')
        .where('(projectUser.projectId = :projectId) AND (projectUser.user.id <> :ownerId)', { projectId, ownerId })
        .getMany();

      if (projectUsers?.length === 0) return;

      const uploadBy = await getRepository(UserEntity).findOne({ id: document.addedBy });
      const header = '📓 New Site Diary';

      projectUsers?.forEach(projectUser => {
        const user = projectUser.user;
        try {
          const payload: INovuPayload = {
            user: {
              avatar: user.avatar,
              name: user.name,
              email: user.email
            },
            header,
            event: 'new-site-diary',
            company: companyName.name,
            title: project.title,
            head: uploadBy?.name,
            body: `has upload a new Site Diary`,
            tail: document?.name,
            bodyColon: true,
            subscriber: {
              firstName: user.name
            },
            link: {
              mobile: mobileLink,
              web: link,
              redirect: link,
              uri: ''
            }
          };

          return this.novuService.trigger('secondary-workflow', {
            to: {
              subscriberId: user.id.toString(),
              email: user.email
            },
            payload,
            overrides: {
              android: {
                priority: 'high'
              },
              fcm: {
                data: {
                  link: mobileLink.toString(),
                  projectId: project.id.toString(),
                  companyId: companyName?.id.toString()
                }
              }
            }
          });
        } catch (error) {
          throw new BadRequestException(error);
        }
      });
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentSubscriber', 'siteDrawingNotification');
    }
  }

  private validateFileName(name: string, type: FileSystemType, extension: string) {
    if (!name) return;
    const fileNameWithoutExtension = type === FileSystemType.Folder ? name : name?.split('.')?.slice(0, -1)?.join('.');
    if (!fileNameWithoutExtension || fileNameWithoutExtension?.trim() === '') {
      const newName = name + '.' + extension;
      return newName.replace(/[\/\\:]/g, '-');
    } else {
      // replace the '/' or '\' or ':' with '-'
      return name.replace(/[\/\\:]/g, '-');
    }
  }
}
