import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectDocumentEntity } from './entity/project-document.entity';
import {
  ProjectDocumentDto,
  CreateProjectDocumentInputDTO,
  UpdateProjectDocumentInputDTO
} from './dto/project-document.gql.dto';
import { ProjectDocumentService } from './project-document.service';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectDocumentResolver } from './project-document.resolver';
import { ProjectDocumentSubscriber } from './project-document.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { ProjectDocumentController } from './project-document.controller';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { BullMQModule } from '@modules/bull-mq/bullmq-module';
import { BimQueProcessor } from './processor/bim.process';
import { StoreDocumentProcessor } from './processor/store-document.process';
import { BimAssetEntity } from '@modules/bim-assets/entities/bim-asset.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';
import { ContactsEmailModule } from '@modules/contacts-email/contacts-email.module';
import { ContactsEmailService } from '@modules/contacts-email/contacts-email.service';
@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [
        ContactsEmailModule,
        NestjsQueryTypeOrmModule.forFeature([
          ProjectDocumentEntity,
          BimAssetEntity,
          ProjectDocumentUserEntity,
          WorkspaceAttachmentEntity,
          WorkspacePhotoEntity,
          ProjectEntity,
          WorkspaceGroupEntity,      
          ContactsEmailEntity    
        ]),
        BullMQModule.register({
          queues: ['bim-process', 'store-document-process']
        }),
        IntegrationModule
      ],
      resolvers: [
        {
          ServiceClass: ProjectDocumentService,
          DTOClass: ProjectDocumentDto,
          EntityClass: ProjectDocumentEntity,
          CreateDTOClass: CreateProjectDocumentInputDTO,
          UpdateDTOClass: UpdateProjectDocumentInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard],
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          }
        }
      ],
      services: [
        ProjectDocumentSubscriber,
        ProjectDocumentResolver,
        WorkspaceAttachmentEntity,
        WorkspacePhotoEntity,        
        ProjectEntity,
        WorkspaceGroupEntity,
        MailgunService,
        ProjectDocumentService,
        ContactsEmailService
      ],
    }),
    IntegrationModule
  ],
  controllers: [ProjectDocumentController],
  providers: [
    BimQueProcessor,
    StoreDocumentProcessor,
  ],
})
export class ProjectDocumentModule {}