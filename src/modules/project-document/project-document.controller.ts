import { Body, Controller, Get, Post, Query, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { ProjectDocumentService } from './project-document.service';
import * as Dto from './dto/project-document.api.dto';
import { GetProjectData } from '@decorators/auth.decorator';
import { CategoryType, FileChannelTypes } from '@constants';
import { In, getRepository } from 'typeorm';
import { ProjectDocumentEntity } from './entity/project-document.entity';
import { AutodeskService } from '@modules/integration/autodesk/autodesk.service';
import { getErrorMessage } from '@common/error';

@ApiTags('Project Document API')
@Controller('cloud-docs/project-document')
export class ProjectDocumentController {
  constructor(private projectDocumentService: ProjectDocumentService, 
    private autodeskService: AutodeskService,
    // @InjectQueue('bimQueue') private bimQue: Queue
  ) {}

  @Post('download-zip')
  // async downloadZipFile() {
  async downloadZipFile(
    @Res() res: Response,
    @GetProjectData() projectId: number,
    @Body() body: Dto.DownloadZipInputDto
  ) {
    const { folderName, documentEntities } = await this.projectDocumentService.getFolderNameAndFileUrl(
      body.id,
      projectId
    );

    const zipBuffer = await this.projectDocumentService.downloadZipFile(body.id);

    res.setHeader('Content-disposition', `attachment; filename="${folderName}.zip"`);
    res.setHeader('Content-Type', 'application/zip');

    res.end(zipBuffer); // Send the buffer directly
  }

  @Post('download-bim')
  async downloadBimFile(
    @Res() res: Response,
    @GetProjectData() projectId: number,
    @Body() body: Dto.DownloadZipInputDto
  ) {
    try {
      const fileType = ['rvt', 'nwc', 'nwd'];
      const projectDocument = await getRepository(ProjectDocumentEntity).findOne(body.id, {
        where: {
          category: CategoryType.BIMDrawings,
          projectId,
          type: In(fileType),
          fileChannel: FileChannelTypes.FORGE
        }
      });

      if (!projectDocument) {
        throw new Error('Project document not found');
      }

      const file = await this.autodeskService.downloadForgeFile(projectDocument.fileUrl);

      res.setHeader('Content-disposition', `attachment; filename="${projectDocument.name}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      res.end(file, 'binary');
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentController', 'downloadBimFile');
    }
  }

  @Post('download-bulk-zip')
  async downloadBulkZip(
    @Res() res: Response,
    @GetProjectData() projectId: number,
    @Body() body: Dto.DownloadBulkZipInputDto
  ) {
    const zip = await this.projectDocumentService.downloadBulkFileAndFolderZip(body.ids, projectId);

    res.setHeader('Content-disposition', `attachment; filename="bulkdownload.zip"`);
    res.setHeader('Content-Type', 'application/zip');

    await zip.generateNodeStream({ type: 'nodebuffer', streamFiles: true }).pipe(res);
  }

  @Post('filter-data')
  async filterData(@Body() body: Dto.FilterProjectsDocumentsDto, @GetProjectData() projectId: number) {
    return await this.projectDocumentService.filterProjectDocuments(body, projectId);
    // zip.generateNodeStream({ type: 'nodebuffer', streamFiles: true }).pipe(res);
  }

  @Post('search-data')
  async searchData(@Body() body: Dto.SearchProjectsDocumentsDto, @GetProjectData() projectId: number) {
    return await this.projectDocumentService.searchProjectDocuments(body, projectId);
    // zip.generateNodeStream({ type: 'nodebuffer', streamFiles: true }).pipe(res);
  }

  @Post('BIM-status')
  async getBIMStatus(@Body() body: Dto.BIMStatusInputDto) {
    const status = await this.projectDocumentService.getBIMStatus(body?.urn);
    return status;
  }

  @Post('translate-svf-to-dwg')
  async translateDwgToSvf(@Body() body: Dto.TranslateSvfToDwgDto) {
    const status = await this.projectDocumentService.translateDwgToSvf(body?.urn, body?.config, body?.haveReference);
    return status;
  }

  @Get('document')
  async getDocumentID(@Query('ID') ID: string) {
    const documentID = await this.projectDocumentService.getDocumentID(ID);
    return documentID;
  }

  @Get('project-docs-sub-group')
  async setProjectDocsSubGroup() {
    const docsSubGroup = await this.projectDocumentService.setSubgroup();
    return docsSubGroup;
  }

  @Get('project-docs-sub-group-code')
  async setSubGroupCode() {
    const docsSubGroup = await this.projectDocumentService.setSubGroupCode();
    return docsSubGroup;
  }

  @Get('project-docs-ungroup-fix')
  async fixUngroupDocuments() {
    const ungroupCode = await this.projectDocumentService.fixUngroupDocuments();
    return ungroupCode;
  }

  @Get('project-docs-workflow-fix')
  async fixWorkflowDocuments() {
    const workflowCode = await this.projectDocumentService.fixWorkflowDocuments();
    return workflowCode;
  }

  @Get('copy-drawing-to-drawing-revision')
  async copyDrawingToDrawingRevision() {
    return await this.projectDocumentService.copyDrawingToDrawingRevision();
  }
}
