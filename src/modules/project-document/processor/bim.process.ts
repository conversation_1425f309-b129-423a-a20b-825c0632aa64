import { WorkerHostProcessor } from '@modules/bull-mq/bullmq-process';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { ProjectDocumentService } from '../project-document.service';

@Processor('bim-process')
export class BimQueProcessor extends WorkerHostProcessor {
  constructor(private readonly projectDocumentService: ProjectDocumentService) {
    super();
  }

  async process(job: Job<any>): Promise<void> {
    if (job.name === 'combine-bim') {
      await this.projectDocumentService.bimCombine(job.data.input, job.data.user);
    }
  }
}
