import { WorkerHostProcessor } from '@modules/bull-mq/bullmq-process';
import { Processor } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { ProjectDocumentService } from '../project-document.service';

@Processor('store-document-process')
export class StoreDocumentProcessor extends WorkerHostProcessor {
  constructor(private readonly projectDocumentService: ProjectDocumentService) {
    super();
  }

  async process(job: Job<any>): Promise<any> {
    if (job.name === 'store-document') {
      const { ids, folderId, category } = job.data;
      await this.projectDocumentService.storeProjectDocument(
        { ids, folderId, category },
        job.data.userId,
        job.data.projectId
      );
    }
  }
}
