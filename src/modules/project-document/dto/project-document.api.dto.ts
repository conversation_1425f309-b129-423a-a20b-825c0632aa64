import { IsNotEmpty } from 'class-validator';

export class DownloadZipInputDto {
  @IsNotEmpty()
  id: number;
}

export class DownloadBulkZipInputDto {
  @IsNotEmpty()
  ids: [number];
}

export class FilterProjectsDocumentsDto {
  name: any;
  category: string;
  projectDocumentId?: string;
  allFormCode?: number;
  status?: any;
  group?: any;
  assignedTo?: any;
  // addedBy?: any;
  // cc?: any;
}

export class SearchProjectsDocumentsDto {
  name: any;
  category: string;
  projectDocumentId?: string;
}

export class DownloadPdfInputDto {
  @IsNotEmpty()
  id: number;
}

export class BIMStatusInputDto {
  @IsNotEmpty()
  urn: string;
}

export class TranslateSvfToDwgDto {
  @IsNotEmpty()
  urn: string;

  @IsNotEmpty()
  config: string;


  haveReference?: boolean;

}

export class ApplyWatermarkInputDto {
  @IsNotEmpty()
  id: number;
}

export class DocumentIDInputDto {
  ID: string;
}
