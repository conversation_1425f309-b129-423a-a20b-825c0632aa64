import {
  CategoryType,
  defaultQueryOptions,
  FileSystemType,
  ProjectDocumentStatus,
  ProjectDocumentDriveType,
  workflowType,
  SourceType,
} from "@constants";
import { relationOption } from "@constants/query.constant";
import { FormCategoryDto } from "@modules/form-category/dto/form-category.gql.dto";
import { ProjectDto } from "@modules/project/dto/project.gql.dto";
import { ProjectDocumentUserDTO } from "@modules/project-document-user/dto/project-document-user.dto";
import {
  CreateRequestForSignatureInputDTO,
  RequestForSignatureDto,
} from "@modules/request-for-signature/dto/request-for-signature.gql.dto";
import { UserDto } from "@modules/user/dto/user.gql.dto";
import {
  CreateWorkspaceAttachmentInputDTO,
  WorkspaceAttachmentDto,
} from "@modules/workspace-attachment/dto/workspace-attachment.gql.dto";
import {
  CreateWorkspaceCCInputDTO,
  WorkspaceCCDto,
} from "@modules/workspace-cc/dto/workspace-cc.gql.dto";
import { WorkspaceGroupDto } from "@modules/workspace-group/dto/workspace-group.gql.dto";
import {
  CreateWorkspacePhotoInputDTO,
  WorkspacePhotoDto,
} from "@modules/workspace-photo/dto/workspace-photo.gql.dto";
import {
  Authorize,
  BeforeCreateMany,
  BeforeCreateOne,
  CreateManyInputType,
  CreateOneInputType,
  FilterableOffsetConnection,
  FilterableRelation,
  FilterableUnPagedRelation,
  IDField,
  QueryArgsType,
  QueryOptions,
  Relation,
  UnPagedRelation,
} from "@nestjs-query/query-graphql";
import {
  ArgsType,
  Field,
  ID,
  InputType,
  ObjectType,
  PartialType,
} from "@nestjs/graphql";
import { GqlContext } from "@types";
import { Request } from "express";
import { GraphQLUpload, FileUpload } from "graphql-upload";
import { ProjectDocumentEntity } from "../entity/project-document.entity";
import { ProjectDocumentAuthorizer } from "../project-document.authorizer";
import {
  CreateWorkspaceDocumentInputDTO,
  WorkspaceDocumentDto,
} from "@modules/workspace-document/dto/workspace-document.dto";
import { DrawingRevisionDto } from "@modules/drawing-revision/dto/drawing-revision.gql.dto";
import { BimAssetDto } from "@modules/bim-assets/dto/bim-asset.gql.dto";
import { ProjectDocumentCommentDto } from "@modules/project-document-comment/dto/project-document-comment.gql.dto";

@ObjectType("ProjectDocument")
@Authorize(ProjectDocumentAuthorizer)
@FilterableRelation("owner", () => UserDto, relationOption())
@Relation("formCategory", () => FormCategoryDto, relationOption(true))
@FilterableRelation(
  "workspaceGroup",
  () => WorkspaceGroupDto,
  relationOption(true)
)
@FilterableRelation(
  "bimAsset",
  () => BimAssetDto,
  relationOption(true)
)
@FilterableUnPagedRelation(
  "requestForSignatures",
  () => RequestForSignatureDto,
  relationOption(true)
)
@FilterableUnPagedRelation(
  "workspaceCCs",
  () => WorkspaceCCDto,
  relationOption(true)
)
@FilterableOffsetConnection("project", () => ProjectDto, relationOption(true))
@UnPagedRelation(
  "projectDocumentUser",
  () => ProjectDocumentUserDTO,
  relationOption(true)
)
@UnPagedRelation(
  "workspaceAttachments",
  () => WorkspaceAttachmentDto,
  relationOption(true)
)
@UnPagedRelation(
  "workspacePhotos",
  () => WorkspacePhotoDto,
  relationOption(true)
)
@UnPagedRelation(
  "workspaceDocuments",
  () => WorkspaceDocumentDto,
  relationOption(true)
)
@FilterableRelation(
  "parentFolder",
  () => ProjectDocumentDto,
  relationOption(true)
)
@FilterableUnPagedRelation(
  "children",
  () => ProjectDocumentDto,
  relationOption(true)
)
@FilterableRelation("project", () => ProjectDto, relationOption(true))
@UnPagedRelation(
  "drawingRevisions",
  () => DrawingRevisionDto,
  relationOption(true)
)
@BeforeCreateOne(
  (instance: CreateOneInputType<ProjectDocumentDto>, context: GqlContext) => {
    const addedBy = context.req.user.id;
    instance.input.addedBy = addedBy;
    const projectId = Number(context.req.headers["project-id"]);
    instance.input.projectId = projectId;
    return instance;
  }
)
@BeforeCreateMany(
  (instance: CreateManyInputType<ProjectDocumentDto>, context: GqlContext) => {
    const addedBy = context.req.user.id;
    const projectId = Number(context.req.headers["project-id"]);
    instance.input.map((item) => ({ ...item, projectId, addedBy }));
    return instance;
  }
)
@QueryOptions({ ...defaultQueryOptions })
export class ProjectDocumentDto extends ProjectDocumentEntity {
  //? offline mode
  remoteId?: string;
  server_created_at?: Date;
  comments?: ProjectDocumentCommentDto[];
}

@InputType()
export class CreateProjectDocumentInputDTO {
  @IDField(() => ID) projectId?: number;
  @IDField(() => ID) projectDocumentId?: number;
  name?: string;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;
  obsUrl?: string;
  obsFileSize?: number;
  obsFileType?: string;
  type?: string;
  fileSystemType: FileSystemType;
  category: CategoryType;
  driveType?: ProjectDocumentDriveType;
  uploadLatitude?: number;
  uploadLongitude?: number;
  uploadAddress?: string;
  xfdf?: string;
  isDocsStored?: boolean;
  versionName?: string;
  notes?: string;
  isBimPhoto?: boolean;
  isCommentResolve?: boolean;
  autosavedAt?: Date;

  @Field(() => GraphQLUpload) videoThumbnail?: FileUpload;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
  localRemoteId?: string;
  fileKey?: string;
  localCurrentUserId?: string;
  status?: ProjectDocumentStatus;
  submittedAt?: Date;
  currentUserId?: number | null;
  workflow?: workflowType;
  workspaceGroupId?: number;
  server_created_at?: Date;
  @Field({ nullable: true }) recordSource?: SourceType;
  fileSize?: number;
  description?: string;
}

@InputType()
export class SearchProjectDocumentInputDTO {
  @IDField(() => ID) projectId?: number;
  req: Request;
  name?: string;
  type?: string;
  fileSystemType?: FileSystemType;
  category: CategoryType;
  driveType?: ProjectDocumentDriveType;
}
@InputType()
export class UpdateProjectDocumentInputDTO extends PartialType(
  CreateProjectDocumentInputDTO
) {
  @IDField(() => ID) formCategoryId?: number;
  @IDField(() => ID) workspaceGroupId?: number;
  description?: string;
  status?: ProjectDocumentStatus;
  watermarkId?: string;
  @Field(() => [CreateRequestForSignatureInputDTO])
  requestForSignatures?: CreateRequestForSignatureInputDTO[];
  @Field(() => [CreateWorkspaceCCInputDTO])
  workspaceCCs?: CreateWorkspaceCCInputDTO[];
  // Still Fixing
  @Field(() => [CreateWorkspaceAttachmentInputDTO])
  workspaceAttachments?: CreateWorkspaceAttachmentInputDTO[];
  submittedAt?: Date;
  @Field(() => [CreateWorkspacePhotoInputDTO])
  workspacePhotos?: CreateWorkspacePhotoInputDTO[];
  @Field(() => [CreateWorkspaceDocumentInputDTO])
  workspaceDocuments?: CreateWorkspaceDocumentInputDTO[];
  workflow?: workflowType;
  autoDeskMetadata?: string;

  autosavedAt?: Date | null;

  //? offline mode
  @Field() localId?: string;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
  server_created_at?: Date;

}

@InputType()
export class UpdateProjectDocumentParentDTO {
  parentId?: number;
  id?: number;
  ids?: number[];
}

@InputType()
export class GetProjectDocumentsBreadcrumbDTO {
  id: number;
}

@InputType()
export class GetDrawingsPdfTronInput {
  documentId: number;
  folderId?: number;
  name?: string;
}

@ObjectType("GetDrawingsPdfTron")
export class GetDrawingsPdfTronDTO {
  nextId?: number;
  previousId?: number;
}

@InputType()
export class GetLastParentDocumentFromBreadcrumbDTO {
  path: string;
  category: CategoryType;
}

@InputType()
export class DeleteProjectDocumentInputDTO {
  @IDField(() => [Number]) ids: number[];
}

@InputType()
export class RestoreProjectDocumentInputDTO {
  @IDField(() => [Number]) ids: number[];
}

@InputType()
export class DuplicateEditedStandardFormInputDTO {
  @IDField(() => Number) id: number;
  xfdf: string;
}

@ArgsType()
export class ProjectDocumentQuery extends QueryArgsType(ProjectDocumentDto) {}
export const ProjectDocumentConnection = ProjectDocumentQuery.ConnectionType;  

@ArgsType()
export class GetProjectDocumentById {
  @Field(() => ID)
  id: number;
}
@InputType()
export class AssignWorkspaceAttachmentInputDTO {
  @IDField(() => ID) projectDocumentId: number;
  @Field(() => [CreateWorkspaceAttachmentInputDTO])
  attachments?: CreateWorkspaceAttachmentInputDTO[];
}
@InputType()
export class AssignWorkspacePhotoInputDTO {
  @IDField(() => ID) projectDocumentId: number;
  @Field(() => [CreateWorkspacePhotoInputDTO])
  photos?: CreateWorkspacePhotoInputDTO[];
}

@InputType()
export class StoreProjectDocumentInputDTO {
  ids: number[];
  folderId: number;
  category: CategoryType;
}

@InputType()
export class UpdateWorkspaceFlowInputDTO {
  @IDField(() => ID) id: number;
  workflow: workflowType;
}

@InputType()
export class LinkPdftronDocumentInputDTO {
  urls: string[];
  title: string;
}

@ObjectType("DeletedProjectDocument")
@QueryOptions({ ...defaultQueryOptions })
export class DeletedProjectDocumentDto extends ProjectDocumentDto {
  owner?: UserDto;
  requestForSignatures: RequestForSignatureDto[];
  workspaceGroup?: WorkspaceGroupDto;
  rootProjectDocumentUsers?: ProjectDocumentUserDTO[];
}
@ArgsType()
export class DeletedProjectDocumentQuery extends QueryArgsType(
  DeletedProjectDocumentDto
) {}
export const DeletedProjectDocumentConnection =
  DeletedProjectDocumentQuery.ConnectionType;

@InputType()
export class GDriveSyncInputDTO {
  @Field()
  folderId: string;
  docs: DocsGDriveInputDTO[];
  category: CategoryType;
  driveType: ProjectDocumentDriveType;
  uploadAddress?: string;
  fileSystemType: FileSystemType;
  accessToken?: string;
}

@InputType()
export class DocsGDriveInputDTO {
  @Field()
  url: string;
  id: string;
  name: string;
  mimeType: string;
  type: string;
  sizeBytes: number;
}

@ObjectType()
export class GDriveSyncResponse {
  @Field()
  message: string;
}

@ObjectType()
export class LinkPdftronDocumentResponse {
  @Field()
  url: string;
}


@InputType()
export class CreateOrUpdateProjectDocumentAutosaveInputDTO{
  @Field(() => GraphQLUpload)
  fileUrl?: FileUpload;
  @Field()
  id?: number;
  category: CategoryType;
  fileSystemType: FileSystemType;
  projectDocumentId?: number;
  xfdf: string;
  autosavedAt: Date;
  uploadAddress?: string;
}

@InputType()
export class EmailBimCompletionDTO {
  @Field()
  id?: string;
}

@ObjectType()
export class EmailBimCompletionResponse {
  @Field()
  msg: string;

}

@InputType()
class ParentInput {
  @Field()
  id: string;
  
  @Field()
  filename: string;

  @Field()
  urn: string;

  @Field()
  urnInBase64: string;
}

@InputType()
class ReferenceInput {
  @Field()
  id: string;
  
  @Field()
  filename: string;

  @Field()
  urn: string;

  @Field()
  urnInBase64: string;
}

@InputType()
export class BIMCombineDTO {
  @Field(() => ParentInput)
  parent: ParentInput;

  @Field(() => [ReferenceInput])
  references: ReferenceInput[]; 
}

@ObjectType()
export class BIMCombineResponse {
  @Field()
  msg: string;
}

@InputType()
export class TransferCloudDocsOwnershipDTO {
  @Field(() => ID) userId: number;
  @Field(() => ID) newUserId: number;
}

@ObjectType()
export class StoreDocumentResponse {
  @Field()
  msg: string;

  @Field(() => ID)
  id: string;

  @Field(() => ProjectDocumentDto)
  document: ProjectDocumentDto;
}
