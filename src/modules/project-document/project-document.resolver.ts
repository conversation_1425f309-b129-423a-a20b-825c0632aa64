import { GqlGetGqlAuthData, GqlGetGqlProjectData } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { Filter } from '@nestjs-query/core';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Query, ResolveField, Resolver, Parent } from '@nestjs/graphql';
import { AuthData } from '@types';
import {
  AssignWorkspaceAttachmentInputDTO,
  AssignWorkspacePhotoInputDTO,
  DeleteProjectDocumentInputDTO,
  RestoreProjectDocumentInputDTO,
  DuplicateEditedStandardFormInputDTO,
  GetDrawingsPdfTronDTO,
  GetDrawingsPdfTronInput,
  GetLastParentDocumentFromBreadcrumbDTO,
  GetProjectDocumentsBreadcrumbDTO,
  ProjectDocumentConnection,
  ProjectDocumentDto,
  ProjectDocumentQuery,
  UpdateProjectDocumentParentDTO,
  UpdateWorkspaceFlowInputDTO,
  DeletedProjectDocumentConnection,
  DeletedProjectDocumentQuery,
  DeletedProjectDocumentDto,
  GDriveSyncInputDTO,
  GDriveSyncResponse,
  LinkPdftronDocumentResponse,
  LinkPdftronDocumentInputDTO,
  EmailBimCompletionDTO,
  EmailBimCompletionResponse,
  CreateOrUpdateProjectDocumentAutosaveInputDTO,
  BIMCombineDTO,
  BIMCombineResponse,
  GetProjectDocumentById,
  TransferCloudDocsOwnershipDTO,
  StoreDocumentResponse,
  StoreProjectDocumentInputDTO
} from "./dto/project-document.gql.dto";
import { ProjectDocumentService } from "./project-document.service";
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ProjectDocumentUserDTO } from '@modules/project-document-user/dto/project-document-user.dto';

@UseGuards(GqlAuthGuard)
@Resolver(() => ProjectDocumentDto)
export class ProjectDocumentResolver {
  constructor(
    private projectDocumentService: ProjectDocumentService,
    @InjectQueue('bim-process') private bimQueue: Queue,
    @InjectQueue('store-document-process') private storeDocumentQueue: Queue
  ) {}

  @Query(() => ProjectDocumentConnection)
  async getWorkSpaceAllForm(
    @Args() query: ProjectDocumentQuery,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    const filter: Filter<ProjectDocumentDto> = {
      ...query.filter
    };

    const { result, totalCount } = await this.projectDocumentService.getWorkSpaceAllForm(user.id, projectId, query);

    const res = await ProjectDocumentConnection.createFromPromise(async () => result, { ...query, ...{ filter } });

    const hasNextPage = query.paging.offset + query.paging.limit < totalCount;
    return { ...res, totalCount, pageInfo: { hasNextPage, hasPreviousPage: query.paging.offset > 0 } };
  }

  @Query(() => ProjectDocumentConnection)
  async getProjectDocuments(
    @Args() query: ProjectDocumentQuery,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    const filter: Filter<ProjectDocumentDto> = {
      ...query.filter
    };

    return await ProjectDocumentConnection.createFromPromise(
      async q => await this.projectDocumentService.getProjectDocumentsByUser(user.id, projectId, q),
      { ...query, ...{ filter } }
    );
  }

  @Query(() => ProjectDocumentDto)
  async getProjectDocument(
    @Args() query: GetProjectDocumentById,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    try {
      return await this.projectDocumentService.getProjectDocumentById(query.id, projectId, user.id);
    } catch (error) {
      return Error(error.message);
    }
  }

  @Query(() => [ProjectDocumentDto])
  async getProjectDocumentsBreadcrumb(
    @Args('input') input: GetProjectDocumentsBreadcrumbDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.getProjectDocumentsBreadcrumb(input.id, projectId, user.id);
  }

  @Query(() => GetDrawingsPdfTronDTO)
  async getDrawingsPdfTron(@GqlGetGqlProjectData() projectId: number, @Args('input') input?: GetDrawingsPdfTronInput) {
    return await this.projectDocumentService.getDrawingsPdfTron(projectId, input);
  }

  @Query(() => ProjectDocumentDto)
  async getLastParentDocumentFromBreadcrumb(
    @Args('input') input: GetLastParentDocumentFromBreadcrumbDTO,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.projectDocumentService.getLastParentDocumentFromBreadcrumb(input, projectId);
  }

  @Mutation(() => ProjectDocumentDto)
  async duplicateEditedStandardForm(
    @Args('input') input: DuplicateEditedStandardFormInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.createAllFormFromStandardForm(input.id, input.xfdf, projectId, user.id);
  }

  @Query(() => [ProjectDocumentDto])
  async getCorrespondenceIn(@GqlGetGqlProjectData() projectId: number) {
    return await this.projectDocumentService.getCorrespondenceIn(projectId);
  }

  @Query(() => [ProjectDocumentDto])
  async getCorrespondenceOut(@GqlGetGqlProjectData() projectId: number) {
    return await this.projectDocumentService.getCorrespondenceOut(projectId);
  }

  @Query(() => DeletedProjectDocumentConnection)
  async getDeletedProjectDocuments(
    @Args() query: DeletedProjectDocumentQuery,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    const filter: Filter<DeletedProjectDocumentDto> = {
      ...query.filter
    };

    return DeletedProjectDocumentConnection.createFromPromise(
      async q => (await this.projectDocumentService.getDeletedProjectDocuments(projectId, q, user)) as any,
      { ...query, ...{ filter } }
    );
  }

  @Query(() => ProjectDocumentConnection)
  async getAutosavedProjectDocumentCount(
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData,
    @Args() query: ProjectDocumentQuery
  ) {
    const count = await this.projectDocumentService.autosavedProjectDocumentCount(projectId, user.id, query);

    return { totalCount: count };
  }

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: ProjectDocumentDto) {
    if (parent.type === 'folder') return null;
    if (parent.category === 'BIMDrawings') return parent.fileUrl; // BIM Drawings is not the same obs url, just unique id for autodesk files

    return await this.projectDocumentService.getPresignedUrl(parent, 'fileKey');
  }

  @ResolveField('obsUrl', () => String)
  async obsUrl(@Parent() parent: ProjectDocumentDto) {
    if (parent.type === 'folder') return null;
    if (parent.category === 'BIMDrawings') return parent.fileUrl; // BIM Drawings is not the same obs url, just unique id for autodesk files

    return await this.projectDocumentService.getPresignedUrl(parent, 'obsKey');
  }

  @ResolveField('videoThumbnail', () => String)
  async videoThumbnail(@Parent() parent: ProjectDocumentDto) {
    if (parent.type === 'folder') return null;
    if (parent.category === 'BIMDrawings') return parent.fileUrl; // BIM Drawings is not the same obs url, just unique id for autodesk files

    return await this.projectDocumentService.getPresignedUrl(parent, 'videoThumbnailKey');
  }

  @ResolveField('rootProjectDocumentUsers', () => [ProjectDocumentUserDTO])
  async rootProjectDocumentUsers(@Parent() projectDocument: ProjectDocumentDto) {

    return await this.projectDocumentService.getParentProjectDocumentUsers(projectDocument)
  }

  // ===============================================================MUTATION===============================================================

  @Mutation(() => Boolean)
  async deleteProjectDocument(
    @Args('id') id: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.deleteProjectDocument(id, projectId, user.id);
  }

  @Mutation(() => Boolean)
  async deleteProjectDocuments(
    @Args('input') input: DeleteProjectDocumentInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.deleteProjectDocuments(input.ids, projectId, user.id);
  }

  @Mutation(() => Boolean)
  async updateProjectDocumentParent(
    @Args('input') input: UpdateProjectDocumentParentDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.updateProjectDocumentParent(
      input.id || input.ids,
      input.parentId,
      projectId,
      user.id
    );
  }

  @Mutation(() => ProjectDocumentDto)
  async assignAttachmentWorkspace(
    @Args('input') input: AssignWorkspaceAttachmentInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    return await this.projectDocumentService.assignAttachments(input, user.id, projectId);
  }

  @Mutation(() => ProjectDocumentDto)
  async assignPhotoWorkspace(@Args('input') input: AssignWorkspacePhotoInputDTO, @GqlGetGqlAuthData() user: AuthData) {
    return await this.projectDocumentService.assignPhotos(
      input,
      user.id
      // projectId
    );
  }

  @Mutation(() => StoreDocumentResponse)
  async storeProjectDocument(
    @Args('input') input: StoreProjectDocumentInputDTO,
    @GqlGetGqlAuthData() user: AuthData,
    @GqlGetGqlProjectData() projectId: number
  ) {
    // First get the document that's being stored
    const document = await this.projectDocumentService.getProjectDocumentById(input.ids[0], projectId, user.id);
    
    const job = await this.storeDocumentQueue.add('store-document', {
      ids: input.ids,
      folderId: input.folderId,
      category: input.category,
      userId: user.id,
      projectId
    });
    
    return { 
      msg: 'Document store job accepted', 
      id: job.id, // Set id to be the same as jobId
      document: document
    };
  }

  @Mutation(() => ProjectDocumentDto)
  async updateWorkspaceWorkflow(@Args('input') input: UpdateWorkspaceFlowInputDTO) {
    return await this.projectDocumentService.updateWorkspaceWorkflow(input);
  }

  @Mutation(() => ProjectDocumentDto)
  async restoreDocument(
    @Args('id') id: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.restoreOneDocument(id, projectId, user.id);
  }

  @Mutation(() => Boolean)
  async restoreDocuments(
    @Args('input') input: RestoreProjectDocumentInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.restoreManyDocuments(input.ids, projectId, user.id);
  }

  @Mutation(() => ProjectDocumentDto)
  async permanentDeleteDocument(
    @Args('id') id: number,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.permanentDeleteOneDocument(id, projectId, user.id);
  }

  @Mutation(() => Boolean)
  async permanentDeleteDocuments(
    @Args('input') input: DeleteProjectDocumentInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.permanentDeleteManyDocuments(input.ids, projectId, user.id);
  }

  @Mutation(() => GDriveSyncResponse)
  async syncGDrive(
    @Args('input') input: GDriveSyncInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.syncGDrive(input, projectId, user);
  }

  @Mutation(() => LinkPdftronDocumentResponse)
  async linkPdftronDocument(@Args('input') input: LinkPdftronDocumentInputDTO) {
    return await this.projectDocumentService.linkPdftronDocument(input);
  }

  @Mutation(() => ProjectDocumentDto)
  async createOrUpdateProjectDocumentAutosave(
    @Args('input') input: CreateOrUpdateProjectDocumentAutosaveInputDTO,
    @GqlGetGqlProjectData() projectId: number,
    @GqlGetGqlAuthData() user: AuthData
  ) {
    return await this.projectDocumentService.createOrUpdateProjectDocumentAutosave(input, projectId, user.id);
  }

  @Mutation(() => EmailBimCompletionResponse)
  async emailBimCompletion(@Args('input') input: EmailBimCompletionDTO, @GqlGetGqlAuthData() user: AuthData) {
    return await this.projectDocumentService.emailBimCompletion(input, user);
  }

  @Mutation(() => BIMCombineResponse)
  async bimCombine(@Args('input') input: BIMCombineDTO, @GqlGetGqlAuthData() user: AuthData) {
    const job = await this.bimQueue.add('combine-bim', { input, user });
    return { msg: 'BIM Combine job accepted', jobId: job.id };
  }

  @Mutation(() => String)
  async transferOwnershipAndRemoveUser(
    @GqlGetGqlProjectData() projectId: number,
    @Args('input') input: TransferCloudDocsOwnershipDTO
  ): Promise<any> {
    const { newUserId, userId } = input;
    if (!newUserId || !userId || !projectId) {
      throw new Error('Please provide userId and newUserId');
    }

    this.projectDocumentService.transferCloudDocsOwnership(projectId, userId, newUserId);

    return 'Transfer Ownership and Remove User Process Started';
  }
}
