import { BaseEntity } from '@modules/base/base';
import { FormCategoryEntity } from '@modules/form-category/entity/form-category.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Filter<PERSON><PERSON><PERSON>, IDField } from '@nestjs-query/query-graphql';
import { Field, GraphQLISODateTime, ID, ObjectType } from '@nestjs/graphql';
import {
  Entity,
  Column,
  ManyToMany,
  JoinColumn,
  ManyToOne,
  Tree,
  TreeChildren,
  TreeParent,
  OneToMany,
  OneToOne,
  Index
} from 'typeorm';
import {
  FileSystemType,
  CategoryType,
  ProjectDocumentStatus,
  FileChannelTypes,
  ProjectDocumentDriveType,
  workflowType
} from '@constants';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { ProjectDocumentCommentEntity } from '@modules/project-document-comment/entity/project-document-comment.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';
import { WorkspaceDocumentEntity } from '@modules/workspace-document/entity/workspace-document.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { DrawingRevisionEntity } from '@modules/drawing-revision/entity/drawing-revision.entity';
import { DrawingLinksEntity } from '@modules/drawing-links/entity/drawing-links.entity';
import { GraphQLJSON } from 'graphql-type-json';
import { BimAssetEntity } from '@modules/bim-assets/entities/bim-asset.entity';


@ObjectType()
@Entity('project_documents')
@Tree('materialized-path')
export class ProjectDocumentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  projectDocumentId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  addedBy: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  formCategoryId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  workspaceGroupId: number;

  @FilterableField()
  @Column('varchar')
  name: string;

  @Field({ nullable: true })
  @Column('text', { nullable: true })
  description: string;

  @FilterableField(() => FileSystemType, { nullable: true })
  @Column('enum', { enum: FileSystemType, nullable: true })
  fileSystemType: FileSystemType;

  @FilterableField()
  @Column('enum', { enum: ProjectDocumentDriveType, default: ProjectDocumentDriveType.Shared })
  driveType: ProjectDocumentDriveType;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  obsUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  obsKey: string;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  obsFileSize: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  obsFileType: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  type: string;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  fileSize: number;

  @FilterableField(() => ProjectDocumentStatus, { nullable: true })
  @Column('enum', { enum: ProjectDocumentStatus, nullable: true })
  status: ProjectDocumentStatus;

  @FilterableField(() => CategoryType, { nullable: true })
  @Column('enum', { enum: CategoryType, nullable: true })
  category: CategoryType;

  @FilterableField(() => FileChannelTypes)
  @Column('enum', { enum: FileChannelTypes, default: FileChannelTypes.OBS })
  fileChannel: FileChannelTypes;

  @Field({ nullable: true })
  @Column('longtext', { nullable: true })
  xfdf: string;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  notes: string;

  @Field({ nullable: true })
  @Column('varchar', { nullable: true })
  versionName: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  allFormCode: number;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  watermarkId: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  uploadLatitude: string;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  uploadLongitude: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  uploadAddress: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  videoThumbnail: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  videoThumbnailKey: string;

  @FilterableField()
  @Column('boolean', { default: false })
  isDocsStored: boolean;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  submittedAt: Date;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  groupCode: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  currentUserId: number;

  @FilterableField(() => workflowType, { nullable: true })
  @Column('enum', { enum: workflowType, nullable: true })
  workflow: workflowType;

  @Field()
  @Column('boolean', { default: false })
  permanentlyDeleted: boolean;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  deletedProjectDocumentId: number;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  deletedChildrenIds: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  fileKey: string;
  
  @Field(() => GraphQLJSON, { nullable: true })
  @Column('json', { nullable: true })
  autoDeskMetadata: JSON;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column('datetime', { nullable: true })
  autosavedAt: Date;

  @FilterableField({ nullable: true })
  @Column('boolean', { default: false })
  isBimPhoto: boolean;

  @FilterableField({ nullable: true })
  @Column('boolean', { default: false })
  isCommentResolve: boolean;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToMany(() => TaskEntity, task => task.documents)
  tasks: TaskEntity[];

  @OneToMany(() => RequestForSignatureEntity, requestForSignatures => requestForSignatures.projectDocument, {
    cascade: true
  })
  requestForSignatures: RequestForSignatureEntity[];

  @OneToMany(() => WorkspaceCCEntity, ccs => ccs.projectDocument, { cascade: true })
  workspaceCCs: WorkspaceCCEntity[];

  @OneToMany(() => ProjectDocumentCommentEntity, projectDocumentComments => projectDocumentComments.projectDocument)
  comments?: ProjectDocumentCommentEntity[];

  @OneToMany(() => ProjectDocumentUserEntity, projectDocumentUser => projectDocumentUser.projectDocument)
  projectDocumentUsers: ProjectDocumentUserEntity[];

  @OneToMany(() => WorkspaceAttachmentEntity, workspaceAttachments => workspaceAttachments.document, {
    cascade: ['insert', 'update']
  })
  workspaceAttachments: WorkspaceAttachmentEntity[];

  @OneToMany(() => WorkspacePhotoEntity, workspacePhotos => workspacePhotos.document, { cascade: ['insert', 'update'] })
  workspacePhotos: WorkspacePhotoEntity[];

  @OneToMany(() => WorkspaceDocumentEntity, workspaceDocument => workspaceDocument.document, {
    cascade: ['insert', 'update']
  })
  @JoinColumn({ name: 'workspaceDocumentId' })
  workspaceDocuments: WorkspaceDocumentEntity[];

  @OneToMany(() => DrawingRevisionEntity, revisions => revisions.projectDocument, {
    cascade: true
  })
  drawingRevisions: DrawingRevisionEntity[];

  @OneToMany(() => BimAssetEntity, asset => asset.projectDocument, {
    cascade: true
  })
  bimAssets: BimAssetEntity[];

  @OneToMany(() => DrawingLinksEntity, drawingLinks => drawingLinks.projectDocument, {
    cascade: true
  })
  drawingLinks: DrawingLinksEntity[];

  @ManyToOne(() => UserEntity, user => user.projectDocuments)
  @JoinColumn({ name: 'addedBy' })
  owner?: UserEntity;

  @ManyToOne(() => FormCategoryEntity, category => category.projectDocuments)
  @JoinColumn({ name: 'formCategoryId' })
  formCategory: FormCategoryEntity;

  @Index()
  @ManyToOne(() => ProjectEntity, project => project.documents)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => WorkspaceGroupEntity, workspaceGroup => workspaceGroup.documents)
  @JoinColumn({ name: 'workspaceGroupId' })
  workspaceGroup?: WorkspaceGroupEntity;

  @TreeChildren({ cascade: ['soft-remove', 'remove', 'recover'] })
  children: ProjectDocumentEntity[];

  @TreeParent({ onDelete: 'CASCADE' })
  @JoinColumn({ name: 'projectDocumentId' })
  parentFolder: ProjectDocumentEntity;

  @ManyToMany(() => ScheduleEntity, schedule => schedule.documents)
  schedules: ScheduleEntity[];

  @OneToOne(() => RequestForSignatureEntity, requestForSignature => requestForSignature.currentUser)
  @JoinColumn({ name: 'currentUserId' })
  currentUser: RequestForSignatureEntity;

  @ManyToMany(() => DrawingLinksEntity, drawingLinks => drawingLinks.drawingLinkDocuments)
  drawingLinkDocuments: DrawingLinksEntity[];
}
