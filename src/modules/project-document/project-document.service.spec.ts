import { ProjectDocumentService } from './project-document.service';
import { Test, TestingModule } from '@nestjs/testing';
import { ProjectDocumentEntity } from './entity/project-document.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { AutodeskService } from '@modules/integration/autodesk/autodesk.service';
import { FileService } from '@modules/integration/file.service';
import * as pdfsMerge from "@common/pdf-merge/pdf-merge";
import fetch from 'node-fetch';

describe('ProjectDocumentService', () => {
  let service: ProjectDocumentService;

  // mock project document
  const projectDocument = {
    "id": 1,
    "createdBy": null,
    "updatedBy": null,
    "deletedBy": null,
    "createdAt": "2023-01-10 16:59:57.775110",
    "updatedAt": "2023-01-14 12:55:35.000000",
    "deletedAt": "2023-01-14 12:55:35.000000",
    "projectId": 1,
    "name": "2D-drawing-2.pdf",
    "type": "pdf",
    "addedBy": 1,
    "formCategoryId": null,
    "fileSize": 0.12,
    "category": "ProjectDocument",
    "projectDocumentId": null,
    "fileSystemType": "Document",
    "status": null,
    "mpath": "14.",
    "xfdf": null,
    "fileChannel": "OBS",
    "allFormCode": null,
    "workspaceGroupId": null,
    "description": null,
    "driveType": "Shared",
    "fileUrl": "https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/Project-Document/aaaaaa-2D-drawing-2.pdf",
    "watermarkId": null,
    "uploadLatitude": null,
    "uploadLongitude": null,
    "uploadAddress": null,
    "videoThumbnail": null,
    "submittedAt": null,
    "isDocsStored": 0,
    "groupCode": null,
    "currentUserId": null,
    "workflow": null,
    "obsUrl": null,
    "obsFileSize": null,
    "obsFileType": null,
    "notes": null,
    "versionName": null,
    "permanentlyDeleted": 0,
    "deletedProjectDocumentId": null,
    "deletedChildrenIds": null,
    "workspaceAttachments": [],
  }
  
  // mock typeorm createQueryBuilder
  const createQueryBuilder = {
    leftJoinAndSelect: jest.fn().mockImplementation(()=> createQueryBuilder),
    where: jest.fn().mockImplementation(()=> createQueryBuilder),
    orderBy: jest.fn().mockImplementation(()=> createQueryBuilder),
    andWhere: jest.fn().mockImplementation(()=> createQueryBuilder),
    skip: jest.fn().mockImplementation(()=> createQueryBuilder),
    take: jest.fn().mockImplementation(()=> createQueryBuilder),
    getCount: jest.fn().mockImplementation(()=> 1),
    addOrderBy: jest.fn().mockImplementation(()=> createQueryBuilder),
    leftJoin: jest.fn().mockImplementation(()=> createQueryBuilder),
    addSelect: jest.fn().mockImplementation(()=> createQueryBuilder),
    select: jest.fn().mockImplementation(()=> createQueryBuilder),
    getMany: jest.fn().mockImplementation(()=> [projectDocument]),
  }

  const saveMock = jest.fn();
  const mockedFileUploadService = jest.fn().mockResolvedValue({key: 'newUrl'})
  const mockDuplicateFilesService = jest.fn().mockResolvedValue({url: 'newUrl'});

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [ProjectDocumentService,
        {
          provide: getRepositoryToken(ProjectDocumentEntity),
          useValue: ProjectDocumentEntity
        },
        {
          provide: getRepositoryToken(ProjectDocumentUserEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(WorkspaceAttachmentEntity),
          useValue: {
            save: saveMock
          }
        },
        {
          provide: getRepositoryToken(WorkspacePhotoEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(ProjectEntity),
          useValue: {}
        },
        {
          provide: getRepositoryToken(WorkspaceGroupEntity),
          useValue: {}
        },
        {
          provide: TMOneService,
          useValue: {
            duplicateFile: mockDuplicateFilesService
          }
        },
        {
          provide: AutodeskService,
          useValue: {
            getAutodeskAuth: jest.fn().mockResolvedValue('token')
          }
        },
        {
          provide: FileService,
          useValue: {
            uploadGqlFile: mockedFileUploadService
          }
        },
      ],
    }).compile();

    service = moduleRef.get<ProjectDocumentService>(ProjectDocumentService);
  });

  it('should be created', () => {
    expect(service).toBeDefined();
  });

  describe('getWorkSpaceAllForm', () => {
    it('should return an array of project documents', async () => {
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any);
      const projectDocuments = await service.getWorkSpaceAllForm(1, 1, { filter: {}, sorting: [], paging: {offset: 0, limit: 10} });

      expect(projectDocuments.result.length).toEqual(1);
      expect(projectDocuments.totalCount).toEqual(1);
      expect(projectDocuments.result[0]).toEqual(projectDocument);
    });
  });

  describe('getProjectDocumentsByUser', () => {
    it('should return an array of project documents', async () => {
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any);
      const projectDocuments = await service.getProjectDocumentsByUser(1, 1, { filter: {}, sorting: [], paging: {offset: 0, limit: 10} });

      expect(projectDocuments.length).toEqual(1);
    });
  });

  describe('searchProjectDocuments', () => {
    it('should return an array of project documents', async () => {
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any);
      const projectDocuments = await service.searchProjectDocuments({name: ''}, 1);

      expect(projectDocuments.length).toEqual(1);
      expect(projectDocuments[0]).toEqual(projectDocument);
    });
  });

  describe('filterProjectDocuments', () => {
    it('should return an array of project documents', async () => {
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any);
      const projectDocuments = await service.filterProjectDocuments({
        name: '',
        status: 'DraftOnly',
        assignedTo: 'Me',
        allFormCode: 'allFormCode',
      }, 1);

      expect(projectDocuments.length).toEqual(1);
      expect(projectDocuments[0]).toEqual(projectDocument);
    });
  });

  // tak faham apa findAncestorsTree ni, kena tanya hakim
  describe('getProjectDocumentsBreadcrumb', () => {
    it.skip('should return something', async () => {
      jest.spyOn(ProjectDocumentEntity, 'findOne').mockReturnValue(projectDocument as any);
      const result = await service.getProjectDocumentsBreadcrumb(1, 1, 1);

      expect(result).toEqual(projectDocument);
    });
  })

  describe('getDrawingsPdfTron', () => {
    it('should return project documents', async () => {
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any);
      jest.spyOn(ProjectDocumentEntity, 'count').mockReturnValue(1 as any);
      const result = await service.getDrawingsPdfTron(1, {folderId:1, documentId:1});

      expect(result).toEqual( {id: 1, nextId: null, previousId: null });
    });
  });

  describe('assignAttachments', () => {
    it('should save attachments', async () => {
      const attachments = [{id: 1, userId: 1, projectDocumentId: 1}];
      jest.spyOn(ProjectDocumentEntity, 'findOne').mockReturnValue(projectDocument as any);
      const result = await service.assignAttachments({projectDocumentId: 1, attachments}, 1,1);

      expect(saveMock).toHaveBeenCalledWith(attachments);
      expect(result).toEqual(projectDocument);
    });
  })

  describe('createAllFormFromStandardForm', () => {
    it('should duplicate a project Document', async () => {
      jest.spyOn(ProjectDocumentEntity, 'findOne').mockReturnValue(projectDocument as any);
      jest.spyOn(ProjectDocumentEntity, 'createQueryBuilder').mockReturnValue(createQueryBuilder as any)
      jest.spyOn(ProjectDocumentEntity, 'create').mockReturnValue(projectDocument as any);
      jest.spyOn(ProjectDocumentEntity, 'save').mockReturnValue(projectDocument as any);
      const result = await service.createAllFormFromStandardForm(1, '', 1, 1);

      expect(mockDuplicateFilesService).toHaveBeenCalledWith(
        'Project-Document/aaaaaa-2D-drawing-2.pdf',
        expect.stringMatching(/^All-Form\/[a-zA-Z0-9\-_]+-2D-drawing-2\.pdf$/)
      );
      expect(result).toEqual(projectDocument);
    });
  });

  describe('getFolderNameAndFileUrl', () => {
    const dummyFolder = { filSystemType: 'Folder', name: 'folder kosong'}
    // skipped for now until we know how to mock createDescendantsQueryBuilder
    it.skip('should return folder name and empty file url', async () => {
      jest.spyOn(ProjectDocumentEntity, 'findOne').mockReturnValue(dummyFolder as any);
      jest.spyOn(ProjectDocumentEntity, 'createDescendantsQueryBuilder' as any).mockReturnValue(createQueryBuilder as any);
      const result = await service.getFolderNameAndFileUrl(1, 1);

      expect(result).toEqual({ folderName: 'folder kosong', documentEntities: [] });
    });
  });

  describe('runMergePdf', () => {
    it ('should return a url', async () => {
      jest.spyOn(pdfsMerge, 'mergePdfs').mockResolvedValue({ 
        buffer: Buffer.from(''), 
        folder: '', 
        file: {
          filename: '',
          mimetype: '',
          encoding: ''
        }, 
        finalPdfBytes: new Uint8Array()
      });
      
      await service.runMergePdf('abcd', [], [], [], 'title');

      expect(pdfsMerge.mergePdfs).toHaveBeenCalled();
      expect(mockedFileUploadService).toHaveBeenCalled();
    })
  });

  describe('getBIMStatus', () => {
    jest.spyOn(fetch, 'default')
      .mockResolvedValueOnce({status: 200, json: jest.fn().mockResolvedValue({
        status: 'success',
        derivatives: [{status: 'success', progress: 'complete'}]
      })} as any)
      .mockResolvedValueOnce({status: 200, json: jest.fn().mockResolvedValue({
        status: 'success',
        derivatives: [{status: 'success', progress: 'complete'}, {status: 'fail', progress: 'complete'}]
      })} as any);

    it('should return a status 200 if all completed', async () => {
      const result = await service.getBIMStatus('urn');

      expect(result).toEqual(200);
    });

    it('should return a status 202 if not all completed', async () => {
      const result = await service.getBIMStatus('urn');

      expect(result).toEqual(202);
    });
  });

  describe('getDocumentID', () => {
    it('should return fileUrl', async () => {
      jest.spyOn(ProjectDocumentEntity, 'findOne').mockReturnValue(projectDocument as any);
      const result = await service.getDocumentID('1');

      expect(result).toEqual(projectDocument.fileUrl);
    });
  });

  describe('linkPdftronDocument', () => {
    it('should merge and return new url', async () => {
      jest.spyOn(pdfsMerge, 'mergePdfs').mockResolvedValue({ 
        buffer: Buffer.from(''), 
        folder: '', 
        file: {
          filename: '',
          mimetype: '',
          encoding: ''
        }, 
        finalPdfBytes: new Uint8Array()
      });
      
      const result = await service.linkPdftronDocument({urls: ['url'], title: 'abc'});

      expect(pdfsMerge.mergePdfs).toHaveBeenCalled();
      expect(mockedFileUploadService).toHaveBeenCalled();
      expect(result.url).toEqual('newUrl');
    });
  });

  describe('deleteProjectDocument', () => {
    it('should call deleteProjectDocuments', async () => {
      jest.spyOn(service, 'deleteProjectDocuments').mockResolvedValue(true);

      await service.deleteProjectDocument(1, 1, 1);

      expect(service.deleteProjectDocuments).toHaveBeenCalledWith([1], 1, 1);
    })
  });
})
