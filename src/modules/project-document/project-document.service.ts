import { getErrorMessage } from '@common/error';
import { mergePdfs } from '@common/pdf-merge/pdf-merge';
import {
  AuditLogActionType,
  AuditLogModuleType,
  CategoryType,
  CorrespondenceType,
  FileChannelTypes,
  FileSystemType,
  ProjectDocumentDriveType,
  ProjectDocumentStatus,
  ProjectDocumentUserPermissionType,
  ProjectUserRoleType,
  RequestForSignatureStatus,
  workflowType
} from '@constants';
import { replaceSpaceWithDash } from '@constants/function';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { DrawingRevisionEntity } from '@modules/drawing-revision/entity/drawing-revision.entity';
import { AutodeskService } from '@modules/integration/autodesk/autodesk.service';
import { FileService } from '@modules/integration/file.service';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspaceDocumentEntity } from '@modules/workspace-document/entity/workspace-document.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AuthData } from '@types';
import axios from 'axios';
import { FileUpload } from 'graphql-upload';
import JSZip from 'jszip';
import * as _ from 'lodash';
import moment from 'moment';
import { nanoid } from 'nanoid/async';
import fetch from 'node-fetch';
import { Brackets, getRepository, getTreeRepository, In, IsNull, Like, Not, Repository } from 'typeorm';
import {
  AssignWorkspaceAttachmentInputDTO,
  AssignWorkspacePhotoInputDTO,
  BIMCombineDTO,
  CreateOrUpdateProjectDocumentAutosaveInputDTO,
  DeletedProjectDocumentQuery,
  EmailBimCompletionDTO,
  GDriveSyncInputDTO,
  GetLastParentDocumentFromBreadcrumbDTO,
  LinkPdftronDocumentInputDTO,
  LinkPdftronDocumentResponse,
  ProjectDocumentQuery,
  StoreProjectDocumentInputDTO,
  UpdateWorkspaceFlowInputDTO
} from './dto/project-document.gql.dto';
import { ProjectDocumentEntity } from './entity/project-document.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';

interface Derivative {
  status: string;
  progress: string;
}

interface ApiResponse {
  status: string;
  progress: string;
  reason?: string;
  derivatives?: Derivative[];
}

@Injectable()
export class ProjectDocumentService extends TypeOrmQueryService<ProjectDocumentEntity> {
  constructor(
    @InjectRepository(ProjectDocumentEntity)
    private projectDocumentRepo: Repository<ProjectDocumentEntity>,
    @InjectRepository(ProjectDocumentUserEntity)
    private projectDocumentUserRepo: Repository<ProjectDocumentUserEntity>,
    @InjectRepository(WorkspaceAttachmentEntity)
    private workspaceAttachmentRepo: Repository<WorkspaceAttachmentEntity>,
    @InjectRepository(WorkspacePhotoEntity)
    private workspacePhotoRepo: Repository<WorkspacePhotoEntity>,
    // @InjectRepository(ContactsEmailEntity)
    // private contactsEmailRepo: Repository<ContactsEmailEntity>,
    private tmOneService: TMOneService,
    private autodeskService: AutodeskService,
    private fileService: FileService,
    private mailgunService: MailgunService
  ) {
    super(projectDocumentRepo, { useSoftDelete: true });
  }

  async getProjectDocumentById(id: number, projectId: number, userId: number) {
    try {
      const projectDocument = await this.projectDocumentRepo.findOne({
        where: {
          projectId,
          id
        }
      });

      if (!projectDocument) throw new BadRequestException('Project document not found');

      const projectUserRole = await this.getProjectUserRole(projectId, userId);
      const accessibleWorkspaceGroupIds = await this.getAccessibleWorkspaceGroupId(userId, projectId);
      if (
        accessibleWorkspaceGroupIds.includes(projectDocument.workspaceGroupId) ||
        projectUserRole === ProjectUserRoleType.ProjectOwner
      ) {
        return projectDocument;
      }

      throw new BadRequestException(
        'Access Restricted: You don’t have permission to view this document. Contact the document creator to request access.'
      );
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getProjectDocumentById');
    }
  }

  async getWorkSpaceAllForm(userId: number, projectId: number, q: any) {
    try {
      const projectUserRole = await this.getProjectUserRole(projectId, userId);
      // use all thing in project document query
      const queryBuilder = await this.projectDocumentRepo
        .createQueryBuilder('projectDocs')
        .leftJoin('projectDocs.requestForSignatures', 'sign')
        .addSelect(['sign.signById', 'sign.ownerId', 'sign.status', 'sign.id', 'sign.createdAt'])
        .leftJoin('sign.signBy', 'signBy')
        .addSelect(['signBy.name', 'signBy.avatar', 'signBy.email', 'signBy.id'])
        .leftJoin('projectDocs.workspaceGroup', 'group')
        .addSelect(['group.name', 'group.id', 'group.code'])
        .leftJoin('group.parent', 'parent')
        .addSelect(['parent.code', 'parent.id'])
        .leftJoin('parent.workspaceGroupUsers', 'workspaceGroupUsers')
        .addSelect(['workspaceGroupUsers.userId', 'workspaceGroupUsers.userId'])
        .leftJoin('projectDocs.comments', 'comments')
        .addSelect(['comments.id'])
        .where('projectDocs.projectId = :projectId', { projectId });

      if (projectUserRole !== ProjectUserRoleType.ProjectOwner) {
        const accessibleWorkspaceGroupIds = await this.getAccessibleWorkspaceGroupId(userId, projectId);
        queryBuilder.andWhere('projectDocs.workspaceGroupId IN (:...accessibleWorkspaceGroupIds)', {
          accessibleWorkspaceGroupIds
        });
      }

      if (q?.filter.category && q.filter.category.eq) {
        queryBuilder.andWhere('projectDocs.category = :category', {
          category: q.filter.category.eq
        });
      }

      if (q?.filter.status) {
        if (q?.filter.status.eq) {
          queryBuilder.andWhere('projectDocs.status = :status', {
            status: q?.filter.status.eq
          });
        } else if (q?.filter.status.neq) {
          queryBuilder.andWhere('projectDocs.status != :status', {
            status: q?.filter.status.neq
          });
        } else if (q?.filter.status.in) {
          queryBuilder.andWhere('projectDocs.status IN (:...status)', {
            status: q?.filter.status.in
          });
        }
      }

      if (q?.filter?.addedBy && q?.filter?.addedBy?.eq) {
        queryBuilder.andWhere('projectDocs.addedBy = :addedBy', {
          addedBy: q?.filter?.addedBy?.eq
        });
      }

      queryBuilder.orderBy(`projectDocs.${q?.sorting[0]?.field}`, q?.sorting[0]?.direction);

      if (!q?.filter?.requestForSignatures?.signById?.eq) {
        queryBuilder.skip(q?.paging?.offset).take(q?.paging?.limit);
      }

      if (q?.filter?.name?.like) {
        const name = q?.filter?.name?.like;
        if (name !== null) {
          queryBuilder.andWhere({
            name: Like(`%${name}%`)
          });
        }
      }

      if (q?.filter?.workspaceGroup?.code?.eq && q?.filter?.status?.eq !== ProjectDocumentStatus.Draft) {
        queryBuilder.andWhere('parent.code = :code', { code: q.filter.workspaceGroup.code.eq });
      }

      if (q?.filter?.groupCode?.eq) {
        queryBuilder.andWhere({
          groupCode: q?.filter?.groupCode?.eq
        });
      }

      //requestForSignatures
      if (q?.filter?.requestForSignatures?.signById?.in) {
        const signatureIds = (q?.filter?.requestForSignatures as any).signById.in;

        if (signatureIds != null) {
          queryBuilder.andWhere('sign.signById IN (:...requestForSignatures)', {
            requestForSignatures: signatureIds
          });
        }
      }

      // allFormCode
      if (q?.filter?.allFormCode?.eq) {
        queryBuilder.andWhere({
          allFormCode: q?.filter?.allFormCode?.eq
        });
      }

      // status
      if (q?.filter?.status?.eq) {
        queryBuilder.andWhere({
          status: q?.filter?.status?.eq
        });
      }

      // workspaceGroup
      if (q?.filter?.workspaceGroup?.id?.eq && q?.filter?.status?.eq !== ProjectDocumentStatus.Draft) {
        const workspaceGroupId = q?.filter?.workspaceGroup?.id?.eq;

        queryBuilder.andWhere({
          workspaceGroupId: workspaceGroupId
        });
      }

      let result = await queryBuilder.getMany();
      let totalCount = await queryBuilder.getCount();
      const reqForSignature = q?.filter?.requestForSignatures?.signById?.eq;

      if (reqForSignature) {
        // check if one of assignee
        result = result
          ?.map((obj: any) => {
            const isAssignee = obj?.requestForSignatures?.some(
              (sign: RequestForSignatureEntity) => sign?.signById === userId
            );

            if (obj?.workflow === workflowType.Linear || obj?.workflow === null) {
              // get request for signature with user id
              const isApprovedByMe = obj?.requestForSignatures?.some(
                (sign: RequestForSignatureEntity) => sign?.signById === userId && sign?.status === 'Approved'
              );

              if (isAssignee && !isApprovedByMe) return obj;
            } else {
              // get current doc if is current assignee
              const getCurrentDoc = obj.requestForSignatures?.find(
                reqObj => reqObj?.id === obj?.currentUserId && obj?.status !== 'Approved'
              );

              const isUserCurrentAssignee = getCurrentDoc?.signById === userId;

              if (getCurrentDoc && isUserCurrentAssignee) return obj;
            }
          })
          .filter(Boolean); // Filter out undefined values

        totalCount = result.length;
      }

      return { result, totalCount };
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'workspaceAllForm');
    }
  }

  async getProjectDocumentsByUser(userId: number, projectId: number, q: ProjectDocumentQuery) {
    try {
      if (
        // q.filter?.category?.eq === CategoryType.WorkProgramme ||
        q.filter?.category?.eq === CategoryType.Correspondence ||
        q.filter?.category?.eq === CategoryType.ProjectDocument
      )
        return this.getProjectDocumentsByUserRole(userId, projectId, q);

      const result = await this.getProjectDocuments(projectId, userId, q);

      return result;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getProjectDocumentsByUser');
    }
  }

  // Get Project Documents in Order (Folders then Documents)
  private async getProjectDocuments(projectId: number, userId: number, q: ProjectDocumentQuery) {
    try {
      const projectDocuments = this.projectDocumentRepo
        .createQueryBuilder('projectDocs')
        .where({
          projectId,
          ...(q.filter?.category?.eq ? { category: q.filter.category.eq } : {})
        })
        .orderBy(`CASE WHEN projectDocs.fileSystemType = 'Folder' THEN 0 ELSE 1 END`, 'ASC')
        .addOrderBy(
          `CASE 
            WHEN projectDocs.category = 'Photo' AND projectDocs.fileSystemType != 'Folder' 
            THEN projectDocs.createdAt 
            ELSE NULL 
          END`,
          'DESC'
        )
        .addOrderBy(
          `CASE 
            WHEN (
              (projectDocs.name REGEXP '^[0-9]+\\.[0-9]+$' AND projectDocs.name NOT REGEXP '[^0-9.]')
              OR (projectDocs.name REGEXP '^[0-9]+$')
            ) THEN 0  -- purely numeric or decimal
            WHEN projectDocs.name REGEXP '^[^a-zA-Z0-9]' THEN 1  -- starts with a non-alphanumeric character
            WHEN projectDocs.name REGEXP '^[0-9]+[^a-zA-Z0-9]' THEN 2  -- starts with a digit followed by a special character
            WHEN projectDocs.name REGEXP '^[0-9]+[a-zA-Z]' THEN 3  -- starts with a digit followed by text
            WHEN projectDocs.name REGEXP '^[a-zA-Z]+$' THEN 4  -- only letters
            WHEN projectDocs.name REGEXP '^[a-zA-Z]+[^a-zA-Z0-9]' 
              AND projectDocs.name NOT REGEXP '[0-9]' THEN 5  -- letters followed by special characters, no numbers
            ELSE 6
          END`,
          'ASC'
        )
        .addOrderBy(`projectDocs.name COLLATE utf8_general_ci`, 'ASC');

      if (!q.filter?.name?.like && !q.filter?.createdAt?.gte && !q.filter?.autosavedAt) {
        projectDocuments.andWhere({
          projectDocumentId: q.filter?.projectDocumentId?.eq
        });
      }

      if (q.filter?.name?.like) {
        const name = q.filter?.name?.like;
        if (name !== null) {
          projectDocuments.andWhere({
            name: Like(`%${name}%`)
          });
        }
      }

      if (q.filter?.fileSystemType?.eq) {
        projectDocuments.andWhere({
          fileSystemType: q.filter?.fileSystemType?.eq
        });
      }

      if (q.filter?.status?.in) {
        projectDocuments.andWhere('projectDocs.status IN (:...status)', {
          status: q.filter?.status?.in
        });
      }

      if (Array.isArray(q.filter?.allFormCode?.in) && q.filter.allFormCode.in.length > 0) {
        projectDocuments.andWhere('projectDocs.allFormCode IN (:...allFormCode)', {
          allFormCode: q.filter.allFormCode.in
        });
      }

      if (q.filter?.workspaceGroupId?.eq) {
        projectDocuments.andWhere({
          workspaceGroupId: q.filter?.workspaceGroupId?.eq
        });
      }

      if (q.filter?.requestForSignatures) {
        const signatureIds = (q.filter.requestForSignatures as any).signById.in;
        if (signatureIds != null) {
          projectDocuments.andWhere('sign.signById IN (:...requestForSignatures)', {
            requestForSignatures: signatureIds
          });
        }
      }

      if (q.filter?.workspaceCCs) {
        const ccIds = (q.filter?.workspaceCCs as any).ccId.in;
        if (ccIds != null) {
          projectDocuments.andWhere('cc.ccId IN (:...workspaceCCs)', {
            workspaceCCs: ccIds
          });
        }
      }

      if (q.filter?.status) {
        // if not equal then return all documents with status not equal to the status
        if (q.filter?.status.neq) {
          projectDocuments.andWhere('projectDocs.status != :status', {
            status: q.filter?.status.neq
          });
        } else {
          projectDocuments.andWhere('projectDocs.status = :status', {
            status: q.filter?.status.eq
          });
        }
      }

      if (q.filter?.addedBy) {
        // if equal then return all documents added by user (usage only on Draft)
        if (q.filter?.addedBy.eq) {
          projectDocuments.andWhere('projectDocs.addedBy = :addedBy', {
            addedBy: q.filter?.addedBy.eq
          });
        }
      }

      if (q.filter?.createdAt) {
        const createdAt = q.filter?.createdAt.gte;
        if (createdAt !== null) {
          // return all documents created on the date ignore the projectDocumentId
          projectDocuments.andWhere('DATE(projectDocs.createdAt) = :createdAt', { createdAt });
        }
        if (createdAt && q.filter?.projectDocumentId?.eq) {
          projectDocuments
            .andWhere('projectDocs.projectDocumentId = :projectDocumentId', {
              projectDocumentId: q.filter?.projectDocumentId?.eq
            })
            .andWhere('DATE(projectDocs.createdAt) = :createdAt', {
              createdAt
            });
        }
      }

      if (q.filter?.workspaceCCs) {
        const ccIds = (q.filter?.workspaceCCs as any).ccId.in;
        if (ccIds != null) {
          projectDocuments.andWhere('cc.ccId IN (:...workspaceCCs)', {
            workspaceCCs: ccIds
          });
        }
      }

      if (q.filter?.category?.in) {
        // Check if category is in the specified list of values
        projectDocuments.andWhere('projectDocs.category IN (:...categories)', {
          categories: q.filter.category.in
        });
      }

      if (q?.filter?.category?.eq === CategoryType.Photo) {
        if (q.filter?.projectDocumentId) {
          if (q.filter?.projectDocumentId.eq) {
            projectDocuments.andWhere('projectDocs.projectDocumentId = :projectDocumentId', {
              projectDocumentId: q.filter?.projectDocumentId?.eq
            });
          } else if (q.filter?.projectDocumentId.is == null) {
            projectDocuments.andWhere('projectDocs.projectDocumentId IS NULL');
          }
        }
      }

      // get autosaved documents only when explicitly requested
      if (q.filter?.autosavedAt?.isNot !== undefined && q.filter?.autosavedAt?.isNot === null) {
        projectDocuments.andWhere('projectDocs.autosavedAt IS NOT NULL');
      } else {
        projectDocuments.andWhere('projectDocs.autosavedAt IS NULL');
      }

      // add filter for isCommentResolve
      if (q.filter?.isCommentResolve) {
        projectDocuments.andWhere('projectDocs.isCommentResolve = :isCommentResolve', {
          isCommentResolve: q.filter.isCommentResolve.is
        });
      }

      // add filter for isBimPhoto
      if (q.filter?.isBimPhoto) {
        projectDocuments.andWhere('projectDocs.isBimPhoto = :isBimPhoto', {
          isBimPhoto: q.filter.isBimPhoto.is
        });
      }

      if (q?.sorting[0]?.field && q?.sorting[0]?.direction) {
        projectDocuments.orderBy(`projectDocs.${q.sorting[0].field}`, q.sorting[0].direction);
        projectDocuments?.skip(q?.paging?.offset).take(q?.paging?.limit);
        return await projectDocuments.getMany();
      }

      projectDocuments?.skip(q?.paging?.offset).take(q?.paging?.limit);

      return await projectDocuments.getMany();
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getProjectDocuments');
    }
  }

  private async getProjectDocumentsByUserRole(userId: number, projectId: number, q: ProjectDocumentQuery) {
    if (q?.filter?.name?.like.length > 0) {
      delete q?.filter?.projectDocumentId;
    }

    try {
      const projectDocumentsQueryBuilder = this.projectDocumentRepo
        .createQueryBuilder('project_documents')
        .leftJoin(
          ProjectDocumentUserEntity,
          'project_document_users',
          "project_document_users.projectDocumentId = SUBSTRING_INDEX(project_documents.mpath, '.', 1)"
        )
        .where(
          new Brackets(qb => {
            qb.where('project_documents.addedBy = :userId', { userId }).orWhere(
              'project_document_users.userId = :userId AND project_document_users.type = "Include"',
              { userId }
            );
          })
        )
        .andWhere('project_documents.projectId = :projectId', { projectId })
        .orderBy(
          `CASE 
            WHEN (
              (project_documents.name REGEXP '^[0-9]+\\.[0-9]+$' AND project_documents.name NOT REGEXP '[^0-9.]')
              OR (project_documents.name REGEXP '^[0-9]+$')
            ) THEN 0  -- purely numeric or decimal
            WHEN project_documents.name REGEXP '^[^a-zA-Z0-9]' THEN 1  -- starts with a non-alphanumeric character
            WHEN project_documents.name REGEXP '^[0-9]+[^a-zA-Z0-9]' THEN 2  -- starts with a digit followed by a special character
            WHEN project_documents.name REGEXP '^[0-9]+[a-zA-Z]' THEN 3  -- starts with a digit followed by text
            WHEN project_documents.name REGEXP '^[a-zA-Z]+$' THEN 4  -- only letters
            WHEN project_documents.name REGEXP '^[a-zA-Z]+[^a-zA-Z0-9]' 
              AND project_documents.name NOT REGEXP '[0-9]' THEN 5  -- letters followed by special characters, no numbers
            ELSE 6
          END`,
          'ASC'
        )
        .addOrderBy(`project_documents.name COLLATE utf8_general_ci`, 'ASC');

      if (q.filter?.category?.eq) {
        projectDocumentsQueryBuilder.andWhere('project_documents.category = :category', {
          category: q.filter.category.eq
        });
      }

      if (q.filter?.driveType?.eq) {
        projectDocumentsQueryBuilder.andWhere('project_documents.driveType = :driveType', {
          driveType: q.filter.driveType.eq
        });
      }

      if (q.filter?.name?.like) {
        projectDocumentsQueryBuilder.andWhere('project_documents.name LIKE :name', {
          name: !_.isEmpty(q.filter?.name?.like) ? `%${q.filter?.name?.like}%` : '%%'
        });
      } else {
        if (q.filter?.projectDocumentId?.eq) {
          projectDocumentsQueryBuilder.andWhere('project_documents.projectDocumentId = :projectDocumentId', {
            projectDocumentId: q.filter?.projectDocumentId?.eq
          });
        } else {
          projectDocumentsQueryBuilder.andWhere(
            new Brackets(qb => {
              qb.where('project_documents.projectDocumentId IS NULL');
            })
          );
        }
      }

      return await projectDocumentsQueryBuilder.getMany();
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getProjectDocumentsByUserRole');
    }
  }

  // Search Project Documents in Order (Folders then Documents)
  async searchProjectDocuments(data: any, projectId: number) {
    try {
      const searchResult = this.projectDocumentRepo
        .createQueryBuilder('search')
        .leftJoinAndSelect('search.owner', 'owner')
        .orderBy(`CASE WHEN search.fileSystemType = "${FileSystemType.Folder}" THEN 0 ELSE 1 END`, 'ASC')
        .addOrderBy('search.updatedAt', 'DESC')
        .where('search.category = :category', { category: data.category })
        .andWhere('search.projectId = :id', { id: projectId })
        .andWhere('search.name LIKE :names', { names: `%${data.name}%` });

      if (data.name == '' && data.projectDocumentId)
        searchResult.andWhere('projectDocumentId = :projectDocumentId', {
          projectDocumentId: data.projectDocumentId
        });
      else if (data.name == '' && !data.projectDocumentId) searchResult.andWhere('projectDocumentId IS NULL');

      return searchResult.getMany();
    } catch (e) {
      getErrorMessage(e, 'document service', 'searchProjectDocuments');
    }
  }

  // Filter Project Documents in Order (Folders then Documents)
  async filterProjectDocuments(data: any, projectId: number) {
    try {
      const searchResult = this.projectDocumentRepo
        .createQueryBuilder('search')
        .leftJoin('search.workspaceGroup', 'group')
        .addSelect('group.name')
        .leftJoin('search.requestForSignatures', 'sign')
        .addSelect(['sign.signById', 'sign.status'])
        .leftJoin('sign.signBy', 'name')
        .addSelect(['name.name', 'name.avatar'])
        .orderBy(`CASE WHEN search.fileSystemType = "${FileSystemType.Folder}" THEN 0 ELSE 1 END`, 'ASC')
        .addOrderBy('search.updatedAt', 'DESC')
        .where('search.category = :category', { category: data.category })
        .andWhere('search.projectId = :id', { id: projectId })
        .andWhere('search.name LIKE :names', { names: `%${data.name}%` })
        .andWhere('group.name LIKE :group', { group: `%${data.group}%` });
      // .andWhere('search.addedBy LIKE :addedBy', { addedBy: `%${data.addedBy}%` });

      if (data.status === 'DraftOnly') {
        searchResult.andWhere('search.status = :status', { status: 'Draft' });
      } else if (data.status === 'NotDraftOnly') {
        // filter by status not draft
        searchResult.andWhere('search.status NOT LIKE :status', {
          status: '%Draft%'
        });
      } else {
        searchResult.andWhere('search.status LIKE :status', {
          status: `%${data.status}%`
        });
      }

      if (data.assignedTo == '') {
      } else if (data.assignedTo != '') {
        searchResult.andWhere('name.name LIKE :assignedTo', {
          assignedTo: `%${data.assignedTo}%`
        });
      }

      if (data.allFormCode == '') {
      } else if (data.allFormCode != '') {
        searchResult.andWhere('search.allFormCode = :allFormCode', {
          allFormCode: data.allFormCode
        });
      }

      if (
        data.name == '' &&
        data.status == '' &&
        data.allFormCode == '' &&
        data.group == '' &&
        data.assignedTo == '' &&
        data.projectDocumentId
      )
        searchResult.andWhere('search.projectDocumentId = :projectDocumentId', {
          projectDocumentId: data.projectDocumentId
        });
      else if (
        data.name == '' &&
        data.status == '' &&
        data.allFormCode == '' &&
        data.group == '' &&
        data.assignedTo == '' &&
        data.projectDocumentId
      )
        searchResult.andWhere('search.projectDocumentId IS NULL');

      return searchResult.getMany();
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'filterProjectDocuments');
    }
  }

  // Get Project Document Breadcrumb
  async getProjectDocumentsBreadcrumb(id: number, projectId: number, userId: number) {
    try {
      const projectDocument = await this.projectDocumentRepo.findOne({
        id,
        projectId
      });
      if (!projectDocument) throw new BadRequestException('Project document not found');

      const unsortBreadcrumb = await getTreeRepository(ProjectDocumentEntity).findAncestorsTree(projectDocument);

      // Get breadcrumb in ancestors sequence
      let breadcrumb = [unsortBreadcrumb];
      let parentDocument = unsortBreadcrumb;

      while (parentDocument.parentFolder) {
        parentDocument = parentDocument.parentFolder;
        breadcrumb = [parentDocument, ...breadcrumb];
      }

      if (
        projectDocument.category === CategoryType.Correspondence ||
        projectDocument.category === CategoryType.ProjectDocument
      ) {
        const parentFolder = breadcrumb[0];

        if (parentDocument.addedBy === userId) {
          return breadcrumb;
        }

        const projectDocumentUsers = await this.projectDocumentUserRepo.find({
          where: {
            projectDocumentId: parentFolder.id,
            userId,
            type: ProjectDocumentUserPermissionType.Include
          }
        });
        if (projectDocumentUsers.length > 0) {
          return breadcrumb;
        }

        return [];
      }

      return breadcrumb;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getProjectDocumentsBreadcrumb');
    }
  }

  // get drawings pdftron
  async getDrawingsPdfTron(projectId: number, input: { folderId?: number; documentId: number }) {
    if (!projectId) throw new BadRequestException('Project Id not found');

    try {
      //get total count of drawings
      const totalDrawings = await this.projectDocumentRepo.count({
        where: {
          projectId,
          category: CategoryType.TwoDDrawings,
          fileSystemType: FileSystemType.Document,
          projectDocumentId: input?.folderId || null
        }
      });

      const result = await this.queryDrawingsPdfTron(projectId, input, totalDrawings, 0);

      // Find the index of the current documentId
      const currentIndex = result.findIndex(item => item.id === input.documentId);

      // Get the next and previous document IDs
      const nextId = currentIndex < result.length - 1 ? result[currentIndex + 1].id : null;
      const previousId = currentIndex > 0 ? result[currentIndex - 1].id : null;

      return { id: input.documentId, nextId, previousId };
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getDrawingsPdfTron');
    }
  }

  // get drawings pdftron
  private async queryDrawingsPdfTron(
    projectId: number,
    input: { folderId?: number; documentId: number; name?: string },
    limit: number,
    offset: number
  ) {
    let result;
    let queryBuilder;

    while (!result || result.length === limit) {
      queryBuilder = await this.projectDocumentRepo
        .createQueryBuilder('projectDoc')
        .select(['projectDoc.id', 'projectDoc.name', 'projectDoc.createdAt'])
        .where('projectDoc.projectId = :projectId', { projectId: projectId })
        .andWhere('projectDoc.category = :category', {
          category: CategoryType.TwoDDrawings
        })
        .andWhere('projectDoc.FileSystemType = :fileType', {
          fileType: FileSystemType.Document
        })
        .andWhere('projectDoc.deletedAt IS NULL')
        .skip(offset)
        .take(limit);

      if (input.name) {
        queryBuilder.andWhere('projectDoc.name LIKE :name', {
          name: `%${input.name}%`
        });
      } else if (input.folderId !== null && input.folderId !== undefined) {
        queryBuilder.andWhere('projectDoc.projectDocumentId = :projectDocumentId', {
          projectDocumentId: input.folderId
        });
      } else {
        queryBuilder.andWhere('projectDoc.projectDocumentId IS NULL');
      }

      result = await queryBuilder.getMany();

      // Check if the desired documentId is in the current result set
      const foundDocument = result.find(doc => doc.id === input.documentId);

      if (foundDocument) {
        // DocumentId found, break out of the loop or handle accordingly
        break;
      }

      // Increment offset for the next iteration
      offset += limit;
    }

    const sortedProjectDocuments = result.sort((a, b) => {
      const aName = a?.name?.trimStart();
      const bName = b?.name?.trimStart();

      // ?? sort by number then name
      const aMatch: any = aName.match(/^(\d+)/) || [0];
      const bMatch: any = bName.match(/^(\d+)/) || [0];
      const aNum = parseInt(aMatch[1]);
      const bNum = parseInt(bMatch[1]);

      return aNum - bNum || aName.localeCompare(bName, undefined, { numeric: true });
    });

    return sortedProjectDocuments;
  }

  // Get Last Parent Document based on string path
  async getLastParentDocumentFromBreadcrumb(input: GetLastParentDocumentFromBreadcrumbDTO, projectId: number) {
    try {
      const { path, category } = input;

      const pathArray = _.split(path, '/');
      let projectDocumentId: number = null;
      let projectFolder: ProjectDocumentEntity = null;

      for (const p of pathArray) {
        const projectDocument = await this.projectDocumentRepo
          .createQueryBuilder()
          .where({
            projectId,
            category,
            projectDocumentId
          })
          .andWhere(`BINARY name = '${p}'`)
          .getOne();

        if (!projectDocument) throw new BadRequestException('Project document not found');

        if (p === _.last(pathArray)) {
          if (projectDocument.fileSystemType === FileSystemType.Folder) {
            projectFolder = projectDocument;
          } else {
            projectFolder = await this.projectDocumentRepo.findOne({
              id: projectDocument.projectDocumentId
            });
          }
        } else {
          projectDocumentId = projectDocument.id;
        }
      }
      return projectFolder;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getLastParentDocumentFromBreadcrumb');
    }
  }

  // Create All Form Once Standard Form have been edited
  async createAllFormFromStandardForm(standardFormId: number, xfdf: string, projectId: number, userId: number) {
    try {
      // Get File Key of the Standard Form that will be duplicated
      const standardForm = await this.projectDocumentRepo.findOne({
        id: standardFormId
      });
      if (!standardForm) throw new BadRequestException('Standard form not found');

      const sourceFolder = replaceSpaceWithDash(_.startCase(standardForm.category));
      const sourceFileName = _.last(_.split(standardForm.fileUrl, '/'));
      const sourceFileKey = `${sourceFolder}/${sourceFileName}`;

      // Set File Key of the All Form that will be created
      const destFolder = replaceSpaceWithDash(_.startCase(CategoryType.AllForm));
      const randomId = await nanoid(15);
      const destFileName = `${randomId}-${replaceSpaceWithDash(standardForm.name)}`;
      const destFileKey = `${destFolder}/${destFileName}`;
      const duplicateFile = await this.tmOneService.duplicateFile(sourceFileKey, destFileKey);
      let docs = await this.projectDocumentRepo
        .createQueryBuilder('entity')
        .where('entity.category = :category', { category: `AllForm` })
        .andWhere('entity.projectId = :id', { id: projectId })
        .getCount();
      docs = docs + 1;

      const newAllForm = this.projectDocumentRepo.create({
        projectId,
        addedBy: userId,
        formCategoryId: standardForm.formCategoryId,
        fileKey: duplicateFile.fileKey,
        name: standardForm.name,
        fileSystemType: FileSystemType.Document,
        type: standardForm.type,
        fileSize: standardForm.fileSize,
        category: CategoryType.AllForm,
        status: ProjectDocumentStatus.Draft,
        fileChannel: FileChannelTypes.OBS,
        allFormCode: docs,
        xfdf
      });

      return await this.projectDocumentRepo.save(newAllForm);
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'createAllFormFromStandardForm');
    }
  }

  async getFolderNameAndFileUrl(id: number, projectId: number) {
    try {
      const folder = await this.projectDocumentRepo.findOne({ id, projectId });

      if (!folder) throw new BadRequestException('Folder not found');
      if (folder.fileSystemType !== FileSystemType.Folder)
        throw new BadRequestException('The selected item is not a folder');

      // Get Zip File/Folder Name
      const folderName = folder.name;

      // Get all files in the folder
      const documentEntities = await getTreeRepository(ProjectDocumentEntity)
        .createDescendantsQueryBuilder('projectDocumentsEntity', 'projectDocumentsClosure', folder)
        .andWhere({ fileSystemType: FileSystemType.Document })
        .getMany();

      return { folderName, documentEntities };
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getFolderNameAndFileUrl');
    }
  }

  async downloadBulkFileAndFolderZip(ids: [number], projectId: number) {
    try {
      const projectDocuments = await this.projectDocumentRepo.findByIds(ids, {
        where: { projectId }
      });

      if (projectDocuments.length === 0) throw new BadRequestException('No file/folder selected');

      const zip = new JSZip();
      const folder = zip.folder('files');

      for (const projectDocument of projectDocuments) {
        if (projectDocument.fileSystemType === FileSystemType.Folder) {
          const { folderName, documentEntities } = await this.getFolderNameAndFileUrl(projectDocument.id, projectId);

          for (const documentEntity of documentEntities) {
            if (!documentEntity.fileKey) throw new BadRequestException('No Document provided');
            const fileName = documentEntity.name;
            const url: string = await this.getPresignedUrl(documentEntity, 'fileKey');
            const config: any = { responseType: 'arraybuffer' };
            const prefix = await nanoid(5);

            let file: any;

            if (projectDocument.fileChannel === FileChannelTypes.FORGE) {
              file = await this.autodeskService.downloadForgeFile(
                projectDocument.fileUrl // will actually return the urn
              );
            } else {
              file = await axios
                .get(url, { ...config })
                .then(data => {
                  return data.data;
                })
                .catch(() => {
                  throw new BadRequestException('Invalid URL provided');
                });
            }

            // remove extension from file name and add prefix at the end of the file name and add extension back to the file name
            const fileExtension = _.last(_.split(fileName, '.'));
            const newFileName = `${_.replace(fileName, '.' + fileExtension, '')}-${prefix}.${fileExtension}`;

            folder.file(`${folderName}/${newFileName}`, new Uint8Array(file));
          }
        } else {
          if (!projectDocument.fileKey) throw new BadRequestException('No Document provided');

          const fileName = projectDocument.name;
          const config: any = { responseType: 'arraybuffer' };
          const prefix = await nanoid(5);
          let file: any;

          if (projectDocument.fileChannel === FileChannelTypes.FORGE) {
            file = await this.autodeskService.downloadForgeFile(projectDocument.fileUrl);
          } else {
            const signedUrl = await this.tmOneService.getPresignedUrl(projectDocument.fileKey);
            file = await axios
              .get(signedUrl.SignedUrl, { ...config })
              .then(data => {
                return data.data;
              })
              .catch(() => {
                throw new BadRequestException('Invalid URL provided');
              });
          }

          // remove extension from file name and add prefix at the end of the file name and add extension back to the file name
          const fileExtension = _.last(_.split(fileName, '.'));
          const newFileName = `${_.replace(fileName, '.' + fileExtension, '')}-${prefix}.${fileExtension}`;

          folder.file(newFileName, new Uint8Array(file));

          // folder.file(prefix + '-' + fileName, file);
        }
      }

      return zip;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'downloadBulkFileAndFolderZip');
    }
  }

  // this function may take a while to execute, so it's better to put put spinner or progress bar
  async downloadZipFile(rootDocumentId: number): Promise<Buffer> {
    const zip = new JSZip();

    const rootFolder = await this.projectDocumentRepo.findOne(rootDocumentId);
    const descendantFolder = await getTreeRepository(ProjectDocumentEntity).findDescendantsTree(rootFolder, {
      relations: ['children']
    });

    if (descendantFolder) {
      await this.addDocumentToZip(descendantFolder, zip, '');
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    return zipBuffer;
  }

  private async addDocumentToZip(document: ProjectDocumentEntity, zip: JSZip, path: string, depth = 0) {
    if (depth >= 11) return;

    // Define the current path by appending the document name to the path
    const currentPath = path.length > 0 ? `${path}/${document.name}` : document.name;

    if (document.fileSystemType !== FileSystemType.Document) {
      // Recursively add each child document to the ZIP
      if (document.children && document.children.length > 0) {
        for (const child of document.children) {
          // Call recursively and pass in the current path
          await this.addDocumentToZip(child, zip, currentPath, depth + 1);
        }
      }
    } else {
      // If this is a file, add it to the current path in the ZIP
      if (document.fileUrl) {
        // const fileData = await this.downloadFile(document.fileUrl);
        // zip.file(currentPath, fileData);

        let uniqueName = currentPath;
        let counter = 1; // Start counter for duplicates

        // Check if a file with the same name already exists in the zip
        while (zip.file(uniqueName)) {
          const extensionIndex = uniqueName.lastIndexOf('.');
          const baseName = extensionIndex !== -1 ? uniqueName.substring(0, extensionIndex) : uniqueName;
          const extension = extensionIndex !== -1 ? uniqueName.substring(extensionIndex) : '';

          // Regex to match 'name(number)' format
          const namePattern = /^(.*?)(\(\d+\))?$/;
          const match = baseName.match(namePattern);

          if (match) {
            const base = match[1];
            uniqueName = `${base}(${counter})${extension}`;
          } else {
            uniqueName = `${baseName}(${counter})${extension}`;
          }

          counter++; // Increment the counter for the next duplicate
        }

        const presignedUrl = await this.getPresignedUrl(document, 'fileKey');
        const fileData = await this.downloadFile(presignedUrl);
        // Convert Buffer to Uint8Array properly
        const fileDataArray = new Uint8Array(fileData.buffer, fileData.byteOffset, fileData.byteLength);
        zip.file(uniqueName, fileDataArray);
      }
    }
  }

  private async downloadFile(signedUrl: string): Promise<Buffer> {
    try {
      const response = await axios.get(signedUrl, {
        responseType: 'arraybuffer'
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to download file: ${error.message}`);
    }
  }

  // To get latest 10 Correspondence In and Out Documents
  async getCorrespondenceIn(projectId: number) {
    try {
      const correspondenceIn = await this.projectDocumentRepo.findOne({
        projectId,
        name: CorrespondenceType.CorrespondenceIn
      });
      return await this.getLatestTenCorrespondenceFiles(correspondenceIn);
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getCorrespondenceIn');
    }
  }

  async getCorrespondenceOut(projectId: number) {
    try {
      const correspondenceOut = await this.projectDocumentRepo.findOne({
        projectId,
        name: CorrespondenceType.CorrespondenceOut
      });
      return await this.getLatestTenCorrespondenceFiles(correspondenceOut);
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getCorrespondenceOut');
    }
  }

  async deleteProjectDocument(id: number, projectId: number, userId: number) {
    try {
      const idArray = [id];
      return this.deleteProjectDocuments(idArray, projectId, userId);
    } catch (err) {
      getErrorMessage(err, 'ProjectDocumentService', 'deleteProjectDocument');
    }
  }

  async deleteProjectDocuments(ids: number[], projectId: number, userId: number) {
    try {
      const projectUserRole = await this.getProjectUserRole(projectId, userId);

      const projectDocuments = await this.projectDocumentRepo
        .createQueryBuilder()
        .where({ projectId, id: In(ids) })
        .getMany();

      if (projectDocuments.length === 0) throw new BadRequestException('Documents not found');

      projectDocuments.forEach(projectDocument => {
        if (
          (projectDocument.addedBy !== userId || projectDocument.category !== CategoryType.Photo) &&
          projectUserRole === ProjectUserRoleType.CanView
        )
          throw new BadRequestException(`You are not allowed to delete ${projectDocument.name}`);
      });

      const user = await getRepository(UserEntity).findOne({ id: userId });

      await Promise.all(
        projectDocuments.map(async projectDocument => {
          if (projectDocument.category == CategoryType.Photo) {
            const msg =
              user.name +
              ' deleted the media' +
              (projectDocument.fileSystemType == FileSystemType.Document ? ' file ' : ' folder with its contents ') +
              projectDocument.name;
            const auditLog = await getRepository(AuditLogEntity).create({
              userId: userId,
              projectId: projectId,
              // taskId: projectDocument.id,
              resourceId: projectDocument.id,
              module: AuditLogModuleType.Photo,
              action: AuditLogActionType.RemovePhoto,
              content: msg
            });
            await auditLog.save();
          } else if (
            projectDocument.category == CategoryType.TwoDDrawings ||
            projectDocument.category == CategoryType.BIMDrawings
          ) {
            const msg =
              user.name +
              ' deleted the drawing' +
              (projectDocument.fileSystemType == FileSystemType.Document ? ' file ' : ' folder with its contents ') +
              projectDocument.name;
            const auditLog = await getRepository(AuditLogEntity).create({
              userId: userId,
              projectId: projectId,
              // taskId: projectDocument.id,
              resourceId: projectDocument.id,
              module: AuditLogModuleType.Drawing,
              action: AuditLogActionType.Delete,
              content: msg
            });
            await auditLog.save();
          }
          // AUDIT LOG FOR CLOUD DOCS (DELETE)
          else if (
            ((projectDocument.category === CategoryType.ProjectDocument ||
              projectDocument.category === CategoryType.Correspondence) &&
              projectDocument.driveType === ProjectDocumentDriveType.Shared) ||
            projectDocument.category === CategoryType.WorkProgramme
          ) {
            // UPDATEDBY COLUMN IN DATABASE TO BE RESOLVED
            const user = await getRepository(UserEntity).findOne({
              where: { id: userId }
            });
            const msg =
              user.name +
              ' deleted the' +
              (projectDocument.fileSystemType == FileSystemType.Document
                ? ' document '
                : ' folder with its contents ') +
              projectDocument.name +
              ' in ' +
              projectDocument.category.replace(/([a-z])([A-Z])/g, '$1 $2');

            const auditLog = await getRepository(AuditLogEntity).create({
              userId: user.id,
              projectId: projectDocument.projectId,
              // taskId: projectDocument.id,
              resourceId: projectDocument.id,
              module: AuditLogModuleType.CloudDocument,
              action: AuditLogActionType.Delete,
              content: msg
            });
            await auditLog.save();
          }

          // SWITCHING PROJECT_DOCUMENT_ID TO DELETED
          projectDocument.deletedProjectDocumentId = projectDocument.projectDocumentId;
          projectDocument.projectDocumentId = null;

          // SOFT-DELETING ALL SHARED USERS // COMMENTED SO CAN REUSE SHARED USER DURING RESTORE
          // await this.projectDocumentUserRepo.softDelete({ projectDocumentId: In(descendantsIds) });

          // SOFT-DELETING SELF AND ALL CHILDREN DOCUMENTS
          const descendants = await getTreeRepository(ProjectDocumentEntity).findDescendants(projectDocument);
          const descendantsIds = await descendants.map(descendant => descendant.id);

          projectDocument.deletedChildrenIds = JSON.stringify(descendantsIds);
          await projectDocument.save();

          await this.projectDocumentRepo.softDelete(descendantsIds);
        })
      );

      return true;
    } catch (err) {
      getErrorMessage(err, 'ProjectDocumentService', 'deleteProjectDocuments');
    }
  }

  private async getLatestTenCorrespondenceFiles(correspondenceInOrOut: ProjectDocumentEntity) {
    try {
      const correspondenceInDescendants = await getTreeRepository(ProjectDocumentEntity).findDescendants(
        correspondenceInOrOut,
        { depth: 50 }
      );
      const correspondenceInDocuments = correspondenceInDescendants.filter(correspondenceInDocument => {
        return correspondenceInDocument.fileSystemType === FileSystemType.Document;
      });
      const latestTenDocuments = _.take(_.orderBy(correspondenceInDocuments, ['updatedAt'], ['desc']), 10);
      return latestTenDocuments;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getLatestTenCorrespondenceFiles');
    }
  }

  async updateProjectDocumentParent(id: number | number[], parentId: number, projectId: number, userId: number) {
    try {
      if (Array.isArray(id)) {
        const documents = await this.projectDocumentRepo.findByIds(id, {
          relations: ['children']
        });
        const manager = this.projectDocumentRepo.manager;

        const BATCH_SIZE = 300;
        await manager.transaction(async transactionalEntityManager => {
          try {
            // Fetch the parent folder once to avoid redundant queries
            const parentFolder = await transactionalEntityManager.findOne(ProjectDocumentEntity, {
              id: parentId,
              projectId
            });

            if (!parentFolder) {
              throw new Error(`Parent folder with ID ${parentId} not found in project ${projectId}`);
            }

            // Split documents into batches
            for (let i = 0; i < documents.length; i += BATCH_SIZE) {
              const batch = documents.slice(i, i + BATCH_SIZE).map(document => {
                document.parentFolder = parentFolder;
                document.updatedBy = userId;
                return document;
              });

              // Bulk save the batch
              await transactionalEntityManager.save(ProjectDocumentEntity, batch);
            }
          } catch (error) {
            throw error; // Re-throw to ensure transaction is rolled back
          }
        });
        // AUDIT LOG CLOUD DOCS (MOVING DOCUMENT / FOLDER)
        if (
          ((documents[0].parentFolder.category === CategoryType.ProjectDocument ||
            documents[0].parentFolder.category === CategoryType.Correspondence) &&
            documents[0].parentFolder.driveType === ProjectDocumentDriveType.Shared) ||
          documents[0].parentFolder.category === CategoryType.WorkProgramme
        ) {
          const user = await getRepository(UserEntity)
            .createQueryBuilder('user')
            .select(['user.id', 'user.name'])
            .where('user.id = :userId', { userId })
            .getOne();
          const msg =
            user.name +
            ' moved total ' +
            documents.length +
            ' content(s) into ' +
            documents[0].parentFolder.name +
            ' in ' +
            documents[0].parentFolder.category.replace(/([a-z])([A-Z])/g, '$1 $2');

          const auditLog = getRepository(AuditLogEntity).create({
            userId,
            projectId,
            resourceId: parentId,
            module: AuditLogModuleType.CloudDocument,
            action: AuditLogActionType.Update,
            content: msg
          });
          await auditLog.save();
        }
        // AUDIT LOG DRAWINGS (MOVING FILE / FOLDER)
        else if (
          documents[0].parentFolder.category === CategoryType.TwoDDrawings ||
          documents[0].parentFolder.category === CategoryType.BIMDrawings
        ) {
          const user = await getRepository(UserEntity)
            .createQueryBuilder('user')
            .select(['user.id', 'user.name'])
            .where('user.id = :userId', { userId })
            .getOne();
          const msg =
            user.name +
            ' moved total ' +
            documents.length +
            ' content(s) into ' +
            documents[0].parentFolder.name +
            ' in ' +
            documents[0].parentFolder.category.replace(/([a-z])([A-Z])/g, '$1 $2');

          const auditLog = getRepository(AuditLogEntity).create({
            userId,
            projectId,
            // taskId: entity.id,
            resourceId: parentId,
            module: AuditLogModuleType.Drawing,
            action: AuditLogActionType.Update,
            content: msg
          });
          await auditLog.save();
        } else if (
          documents[0].parentFolder.category === CategoryType.AllForm ||
          documents[0].parentFolder.category === CategoryType.StandardForm
        ) {
          const user = await getRepository(UserEntity)
            .createQueryBuilder('user')
            .select(['user.id', 'user.name'])
            .where('user.id = :userId', { userId })
            .getOne();
          const msg =
            user.name +
            ' moved total ' +
            documents.length +
            ' content(s) into ' +
            documents[0].parentFolder.name +
            ' in ' +
            documents[0].parentFolder.category.replace(/([a-z])([A-Z])/g, '$1 $2');

          const auditLog = getRepository(AuditLogEntity).create({
            userId,
            projectId,
            // taskId: entity.id,
            resourceId: parentId,
            module: AuditLogModuleType.Workspace,
            action: AuditLogActionType.Update,
            content: msg
          });
          await auditLog.save();
        }
      } else {
        const document = await this.projectDocumentRepo.findOne(id);
        document.parentFolder =
          (await this.projectDocumentRepo.findOne({
            id: parentId,
            projectId
          })) || null;
        document.updatedBy = userId;
        await this.projectDocumentRepo.save(document);
      }
      return true;
    } catch (err) {
      getErrorMessage(err, 'ProjectDocumentService', 'updateProjectDocumentParent');
    }
  }

  async assignAttachments(input: AssignWorkspaceAttachmentInputDTO, userId: number, projectId: number) {
    try {
      const { projectDocumentId, attachments } = input;
      const entity = await this.projectDocumentRepo.findOne({
        where: { id: projectDocumentId },
        relations: ['workspaceAttachments']
      });

      const removed = entity.workspaceAttachments.filter(function (obj) {
        return !attachments.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (removed.length > 0) {
        const user = await getRepository(UserEntity).findOne({ id: userId });

        let names = '';
        removed.forEach((element, index) => {
          names = names + element.name;
          if (index != removed.length - 1) names = names + ', ';
        });
        const msg = user.name + ' deleted attachment(s) ' + names;

        const auditLog = getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.AddAttachment,
          content: msg
        });
        await auditLog.save();

        await this.workspaceAttachmentRepo.softRemove(removed);
      }

      const added = attachments.filter(function (obj) {
        return !entity.workspaceAttachments.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (added.length > 0) await this.workspaceAttachmentRepo.save(added);

      return entity;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'assignAttachments');
    }
  }

  async assignPhotos(input: AssignWorkspacePhotoInputDTO, userId: number) {
    try {
      const { projectDocumentId, photos } = input;
      const entity = await this.projectDocumentRepo.findOne({
        where: { id: projectDocumentId },
        relations: ['workspacePhotos']
      });

      const removed = entity.workspacePhotos.filter(function (obj) {
        return !photos.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (removed.length > 0) {
        const user = await getRepository(UserEntity).findOne({ id: userId });

        let names = '';
        removed.forEach((element, index) => {
          names = names + element.name;
          if (index != removed.length - 1) names = names + ', ';
        });
        const msg = user.name + ' deleted media(s) ' + names;

        const auditLog = getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Workspace,
          action: AuditLogActionType.RemovePhoto,
          content: msg
        });
        await auditLog.save();

        await this.workspacePhotoRepo.softRemove(removed);
      }

      const added = photos.filter(function (obj) {
        return !entity.workspacePhotos.some(function (obj2) {
          return obj.id == obj2.id;
        });
      }) as any;

      if (added.length > 0) await this.workspacePhotoRepo.save(added);

      return entity;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'assignPhotos');
    }
  }

  async getBIMStatus(urn: string): Promise<{ status: string; progress: string; failedDerivative?: any }> {
    try {
      const token: string = await this.autodeskService.getAutodeskAuth();
      const url = `${process.env.FORGE_API_URL}/modelderivative/v2/designdata/${urn}/manifest`;

      const response: any = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      if (response.status === 404) {
        return { status: 'failed', progress: 'complete' };
      }

      const jsonResponse: ApiResponse = await response.json();
      const allDerivativesCompleted: boolean =
        jsonResponse.derivatives?.every(
          derivative => derivative.status === 'success' && derivative.progress === 'complete'
        ) ?? false;

      const failedDerivative = jsonResponse.derivatives?.find(derivative => derivative.status === 'failed');

      return { status: jsonResponse.status, progress: jsonResponse.progress, failedDerivative: failedDerivative };
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'getBIMStatus');
    }
  }

  async translateDwgToSvf(urn: string, config: any, haveReference: boolean) {
    try {
      return await this.autodeskService.convertDrawingsToSVF(urn, config, haveReference);
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'translateDwgToSvf');
    }
  }

  async getDocumentID(ID: string) {
    try {
      //get document by watermark ID
      const document = await this.projectDocumentRepo.findOne({
        where: { watermarkId: ID }
      });
      if (!document) throw new BadRequestException('Document not found');
      const presignedUrl = await this.getPresignedUrl(document, 'fileKey');
      return presignedUrl;
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'getDocumentID');
    }
  }

  async storeProjectDocument(input: StoreProjectDocumentInputDTO, userId: number, projectId: number) {
    try {
      let document;
      if (input.ids.length === 0) {
        throw new BadRequestException('No document selected');
      }

      await this.projectDocumentRepo.manager.transaction(async transactionalEntityManager => {
        await transactionalEntityManager.update(ProjectDocumentEntity, { id: In(input.ids) }, { isDocsStored: true });

        // Retrieve and create new documents
        const updatedDocuments = await transactionalEntityManager.findByIds(ProjectDocumentEntity, input.ids, {
          relations: ['workspaceAttachments', 'workspacePhotos', 'workspaceDocuments']
        });

        document = updatedDocuments[0];

        const newDocumentsPromises = updatedDocuments.map(async doc => {
          const fileUrl = await this.getPresignedUrl(doc, 'fileKey');
          const { key, size } = await this.runMergePdf(
            fileUrl,
            doc?.workspaceAttachments,
            doc.workspacePhotos,
            doc?.workspaceDocuments,
            doc?.name
          );

          return {
            category: input.category,
            currentUserId: null,
            projectDocumentId: input.folderId,
            projectId: projectId,
            name: doc.name,
            fileKey: key,
            fileSystemType: doc.fileSystemType,
            type: doc.type,
            fileSize: size,
            status: doc.status,
            fileChannel: doc.fileChannel,
            xfdf: doc.xfdf,
            addedBy: userId,
            driveType: ProjectDocumentDriveType.Shared,
            obsUrl: key
          };
        });

        // Use Promise.all to wait for all promises to resolve
        const newDocuments = await Promise.all(newDocumentsPromises);

        // Now you can save the resolved documents
        return await transactionalEntityManager.save(ProjectDocumentEntity, newDocuments);
      });
      return document;
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'storeProjectDocument');
    }
  }

  async runMergePdf(
    documentUrl: string,
    workspaceAttachments: WorkspaceAttachmentEntity[],
    workspacePhotos: WorkspacePhotoEntity[],
    workspaceDocuments: WorkspaceDocumentEntity[],
    title: string
  ) {
    // Get presigned URLs for all attachments
    const allPdfs = [
      { fileUrl: documentUrl, type: 'pdf' },
      ...(await Promise.all((workspaceAttachments ?? []).map(async (doc) => {
        const presignedUrl = await this.getPresignedUrl(doc as any, 'fileKey');
        return {
          ...doc,
          fileUrl: presignedUrl
        };
      }))),
      ...(await Promise.all((workspacePhotos ?? []).map(async (doc) => {
        const presignedUrl = await this.getPresignedUrl(doc as any, 'fileKey');
        return {
          ...doc,
          fileUrl: presignedUrl
        };
      }))),
      ...(await Promise.all((workspaceDocuments ?? []).map(async (doc) => {
        const presignedUrl = await this.getPresignedUrl(doc as any, 'fileKey');
        return {
          ...doc,
          fileUrl: presignedUrl
        };
      })))
    ];
    try {
      const { folder, file, finalPdfBytes, size } = await mergePdfs(allPdfs as any, title);

      // Convert size from bytes to megabytes and round to 2 decimal places
      const sizeInMB = parseFloat((size / (1024 * 1024)).toFixed(2));

      const { key } = await this.fileService.uploadGqlFile(file as FileUpload, folder, null, finalPdfBytes);

      return { key, size: sizeInMB };
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'runMergePdf');
    }
  }

  // probably one off script
  async setSubgroup() {
    try {
      const projectDocuments = await this.projectDocumentRepo
        .createQueryBuilder('projectDocs')
        .leftJoin('projectDocs.workspaceGroup', 'group')
        .addSelect(['group.id', 'group.name', 'group.workspaceGroupId'])
        .where('projectDocs.workspaceGroupId IS NOT NULL')
        .getMany();

      for (const document of projectDocuments) {
        const child = await getRepository(WorkspaceGroupEntity).findOne({
          where: { workspaceGroupId: document.workspaceGroupId }
        });

        if (child) {
          await this.projectDocumentRepo.update(
            { workspaceGroupId: document.workspaceGroupId },
            { workspaceGroupId: child.id }
          );
        }
      }
    } catch (error) {
      // Handle the error here
    }
  }

  async setSubGroupCode() {
    const BATCH_SIZE = 25;
    let offset = 1500;
    let hasMore = true;

    while (hasMore) {
      const projectDocumentsBatch = await this.projectDocumentRepo
        .createQueryBuilder('projectDocs')
        .where('projectDocs.category = :category', {
          category: CategoryType.AllForm
        })
        .withDeleted()
        .select(['projectDocs.id', 'projectDocs.name', 'projectDocs.groupCode', 'projectDocs.projectId'])
        .leftJoin('projectDocs.workspaceGroup', 'group')
        .addSelect(['group.id', 'group.name'])
        .leftJoin('group.documents', 'parentDocuments')
        .addSelect(['parentDocuments.id', 'parentDocuments.status'])
        .leftJoin('group.parent', 'parent')
        .addSelect(['parent.id', 'parent.name'])
        .leftJoin('parent.children', 'children')
        .addSelect(['children.id', 'children.name'])
        .leftJoin('children.documents', 'documents')
        .addSelect(['documents.id', 'documents.name'])
        .andWhere('projectDocs.status != :status', {
          status: ProjectDocumentStatus.Draft
        })
        .orderBy('projectDocs.id') // ensure a consistent ordering
        .skip(offset)
        .take(BATCH_SIZE)
        .getMany();

      if (projectDocumentsBatch.length < BATCH_SIZE) {
        hasMore = false; // This would be our last batch
      }

      function extractDocumentIds(group) {
        const documentIds = [];

        if (group.documents) {
          for (const document of group.documents) {
            documentIds.push(document.id);
          }
        }

        if (group.children) {
          for (const childGroup of group.children) {
            documentIds.push(...extractDocumentIds(childGroup));
          }
        }

        return documentIds;
      }

      for (const projectDocument of projectDocumentsBatch) {
        const parentGroup = projectDocument.workspaceGroup.parent;
        if (
          projectDocument.workspaceGroup.name !== 'Ungroup Documents' &&
          projectDocument.workspaceGroup.name !== 'Site Diary'
        ) {
          const allDocumentIds = [];
          if (parentGroup && parentGroup.children) {
            for (const childGroup of parentGroup.children) {
              const documentIds = extractDocumentIds(childGroup);
              allDocumentIds.push(...documentIds);
            }
          }
          allDocumentIds.sort((a, b) => a - b);
          // Initialize a variable to store the index of the array containing projectId
          let foundIndex = -1;
          // Iterate through the allDocumentIds array to find the index
          for (let i = 0; i < allDocumentIds.length; i++) {
            if (allDocumentIds[i] === projectDocument.id) {
              foundIndex = i;
              break; // Stop the loop once a match is found
            }
          }
          await this.projectDocumentRepo.update({ id: projectDocument.id }, { groupCode: foundIndex + 1 });
        } else {
          if (projectDocument.workspaceGroup.name === 'Site Diary' && projectDocument.status !== 'Draft') {
            const docIds = [];
            let foundIndex = -1;
            projectDocument.workspaceGroup.documents.forEach(document => {
              if (document.status !== 'Draft') docIds.push(document.id);
            });
            docIds.sort((a, b) => a - b);
            for (let i = 0; i < docIds.length; i++) {
              if (docIds[i] === projectDocument.id) {
                foundIndex = i;
                break;
              }
            }
            await this.projectDocumentRepo.update({ id: projectDocument.id }, { groupCode: foundIndex + 1 });
          } else if (
            projectDocument.workspaceGroup.name === 'Ungroup Documents' &&
            projectDocument.status !== 'Draft'
          ) {
            const docIds = [];
            let foundIndex = -1;
            projectDocument.workspaceGroup.documents.forEach(document => {
              if (document.status !== 'Draft') {
                docIds.push(document.id);
              }
            });
            docIds.sort((a, b) => a - b);
            for (let i = 0; i < docIds.length; i++) {
              if (docIds[i] === projectDocument.id) {
                foundIndex = i;
                break;
              }
            }

            await this.projectDocumentRepo.update({ id: projectDocument.id }, { groupCode: foundIndex + 1 });
          }
        }
      }
      //? kasi database bernafas bro
      await new Promise(resolve => setTimeout(resolve, 2000));
      offset += BATCH_SIZE;
    }

    return true;
  }

  async fixUngroupDocuments() {
    const projectDocuments = await this.projectDocumentRepo.find({
      select: ['id', 'name', 'workspaceGroupId', 'projectId'],
      withDeleted: true,
      where: {
        workspaceGroupId: 1,
        projectId: Not(1)
      }
    });

    for (const projectDocument of projectDocuments) {
      const parentGroup = await getRepository(WorkspaceGroupEntity).findOne({
        where: {
          projectId: projectDocument.projectId,
          name: 'Ungroup Documents'
        }
      });
      await this.projectDocumentRepo.update({ id: projectDocument.id }, { workspaceGroupId: parentGroup.id });
    }

    return projectDocuments;
  }

  async updateWorkspaceWorkflow(input: UpdateWorkspaceFlowInputDTO) {
    try {
      await getRepository(RequestForSignatureEntity).softDelete({
        projectDocumentId: input.id
      });

      await this.projectDocumentRepo.update({ id: input.id }, { workflow: input.workflow });

      return input?.workflow;
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'updateWorkspaceWorkflow');
    }
  }

  async fixWorkflowDocuments() {
    try {
      const batchSize = 1000;
      let offset = 0;

      while (true) {
        const projectDocuments = await this.projectDocumentRepo.find({
          where: {
            workflow: null,
            category: CategoryType.AllForm
          },
          take: batchSize,
          skip: offset
        });

        if (projectDocuments.length === 0) {
          break;
        }

        for (const document of projectDocuments) {
          await this.projectDocumentRepo.update(
            { id: document.id },
            {
              workflow: workflowType.Linear,
              //? prevent updatedAt from being updated
              updatedAt: document?.updatedAt
            }
          );
        }

        offset += batchSize;
      }

      return true;
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'fixWorkflowDocuments');
    }
  }

  async copyDrawingToDrawingRevision() {
    try {
      //  create drawing revision by drawing loop by 10
      const drawings = await this.projectDocumentRepo.find({
        where: {
          category: CategoryType.TwoDDrawings,
          fileSystemType: FileSystemType.Document,
          deletedAt: null
        }
      });

      const batchSize = 10;
      let offset = 0;
      // let processed = 0;

      while (true) {
        const batchDrawings = drawings.slice(offset, offset + batchSize);

        if (batchDrawings.length === 0) {
          break;
        }

        for (const drawing of batchDrawings) {
          const drawingRevisionEntity = new DrawingRevisionEntity();
          drawingRevisionEntity.projectDocumentId = drawing.id;
          drawingRevisionEntity.fileKey = drawing.fileKey;
          drawingRevisionEntity.fileName = drawing.name;
          drawingRevisionEntity.version = 1;

          await getRepository(DrawingRevisionEntity).insert(drawingRevisionEntity);
        }

        offset += batchSize;
      }

      return 'All drawings have been processed.';
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'copyDrawingToDrawingRevision');
    }
  }

  // Get Project Documents in Order (Folders then Documents)
  async getDeletedProjectDocuments(projectId: number, q: DeletedProjectDocumentQuery, user: AuthData) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const projectDocuments = this.projectDocumentRepo
        .createQueryBuilder('projectDocs')
        .withDeleted()
        .where('projectDocs.deletedAt IS NOT NULL')
        .andWhere('projectDocs.permanentlyDeleted = :permanentlyDeleted', {
          permanentlyDeleted: false
        })
        .andWhere('projectDocs.deletedAt >= :startDate', { startDate })
        .andWhere('projectDocs.deletedAt <= :endDate', { endDate })
        .andWhere('projectDocs.projectDocumentId IS NULL')
        .andWhere({
          projectId,
          ...(q.filter?.category?.eq ? { category: q.filter.category.eq } : {})
        })
        .leftJoinAndSelect('projectDocs.workspaceGroup', 'workspaceGroup')
        .leftJoinAndSelect('workspaceGroup.parent', 'parent')
        .leftJoinAndSelect('projectDocs.workspaceCCs', 'cc')
        .leftJoinAndSelect('projectDocs.owner', 'owner')
        .leftJoinAndSelect('projectDocs.requestForSignatures', 'requestForSignatures')
        .leftJoinAndSelect('requestForSignatures.signBy', 'signBy')
        .orderBy(`CASE WHEN projectDocs.fileSystemType = "${FileSystemType.Folder}" THEN 0 ELSE 1 END`, 'ASC');

      if (q.filter?.category?.in?.includes(CategoryType.ProjectDocument)) {
        const projectUserRole = await this.getProjectUserRole(projectId, user.id);

        // If user is a project owner or cloud coordinator, they can view all shared documents and their own documents
        if (
          projectUserRole === ProjectUserRoleType.ProjectOwner ||
          projectUserRole === ProjectUserRoleType.CloudCoordinator
        ) {
          projectDocuments.andWhere(
            new Brackets(qb => {
              qb.where('projectDocs.driveType = :driveType', { driveType: ProjectDocumentDriveType.Shared })
                .orWhere('projectDocs.addedBy = :addedBy', { addedBy: user.id })
                .orWhere('projectDocs.category IN (:...categories)', { categories: q.filter.category.in });
            })
          );
        } else {
          // If user is not a project owner or cloud coordinator, they can only view their own documents
          projectDocuments.andWhere('projectDocs.addedBy = :addedBy AND projectDocs.category = :category', {
            addedBy: user.id,
            category: CategoryType.ProjectDocument
          });
        }
      }

      if (q.filter?.name?.like) {
        const name = q.filter?.name?.like;
        if (name !== null) {
          projectDocuments.andWhere({
            name: Like(`%${name}%`)
          });
        }
      }

      if (q.filter?.driveType?.eq) {
        projectDocuments.andWhere({
          driveType: q.filter.driveType.eq
        });
      }

      if (q?.filter.status) {
        if (q?.filter.status.eq) {
          projectDocuments.andWhere('projectDocs.status = :status', {
            status: q?.filter.status.eq
          });
        } else if (q?.filter.status.neq) {
          projectDocuments.andWhere('projectDocs.status != :status', {
            status: q?.filter.status.neq
          });
        }
      }

      if (q?.filter?.addedBy && q?.filter?.addedBy?.eq) {
        projectDocuments.andWhere('projectDocs.addedBy = :addedBy', {
          addedBy: q?.filter?.addedBy?.eq
        });
      }

      if (q.filter?.category?.in) {
        // Check if category is in the specified list of values
        projectDocuments.andWhere('projectDocs.category IN (:...categories)', {
          categories: q.filter.category.in
        });
      }

      if (q?.sorting[0]?.field && q?.sorting[0]?.direction) {
        projectDocuments.orderBy(`projectDocs.${q.sorting[0].field}`, q.sorting[0].direction);
        projectDocuments?.skip(q?.paging?.offset).take(q?.paging?.limit);
        const res = await projectDocuments.getMany();
        return res;
      }

      const getProjectDocuments = await projectDocuments.getMany();
      const sortedProjectDocuments = getProjectDocuments.sort((a, b) => {
        // ?? Sort by Folder then file
        if (a.fileSystemType === FileSystemType.Folder && b.fileSystemType !== FileSystemType.Folder) {
          return -1;
        } else if (a.fileSystemType !== FileSystemType.Folder && b.fileSystemType === FileSystemType.Folder) {
          return 1;
        }

        if (a.category === CategoryType.Photo) {
          const aCreatedAt = moment(a.createdAt);
          const bCreatedAt = moment(b.createdAt);

          // If timestamps are different, sort by timestamp in descending order (latest first)
          if (!aCreatedAt.isSame(bCreatedAt)) {
            return bCreatedAt.diff(aCreatedAt);
          }
        }

        const aName = a?.name?.trimStart();
        const bName = b?.name?.trimStart();

        // ?? sort by number then name
        const aMatch: any = aName.match(/^(\d+)/) || [0];
        const bMatch: any = bName.match(/^(\d+)/) || [0];
        const aNum = parseInt(aMatch[1]);
        const bNum = parseInt(bMatch[1]);

        return aNum - bNum || aName.localeCompare(bName, undefined, { numeric: true });
      });

      const result = sortedProjectDocuments.slice(q.paging.offset, q.paging.offset + q.paging.limit);
      return result;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'getDeletedProjectDocuments');
    }
  }

  // DEPRECATED, CAN BE REMOVED
  async restoreOneDocument(id, projectId: number, userId: number) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const projectUserRole = await this.getProjectUserRole(projectId, userId);

      if (projectUserRole === ProjectUserRoleType.CanView)
        throw new BadRequestException(`You are not allowed to perform restore action`);

      const projectDocument = await getRepository(ProjectDocumentEntity)
        .createQueryBuilder('restoreDoc')
        .withDeleted()
        .where('restoreDoc.id = :id', { id })
        .andWhere('restoreDoc.deletedAt IS NOT NULL')
        .andWhere('restoreDoc.permanentlyDeleted = :permanentlyDeleted', {
          permanentlyDeleted: false
        })
        .andWhere('restoreDoc.deletedAt >= :startDate', { startDate })
        .andWhere('restoreDoc.deletedAt <= :endDate', { endDate })
        .andWhere('restoreDoc.projectDocumentId IS NULL')
        .getOne();

      if (!projectDocument) throw new BadRequestException('The document cannot be found, please try again');

      if (projectDocument.deletedProjectDocumentId) {
        const temp = await this.projectDocumentRepo
          .createQueryBuilder('projectDoc')
          .where({ projectId, id: projectDocument.deletedProjectDocumentId })
          .getOne();

        if (!temp)
          throw new BadRequestException(
            `Directory folder does not exist anymore, unable to restore ${projectDocument.name}.`
          );
      }

      projectDocument.projectDocumentId = projectDocument.deletedProjectDocumentId;
      projectDocument.deletedProjectDocumentId = null;
      await projectDocument.save();

      await this.projectDocumentRepo.recover(projectDocument);

      const ids = JSON.parse(projectDocument.deletedChildrenIds);
      ids?.map?.(async id => {
        const doc = await getRepository(ProjectDocumentEntity)
          .createQueryBuilder('restoreDoc')
          .withDeleted()
          .where('restoreDoc.id = :id', { id })
          .getOne();

        await this.projectDocumentRepo.recover(doc);
      });
      return projectDocument;
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'restoreOneDocument');
    }
  }

  async restoreManyDocuments(ids: number[], projectId: number, userId: number) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const projectUserRole = await this.getProjectUserRole(projectId, userId);

      if (projectUserRole === ProjectUserRoleType.CanView)
        throw new BadRequestException(`You are not allowed to perform restore action`);

      const projectDocuments = await this.projectDocumentRepo
        .createQueryBuilder('restoreDocs')
        .withDeleted()
        .where({ projectId, id: In(ids) })
        .andWhere('restoreDocs.deletedAt IS NOT NULL')
        .andWhere('restoreDocs.permanentlyDeleted = :permanentlyDeleted', {
          permanentlyDeleted: false
        })
        .andWhere('restoreDocs.deletedAt >= :startDate', { startDate })
        .andWhere('restoreDocs.deletedAt <= :endDate', { endDate })
        .andWhere('restoreDocs.projectDocumentId IS NULL')
        .leftJoinAndSelect('restoreDocs.children', 'children')
        .getMany();

      if (projectDocuments.length === 0)
        throw new BadRequestException('One or more of the document(s) not found, please try again');

      // const user = await getRepository(UserEntity).findOne({ id: userId });

      await Promise.all(
        projectDocuments.map(async projectDocument => {
          try {
            if (projectDocument.deletedProjectDocumentId) {
              const temp = await this.projectDocumentRepo
                .createQueryBuilder('projectDoc')
                .where({
                  projectId,
                  id: projectDocument.deletedProjectDocumentId
                })
                .getOne();

              if (!temp)
                throw new BadRequestException(
                  `Directory folder does not exist anymore, unable to restore ${projectDocument.name}.`
                );
            }

            projectDocument.projectDocumentId = projectDocument.deletedProjectDocumentId;
            projectDocument.deletedProjectDocumentId = null;
            await projectDocument.save();

            await this.projectDocumentRepo.recover(projectDocument);

            const ids = JSON.parse(projectDocument.deletedChildrenIds);
            ids.map(async id => {
              const doc = await getRepository(ProjectDocumentEntity)
                .createQueryBuilder('restoreDoc')
                .withDeleted()
                .where('restoreDoc.id = :id', { id })
                .getOne();

              await this.projectDocumentRepo.recover(doc);
            });
          } catch (e) {
            getErrorMessage(e, 'ProjectDocumentService', 'restoreManyDocumentsOriginDeleted');
          }
        })
      );

      return true;
    } catch (err) {
      getErrorMessage(err, 'ProjectDocumentService', 'restoreManyDocuments');
    }
  }

  // DEPRECATED, CAN BE REMOVED
  async permanentDeleteOneDocument(id, projectId: number, userId: number) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const projectUserRole = await this.getProjectUserRole(projectId, userId);

      if (
        projectUserRole !== ProjectUserRoleType.ProjectOwner &&
        projectUserRole !== ProjectUserRoleType.CloudCoordinator
      )
        throw new BadRequestException(`You are not allowed to perform permanent delete action`);

      const builder = await getRepository(ProjectDocumentEntity)
        .createQueryBuilder('permDeleteDoc')
        .withDeleted()
        .where('permDeleteDoc.id = :id', { id })
        .andWhere('permDeleteDoc.deletedAt IS NOT NULL')
        .andWhere('permDeleteDoc.permanentlyDeleted = :permanentlyDeleted', {
          permanentlyDeleted: false
        })
        .andWhere('permDeleteDoc.deletedAt >= :startDate', { startDate })
        .andWhere('permDeleteDoc.deletedAt <= :endDate', { endDate })
        .andWhere('permDeleteDoc.projectDocumentId IS NULL')
        .getOne();

      if (!builder) throw new BadRequestException('The document cannot be found, please try again');

      if (!builder.deletedAt) throw new BadRequestException('This document cannot be permanently deleted.');

      builder.permanentlyDeleted = true;

      const descendants = await getTreeRepository(ProjectDocumentEntity).findDescendants(builder);

      await this.projectDocumentUserRepo.softDelete({
        projectDocumentId: builder.id
      });

      if (descendants.length > 0) {
        const descendantsIds = descendants.map(descendant => descendant.id);
        await this.projectDocumentUserRepo.softDelete(descendantsIds);
      }

      return await builder.save();
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'permanentDeleteOneDocument');
    }
  }

  async permanentDeleteManyDocuments(ids: number[], projectId: number, userId: number) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);

      const endDate = new Date();
      endDate.setHours(23, 59, 59, 999);

      const projectUserRole = await this.getProjectUserRole(projectId, userId);

      if (
        projectUserRole !== ProjectUserRoleType.ProjectOwner &&
        projectUserRole !== ProjectUserRoleType.CloudCoordinator
      )
        throw new BadRequestException(`You are not allowed to perform permanent delete action`);

      const projectDocuments = await this.projectDocumentRepo
        .createQueryBuilder('permDeleteDocs')
        .withDeleted()
        .where({ projectId, id: In(ids) })
        .andWhere('permDeleteDocs.deletedAt IS NOT NULL')
        .andWhere('permDeleteDocs.permanentlyDeleted = :permanentlyDeleted', {
          permanentlyDeleted: false
        })
        .andWhere('permDeleteDocs.deletedAt >= :startDate', { startDate })
        .andWhere('permDeleteDocs.deletedAt <= :endDate', { endDate })
        .andWhere('permDeleteDocs.projectDocumentId IS NULL')
        .getMany();

      if (projectDocuments.length === 0)
        throw new BadRequestException('One or more of the document(s) not found, please try again');

      // const user = await getRepository(UserEntity).findOne({ id: userId });

      Promise.all(
        projectDocuments.map(async projectDocument => {
          projectDocument.permanentlyDeleted = true;

          const descendants = await getTreeRepository(ProjectDocumentEntity).findDescendants(projectDocument);

          await this.projectDocumentUserRepo.softDelete({
            projectDocumentId: projectDocument.id
          });

          if (descendants.length > 0) {
            const descendantsIds = descendants.map(descendant => descendant.id);
            await this.projectDocumentUserRepo.softDelete(descendantsIds);
          }
          await projectDocument.save();
        })
      );

      return true;
    } catch (err) {
      getErrorMessage(err, 'ProjectDocumentService', 'permanentDeleteManyDocuments');
    }
  }

  async fetchUserIds(projectDocumentId) {
    const users = await this.projectDocumentUserRepo.find({
      where: {
        projectDocumentId,
        type: ProjectDocumentUserPermissionType.Include
      },
      select: ['userId']
    });
    return users.map(user => user.userId);
  }

  areArraysSimilar(arr1, arr2) {
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const notInSet2 = [...set1].filter(id => !set2.has(id));
    const notInSet1 = [...set2].filter(id => !set1.has(id));

    return {
      areSimilar: set1.size === set2.size && notInSet2.length === 0 && notInSet1.length === 0,
      notInSet1,
      notInSet2
    };
  }

  async getAllDocumentIds(documents: any): Promise<any> {
    const allIds: number[] = [];
    for (const document of documents) {
      if (document.fileSystemType === FileSystemType.Folder) {
        const descendants = await getTreeRepository(ProjectDocumentEntity).findDescendants(document);
        const descendantsIds = descendants.map(descendant => descendant.id);
        allIds.push(...descendantsIds);
      } else {
        allIds.push(document.id);
      }
    }
    return allIds;
  }

  async syncGDrive(input: GDriveSyncInputDTO, projectId: number, user: AuthData) {
    try {
      const promises = await input?.docs?.map(async doc => {
        // convert bytes to mb
        const fileSize = doc?.sizeBytes / 1024 / 1024;

        try {
          const url = `https://www.googleapis.com/drive/v3/files/${doc.id}?alt=media&key=${process.env.GDRIVE_API_KEY}`;

          let fileType;
          if (doc?.mimeType?.includes('pdf')) {
            fileType = 'pdf';
          } else if (doc?.mimeType?.includes('png')) {
            fileType = 'png';
          } else if (doc?.mimeType?.includes('jpeg')) {
            fileType = 'jpeg';
          } else if (doc?.mimeType?.includes('jpg')) {
            fileType = 'jpg';
          }

          const response = await fetch(url, {
            headers: {
              Authorization: `Bearer ${input?.accessToken}` // Replace with your actual access token
            }
          });

          const buffer = await response.buffer();

          if (!response.ok) {
            getErrorMessage(response, 'ProjectDocumentService', 'syncGDrive');
            // Handle the error, throw an exception, or return a meaningful response.
            return;
          }

          const file = {
            filename: doc?.name,
            mimetype: doc?.mimeType,
            encoding: '7bit'
          };

          if (doc) {
            const folder = 'Project-Document';
            const { key } = await this.fileService.uploadGqlFile(file as any, folder, null, buffer);

            // create project document
            const res = await this.projectDocumentRepo.insert({
              projectId: projectId,
              name: doc?.name,
              fileKey: key,
              type: fileType,
              fileSystemType: input.fileSystemType,
              projectDocumentId: Number(input.folderId),
              category: input.category,
              fileSize: Number(fileSize.toFixed(2)),
              obsFileSize: doc?.sizeBytes,
              addedBy: user.id,
              driveType: input?.driveType
            });
            const id = res?.generatedMaps[0]?.id;

            const parent = await this.projectDocumentRepo
              .createQueryBuilder()
              .select('*')
              .where({ id: input.folderId })
              .getRawOne();

            await this.projectDocumentRepo.query(
              `UPDATE project_documents SET mpath = '${parent.mpath ? parent.mpath + id : id}.' WHERE id = '${id}'`
            );
          }
        } catch (e) {
          getErrorMessage(e, 'ProjectDocumentService', 'syncGDrive');
        }
      });

      // Wait for all promises to resolve
      await Promise.all(promises);

      return { message: 'GDrive Synced Successfully' };
    } catch (e) {
      getErrorMessage(e, 'ProjectDocumentService', 'syncGDrive');
    }
  }

  async getPresignedUrl(project_document: ProjectDocumentEntity, type: 'fileKey' | 'obsKey' | 'videoThumbnailKey') {
    try {
      const keyAndFallback = { fileKey: 'fileUrl', obsKey: 'obsUrl', videoThumbnailKey: 'videoThumbnail' };
      let key = project_document[type];
      // fallback if key is missing
      if (!key) {
        const fileName = project_document[keyAndFallback[type]];
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);
      return signedUrl.SignedUrl;
    } catch (error) {
      return null;
    }
  }

  async signKey(url: string) {
    if (url.includes('https://')) {
      return url;
    }

    return (await this.tmOneService.getPresignedUrl(url)).SignedUrl;
  }

  async linkPdftronDocument(input: LinkPdftronDocumentInputDTO): Promise<LinkPdftronDocumentResponse> {
    const { urls, title } = input;

    const urlPromise = urls.map(async url => {
      const signedUrl = await this.signKey(url);
      return { fileUrl: signedUrl, type: 'pdf' };
    });

    const allPdfs = await Promise.all(urlPromise);

    try {
      if (allPdfs.length === 0) {
        throw new BadRequestException('No document selected');
      }
      const { folder, file, finalPdfBytes } = await mergePdfs(allPdfs as any[], title);

      const { key } = await this.fileService.uploadGqlFile(file as FileUpload, folder, null, finalPdfBytes);
      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return {
        url: signedUrl.SignedUrl
      };
    } catch (error) {
      getErrorMessage(error, 'linkPdftronDocument', 'linkPdftronDocument');
    }
  }

  async emailBimCompletion(input: EmailBimCompletionDTO, user: AuthData) {
    const { id } = user;

    //find user use query builder
    const userDetails = await getRepository(UserEntity)
      .createQueryBuilder('user')
      .select(['user.email', 'user.name'])
      .where('user.id = :id', { id })
      .getOne();

    // get document details
    const document = await getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .select(['projectDocument.id', 'projectDocument.name', 'projectDocument.obsFileSize', 'projectDocument.type'])
      .where('projectDocument.id = :id', { id: input.id })
      .getOne();

    let iconUrl;
    if (document.type === 'dwg') {
      iconUrl =
        'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/public-assets/drawing-link-icon/dwg.png';
    } else if (document.type === 'rvt') {
      iconUrl =
        'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/public-assets/drawing-link-icon/rvt.png';
    } else if (document.type === 'nwd') {
      iconUrl =
        'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/public-assets/drawing-link-icon/nwd.png';
    } else if (document.type === 'fbx') {
      iconUrl =
        'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/public-assets/drawing-link-icon/fbx.png';
    }

    const file = {
      name: document.name,
      size: document?.obsFileSize ? `${(document.obsFileSize / (1024 * 1024)).toFixed(2)} MB` : '0',
      redirect: `${process.env.APP_URI}drawings/BIM-drawing?documentId=${input?.id}`,
      iconUrl: iconUrl
    };

    try {
      await this.mailgunService.sendMail({
        to: userDetails?.email,
        subject: 'Completion of BIM Model',
        template: 'completion_of_uploading_bim',
        file: file,
        owner: userDetails?.name,
        email: userDetails?.email
      });

      return { msg: 'Complete' };
    } catch (error) {
      getErrorMessage(error, 'linkPdftronDocument', 'linkPdftronDocument');
    }
  }

  async createOrUpdateProjectDocumentAutosave(
    input: CreateOrUpdateProjectDocumentAutosaveInputDTO,
    projectId: number,
    userId: number
  ): Promise<ProjectDocumentEntity> {
    try {
      const { id, fileUrl, autosavedAt, xfdf, category, projectDocumentId } = input;
      let projectDocument;

      if (!id) {
        projectDocument = new ProjectDocumentEntity();
        projectDocument.projectId = projectId;
        projectDocument.addedBy = userId;
        projectDocument.category = category;
        projectDocument.projectDocumentId = projectDocumentId;
      } else {
        projectDocument = await this.projectDocumentRepo.findOne(id);

        if (!projectDocument) {
          throw new BadRequestException('Project Document not found');
        }
      }

      if (fileUrl && _.isObject(fileUrl)) {
        const { key, filename, type } = await this.fileService.uploadGqlFile(fileUrl as FileUpload);
        projectDocument.fileUrl = key; // reset the column
        projectDocument.fileKey = key;
        projectDocument.name = filename;
        projectDocument.type = type;
      }

      projectDocument.autosavedAt = autosavedAt;
      projectDocument.xfdf = xfdf;

      // save the project document
      projectDocument = await this.projectDocumentRepo.save(projectDocument);

      return projectDocument;
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'createOrUpdateProjectDocumentAutosave');
    }
  }

  async autosavedProjectDocumentCount(projectId: number, userId: number, query): Promise<number> {
    try {
      return await this.projectDocumentRepo.count({
        where: {
          projectId,
          addedBy: userId,
          autosavedAt: Not(IsNull()),
          category: query.filter.category.eq
        }
      });
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'getAutosavedProjectDocumentCount');
    }
  }

  async bimCombine(input: BIMCombineDTO, user: AuthData) {
    const { parent, references } = input;
    //combine parent and references
    const urns = [parent, ...references];

    try {
      const token: string = await this.autodeskService.getAutodeskAuth();

      if (references?.length > 0) {
        const data = {
          urn: parent.urn,
          filename: parent.filename,
          references: references?.map((obj: any) => {
            return {
              urn: obj?.urn,
              filename: obj.filename
              // relativePath: ''
            };
          })
        };

        await axios.post(
          `https://developer.api.autodesk.com/modelderivative/v2/designdata/${parent?.urnInBase64}/references`,
          data,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );
      }

      await this.translateDwgToSvf(parent.urnInBase64, token, references?.length > 1);
      const manifestRes = await this.checkTranslationProgress(token, parent.urnInBase64, parent, user);

      const metadata = {
        parentFilename: parent.filename,
        processingLog: manifestRes?.processingLog,
        urns: urns?.map((obj: any) => obj)
      };

      const jsonMetadata = JSON.stringify(metadata);

      this.projectDocumentRepo.update(
        { id: Number(parent.id) },
        { autoDeskMetadata: jsonMetadata as any, category: CategoryType.BIMDrawings }
      );

      if (references?.length > 0) {
        this.projectDocumentRepo.softDelete(references?.map((obj: any) => Number(obj.id)));
      }

      return { msg: 'BIM Combined Successfully' };
    } catch (error) {
      getErrorMessage(error, 'ProjectDocumentService', 'bimCombine');
    }
  }

  async delay(ms: number) {
    resolve => setTimeout(resolve, ms);
  }

  // Poll translation status
  async checkTranslationProgress(accessToken: string, urnInBase64: string, parent: any, user: AuthData) {
    const axios = require('axios');
    try {
      const response = await axios.get(
        `https://developer.api.autodesk.com/modelderivative/v2/designdata/${urnInBase64}/manifest`,
        {
          headers: { Authorization: `Bearer ${accessToken}` }
        }
      );

      const manifest = response.data;
      let processingLogMsg = '';

      if (manifest.status === 'failed' || manifest.status === 'success') {
        if (manifest.derivatives && manifest.derivatives.length > 0) {
          manifest.derivatives.forEach((derivative: any) => {
            if (derivative.messages && derivative.messages.length > 0) {
              derivative.messages.forEach((message: any) => {
                processingLogMsg += `${message.type} - ${message.message}\n`;
              });
            }
          });

          if (manifest.status === 'success') {
            this.emailBimCompletion({ id: parent.id }, user);
          }

          return { processingLog: processingLogMsg };
        } else if (manifest.messages && manifest.messages.length > 0) {
          manifest.messages.forEach((message: any) => {
            processingLogMsg += `${message.type} - ${message.message}\n`;
          });
        }
      } else {
        await this.delay(5000); // Wait for 5 seconds before checking again
        return await this.checkTranslationProgress(accessToken, urnInBase64, parent, user);
      }
    } catch (error) {
      console.error('Error while retrieving translation messages:', error);
    }
  }

  // get all workspacegroup ids that user can access
  // to do: cache this
  async getAccessibleWorkspaceGroupId(userId: number, projectId: number) {
    const accessibleWorkspaceGroupId = await getRepository('workspace_groups')
      .createQueryBuilder('parent')
      .leftJoin('workspace_group_users', 'workspace_group_users', 'parent.id = workspace_group_users.workspaceGroupId')
      .leftJoin('workspace_groups', 'children', 'children.workspaceGroupId = parent.id')
      // .leftJoin('users', 'users', 'users.id = workspace_group_users.userId')//to be confirmed, macam tak perlu pun join users
      .select('parent.id', 'parentId')
      .addSelect('GROUP_CONCAT(children.id)', 'childrenIds')
      .where('parent.projectId = :projectId', { projectId })
      .andWhere('parent.workspaceGroupId IS NULL')
      .andWhere(
        new Brackets(qb => {
          qb.where('workspace_group_users.userId = :userId', { userId }).orWhere(
            'workspace_group_users.userId IS NULL'
          );
        })
      )
      .groupBy('parent.id')
      .addGroupBy('workspace_group_users.userId')
      .getRawMany();

    const accessibleWorkspaceGroupIds = [
      ...new Set(
        accessibleWorkspaceGroupId
          .map(item => [item.parentId, ...(item.childrenIds ? [...item.childrenIds.split(',').map(Number)] : [])])
          .flat()
      )
    ];

    return accessibleWorkspaceGroupIds;
  }

  async transferCloudDocsOwnership(projectId: number, userId: number, newUserId: number) {
    // cloud docs
    const cloudDocs = await getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .where('projectId = :projectId', { projectId })
      .andWhere('addedBy = :userId', { userId })
      .andWhere('projectDocument.category IN (:...categories)', {
        categories: [CategoryType.ProjectDocument, CategoryType.WorkProgramme, CategoryType.Correspondence]
      })
      .getMany();

    cloudDocs.map(async doc => {
      doc.addedBy = newUserId;
      doc.updatedAt = doc.updatedAt;
      return await doc.save();
    });

    // workspace
    const workspaceDocuments = await getRepository(ProjectDocumentEntity)
      .createQueryBuilder('projectDocument')
      .where('projectId = :projectId', { projectId })
      .andWhere('addedBy = :userId', { userId })
      .andWhere('projectDocument.category IN (:...categories)', {
        categories: [CategoryType.AllForm, CategoryType.StandardForm]
      })
      .andWhere('projectDocument.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [ProjectDocumentStatus.Approved, ProjectDocumentStatus.Rejected]
      })
      .getMany();

    workspaceDocuments.map(async doc => {
      doc.addedBy = newUserId;
      doc.updatedAt = doc.updatedAt;
      return await doc.save();
    });

    // request for signature
    const requestForSignatures = await getRepository(RequestForSignatureEntity)
      .createQueryBuilder('requestForSignature')
      .leftJoin('project_documents', 'projectDocument', 'projectDocument.id = requestForSignature.projectDocumentId')
      .where('projectDocument.projectId = :projectId', { projectId })
      .andWhere('projectDocument.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [ProjectDocumentStatus.Approved, ProjectDocumentStatus.Rejected]
      })
      .andWhere('requestForSignature.signById = :userId', { userId })
      .andWhere('requestForSignature.status NOT IN (:...excludedStatuses)', {
        excludedStatuses: [RequestForSignatureStatus.Approved, RequestForSignatureStatus.Rejected]
      })
      .getMany();

    requestForSignatures.map(async doc => {
      doc.signById = newUserId;
      doc.updatedAt = doc.updatedAt;
      return await doc.save();
    });

    await getRepository(ProjectDocumentUserEntity)
      .createQueryBuilder('projectDocumentUser')
      .where('project_document_users.userId = :userId', { userId })
      .softDelete()
      .execute();

    const user = await getRepository(UserEntity).findOne({
      id: userId
    });

    user.removedAt = new Date();
    user.save();

    // put value in contactEmail deleteAt
    const contactEmail = await getRepository(ContactsEmailEntity).findOne({
      userId: userId,
      projectId
    });

    contactEmail.deletedAt = new Date();
    contactEmail.save();

    return true;
  }

  async getParentProjectDocumentUsers(projectDocument: ProjectDocumentEntity) {
    const project_document = await this.projectDocumentRepo
      .createQueryBuilder('project_documents')
      .select("SUBSTRING_INDEX(project_documents.mpath, '.', 1) as parentId")
      .where({ id: projectDocument.id })
      .getRawOne();

    if (!project_document?.parentId) return [];

    // Fetch users from the parent document instead of the current one
    return await this.projectDocumentUserRepo.find({
      where: { projectDocumentId: project_document.parentId }
    });
  }

  private async getProjectUserRole(projectId: number, userId: number) {
    return await getRepository(ProjectUserEntity)
      .findOne({
        projectId,
        userId
      })
      .then(projectUser => projectUser.role);
  }
}
