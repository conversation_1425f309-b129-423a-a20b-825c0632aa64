import JwtConfig from '@configs/jwt.config';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { InjectRepository } from '@nestjs/typeorm';
import { AuthData } from '@types';
import { ExtractJwt, Strategy as JWTStrategy } from 'passport-jwt';
import { AdminEntity } from 'src/modules/admin/entity/admin.entity';
import { UserEntity } from 'src/modules/user/entity/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AdminJwtAuthStrategy extends PassportStrategy(JWTStrategy, 'admin-jwt') {
  constructor(
    @InjectRepository(AdminEntity)
    private adminRepository: Repository<AdminEntity>
  ) {
    super({
      passReqToCallback: true,
      secretOrKey: JwtConfig.adminAccessTokenConfig.secret,
      ignoreExpiration: false,
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken()
    });
  }
  async validate(req: any, payload: AuthData) {
    const admin = await this.adminRepository.findOne({ id: payload.id });
    if (!admin) throw new UnauthorizedException();
    return payload;
  }
}

@Injectable()
export class UserJwtAuthStrategy extends PassportStrategy(JWTStrategy, 'user-jwt') {
  constructor(
    @InjectRepository(UserEntity)
    private UserRepository: Repository<UserEntity>
  ) {
    super({
      passReqToCallback: true,
      secretOrKey: JwtConfig.userAccessTokenConfig.secret,
      ignoreExpiration: false,
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken()
    });
  }
  async validate(req: any, payload: AuthData) {
    const user = await this.UserRepository.findOne({ id: payload.id });
    if (!user) throw new UnauthorizedException();
    return { ...payload, type: 'USER' };
  }
}
