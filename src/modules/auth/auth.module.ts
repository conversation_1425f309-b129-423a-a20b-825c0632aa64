import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminEntity } from 'src/modules/admin/entity/admin.entity';
import { AdminAuthController } from './admin-auth.controller';
import { AdminAuthService } from './admin-auth.service';
import { AdminJwtAuthStrategy, UserJwtAuthStrategy } from './strategy/auth.strategy';
import { UserAuthController } from './user-auth.controller';
import { UserAuthService } from './user-auth.service';
import { NovuService } from '@modules/integration/novu/novu.service';

const strategies = [AdminJwtAuthStrategy, UserJwtAuthStrategy];

@Module({
  imports: [TypeOrmModule.forFeature([AdminEntity, UserEntity]), JwtModule.register({})],
  providers: [...strategies, UserAuthService, AdminAuthService, MailgunService, NovuService],
  controllers: [AdminAuthController, UserAuthController]
})
export class AuthModule {}
