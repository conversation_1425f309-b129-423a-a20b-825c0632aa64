import { IsNotEmpty } from 'class-validator';

export class UserLoginInputDto {
  //@example <EMAIL>
  @IsNotEmpty()
  email: string;

  //@example 1234
  @IsNotEmpty()
  password: string;

  //@example false
  @IsNotEmpty()
  rememberMe: boolean;

  clientDeviceInfo?: string;
}

export class UserSignUpInputDto {
  //@example <EMAIL>
  @IsNotEmpty()
  email: string;

  //@example 1234
  @IsNotEmpty()
  password: string;
}

export class UserFirstTimeLoginInputDto {
  //@example <EMAIL>
  @IsNotEmpty()
  email: string;
}

export class UserChangeInitPasswordInputDto {
  @IsNotEmpty()
  email: string;
  @IsNotEmpty()
  oldPassword: string;
  @IsNotEmpty()
  newPassword: string;
}

export class UserForgotPasswordInputDto {
  @IsNotEmpty()
  email: string;
}

export class UserResetPasswordInputDto {
  @IsNotEmpty()
  resetToken: string;
  @IsNotEmpty()
  newPassword: string;
}

export class UserOnboardingSignInInputDto {
  @IsNotEmpty()
  signUpToken: string;
}

export class UserRevokeTokenInputDto {
  @IsNotEmpty()
  refreshToken: string;
}

export class UserRemoveFcmTokenInputDto {
  @IsNotEmpty()
  fcmToken: string;
}

export class UserChangePasswordInput {
  @IsNotEmpty()
  oldPassword: string;
  @IsNotEmpty()
  newPassword: string;
}

export class FacebookTokenInputDto {
  @IsNotEmpty()
  facebookToken: string;
}
export class GoogleTokenInputDto {
  @IsNotEmpty()
  googleToken: string;
}
export class AppleTokenInputDto {
  @IsNotEmpty()
  appleToken: string;
}

/* ------------------------------ Response DTO ------------------------------ */
export class UserVerifyResponseOtp {
  token: string;
}

export class UserSignUpResponseDto {
  accessToken: string;
}

export class UserLoginResponseDto {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiry: number;
}

export class UserRevokeTokenResponseDto {
  accessToken: string;
  refreshToken: string;
}
