import { IsEmail, IsNotEmpty } from 'class-validator';

export class AdminLoginInputDto {
  //@example <EMAIL>
  @IsNotEmpty()
  @IsEmail()
  email: string;
  //@example 1234
  @IsNotEmpty()
  password: string;
}

export class AdminRevokeTokenInputDto {
  @IsNotEmpty()
  refreshToken: string;
}

export class AdminForgotPasswordInputDto {
  @IsEmail()
  email: string;
}

export class AdminResetPasswordInputDto {
  @IsNotEmpty()
  resetToken: string;
  @IsNotEmpty()
  newPassword: string;
}

export class AdminChangePasswordInputDto {
  @IsNotEmpty()
  oldPassword: string;
  @IsNotEmpty()
  newPassword: string;
}

/* ------------------------------ Response DTO ------------------------------ */
export class AdminMeResponseDto {
  id: number;
  type: string;
  isActive: boolean;
  name: string;
  email: string;
  refreshToken: string;
}

export class AdminLoginResponseDto {
  accessToken: string;
  refreshToken: string;
}

export class AdminRevokeTokenResponseDto {
  accessToken: string;
  refreshToken: string;
}
