import { GetAuthData, GetP<PERSON>jectData, UseApiUserAuthGuard } from '@decorators/auth.decorator';
import { UserEntity } from '@modules/user/entity/user.entity';
import { Body, Controller, Get, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import appleSignIn from 'apple-signin-auth';
import axios from 'axios';
import * as Dto from 'src/modules/auth/dto/user.api.dto';
import { UserAuthService } from './user-auth.service';
@ApiTags('User AUTH')
@Controller('auth/user')
export class UserAuthController {
  constructor(private userService: UserAuthService) {}

  /** Sign In */
  @Post('sign-in')
  async signIn(@Body() userDto: Dto.UserLoginInputDto): Promise<Dto.UserLoginResponseDto> {
    const { accessToken, refreshToken, accessTokenExpiry } = await this.userService.signIn(
      userDto.email,
      userDto.password,
      userDto.rememberMe,
      userDto.clientDeviceInfo
    );
    return {
      accessToken,
      refreshToken,
      accessTokenExpiry
    };
  }

  /** Apple Sign In */
  @Post('apple-signIn')
  async appleSignIn(@Body() body: Dto.AppleTokenInputDto) {
    try {
      const appleUser = JSON.parse(JSON.stringify(body.appleToken));

      const clientId = 'cloud.bina.ios';
      const { sub: userAppleId } = await appleSignIn.verifyIdToken(appleUser.identityToken, {
        audience: clientId,
        ignoreExpiration: true
      });
      const { accessToken, refreshToken, accessTokenExpiry } = await this.userService.appleSignInSvc(
        userAppleId,
        appleUser
      );
      return {
        accessToken,
        refreshToken,
        accessTokenExpiry
      };
    } catch (e) {
      return e;
    }
  }

  /** Facebook Sign In */
  @Post('facebook-signIn')
  async facebookSignIn(@Body() body: Dto.FacebookTokenInputDto) {
    const facebookUser = await axios.get('https://graph.facebook.com/me', {
      params: {
        access_token: body.facebookToken,
        fields: 'id, name, picture, email'
      }
    });
    const { accessToken, refreshToken, accessTokenExpiry } = await this.userService.facebookSignIn(facebookUser.data);
    return {
      accessToken,
      refreshToken,
      accessTokenExpiry
    };
  }

  /** Google Sign In */
  @Post('google-signIn')
  async googleSignIn(@Body() body: Dto.GoogleTokenInputDto) {
    const googleUser = await axios.get('https://www.googleapis.com/oauth2/v1/userinfo?', {
      params: {
        alt: 'json',
        access_token: body.googleToken
      }
    });
    const { accessToken, refreshToken, accessTokenExpiry } = await this.userService.googleSignIn(googleUser.data);
    return {
      accessToken,
      refreshToken,
      accessTokenExpiry
    };
  }

  /** Sign Up */
  @Post('sign-up')
  async signUp(@Body() userDto: Dto.UserSignUpInputDto) {
    await this.userService.signUp(userDto.email, userDto.password);
  }

  /** Verify email, sign in after email verified */
  @Post('email-verification')
  async emailVerification(@Body() userDto: Dto.UserOnboardingSignInInputDto) {
    const { accessToken, refreshToken, accessTokenExpiry } = await this.userService.emailVerification(
      userDto.signUpToken
    );
    return { accessToken, refreshToken, accessTokenExpiry };
  }

  /** Check is first time login for this account.*/
  @UseApiUserAuthGuard()
  @Get('is-first-time-sign-in')
  async isFirstTimeSignIn(@GetAuthData() user: AuthData): Promise<boolean> {
    
    const isFirstTimeSignIn = await this.userService.isFirstTimeSignIn(user.id);   

    return isFirstTimeSignIn;
  }

  /** Forget Password */
  @Post('forgot-password')
  async forgotPassword(@Body() input: Dto.UserForgotPasswordInputDto) {
    await this.userService.forgotPassword(input.email);
  }

  /** Reset Password */
  @Post('reset-password')
  async resetPassword(@Body() body: Dto.UserResetPasswordInputDto) {
    await this.userService.resetPassword(body.resetToken, body.newPassword);
    return { message: 'Reset password successfully.' };
  }

  /** Change Password */
  @UseApiUserAuthGuard()
  @Post('change-password')
  async changePassword(@GetAuthData() user: AuthData, @Body() input: Dto.UserChangePasswordInput) {
    await this.userService.changePassword(user.id, input.oldPassword, input.newPassword);
    return { message: 'Change password successfully.' };
  }

  /** Logout */
  @UseApiUserAuthGuard()
  @Post('logout')
  async logout(@GetAuthData() user: AuthData) {
    await this.userService.logout(user.id);
    return { message: 'Logout successfully' };
  }

  /** Get Project User Role */
  @UseApiUserAuthGuard()
  @Get('current-role')
  async getCurrentProjectUserRole(@GetAuthData() user: AuthData, @GetProjectData() projectId: number) {
    if (!projectId) return null;
    return await this.userService.getCurrentProjectUserRole(user.id, projectId);
  }

  // /** Get User Session */
  @UseApiUserAuthGuard()
  @Get('session')
  async getUserSession(@GetAuthData() user: AuthData) {
    const userData = await this.userService.getSession(user.id);

    if (!userData) return null;

    return userData;
  }

  /** To heck is company owner */
  @UseApiUserAuthGuard()
  @Get('is-company-owner')
  async isCompanyOwner(@GetAuthData() user: AuthData) {
    return await this.userService.isCompanyOwner(user.id);
  }

  /** Refresh Token */
  @UseApiUserAuthGuard()
  @Post('revoke-authentication')
  async revokeToken(
    @Body() body: Dto.UserRevokeTokenInputDto,
    @GetAuthData() user: AuthData
  ): Promise<Dto.UserRevokeTokenResponseDto> {
    return await this.userService.revokeToken(user.id, body.refreshToken);
  }

  /** Remove User FCM Token when logout */
  @UseApiUserAuthGuard()
  @Post('remove-fcm-token')
  async removeFcmToken(@Body() body: Dto.UserRemoveFcmTokenInputDto, @GetAuthData() user: AuthData) {
    await this.userService.removeFcmToken(body.fcmToken, user.id);
    return { message: 'Remove User FCM token successfully' };
  }

  /** Update User Signature */
  @UseApiUserAuthGuard()
  @Get('change-signature-token')
  async updateSignature(@GetAuthData() user: UserEntity) {
    const res = await this.userService.getSignatureToken(user);
    return res;
  }
}
