import JwtConfig from '@configs/jwt.config';
import { RoleTypeEnum } from '@constants';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { comparePassword } from '@providers/bcrypt.service';
import { AuthData } from '@types';
import { AdminEntity } from 'src/modules/admin/entity/admin.entity';
import { Repository } from 'typeorm';
// import { AdminMeResponseDto } from './dto/admin.api.dto';

@Injectable()
export class AdminAuthService {
  constructor(
    @InjectRepository(AdminEntity)
    private adminRepository: Repository<AdminEntity>,
    private jwtService: JwtService
  ) {}

  // SignIn
  async signIn(email: string, password: string) {
    const admin = await this.adminRepository.findOne({ email });
    if (!admin) throw new BadRequestException('Email or password is incorrect');
    const valid = await comparePassword(password, admin.password);
    if (!valid) throw new BadRequestException('Email or password is incorrect');
    if (!this.isSecurePassword(password)) throw new BadRequestException('Please reset your password then login again.');

    const accessToken = this.getAccessToken(admin);
    const refreshToken = this.getRefreshToken(admin);
    admin.refreshToken = refreshToken;
    await this.adminRepository.save(admin);

    return { accessToken, refreshToken };
  }

  // Forgot Password
  async forgotPassword(email: string) {
    const admin = await this.adminRepository.findOne({ email });
    if (!admin) throw new BadRequestException('Email is incorrect');

    const resetPasswordToken = this.getResetPasswordToken(admin);
    admin.resetPasswordToken = resetPasswordToken;
    await this.adminRepository.save(admin);

    return resetPasswordToken;
    // const url = new URL(`${process.env.APP_URI}/reset-password/`);
    // url.searchParams.append('token', resetPasswordToken);

    // await this.mailgunService.sendMail({
    //   to: email,
    //   subject: 'Forgot Password',
    //   text: `Hi ${user.name},\n\nClick on the link below to reset your account password.\n${url}`,
    // });
  }

  // Reset Password
  async resetPassword(resetPasswordToken: string, newPassword: string) {
    this.jwtService.verify(resetPasswordToken, JwtConfig.adminResetPasswordTokenConfig);

    const admin = await this.adminRepository.findOne({ resetPasswordToken });
    if (!admin) throw new ForbiddenException('Invalid Token.');
    // if (!this.isSecurePassword(admin.password)) throw new BadRequestException('Please check password format then try again.');
    const passwordCheck = this.isSecurePassword(admin.password);
    if (passwordCheck !== true) {
        throw new BadRequestException(passwordCheck); // Throws the specific error message
    }

    this.adminRepository.merge(admin, {
      password: newPassword,
      resetPasswordToken: null
    });
    return await this.adminRepository.save(admin);
  }

  // Change Password
  async changePassword(id: number, oldPassword: string, newPassword: string) {
    const admin = await this.adminRepository.findOne({ id });
    if (!admin) throw new BadRequestException('Invalid Email');

    const valid = await comparePassword(oldPassword, admin.password);

    if (!valid) throw new BadRequestException('Invalid Old Password');
    // if (!this.isSecurePassword(admin.password)) throw new BadRequestException('Please check password format then try again.');
    const passwordCheck = this.isSecurePassword(admin.password);
    if (passwordCheck !== true) {
        throw new BadRequestException(passwordCheck); // Throws the specific error message
    }

    admin.password = newPassword;
    await this.adminRepository.save(admin);
  }

  // Logout
  async logout(adminId: number) {
    const admin = await this.adminRepository.findOne({ id: adminId });
    if (!admin) throw new BadRequestException('Account not found');
    this.adminRepository.merge(admin, {
      refreshToken: null
    });
    return await this.adminRepository.save(admin);
  }

  async revokeToken(id: number, refreshToken: string) {
    const admin = await this.adminRepository.findOne({ id, refreshToken });
    if (!admin) throw new BadRequestException('Invalid Refresh Token');

    const newRefreshToken = this.getRefreshToken(admin);
    const newAccessToken = this.getAccessToken(admin);
    admin.refreshToken = newRefreshToken;
    await this.adminRepository.save(admin);

    return { accessToken: newAccessToken, refreshToken: newRefreshToken };
  }

  private getAuthData(admin: AdminEntity) {
    return {
      id: admin.id,
      type: RoleTypeEnum.Admin
    } as AuthData;
  }

  private getAccessToken(admin: AdminEntity) {
    return this.jwtService.sign(this.getAuthData(admin), JwtConfig.adminAccessTokenConfig);
  }

  private getRefreshToken(admin: AdminEntity) {
    return this.jwtService.sign(this.getAuthData(admin), JwtConfig.adminRefreshTokenConfig);
  }

  private getResetPasswordToken(admin: AdminEntity) {
    return this.jwtService.sign(this.getAuthData(admin), JwtConfig.adminResetPasswordTokenConfig);
  }

  isSecurePassword(password: string) {
    if (password.length < 8) {
        return "Password must be at least 8 characters long.";
    }
    
    if (!/[A-Z]/.test(password)) {
        return "Password must include at least one uppercase letter (A-Z).";
    }

    if (!/[a-z]/.test(password)) {
        return "Password must include at least one lowercase letter (a-z).";
    }

    if (!/\d/.test(password)) {
        return "Password must include at least one digit (0-9).";
    }

    if (!/^[A-Za-z\d@$!%*?&]+$/.test(password)) {
        return "Password contains invalid characters. Only letters, digits, and @$!%*?& are allowed.";
    }

    return true;
  }
}
