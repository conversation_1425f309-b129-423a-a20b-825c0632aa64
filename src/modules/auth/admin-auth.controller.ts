import { GetAuthData, UseApiAdminAuthGuard } from '@decorators/auth.decorator';
import { Body, Controller, Post, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AuthData } from '@types';
import { AdminAuthService } from './admin-auth.service';
import { Response } from 'express';
import * as Dto from './dto/admin.api.dto';

@ApiTags('Admin AUTH')
@Controller('auth/admin')
export class AdminAuthController {
  constructor(private adminService: AdminAuthService) {}

  /** Sign In */
  @Post('sign-in')
  async signIn(@Body() adminDto: Dto.AdminLoginInputDto): Promise<Dto.AdminLoginResponseDto> {
    const { accessToken, refreshToken } = await this.adminService.signIn(adminDto.email, adminDto.password);
    return {
      accessToken,
      refreshToken
    };
  }

  /** Forget Password */
  @Post('forget-password')
  async forgetPassword(@Body() input: Dto.AdminForgotPasswordInputDto) {
    const resetPasswordToken = await this.adminService.forgotPassword(input.email);
    return { resetPasswordToken };
  }

  /** Reset Password */
  @Post('reset-password')
  async resetPassword(@Body() body: Dto.AdminResetPasswordInputDto) {
    await this.adminService.resetPassword(body.resetToken, body.newPassword);
    return { message: 'Reset password successfully.' };
  }

  /** Change Password */
  @UseApiAdminAuthGuard()
  @Post('change-password')
  async changePassword(@Body() input: Dto.AdminChangePasswordInputDto, @GetAuthData() admin: AuthData) {
    await this.adminService.changePassword(admin.id, input.oldPassword, input.newPassword);
    return { message: 'Change password successfully.' };
  }

  /** Logout */
  @UseApiAdminAuthGuard()
  @Post('logout')
  async logout(@GetAuthData() admin: AuthData, @Res({ passthrough: true }) res: Response) {
    await this.adminService.logout(admin.id);
    res.clearCookie('accessToken');
    res.clearCookie('refreshToken');

    return { message: 'Logout successfully' };
  }

  /** Refresh Token */
  @UseApiAdminAuthGuard()
  @Post('revoke-authentication')
  async revokeToken(
    @Body() body: Dto.AdminRevokeTokenInputDto,
    @GetAuthData() admin: AuthData
  ): Promise<Dto.AdminRevokeTokenResponseDto> {
    return await this.adminService.revokeToken(admin.id, body.refreshToken);
  }
}
