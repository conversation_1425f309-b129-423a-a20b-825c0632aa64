import JwtConfig from '@configs/jwt.config';
import { RoleTypeEnum } from '@constants';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { UserFcmTokenEntity } from '@modules/user-fcm-token/entity/user-fcm-token.entity';
import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { comparePassword } from '@providers/bcrypt.service';
import { UserEntity } from 'src/modules/user/entity/user.entity';
import { AuthData, ExpireDate } from 'src/types';
import { getRepository, Repository } from 'typeorm';

@Injectable()
export class UserAuthService {
  constructor(
    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
    private jwtService: JwtService,
    private mailgunService: MailgunService
  ) {}
  // Sign In
  async signIn(email: string, password: string, rememberMe: boolean, clientDeviceInfo?: string) {
    const user = await this.userRepository.findOne({ email });
    if (!user) throw new BadRequestException('Email Not Found');

    const valid = await comparePassword(password, user.password);

    if (user.removedAt) throw new BadRequestException('Account is removed');
    if (!password || password === '') throw new BadRequestException('Password is empty');
    if (!valid) throw new BadRequestException('Email or Password is incorrect');

    if (!user.isEmailVerified) {
      const signUpToken = this.getSignUpToken(user);
      this.userRepository.merge(user, { signUpToken });
      await this.userRepository.save(user);

      const url = new URL(`${process.env.APP_URI}/onboarding/`);
      url.searchParams.append('token', signUpToken);

      await this.mailgunService.sendMail({
        to: email,
        subject: 'Email Verification - Welcome Aboard To The New Era Of Construction Management',
        text: `Hi,\n\nClick on the link below to verify your account.\n${url}`,
        template: `verify_email`,
        url: url
      });
      throw new BadRequestException('Please check your email to verify account before login');
    }

    this.userRepository.merge(user, {
      clientDeviceInfo
    });

    return this.getSignInToken(user, rememberMe);
  }

  // get session
  async getSession(userId: number) {
    const user = await this.userRepository.findOne({ id: userId, removedAt: null });
    if (!user) throw new BadRequestException('User not found');
    return user;
  }

  // Apple User Sign In
  async appleSignInSvc(userAppleId, reqUser) {
    if (userAppleId === reqUser.user) {
      const appleUser = {
        appleId: reqUser.user,
        email: reqUser.email
      };

      let user: UserEntity | null = null;
      user = await this.userRepository.findOne({
        appleId: reqUser.user
      });

      // if(!user && appleUser.email)
      //    user = await this.userRepository.findOne({
      //     email: appleUser.email
      // });

      if (user) {
        // CAN BE DEACTIVATED, APPLE ID NEED TO BE SET FROM PROFILE PAGE AFTER LOGIN
        this.userRepository.merge(user, {
          appleId: appleUser.appleId
        });
        await this.userRepository.save(user);
        // -------

        return this.getSignInToken(user);
      } else if (appleUser.email) return this.checkEmailExist(appleUser);
    }
    throw new BadRequestException('Authentication with Apple failed.');
  }

  // Facebook User Sign In
  async facebookSignIn(reqUser) {
    const facebookUser = {
      facebookId: reqUser.id,
      name: reqUser.name,
      picture: reqUser.picture.data.url,
      email: reqUser.email
    };

    // Return error if no email on Facebook account
    if (!facebookUser.email) throw new BadRequestException('Please add Email to your Facebook account');

    // Check exist account with facebookId
    const user = await this.userRepository.findOne({
      facebookId: facebookUser.facebookId
    });
    if (user) return this.getSignInToken(user);

    // Check exist accounts' email with facebook email (For linking)
    return this.checkEmailExist(facebookUser);
  }

  // Google User Sign In
  async googleSignIn(reqUser) {
    const googleUser = {
      googleId: reqUser.id,
      name: reqUser.name,
      picture: reqUser.picture,
      // picture: reqUser.photo, // Google returns key photo instead of picture
      email: reqUser.email
    };

    // Check exist account with googleId
    const user = await this.userRepository.findOne({
      googleId: googleUser.googleId
    });
    if (user) return this.getSignInToken(user);

    // Check exist accounts' email with google email (For linking)
    return this.checkEmailExist(googleUser);
  }

  // Sign Up
  async signUp(email: string, password: string) {
    const user = await this.userRepository.findOne({ email });
    if (user) throw new BadRequestException('Email already exists');

    if (!password || password === '') throw new BadRequestException('Password is empty');
    if (!this.isSecurePassword(password)) throw new BadRequestException('Please check password format then try again.');
    const passwordCheck = this.isSecurePassword(password);
    if (passwordCheck !== true) {
      throw new BadRequestException(passwordCheck); // Throws the specific error message
    }

    const newUser = this.userRepository.create({
      email,
      password,
      type: RoleTypeEnum.User
    });
    const _user = await this.userRepository.save(newUser);

    const signUpToken = this.getSignUpToken(newUser);
    this.userRepository.merge(newUser, { signUpToken });
    await this.userRepository.save(newUser);

    const url = new URL(`${process.env.APP_URI}/onboarding/`);
    url.searchParams.append('token', signUpToken);

    await this.mailgunService.sendMail({
      to: email,
      subject: 'Email Verification - Welcome Aboard To The New Era Of Construction Management',
      text: `Hi,\n\nClick on the link below to verify your account.\n${url}`,
      template: `verify_email`,
      url: url
    });
  }

  /** Verify email, sign in after email verified */
  async emailVerification(signUpToken: string) {
    const user = await this.userRepository.findOne({ signUpToken });
    if (!user) throw new ForbiddenException('Invalid Token.');

    this.userRepository.merge(user, {
      isEmailVerified: true
    });
    await this.userRepository.save(user);
    return await this.getSignInToken(user);
  }

  // Check user is first time sign in
  async isFirstTimeSignIn(id: number) {
    const user = await this.userRepository.findOne({ id });
    if (!user) throw new BadRequestException('User Not Found');    

    if (!user.isFirstTimeLogin) return false;

    return true;
  }

  // Forgot Password
  async forgotPassword(email: string) {
    const user = await this.userRepository.findOne({ email });
    if (!user) throw new BadRequestException('Email is incorrect');

    const resetPasswordToken = this.getResetPasswordToken(user);
    user.resetPasswordToken = resetPasswordToken;
    await this.userRepository.save(user);

    const url = new URL(`${process.env.APP_URI}/reset-password/`);
    url.searchParams.append('token', resetPasswordToken);

    await this.mailgunService.sendMail({
      to: email,
      subject: 'Forgot Password - Click the link below to reset password',
      text: `Hi ${user.name},\n\nClick on the link below to reset your account password.\n${url}`,
      template: 'forgot_password',
      url: url
    });
  }

  // Reset Password
  async resetPassword(resetPasswordToken: string, newPassword: string) {
    try {
      this.jwtService.verify(resetPasswordToken, JwtConfig.userResetPasswordTokenConfig);

      const user = await this.userRepository.findOne({ resetPasswordToken });
      if (!user) throw new ForbiddenException('Invalid Token.');
      // if (!this.isSecurePassword(newPassword))
      //   throw new BadRequestException('Please check password format then try again.');

      // check if the password is same as the old password
      const isOldPassword = await comparePassword(newPassword, user.password);

      if (isOldPassword) throw new BadRequestException('New password must be different from old password');

      const passwordCheck = this.isSecurePassword(newPassword);
      if (passwordCheck !== true) {
        throw new BadRequestException(passwordCheck); // Throws the specific error message
      }

      this.userRepository.merge(user, {
        password: newPassword,
        resetPasswordToken: null
      });
      return await this.userRepository.save(user);
    }
    catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new BadRequestException('Token has expired.');
      }
      throw new BadRequestException(error?.message || 'Invalid Token.');
    }
  }

  // Change Password
  async changePassword(id: number, oldPassword: string, newPassword: string) {
    const user = await this.userRepository.findOne({ id });
    if (!user) throw new BadRequestException('User not found.');

    const valid = await comparePassword(oldPassword, user.password);
    if (!valid) throw new BadRequestException('Old Password is incorrect');
    // if (!this.isSecurePassword(newPassword))
    //   throw new BadRequestException('Please check password format then try again.');

    const isOldPassword = await comparePassword(newPassword, user.password);

    if (isOldPassword) throw new BadRequestException('New password must be different from old password');

    const passwordCheck = this.isSecurePassword(newPassword);
    if (passwordCheck !== true) {
      throw new BadRequestException(passwordCheck); // Throws the specific error message
    }

    user.password = newPassword;
    await this.userRepository.save(user);
  }

  // Logout
  async logout(userId: number) {
    const user = await this.userRepository.findOne({ id: userId });
    if (!user) throw new BadRequestException('Account not found');
    this.userRepository.merge(user, {
      refreshToken: null
    });
    return await this.userRepository.save(user);
  }
  
  // Get Project User Role
  async getCurrentProjectUserRole(userId: number, projectId: number) {
    if (!projectId) throw new BadRequestException('Not in a project');
    const user = await getRepository(ProjectUserEntity).findOne({ userId, projectId });
    if (!user) throw new BadRequestException('Project user not found');
    return user.role;
  }

  // To check is company owner
  async isCompanyOwner(userId: number) {
    const user = await this.userRepository.findOne({ id: userId });
    const company = await getRepository(CompanyEntity).findOne({ id: user.companyId });

    if (company.ownerId === user.id) return true;
    return false;
  }

  // Revoke Token
  async revokeToken(id: number, refreshToken: string) {
    const user = await this.userRepository.findOne({
      id,
      refreshToken
    });
    if (!user) throw new BadRequestException('Invalid Refresh Token');

    const newAccessToken = this.getAccessToken(user);
    const newRefreshToken = this.getRefreshToken(user);
    const accessTokenExpiry = this.getAccessTokenExpiry(newAccessToken);
    user.refreshToken = newRefreshToken;
    await this.userRepository.save(user);

    return { accessToken: newAccessToken, refreshToken: newRefreshToken, accessTokenExpiry };
  }

  // Remove FCM Token
  async removeFcmToken(fcmToken: string, userId: number) {
    const userFcmToken = await getRepository(UserFcmTokenEntity).findOne({ fcmToken, userId });
    if (!userFcmToken) throw new BadRequestException('FCM Token not found');

    await getRepository(UserFcmTokenEntity).softRemove(userFcmToken);
  }

  private getAuthData(user: UserEntity) {
    return {
      id: user.id,
      type: RoleTypeEnum.User
    } as AuthData;
  }

  // Get AccessToken and RefreshToken to SignIn
  private async getSignInToken(user: UserEntity, rememberMe?: boolean) {
    const refreshToken = await this.getRefreshToken(user, rememberMe);
    const accessToken = await this.getAccessToken(user);
    const accessTokenExpiry = await this.getAccessTokenExpiry(accessToken);
    user.refreshToken = refreshToken;
    await this.userRepository.save(user);

    return { accessToken, refreshToken, accessTokenExpiry };
  }

  private getAccessToken(user: UserEntity) {
    return this.jwtService.sign(
      { ...this.getAuthData(user), type: RoleTypeEnum.User },
      JwtConfig.userAccessTokenConfig
    );
  }

  private getRefreshToken(user: UserEntity, rememberMe?: boolean) {
    JwtConfig.userRefreshTokenConfig.expiresIn = rememberMe ? '20y' : '10y';
    // JwtConfig.userRefreshTokenConfig.expiresIn = rememberMe ? '30d' : '2d';
    return this.jwtService.sign(this.getAuthData(user), JwtConfig.userRefreshTokenConfig);
  }

  private getAccessTokenExpiry(accessToken: string) {
    const decodedToken = this.jwtService.decode(accessToken) as ExpireDate;
    const accessTokenExpiry = decodedToken?.exp;
    return accessTokenExpiry;
  }

  // Get ResetPasswordToken
  private getResetPasswordToken(user: UserEntity) {
    return this.jwtService.sign(this.getAuthData(user), JwtConfig.userResetPasswordTokenConfig);
  }

  // Get SignUpToken
  private getSignUpToken(user: UserEntity) {
    return this.jwtService.sign(this.getAuthData(user), JwtConfig.userSignUpTokenConfig);
  }

  // get signature token
  async getSignatureToken(user: UserEntity) {    
    return this.jwtService.sign(this.getAuthData(user), JwtConfig.userChangeSignatureTokenConfig);
  }

  /* --------------------- Facebook, Google, and Apple SignIn --------------------- */
  // Check exist email has match with Facebook, Google, and Apple email (For New User)
  private async checkEmailExist(reqUser: any) {
    const newUser = await this.userRepository.findOne({
      email: reqUser.email
    });
    return this.createOrUpdateUser(newUser, reqUser);
  }

  private async createOrUpdateUser(newUser: UserEntity, reqUser: any) {
    if (newUser) {
      await this.updateUserLogin(newUser, reqUser);
      return this.getSignInToken(newUser);
    }
    const createNewUser = await this.createNewUserLogin(reqUser);
    return this.getSignInToken(createNewUser);
  }

  // Update Login by Facebook, Google, and Apple
  private async updateUserLogin(user: UserEntity, reqUser: any) {
    this.userRepository.merge(user, {
      facebookId: reqUser?.facebookId,
      googleId: reqUser?.googleId,
      appleId: reqUser?.appleId
    });
    return await this.userRepository.save(user);
  }

  // New Login by Facebook, Google, and Apple
  private async createNewUserLogin(reqUser?: any) {
    const newUser = this.userRepository.create({
      name: reqUser?.name,
      email: reqUser?.email,
      password: '',
      facebookId: reqUser?.facebookId,
      googleId: reqUser?.googleId,
      // appleId: reqUser?.appleId,
      avatar: reqUser?.picture,
      isEmailVerified: true,
      type: RoleTypeEnum.User
    });
    return await this.userRepository.save(newUser);
  }

  isSecurePassword(password: string) {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long.';
    }

    if (!/[A-Z]/.test(password)) {
      return 'Password must include at least one uppercase letter (A-Z).';
    }

    if (!/[a-z]/.test(password)) {
      return 'Password must include at least one lowercase letter (a-z).';
    }

    if (!/\d/.test(password)) {
      return 'Password must include at least one digit (0-9).';
    }

    if (!/^[A-Za-z\d@$!%*?&]+$/.test(password)) {
      return 'Password contains invalid characters. Only letters, digits, and @$!%*?& are allowed.';
    }

    return true;
  }

  /* ------------------------------------- End ------------------------------------- */
}
