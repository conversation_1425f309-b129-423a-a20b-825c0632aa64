import { Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { RoleTypeEnum } from '@constants';
import { CompanySubscriptionDto } from './dto/company-subscription.gql.dto';
import { getRepository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import moment from 'moment';

@Injectable()
export class CompanySubscriptionAuthorizer implements Authorizer<CompanySubscriptionDto> {
  async authorize(context: any): Promise<Filter<CompanySubscriptionDto>> {
    try {
      // Admin authorizer
      if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

      // User authorizer
      const userId = context?.req?.user?.id;
      if (!userId) throw new UnauthorizedException('User not found');

      const user = await getRepository(UserEntity).findOne({
        id: userId
      });

      if (context?.req?.user?.type === RoleTypeEnum.User) {
        return Promise.resolve({
          companyId: {
            eq: user.companyId
          },
          subscriptionEndDate: {
            gt: moment().subtract(10, 'days').toDate()
          }
        });
      }
    } catch (e) {
      throw new Error('Something went wrong in company subscription authorizer! authorize function');
    }
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    return Promise.resolve({});
  }
}
