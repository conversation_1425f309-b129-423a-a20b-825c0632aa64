import { defaultQueryOptions } from '@constants';
import { Authorize, IDField, QueryOptions, Relation } from '@nestjs-query/query-graphql';
import { Field, ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { CompanySubscriptionEntity } from '../entity/company-subscription.entity';
import { SubscriptionPackageDto } from '@modules/subscription-package/dto/subscription-package.gql.dto';
import { relationOption } from '@constants/query.constant';
import { CompanySubscriptionAuthorizer } from '../company-subscription.authorizer';

@ObjectType('CompanySubscription')
@Authorize(CompanySubscriptionAuthorizer)
@Relation('subscriptionPackage', () => SubscriptionPackageDto, relationOption(true))
@QueryOptions({ ...defaultQueryOptions })
export class CompanySubscriptionDto extends CompanySubscriptionEntity {
  @Field(() => Boolean, { nullable: true })
  isFreeTrial?: boolean;

  // All computed fields are now handled by resolvers in company-subscription.resolver.ts:
  // - isSubscriptionActive
  // - isSubscriptionInGracePeriod
  // - isTrialEnding
  // - daysLeftInTrial
  // - nextPaymentDate
  // - nextBillingAmount
}

@InputType()
export class CreateCompanySubscriptionInputDTO {
  @IDField(() => ID) subscriptionPackageId: number;
  @IDField(() => ID) companyId: number;
  subscriptionEndDate: Date;
  seatCount?: number;
  isYearly?: boolean;
  nextBillingDate?: Date;
}

@InputType()
export class UpdateCompanySubscriptionInputDTO extends PartialType(CreateCompanySubscriptionInputDTO) {}

@InputType()
export class ChangeSeatCountInputDTO {
  @IDField(() => ID) subscriptionId: number;
  newSeatCount: number;
}
