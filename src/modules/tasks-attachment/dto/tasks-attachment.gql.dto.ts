import { defaultQueryOptions, SourceType } from '@constants';
import { IDField, QueryOptions } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { GraphQLUpload, FileUpload } from 'graphql-upload';
import { TasksAttachmentEntity } from '../entity/tasks-attachment.entity';

@ObjectType('TasksAttachment')
@QueryOptions({ ...defaultQueryOptions })
export class TasksAttachmentDto extends TasksAttachmentEntity {
  remoteId?: number;
}

@InputType()
export class CreateTasksAttachmentInputDTO {
  @IDField(() => Number) id?: number;
  @IDField(() => Number) userId?: number;
  @IDField(() => Number) taskId?: number;
  @Field(() => GraphQLUpload) fileUrl?: FileUpload;

  //? For offline sync use
  @Field() localId?: string;
  @Field() localTaskId?: string;
  @Field() updatedBy?: string;
  updated_at?: Date;
  created_at?: Date;
  deleted_at?: Date;
  _changed?: string;
  remoteId?: number;
  @Field({ nullable: true }) recordSource?: SourceType;

}
@InputType()
export class UpdateTasksAttachmentInputDTO extends PartialType(CreateTasksAttachmentInputDTO) {}
