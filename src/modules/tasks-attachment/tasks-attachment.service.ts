import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TasksAttachmentEntity } from './entity/tasks-attachment.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';

@Injectable()
export class TasksAttachmentService extends TypeOrmQueryService<TasksAttachmentEntity> {
  constructor(
    @InjectRepository(TasksAttachmentEntity)
    private tasksAttachmentRepo: Repository<TasksAttachmentEntity>,
    private tmOneService: TMOneService
  ) {
    super(tasksAttachmentRepo, { useSoftDelete: true });
  }

  async deleteOneAttachment(id: number) { 
    return await this.tasksAttachmentRepo.softDelete(id);
  }

  async getPresignedUrl(task_attachment: TasksAttachmentEntity) {
    try {
      let key = task_attachment.fileKey;
      // fallback to fileUrl if fileKey is missing
      if (!key){
        const fileName = task_attachment.fileUrl;
        key = fileName?.replace(/https:\/\/bina-(dev|prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my\//g, '');
      }

      if (!key) return null;

      const signedUrl = await this.tmOneService.getPresignedUrl(key);

      return signedUrl.SignedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      return null;
    }
  }
}
