import { BaseEntity } from '@modules/base/base';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne } from 'typeorm';

@ObjectType()
@Entity('tasks_attachments')
export class TasksAttachmentEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  taskId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  userId: number;

  @FilterableField()
  @Column('text')
  name: string;

  @FilterableField()
  @Column('text', { nullable: true })
  fileUrl: string;

  @FilterableField()
  @Column('text')
  fileKey: string;

  @FilterableField()
  @Column('text')
  type: string;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => TaskEntity, task => task.attachments, { orphanedRowAction: 'soft-delete' })
  @JoinColumn({ name: 'taskId' })
  task: TaskEntity;

  @ManyToOne(() => UserEntity, user => user.taskAttachments)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}
