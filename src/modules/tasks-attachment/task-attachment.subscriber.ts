import { BadRequestException, Injectable } from '@nestjs/common';
import {
  Connection,
  EntitySubscriberInterface,
  EventSubscriber,
  In,
  InsertEvent,
  UpdateEvent
} from 'typeorm';
import { TasksAttachmentEntity } from './entity/tasks-attachment.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '@modules/integration/file.service';
import * as _ from 'lodash';
import { getEllipsisText } from '@constants/function';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { SoftRemoveEvent } from 'typeorm/subscriber/event/SoftRemoveEvent';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { getErrorMessage } from '@common/error';

const { APP_URI } = process.env;
@Injectable()
@EventSubscriber()
export class TasksAttachmentSubscriber implements EntitySubscriberInterface<TasksAttachmentEntity> {
  constructor(connection: Connection, private fileService: FileService, private novuService: NovuService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return TasksAttachmentEntity;
  }

  // Can be removed if the db remigrated
  // removed and see if it breaks anything
  // async afterLoad(entity: TasksAttachmentEntity, event: LoadEvent<TasksAttachmentEntity>) {
  //   const fileUrl = entity.fileUrl;
  //   if (!isValidUrl(fileUrl) && !(entity.type === 'dwg' || entity.type === 'rvt'))
  //     entity.fileUrl = process.env.TMONE_OBS_STORAGE + _.replace(fileUrl, '/', '');
  // }

  async beforeUpdate(event: UpdateEvent<TasksAttachmentEntity>) {
    const { entity } = event;

    const fileObject: any = entity.fileUrl;// fileUrl is the file uploaded from the client

    if (!fileObject) throw new BadRequestException('No Document has been upload');
    if (fileObject && _.isObject(fileObject)) {
      const folder = 'Task-Attachments';
      const { filename, key, type } = await this.fileService.uploadGqlFile(fileObject as FileUpload, folder);
      entity.fileKey = key;
      entity.name = filename;
      entity.type = type;
    }
  }

  async beforeInsert(event: InsertEvent<TasksAttachmentEntity>) {
    const { entity } = event;

    const fileObject: any = entity.fileUrl; // fileUrl is the file uploaded from the client

    if (!fileObject) throw new BadRequestException('No Document has been upload');
    if (fileObject && _.isObject(fileObject)) {
      const folder = 'Task-Attachments';
      const { filename, key, type } = await this.fileService.uploadGqlFile(fileObject as FileUpload, folder);
      entity.fileKey = key;
      entity.fileUrl = key;
      entity.name = filename;
      entity.type = type;
    }
  }

  //
  // NEED TO OPTIMIZE TO GET USER DETAILS FROM REQUEST HEADER
  async afterInsert(event: InsertEvent<TasksAttachmentEntity>) {
    const { entity } = event;

    // CAPTURING FOR TASK ATTACHMENT
    if (entity.userId) {
      const task = await event.manager
        .getRepository(TaskEntity)
        .findOne({ where: { id: entity?.taskId }, relations: ['assignees', 'copies'] });

      const owner = { userId: task?.ownerId };
      const assignees = (await task.assignees) ?? [];
      const copies = (await task.copies) ?? [];

      const users = _.uniqBy([...assignees, ...copies, owner], 'userId');
      const project = await event.manager
        .getRepository(ProjectEntity)
        .findOne({ id: task?.projectId }, { relations: ['company'] });

      const sanitizedUsers = await event.manager.find(UserEntity, {
        where: {
          id: In(users.map(user => user.userId))
        }
      });

      const link = `/tasks?taskId=${entity?.taskId}&projectId=${task?.projectId}&companyId=${project?.companyId}`;
      const mobileLink = `task/1/${entity?.taskId}/task`;
      const header = `📎 Task - New Attachment`;

      sanitizedUsers?.forEach(async user => {
        try {
          const body = ` add attachment in ${task?.title}: ${getEllipsisText(entity?.name)}`;
          return this.attachmentNotification(
            user,
            project,
            task,
            link,
            mobileLink,
            body,
            body,
            project?.company,
            header
          );
        } catch (error) {
          getErrorMessage(error, 'TaskAttachmentSubscriber', 'afterInsert');
        }
      });

      const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

      const msg = user.name + ' added ' + entity.name;

      const auditLog = event.manager.getRepository(AuditLogEntity).create({
        userId: user.id,
        projectId: task.projectId,
        taskId: task.id,
        resourceId: entity.id,
        module: AuditLogModuleType.Task,
        action: AuditLogActionType.AddAttachment,
        content: msg
      });
      await auditLog.save();
    }
  }

  async beforeSoftRemove(event: SoftRemoveEvent<TasksAttachmentEntity>) {
    try {
      const { entity } = event;

      const task = await event.manager.getRepository(TaskEntity).findOne({ id: entity.taskId });
      const user = await event.manager.getRepository(UserEntity).findOne({ id: entity?.userId });

      if (user) {
        const msg = user?.name + ' deleted ' + entity.name;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: user?.id ?? null,
          projectId: task.projectId,
          taskId: task.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Task,
          action: AuditLogActionType.RemoveAttachment,
          content: msg
        });
        await auditLog.save();
      }
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  attachmentNotification(user, project, task, link, mobileLink, owner, body, companyName = null, header) {
    const payload: INovuPayload = {
      user: {
        avatar: owner.avatar,
        name: owner.name,
        email: owner.email
      },
      header: header,
      company: companyName.name,
      title: project.title,
      head: owner?.name,
      body: body,
      headColon: true,
      link: {
        mobile: mobileLink,
        web: link,
        uri: APP_URI,
        redirect: link
      },
      subscriber: {
        firstName: user.name
      },
      pushMessageTitle: 'New - Task Attachment'
    };

    this.novuService.trigger('new-attachment', {
      to: {
        subscriberId: user.id.toString(),
        email: user.email
      },
      payload,
      overrides: {
        android: {
          priority: 'high'
        },
        fcm: {
          android: {
            priority: 'high'
          },
          data: {
            link: mobileLink.toString(),
            projectId: task.projectId.toString(),
            companyId: companyName?.id.toString()
          }
        }
      }
    });
  }
}
