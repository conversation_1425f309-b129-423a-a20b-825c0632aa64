import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { TasksAttachmentEntity } from './entity/tasks-attachment.entity';
import {
  TasksAttachmentDto,
  CreateTasksAttachmentInputDTO,
  UpdateTasksAttachmentInputDTO
} from './dto/tasks-attachment.gql.dto';
import { TasksAttachmentService } from './tasks-attachment.service';
import { TasksAttachmentSubscriber } from './task-attachment.subscriber';
import { IntegrationModule } from '@modules/integration/integration.module';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectUserRoleType } from '@constants';
import { ProjectRoles } from '@decorators/auth.decorator';
import { TasksAttachmentResolver } from './tasks-attachment.resolver';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([TasksAttachmentEntity]), IntegrationModule],
      resolvers: [
        {
          ServiceClass: TasksAttachmentService,
          DTOClass: TasksAttachmentDto,
          EntityClass: TasksAttachmentEntity,
          CreateDTOClass: CreateTasksAttachmentInputDTO,
          UpdateDTOClass: UpdateTasksAttachmentInputDTO,
          create: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          update: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          delete: {
            decorators: [
              ProjectRoles(
                ProjectUserRoleType.ProjectOwner,
                ProjectUserRoleType.CloudCoordinator,
                ProjectUserRoleType.CanEdit
              )
            ]
          },
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard]
        }
      ],
      services: [TasksAttachmentSubscriber, TasksAttachmentService, TasksAttachmentResolver]
    })
  ]
})
export class TasksAttachmentModule {}
