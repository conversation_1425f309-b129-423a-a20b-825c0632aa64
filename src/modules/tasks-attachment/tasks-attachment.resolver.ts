import { UseGuards } from "@nestjs/common";
import { Args, Mu<PERSON>, Resolver, ResolveField, Parent } from "@nestjs/graphql";
import { GqlAuthGuard } from "@guards/auth.guard";
import { TasksAttachmentDto } from "./dto/tasks-attachment.gql.dto";
import { TasksAttachmentService } from "./tasks-attachment.service";

@UseGuards(GqlAuthGuard)
@Resolver(() => TasksAttachmentDto)

export class TasksAttachmentResolver {
  constructor(private taskAttachmentService: TasksAttachmentService) { }
  
  @Mutation(() => TasksAttachmentDto)
  async deleteOneAttachment(
    @Args('id') id: number
  ) {
    return await this.taskAttachmentService.deleteOne(id);
  }

  @ResolveField('fileUrl', () => String)
  async fileUrl(@Parent() parent: TasksAttachmentDto) {
    return await this.taskAttachmentService.getPresignedUrl(parent);
  }
}