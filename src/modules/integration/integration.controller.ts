import { <PERSON>, Get, Query, Render } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AutodeskService } from './autodesk/autodesk.service';

@ApiTags('Integration API')
@Controller('integration')
export class IntegrationController {
  constructor(private autodeskService: AutodeskService) {}
  @Get('autodesk-auth')
  async getAutoDeskAuth() {
    const accessToken = await this.autodeskService.getAutodeskAuth();
    return accessToken;
  }

  @Get('autodesk-viewer')
  @Render('autodesk.view.hbs')
  async getAutodeskViewer(@Query('autodeskToken') autodeskToken: string, @Query('urn') urn: string) {
    return { autodeskToken, urn };
  }
}
