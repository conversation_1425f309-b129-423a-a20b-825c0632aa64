import { URL } from 'url';

export class MailgunDto {
  to: string | string[];
  cc?: string | string[];
  from?: string;
  replyTo?: string;
  type?: string;
  name?: string;
  subject: string;
  text?: string;
  html?: string;
  template?: string;
  url?: URL;
  projectTitle?: string;
  signBy?: string;
  owner?: string;
  form?: string;
  assigner?: string;
  title?: string;
  dueDate?: Date;
  message?: string;
  loginLink?: URL;
  signupLink?: URL;
  email?: string;
  recipient?: any;
  reference?: string;
  sentAt?: string;
  file?: { redirect: string; name: string; size: string; iconUrl: string };
  inline?: any;
  attachmentUrls?: string[];
  inlineUrls?: string[];
  reply?: boolean;
}