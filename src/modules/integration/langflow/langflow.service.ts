// langflow-service.ts

import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class LangflowService {
  private readonly logger = new Logger(LangflowService.name);

  private readonly flowId = 'f14ad08d-0264-4eea-8d96-be3a6d421c2e';
  private readonly endpointUrl = `${process.env.LANGFLOW_BASE_URL}/api/v1/run/${this.flowId}`;

  /**
   * Process multiple schedules by batching them into a single request.
   * @param schedules Array of ScheduleEntity to be processed.
   */
  async uploadAndProcessSchedules(schedules: ScheduleEntity[], collectionName: string): Promise<void> {
    const payload = {
      input_value: JSON.stringify(schedules.map(schedule => JSON.stringify(schedule))),
      input_type: 'text',
      output_type: 'text',
      tweaks: {
        'AstraVectorStoreComponent-Ktl9a': {
          collection_name: collectionName
        }
      },
      stream: false
    };

    try {
      const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${process.env.LANGFLOW_API_KEY}`
      };

      const response = await axios.post(this.endpointUrl, payload, { headers }).catch(error => {
        this.logger.error(`Error in uploadAndProcessSchedules: ${error.message}`);
      });

      if (response && response.data) {
        this.logger.log(`Langflow processing completed for ${schedules.length} schedules.`);
      } else {
        this.logger.warn(`Langflow returned an empty response.`);
      }
    } catch (error) {
      this.logger.error(`Error in uploadAndProcessSchedules: ${error.message}`);
    }
  }
}
