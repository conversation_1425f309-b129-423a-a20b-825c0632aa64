import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import qs from 'qs';
import * as _ from 'lodash';
import { getErrorMessage } from '@common/error';

const {
  AUTODESK_CLIENT_ID,
  AUTODESK_CLIENT_SECRET,
  AUTODESK_GRANT_TYPE,
  AUTODESK_SCOPE,
  AUTODESK_BUCKET
} = process.env;
@Injectable()
export class AutodeskService {
  // To Get Autodesk Forge Token
  async getAutodeskAuth() {
    try {
      const data = qs.stringify({
        client_id: AUTODESK_CLIENT_ID,
        client_secret: AUTODESK_CLIENT_SECRET,
        grant_type: AUTODESK_GRANT_TYPE,
        scope: AUTODESK_SCOPE
      });
      const config = { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } };

      const autodeskAuth = await axios.post(
        'https://developer.api.autodesk.com/authentication/v2/token',
        data,
        config
      );

      const accessToken = autodeskAuth.data.access_token;      

      return accessToken;
    } catch (e) {
      getErrorMessage(e, 'AutodeskService', 'getAutodeskAuth');
    }
  }

  // Upload Forge/Drawing Files to Forge Bucket
  async uploadForgeFile(file: any, fileName: string) {
    try {
      const accessToken = await this.getAutodeskAuth();
      const config = {
        headers: { Authorization: `Bearer ${accessToken}` }
      };

      const { createReadStream } = await file;
      const stream = createReadStream();

      const uploadObject = await axios
        .put(`https://developer.api.autodesk.com/oss/v2/buckets/${AUTODESK_BUCKET}/objects/${fileName}`, stream, {
          ...config,
          maxContentLength: Infinity,
          maxBodyLength: Infinity
        })
        .then(res => {
          return res;
        })
        .catch(err => {
          //handle error
          if (err?.response?.data) throw err?.response?.data;
          throw err.message;
        });

      const urn = uploadObject.data?.objectId;

      if (!urn) throw new BadRequestException('Failed to upload drawings file.');

      const urnInBase64 = _.replace(Buffer.from(urn).toString('base64'), new RegExp('=', 'g'), '');
      const fileSize = _.floor(uploadObject.data?.size / (1024 * 1024), 2);

      await this.convertDrawingsToSVF(urnInBase64, config, false);

      return await { fileSize, urnInBase64 };
    } catch (e) {
      getErrorMessage(e, 'AutodeskService', 'uploadForgeFile');
    }
  }

  async downloadForgeFile(urn: string) {
    try {
      const accessToken = await this.getAutodeskAuth();
      const config = {
        headers: { Authorization: `Bearer ${accessToken}` }
      };
      if (!urn) throw new BadRequestException('Failed to download drawings file.');

      const urnFromBase64 = Buffer.from(urn, 'base64').toString('ascii');
      const fileName = urnFromBase64.split('/').pop();
      const downloadObject = await axios
        .get(`https://developer.api.autodesk.com/oss/v2/buckets/${AUTODESK_BUCKET}/objects/${fileName}`, config)
        .then(res => {
          return res;
        })
        .catch(err => {
          if (err?.response?.data) throw err?.response?.data;
          throw err.message;
        });
      return downloadObject.data;
    } catch (e) {
      throw e;
    }
  }

  // Convert Uploaded Drawings to SVF
  async convertDrawingsToSVF(urn: string, config: any, haveReference: boolean) {

    const header = {
      headers: { Authorization: `Bearer ${config}` }
    };

    try {
      const data = {

        input: {
        urn: urn, 
        checkReferences: haveReference 

      },

        output: {          
          destination: {
            region: 'us'
          },
          formats: [
            {
             "type": "svf2",
             "views": ["2d", "3d"],
              advanced: {
                '2dviews': 'pdf',
                timelinerProperties: true,
              }
            }
          ]
        }
      };

     const res =  await axios
        .post('https://developer.api.autodesk.com/modelderivative/v2/designdata/job', data, header)
        .then(res => {

          return res;
        })
        .catch(err => {          
          //handle error
          if (err?.response?.data) throw err?.response?.data;
          throw err.message;
        });   
      
      return res?.data;
    } catch (e) {
      getErrorMessage(e, 'AutodeskService', 'convertDrawingsToSVF');
    }
  }
}
