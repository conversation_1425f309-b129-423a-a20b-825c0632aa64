import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutodeskService } from './autodesk/autodesk.service';
import { FileService } from './file.service';
import { ConfigService } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';

import { IntegrationController } from './integration.controller';
import { MailgunService } from './mailgun/mailgun.service';
import { TMOneService } from './tmOneObs/tmOneObs.service';
import { NovuService } from './novu/novu.service';
import { SenangPayService } from './senangpay/senangpay.service';
import { BillplzService } from './billplz/billplz.service';
import { LangflowService } from './langflow/langflow.service';
import { BullMqService } from '@modules/bull-mq/bullmq-service';

@Module({
  imports: [TypeOrmModule.forFeature([]), HttpModule],
  providers: [
    MailgunService,
    FileService,
    AutodeskService,
    TMOneService,
    // FirebaseService,
    NovuService,
    SenangPayService,
    BillplzService,
    LangflowService,
    BullMqService,
    ConfigService
  ],
  exports: [
    FileService,
    AutodeskService,
    TMOneService,
    // FirebaseService,
    NovuService,
    SenangPayService,
    BillplzService,
    MailgunService,
    LangflowService,
    BullMqService
  ],
  controllers: [
    IntegrationController,
  ]
})
export class IntegrationModule {}
