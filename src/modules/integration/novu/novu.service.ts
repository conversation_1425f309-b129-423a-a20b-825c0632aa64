import { UserEntity } from '@modules/user/entity/user.entity';
import { Injectable } from '@nestjs/common';
import { Novu, ITriggerPayloadOptions, PushProviderIdEnum, ChannelTypeEnum, PreferenceLevelEnum } from '@novu/node';
import { createHmac } from 'crypto';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { getErrorMessage } from '@common/error';

interface cpmTask {
  name: string;
  projectScheduleId: string;
  task: string[];
}

export interface INovuPayload {
  user: {
    avatar: string;
    name: string;
    email: string;
  };
  event?: string;
  // for email template //
  subject?: string;
  bodyContent?: string;
  // ----------------------- //
  header?: string;
  company?: string;
  title: string;
  head?: string;
  body?: string;
  tail?: string;
  link?: {
    mobile: string;
    web: string;
    uri: string;
    redirect: string;
    loginLink?: string;
    signupLink?: string;
  };
  headColon?: boolean;
  bodyColon?: boolean;
  headBold?: boolean;
  tailBold?: boolean;
  list?: {
    upcomingDueDateTask?: {
      title: string;
      link: string;
    }[];
    overdueTask?: {
      title: string;
      link: string;
    }[];
    submittedWorkspace?: {
      title: string;
      link: string;
    }[];
    inReviewWorkspace?: {
      title: string;
      link: string;
    }[];
    rejectedWorkspace?: {
      title: string;
      link: string;
    }[];
    cpmTask?: cpmTask[];
  };
  date?: string;
  unsubscribeWeeklyLink?: string;
  subscriber?: {
    firstName: string;
  };
  projectName?: string;
  pushMessageTitle?: string;
}

export const novu = new Novu(process.env.NOVU_API_KEY, {
  backendUrl: process.env.NOVU_BACKEND_URL
});

@Injectable()
export class NovuService {
  constructor(private readonly webSocketService: WebSocketService) {}

  async createSubscriber(user: UserEntity) {
    try {
      return await novu.subscribers
        .identify(user.id.toString(), {
          email: user.email,
          firstName: user.name,
          phone: user.phoneNo,
          avatar: user.avatar
        })
        .then(res => {
          return res.data;
        })
        .catch(err => {
          throw err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'createSubscriber');
    }
  }

  async updateSubscriber(user: UserEntity) {
    try {
      return await novu.subscribers
        .update(user.id.toString(), {
          email: user.email,
          firstName: user.name,
          phone: user.phoneNo,
          avatar: user.avatar
        })
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'updateSubscriber');
    }
  }

  async trigger(eventId: string, data: ITriggerPayloadOptions | any) {
    try {
      await novu
        .trigger(eventId, data)
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });

      const socketEvent = 'event:notification';
      const room = `notification-room-${data?.to?.subscriberId}`;
      return this?.webSocketService?.socket?.to?.(room)?.emit?.(socketEvent, data);
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'trigger');
    }
  }

  async deleteTrigger(transactionId: string) {
    try {
      return await fetch(`${process.env.NOVU_BACKEND_URL}/v1/events/trigger/${transactionId}`, {
        method: 'DELETE'
      })
        .then(res => {
          return res.json();
        })
        .catch(err => {
          return err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'deleteTrigger');
    }
  }

  async getNotification(subscriberId: number, page: number) {
    try {
      return await fetch(
        `${process.env.NOVU_BACKEND_URL}/subscribers/${subscriberId}/notifications/feed?page=${page}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `ApiKey ${process.env.NOVU_API_KEY}`
          }
        }
      )
        .then(async res => {
          return await res.json();
        })
        .catch(err => {
          return err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'getNotification');
    }
  }

  async getUnreadNotification(subscriberId: number) {
    try {
      return await fetch(`${process.env.NOVU_BACKEND_URL}/subscribers/${subscriberId}/notifications/unseen?limit=1`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `ApiKey ${process.env.NOVU_API_KEY}`
        }
      })
        .then(async res => {
          return await res.json();
        })
        .catch(err => {
          return err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'getUnreadNotification');
    }
  }

  async markAsRead(subscriberId: number, messageId: string) {
    try {
      return await fetch(`${process.env.NOVU_BACKEND_URL}/subscribers/${subscriberId}/messages/markAs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `ApiKey ${process.env.NOVU_API_KEY}`
        },
        body: JSON.stringify({
          messageId,
          mark: {
            read: true,
            seen: true
          }
        })
      })
        .then(async res => {
          return await res.json();
        })
        .catch(err => {
          return err;
        });
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'markAsRead');
    }
  }

  async setDeviceToken(subscriberId: string, deviceToken: string) {
    try {
      // get all the previous device token from the subscriber
      const subscriber = await novu.subscribers
        .get(subscriberId)
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });

      const previousDeviceTokens = subscriber.data?.channels[0]?.credentials?.deviceTokens || [];

      await novu.subscribers
        .setCredentials(subscriberId, PushProviderIdEnum.FCM, {
          deviceTokens: [...previousDeviceTokens, deviceToken]
        })
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });
    } catch (error) {
      getErrorMessage(error, 'NovuService', 'setDeviceToken');
    }
  }

  async removeDeviceToken(subscriberId: string, deviceToken: string) {
    try {
      // get all the previous device token from the subscriber
      const subscriber = await novu.subscribers
        .get(subscriberId)
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });

      const previousDeviceTokens = subscriber.data?.channels[0]?.credentials?.deviceTokens || [];
      const removedToken = previousDeviceTokens.filter((token: string) => token !== deviceToken);

      await novu.subscribers.deleteCredentials(subscriberId, PushProviderIdEnum.FCM);

      await novu.subscribers
        .setCredentials(subscriberId, PushProviderIdEnum.FCM, {
          deviceTokens: removedToken
        })
        .then(res => {
          return res.data;
        })
        .catch(err => {
          return err;
        });
    } catch (error) {
      getErrorMessage(error, 'NovuService', 'removeDeviceToken');
    }
  }

  async getSubscriberPreference(subscriberId: string) { 
    try {
      return await novu.subscribers.getPreferenceByLevel(subscriberId, PreferenceLevelEnum.GLOBAL).then(res => { 
        return res?.data.data[0].preference
      });
    } catch (e) { 
      throw e;
    }
  }

  async updateNotificationPreference(subscriberId: string, preference: boolean) { 
    try {
      return await novu.subscribers
        .updateGlobalPreference(subscriberId, { enabled: true, preferences: [{ type: ChannelTypeEnum.EMAIL, enabled: preference }] })
        .then(res => {
          return res.data.data.preference;
        })
    } catch (e) {
      getErrorMessage(e, 'NovuService', 'updateNotificationPreference');
    }
  
  }

  hmacHash(subscriberId: string) {
    return createHmac('sha256', process.env.NOVU_API_KEY).update(subscriberId).digest('hex');
  }
}
