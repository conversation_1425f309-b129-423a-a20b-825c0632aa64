import { FileChannelTypes } from '@constants';
import { replaceSpaceWithDash } from '@constants/function';
import { BadRequestException, Injectable } from '@nestjs/common';
import { FileUpload } from 'graphql-upload';
import _ from 'lodash';
import { nanoid } from 'nanoid/async';
import { AutodeskService } from './autodesk/autodesk.service';
import { TMOneService } from './tmOneObs/tmOneObs.service';
import { getErrorMessage } from '@common/error';

@Injectable()
export class FileService {
  constructor(private tmOneService: TMOneService, private autodeskService: AutodeskService) {}

  async uploadGqlFile(file: FileUpload | Promise<FileUpload>, folder?: string, logParams?: any, stream?: any) {
    try {
      if (!_.isObject(file)) throw new BadRequestException('Failed to upload file.');

      const { mimetype } = await file;
      let { filename } = await file;

      if (filename.match(/[#+%?–—]/)) {
        filename = filename.replace(/[#+%?–—]/g, '');
      }

      const id = await nanoid(15);
      const type = _.last(_.split(filename, '.'));

      // If File is Forge/Drawing File, Upload It to Forge Bucket
      if (mimetype === 'application/octet-stream' && type !== 'mpp') {
        const forgeFileName = `${id}-${replaceSpaceWithDash(filename)}`;
        return 
        // await this.uploadFileToForge(forgeFileName, filename, file, type);
      }
      // Else Upload File to TM One Server Bucket
      const tmFileName = `${folder ? folder + '/' : ''}${id}-${replaceSpaceWithDash(filename)}`;
      return await this.uploadFileToTmOne(tmFileName, mimetype, filename, file, type, logParams, stream);
    } catch (err) {
      getErrorMessage(err, 'FileService', 'uploadGqlFile');
    }
  }

  private async uploadFileToTmOne(
    tmFileName: string,
    mimetype: string,
    filename: string,
    file: any,
    type: string,
    logParams?: any,
    stream?: any
  ) {
    try {
      const contentType = mimetype === 'application/pdf' ? 'application/pdf' : 'text/plain';
      const formParams = {
        'x-obs-acl': 'public-read',
        'Content-Type': contentType
      };

      const { policy, signature } = await this.tmOneService.getPolicyAndSignature(tmFileName, formParams);

      const fileSize = await this.tmOneService.uploadTmOneFile(
        tmFileName,
        policy,
        signature,
        contentType,
        file,
        logParams,
        stream
      );
      const key = tmFileName;
      const channel = FileChannelTypes.OBS;

      return { filename, key, fileSize, type, channel };
    } catch (err) {
      getErrorMessage(err, 'FileService', 'uploadFileToTmOne');
    }
  }
}
