import { Injectable, Logger } from '@nestjs/common';
import { createHmac, timingSafeEqual } from 'crypto';
import { HttpService } from '@nestjs/axios';
import { map } from 'rxjs';
import { getErrorMessage } from '@common/error';
import { BillplzCallbackDto } from '@modules/sales-order/dto/sales-order.gql.dto';

@Injectable()
export class BillplzService {
  private readonly logger = new Logger(BillplzService.name);
  private apiKey: string;
  private xSignatureKey: string;
  private baseUri: string;
  private collectionId: string;

  constructor(private httpService: HttpService) {
    this.apiKey = process.env.BILLPLZ_API_KEY?.trim();
    this.xSignatureKey = process.env.BILLPLZ_X_SIGNATURE_KEY;
    // Don't add version in baseUri as it's already in the env var
    this.baseUri = process.env.BILLPLZ_URI || 'https://www.billplz.com/api';
    this.collectionId = process.env.BILLPLZ_COLLECTION_ID?.trim();
    this.logger.log(
      `Billplz configuration: URI=${this.baseUri}, Collection=${this.collectionId}, API Key=${
        this.apiKey ? this.apiKey.substr(0, 8) + '...' : 'missing'
      }`
    );
  }

  /**
   * Create a bill in Billplz
   * @param details Description of the bill
   * @param amount Amount in cents (e.g., 100 for RM 1.00)
   * @param orderId Order ID for reference
   * @param name Customer name
   * @param email Customer email
   * @param mobile Customer mobile number (optional)
   * @param callbackUrl Webhook URL for payment notification
   * @param redirectUrl URL to redirect after payment
   * @returns Bill object from Billplz
   */
  async createBill(
    details: string,
    amount: number,
    orderId: string,
    name: string,
    email: string,
    callbackUrl: string,
    redirectUrl?: string
  ): Promise<any> {
    try {
      // Don't append /bills if the URI already contains the full path
      const url = `${this.baseUri}/bills`;

      const payload = {
        collection_id: this.collectionId,
        description: details,
        email: email,
        name: name,
        amount: amount,
        callback_url: callbackUrl,
        redirect_url: redirectUrl || null,
        reference_1_label: 'Order ID',
        reference_1: orderId
      };

      this.logger.debug(`Creating bill with URL: ${url}`);
      this.logger.debug(`Payload: ${JSON.stringify(payload)}`);
      this.logger.debug(`Callback URL: ${callbackUrl}, Redirect URL: ${redirectUrl || 'null'}`);

      // Log authentication details without exposing full key
      this.logger.debug(`Using API Key: ${this.apiKey ? this.apiKey.substring(0, 5) + '...' : 'missing'}`);

      // Create Authorization header with Base64 encoded API key
      const authHeader = `Basic ${Buffer.from(`${this.apiKey}:password`).toString('base64')}`;
      this.logger.debug(`Authorization Header: ${authHeader}`);

      const response = await this.httpService
        .post(url, payload, {
          auth: {
            username: this.apiKey,
            password: ''
          }
        })
        .pipe(map(response => response.data))
        .toPromise();

      this.logger.debug(`Billplz response: ${JSON.stringify(response)}`);
      return response;
    } catch (e) {
      this.logger.error(`Billplz API error: ${e.message}`);
      if (e.response) {
        this.logger.error(`Response status: ${e.response.status}`);
        this.logger.error(`Response data: ${JSON.stringify(e.response.data)}`);

        // Additional debugging for authentication issues
        if (e.response.status === 401) {
          this.logger.error('Authentication failed. Please check API key and credentials.');
          this.logger.error(`API Key length: ${this.apiKey ? this.apiKey.length : 0}`);
          this.logger.error(`Collection ID: ${this.collectionId}`);
        }
      }

      // Rethrow the error after logging
      throw e;
    }
  }

  /**
   * Get bill details from Billplz
   * @param billId Billplz bill ID
   * @returns Bill object
   */
  async getBill(billId: string): Promise<any> {
    try {
      this.logger.debug(`[BillplzService] Fetching bill details for bill ID: ${billId}`);

      // Use the correct version for bills endpoint
      const url = `${this.baseUri}/bills/${billId}`;
      this.logger.debug(`[BillplzService] Request URL: ${url}`);

      // Create Authorization header with Base64 encoded API key
      const authHeader = `Basic ${Buffer.from(`${this.apiKey}:password`).toString('base64')}`;

      const response = await this.httpService
        .get(url, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: authHeader
          }
        })
        .pipe(map(response => response.data))
        .toPromise();

      if (!response) {
        this.logger.warn(`[BillplzService] No response received for bill ID: ${billId}`);
        return null;
      }

      this.logger.debug(`[BillplzService] Bill details retrieved successfully for bill ID: ${billId}`);

      // Log important bill details for debugging
      if (response.reference_1) {
        this.logger.debug(`[BillplzService] Bill reference_1 (order ID): ${response.reference_1}`);
      } else {
        this.logger.warn(`[BillplzService] Bill is missing reference_1 field: ${JSON.stringify(response)}`);
      }

      return response;
    } catch (e) {
      this.logger.error(`[BillplzService] Error fetching bill details for bill ID ${billId}: ${e.message}`);
      if (e.response) {
        this.logger.error(`[BillplzService] Response status: ${e.response.status}`);
        this.logger.error(`[BillplzService] Response data: ${JSON.stringify(e.response.data)}`);
      }
      return null; // Return null instead of throwing an error to handle gracefully in the controller
    }
  }

  public verifyRedirectSignature(
    redirectParams: { id?: string; paid_at?: string; paid?: string },
    receivedXSignature: string
  ): boolean {
    this.logger.debug('[BillplzService] Verifying Redirect X-Signature...');
    this.logger.debug(`[BillplzService] Redirect params for signature base: ${JSON.stringify(redirectParams)}`);
    this.logger.debug(`[BillplzService] Received X-Signature from redirect: ${receivedXSignature}`);

    const { id, paid_at, paid } = redirectParams;

    if (!id || !paid_at || typeof paid === 'undefined' || !receivedXSignature) {
      this.logger.warn('[BillplzService] Missing one or more required parameters for redirect signature verification.');
      return false;
    }

    if (!this.xSignatureKey) {
      this.logger.error('[BillplzService] BILLPLZ_X_SIGNATURE_KEY is not configured in environment variables!');
      return false;
    }

    // Construct the signature base string *exactly* as per Billplz documentation for REDIRECT URL.
    // Format: id<ID_VALUE>|paid_at<PAID_AT_VALUE>|paid<PAID_VALUE>
    // The 'paid' value from redirect query param is typically a string 'true' or 'false'.
    const signatureString = `id${id}|paid_at${paid_at}|paid${String(paid).toLowerCase()}`;

    this.logger.debug(`[BillplzService] Constructed Redirect Signature Base String: [${signatureString}]`);

    // Compute the HMAC-SHA256 signature
    const computedSignature = createHmac('sha256', this.xSignatureKey).update(signatureString).digest('hex');

    this.logger.debug(`[BillplzService] Computed Redirect Signature: ${computedSignature}`);

    const isMatch = computedSignature === receivedXSignature;

    if (!isMatch) {
      this.logger.warn(
        `[BillplzService] Redirect X-Signature Mismatch! System Computed: ${computedSignature}, Billplz Provided: ${receivedXSignature}, Using Base: [${signatureString}]`
      );
    } else {
      this.logger.debug('[BillplzService] Redirect X-Signature verification successful.');
    }
    return isMatch;
  }

  /**
   * Verifies the X-Signature from a Billplz PAYMENT COMPLETION CALLBACK (POST request).
   *
   * Billplz sends a server-to-server POST request to your callback URL upon payment completion.
   * This function verifies the signature of the entire POST body.
   *
   * Billplz V3 Callback Signature Base String Format (as per https://www.billplz.com/api#payment-completion-x-signature-callback-url):
   * 1. Take all parameters from the POST body (except 'x_signature' itself).
   * 2. For each parameter, create a string: 'billplz' + key + value (e.g., 'billplzidXXXX', 'billplzamount1000').
   * 3. Collect all these 'billplz<key><value>' strings.
   * 4. Sort these strings alphabetically (ascending).
   * 5. Join the sorted strings with a pipe '|' character.
   *
   * @param callbackBody The entire parsed POST body object received from Billplz.
   * @returns Boolean - true if the signature is valid, false otherwise.
   */
  public verifyCallbackSignature(payload: BillplzCallbackDto): boolean {
    this.logger.debug(`[BillplzService] Verifying callback signature for bill ID: ${payload.id}`);

    const received = payload.x_signature;
    if (!received) {
      this.logger.warn('[BillplzService] Missing x_signature in callback payload');
      return false;
    }

    if (!this.xSignatureKey) {
      this.logger.error('[BillplzService] BILLPLZ_X_SIGNATURE_KEY is not configured in environment variables!');
      return false;
    }

    // Log important callback data for debugging
    this.logger.debug(`[BillplzService] Callback data - Bill ID: ${payload.id}, Paid: ${payload.paid}, Reference_1: ${payload.reference_1}`);

    // 1) Extract all fields except x_signature as raw strings
    const data: Record<string, string> = {};
    for (const [k, v] of Object.entries(payload)) {
      if (k !== 'x_signature') {
        data[k] = String(v ?? '');
      }
    }

    // 2) Build an array of "key+value" strings, then sort that array lexicographically
    const keyValuePairs = Object.entries(data)
      .map(([k, v]) => `${k}${v}`)
      .sort(); // <-- sort the combined strings, not just the keys

    // 3) Join with '|' to form the base string
    const baseString = keyValuePairs.join('|');
    this.logger.debug(`[BillplzService] Billplz base string: ${baseString}`);

    // 4) HMAC-SHA256 → hex
    const computedHex = createHmac('sha256', this.xSignatureKey).update(baseString).digest('hex');
    this.logger.debug(`[BillplzService] Computed signature: ${computedHex}`);
    this.logger.debug(`[BillplzService] Received signature: ${received}`);

    // 5) Constant-time compare
    const aU8 = new Uint8Array(Buffer.from(computedHex, 'hex'));
    const bU8 = new Uint8Array(Buffer.from(received, 'hex'));
    if (aU8.length !== bU8.length) {
      this.logger.warn('[BillplzService] Signature length mismatch');
      return false;
    }

    const valid = timingSafeEqual(aU8, bU8);
    if (!valid) {
      this.logger.warn(`[BillplzService] Signature mismatch — computed=${computedHex} provided=${received}`);
    } else {
      this.logger.debug('[BillplzService] Signature verification successful');
    }

    return valid;
  }

  /**
   * Get payment gateways available in Billplz
   * @returns List of payment gateways
   */
  async getPaymentGateways(): Promise<any> {
    try {
      // Use the correct version for payment_gateways endpoint
      const url = `${this.baseUri.replace('/v3', '/v4')}/payment_gateways`;

      // Create Authorization header with Base64 encoded API key
      const authHeader = `Basic ${Buffer.from(`${this.apiKey}:password`).toString('base64')}`;

      const response = await this.httpService
        .get(url, {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: authHeader
          }
        })
        .pipe(map(response => response.data))
        .toPromise();

      return response;
    } catch (e) {
      getErrorMessage(e, 'BillplzService', 'getPaymentGateways');
    }
  }
}
