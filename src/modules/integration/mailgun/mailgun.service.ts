import { Injectable } from '@nestjs/common';
import formData from 'form-data';
import Mailgun from 'mailgun.js';
import dotenv from 'dotenv';
import { MailgunDto } from '../dto/mailgun.dto';
import { getErrorMessage } from '@common/error';
import path from 'path';
import axios from 'axios';
const fs = require('fs');
dotenv.config();

const { MAILGUN_API_USER, MAILGUN_API_KEY, MAILGUN_SENDER_EMAIL, MAILGUN_DOMAIN, MAILGUN_CORRESPONDENCE_DOMAIN } = process.env;

const mailgun = new Mailgun(formData);
const mg = mailgun.client({
  username: MAILGUN_API_USER,
  key: MAILGUN_API_KEY || 'key-yourkeyhere'
});

@Injectable()
export class MailgunService {
  async sendMail(input: MailgunDto) {    
          
    try {
      
      const messageData: any = {
        from: input?.type === 'correspondence' ? `${input.name} via Bina Cloud Correspondence <${input?.reply || input?.reply === undefined ? 'reply' : 'noreply'}@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}>` : `BINA <${MAILGUN_SENDER_EMAIL}>`,
        to: input.to,
        cc: input.cc, 
        subject: input.subject,
        text: input.text,
        template: input.template,
        html: input.html,        
        'h:X-Mailgun-Variables': JSON.stringify({
          inviter_name: input.name,
          user_name: input.name,
          project_title: input.projectTitle,
          url: input.url,
          signBy: input.signBy,
          owner: input.owner,
          form: input.form,
          title: input.title,
          assigner: input.assigner,
          dueDate: input.dueDate,
          message: input.message,
          loginLink: input.loginLink,
          signupLink: input.signupLink,
          email: input.email,
          file: input.file,
          recipient: input.recipient,
          reference: input.reference,
          sentAt: input.sentAt,
          subject: input.subject,
        })
      };

      if (input.replyTo) {
        messageData['h:Reply-To'] = input.replyTo;
      }   

       // Function to download multiple files from attachmentUrls
       const downloadFiles = async (urls: any[]): Promise<string[]> => {
        return Promise.all(
          urls.map(async (attachment) => {
            // Support both string URLs and {url, name} objects

            const url = typeof attachment === 'string' ? attachment : attachment.url;
            // Use provided name or extract from URL if not available
            const filename = attachment.name ?? path.basename(new URL(url).pathname);
            
            const filePath = path.join('/tmp', filename); // Save in /tmp            
            
            const response = await axios({
              url,
              method: 'GET',
              responseType: 'arraybuffer', // Download as binary
            });            
            fs.writeFileSync(filePath, response.data); // Save to temp storage
            return filePath;
          })
        );
      };

      // Process Attachments
      let tempFiles: string[] = [];
      if (input.attachmentUrls && input.attachmentUrls.length > 0) {
        tempFiles = await downloadFiles(input.attachmentUrls); // Download all files
        messageData.attachment = tempFiles.map(filePath => ({
          filename: path.basename(filePath),
          data: fs.createReadStream(filePath),
        }));
      }

      // inline
      // if (input.inlineUrls && input.inlineUrls.length > 0) {
      //   tempFiles = await downloadFiles(input.inlineUrls); // Download all files
      //   messageData.inline = input.inline[0]
      //   console.log("🚀 ~ MailgunService ~ sendMail ~  input.html:",  input.html)
      //   messageData.html = input.html
      // }         

      return await mg.messages
      .create(
        `${input?.type === 'correspondence' ? MAILGUN_CORRESPONDENCE_DOMAIN : MAILGUN_DOMAIN}`,
        messageData
      )
      .then((msg) => {
                
        return msg; // Return the response to assign it to `res`
      })
      .catch((err) => {        
        throw err; // Re-throw the error to be caught by the outer `catch`
      }).finally(() => {
        // Clean up: Delete temporary files
        tempFiles.forEach((filePath) => {          
          fs.unlinkSync(filePath);
        });
      });          

    } catch (e) {      
      getErrorMessage(e, 'MailgunService', 'sendMail');
    }
  }

  
}
