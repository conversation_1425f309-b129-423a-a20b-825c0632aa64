import { FileLogEntity } from '@modules/file-log/entity/file-log.entity';
import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import ObsClient from 'esdk-obs-nodejs';
import * as _ from 'lodash';
import { getRepository } from 'typeorm';
import { Readable } from 'stream';
import { getErrorMessage } from '@common/error';
require('events').EventEmitter.setMaxListeners(100);
// require('events').EventEmitter.prototype._maxListeners = 1;

const { TMONE_BUCKET, TMONE_ACCESS_KEY_ID, TMONE_SECRET_ACCESS_KEY, TMONE_SERVER, TMONE_OBS_STORAGE, OBS_EXPIRY_TIME } = process.env;
const obsClient = new ObsClient({
  access_key_id: TMONE_ACCESS_KEY_ID,
  secret_access_key: TMONE_SECRET_ACCESS_KEY,
  server: TMONE_SERVER
});

@Injectable()
export class TMOneService {
  constructor(private readonly httpService: HttpService) {}
  // Get Policy and Signature before Upload File
  async getPolicyAndSignature(fileName: string, formParams: any) {
    try {
      const res = await obsClient.createPostSignatureSync({
        Bucket: TMONE_BUCKET,
        Key: fileName,
        Expires: 3600,
        ...formParams
      });
      const policy: string = res['Policy'];
      const signature: string = res['Signature'];
      return { policy, signature };
    } catch (err) {
      getErrorMessage(err, 'TMOneService', 'getPolicyAndSignature');
    }
  }

  // Upload File TM One Server Bucket
  async uploadTmOneFile(
    fileName: string,
    policy: string,
    signature: string,
    contentType: string,
    file: any,
    logParams?: any,
    readStream?: any
  ) {
    try {
      const waitedFile = await file;
      if (!waitedFile) throw 'File error, try a different file.';

      // WATERMARK ID FILE UPLOAD
      if (readStream) {
        const readableStream = new Readable({
          read() {
            this.push(readStream);
            this.push(null); // Signal the end of the stream
          }
        });

        const { fileSizeInMB } = await this.getFileSize(readStream);

        let fileLog: FileLogEntity = null;

        // LOGGING ONLY IF LOG PARAMS OBJECT IS SENT FROM PARENT
        if (logParams) {
          const logParamFinal = {
            ...logParams,
            before_file_size: fileSizeInMB,
            after_file_url: fileName,
            after_timestamp: new Date().toString()
          };
          fileLog = await getRepository(FileLogEntity).save(logParamFinal);
        }

        // THROWING IF FILE SIZE IS 0 OR > 100MB EVEN IF NOT LOGGED (EVEN IF PARENT DIDNT SEND LOG OBJECT)
        if (+fileSizeInMB === 0) throw 'Cannot upload file size with 0 KB';
        // if (+fileSizeInMB > 100) throw 'Cannot upload file size of more than 100 MB';

        // UPLOAD TO TM ONE BUCKET
        await obsClient.putObject(
          {
            Bucket: TMONE_BUCKET,
            Key: fileName,
            Body: readableStream,
            Policy: policy,
            Signature: signature,
            Metadata: { 'Content-Type': contentType }
          },
          (err, result) => {
            if (err) {
              console.error('Error uploading object:', err);
              throw err;
            }
            this.checkMetadata(fileName, fileLog);
          }
        );

        return fileSizeInMB;
      } else {// NORMAL FILES UPLOAD
        const { createReadStream } = await file;

        const { fileSizeInMB } = await this.getFileSize(await createReadStream());

        let fileLog: FileLogEntity = null;

        // LOGGING ONLY IF LOG PARAMS OBJECT IS SENT FROM PARENT
        if (logParams) {
          const logParamFinal = {
            ...logParams,
            before_file_size: fileSizeInMB,
            after_file_url: fileName,
            after_timestamp: new Date().toString()
          };
          fileLog = await getRepository(FileLogEntity).save(logParamFinal);
        }

        // THROWING IF FILE SIZE IS 0 OR > 100MB EVEN IF NOT LOGGED (EVEN IF PARENT DIDNT SEND LOG OBJECT)
        // if (+fileSizeInMB === 0) throw 'Cannot upload file size with 0 KB';
        if (+fileSizeInMB > 100) throw 'Cannot upload file size of more than 100 MB';
        // if (+fileSizeInMB <= 0.015) throw 'Cannot upload file size of 15 KB or less';

        // UPLOAD TO TM ONE BUCKET
        await obsClient.putObject(
          {
            Bucket: TMONE_BUCKET,
            Key: fileName,
            Body: await createReadStream(),
            Policy: policy,
            Signature: signature,
            Metadata: { 'Content-Type': contentType }
          },
          async (err, result) => {
            if (err) {
              console.error('Error uploading object:', err);
              throw err;
            }
            await this.checkMetadata(fileName, fileLog);
          }
        );
        return fileSizeInMB;
      }
    } catch (err) {
      getErrorMessage(err, 'TMOneService', 'uploadTmOneFile');
    }
  }

  checkMetadata = async (fileName, fileLog) => {
    try {
      await obsClient.getObjectMetadata(
        {
          Bucket: TMONE_BUCKET,
          Key: fileName
        },
        (err, result) => {
          if (err) {
            // REPEAT CHECKING UNTIL TMONE REFLECT METADATA
            if (err.CommonMsg.Code === 'NoSuchKey') {
              console.log('Object metadata not available yet. Retrying...');
              setTimeout(() => this.checkMetadata(fileName, fileLog), 500); // Retry after 1 second
            } else {
              console.error('Error getting object metadata:', err);
            }
          } else {
            // SAVE POST-UPLOADED SIZE AND NAME TO FILE LOG
            const logParamFinal = {
              ...fileLog,
              after_file_size: _.floor(result.InterfaceResult.ContentLength / (1024 * 1024), 2),
              after_file_name: fileName
            };
            getRepository(FileLogEntity).save(logParamFinal);
          }
        }
      );
    } catch (err) {
      getErrorMessage(err, 'TMOneService', 'checkMetadata');
    }
  };

  async duplicateFile(sourceFileKey: string, destFileKey: string) {
    await obsClient.copyObject({
      Bucket: TMONE_BUCKET,
      Key: destFileKey,
      CopySource: `${TMONE_BUCKET}/${sourceFileKey}`
    });

    const fileKey = destFileKey;
    return { fileKey };
  }

  private async getFileSize(stream: any) {
    let fileSize = 0;

    for await (const uploadChunk of stream) {
      fileSize += (uploadChunk as Buffer).byteLength;
    }
    const fileSizeInMB = _.floor(fileSize / (1024 * 1024), 2);

    return {
      fileSizeInMB
    };
  }

  async getPresignedUrl(fileKey: string) {
    try {
      // GENERATE PRESIGNED URL FOR VIEW
      const signedUrl = await obsClient.createSignedUrlSync({
        Method: 'GET',
        Bucket: TMONE_BUCKET,
        Key: fileKey,
        Expires: OBS_EXPIRY_TIME
      });

      return signedUrl;
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
    }
  }

  async getDirectUrl(fileKey: string) {
    return TMONE_OBS_STORAGE + encodeURIComponent(fileKey);
  }

  async  waitForFileUpload(fileKey: string, maxRetries = 10, delay = 1000): Promise<boolean> {
    let retries = 0;
  
    while (retries < maxRetries) {        
  
        const fileExists = await new Promise<boolean>((resolve) => {
            obsClient.getObjectMetadata(
                {
                    Bucket: TMONE_BUCKET,
                    Key: fileKey,
                },
                (err: any, result: any) => {
                    
                    if (err) {
                        if (err.Status === 404) {                            
                            resolve(false);
                        } else {
                            
                            resolve(false);
                        }
                    } else {                        
                        resolve(true);
                    }
                }
            );
        });
  
        if (fileExists) return true; // File found, exit loop
  
        retries++;
        await new Promise(resolve => setTimeout(resolve, delay)); // Wait before retrying
    }  
        return false; 
    }
  
}
