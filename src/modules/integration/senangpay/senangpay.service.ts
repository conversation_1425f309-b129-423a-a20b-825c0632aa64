import { Injectable } from '@nestjs/common';
import { createHmac } from 'crypto';
import { HttpService } from '@nestjs/axios';
import { map } from 'rxjs';
import { getErrorMessage } from '@common/error';

@Injectable()
export class SenangPayService {
  private merchantId: string;
  private secretKey: string;
  private baseUri: string;

  constructor(private httpService: HttpService) {
    this.merchantId = process.env.SENANGPAY_MERCHANT_ID;
    this.secretKey = process.env.SENANGPAY_SECRET_KEY;
    this.baseUri = process.env.SENANGPAY_URI;
  }

  generateHash(details: string, amount: number, orderId: string): string {
    const hashString =
      this.secretKey +
      decodeURIComponent(details) +
      decodeURIComponent(amount.toString()) +
      decodeURIComponent(orderId);
    const hash = createHmac('sha256', this.secretKey).update(hashString).digest('hex');
    return hash;
  }

  async transactionStatus(transactionReference: string): Promise<any> {
    try {
      const hash = this.generateTransactionStatusHash(transactionReference);
      const url = `${this.baseUri}/apiv1/query_transaction_status`;

      const queryParams = {
        merchant_id: this.merchantId,
        transaction_reference: transactionReference,
        hash: hash
      };

      const response = await this.httpService
        .get(url, { params: queryParams })
        .pipe(map(response => response.data))
        .toPromise();

      return response;
    } catch (e) {
      getErrorMessage(e, 'SenangPayService', 'transactionStatus');
    }
  }

  private generateTransactionStatusHash(transactionReference: string): string {
    const hashString = this.merchantId + this.secretKey + decodeURIComponent(transactionReference);
    const hash = createHmac('sha256', this.secretKey).update(hashString).digest('hex');
    return hash;
  }

  async orderStatus(orderId: string): Promise<any> {
    try {
      const hash = this.generateOrderStatusHash(orderId);
      const url = `${this.baseUri}/apiv1/query_order_status`;

      const queryParams = {
        merchant_id: this.merchantId,
        order_id: orderId,
        hash: hash
      };

      const response = await this.httpService
        .get(url, { params: queryParams })
        .pipe(map(response => response.data))
        .toPromise();

      return response;
    } catch (e) {
      getErrorMessage(e, 'SenangPayService', 'orderStatus');
    }
  }

  private generateOrderStatusHash(orderId: string): string {
    const hashString = this.merchantId + this.secretKey + decodeURIComponent(orderId);
    const hash = createHmac('sha256', this.secretKey).update(hashString).digest('hex');
    return hash;
  }
}
