import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { ChangeLogMobileEntity } from './entity/change-log-mobile.entity';

@Injectable()
export class ChangeLogMobileService extends TypeOrmQueryService<ChangeLogMobileEntity> {
  constructor(
    @InjectRepository(ChangeLogMobileEntity)
    private changeLogRepo: Repository<ChangeLogMobileEntity>
  ) {
    super(changeLogRepo, { useSoftDelete: true });
  }
}
