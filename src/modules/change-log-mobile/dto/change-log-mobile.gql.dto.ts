import { defaultQueryOptions } from '@constants';
import { QueryOptions } from '@nestjs-query/query-graphql';
import { InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ChangeLogMobileEntity } from '../entity/change-log-mobile.entity';

@ObjectType('ChangeLogMobile')
@QueryOptions({ ...defaultQueryOptions })
export class ChangeLogMobileDto extends ChangeLogMobileEntity {}
@InputType()
export class CreateChangeLogMobileInputDTO {
  descriptions?: string;
}
@InputType()
export class UpdateChangeLogMobileInputDTO extends PartialType(CreateChangeLogMobileInputDTO) {}
