import { BaseEntity } from '@modules/base/base';
import { Entity, Column } from 'typeorm';
import { ObjectType } from '@nestjs/graphql';
import { FilterableField } from '@nestjs-query/query-graphql';

@ObjectType()
@Entity('change_logs_mobile')
export class ChangeLogMobileEntity extends BaseEntity {
  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  imageUrl: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  title: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  buttonName: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  buttonUrl: string;
}
