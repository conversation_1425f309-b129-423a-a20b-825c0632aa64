import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { ChangeLogMobileService } from './change-log-mobile.service';
import { ChangeLogMobileDto } from './dto/change-log-mobile.gql.dto';
import { ChangeLogMobileEntity } from './entity/change-log-mobile.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { ChangeLogMobileSubscriber } from './change-log-mobile.subscriber';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ChangeLogMobileEntity]), IntegrationModule],
      resolvers: [
        {
          // ServiceClass: ChangeLogService,
          DTOClass: ChangeLogMobileDto,
          EntityClass: ChangeLogMobileEntity,
          // CreateDTOClass: CreateChangeLogInputDTO,
          // UpdateDTOClass: UpdateChangeLogInputDTO,

          guards: [GqlAuthGuard, GqlRolesGuard]
        }
      ],

      services: [ChangeLogMobileService, ChangeLogMobileEntity, ChangeLogMobileSubscriber]
    })
  ]
})
export class ChangeLogMobileModule {}
