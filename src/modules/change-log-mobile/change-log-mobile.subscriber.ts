import { BadRequestException, Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import _ from 'lodash';
import { FileService } from '@modules/integration/file.service';
import { createReadStream } from 'fs';
import { ChangeLogMobileEntity } from '@modules/change-log-mobile/entity/change-log-mobile.entity';


@Injectable()
@EventSubscriber()
export class ChangeLogMobileSubscriber implements EntitySubscriberInterface<ChangeLogMobileEntity> {
  constructor(connection: Connection, private fileService: FileService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ChangeLogMobileEntity;
  }

  async beforeInsert(event: InsertEvent<ChangeLogMobileEntity>) {
    const { entity } = event;

    const file: any = entity.imageUrl;
    if (!file) throw new BadRequestException('No image has been upload');

    const preparedFile = await this.prepareFileObject(file);

    if (file && _.isObject(file)) {
      const folder = 'ChangeLog';
      const { key } = await this.fileService.uploadGqlFile(preparedFile as any, folder);
      entity.imageUrl = key;
    }
  }

  async prepareFileObject(file) {
  return Promise.resolve({
    filename: file?.name,
    mimetype: file?.type,
    encoding: '7bit',
    createReadStream: () => createReadStream(file.path)
  });
}
}
