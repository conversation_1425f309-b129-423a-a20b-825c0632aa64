import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, InsertEvent, Repository } from 'typeorm';
import { EmailEntity } from './entity/email.entity';
import { getErrorMessage } from '@common/error';
import { EmailService } from './email.service';
import { FileService } from '@modules/integration/file.service';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';
import { EmailAssetType } from '@constants';
import { EmailDeliveryStatus } from './dto/email.api.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { TMOneService } from '@modules/integration/tmOneObs/tmOneObs.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Injectable()
@EventSubscriber()
export class EmailSubscriber implements EntitySubscriberInterface<EmailEntity> {
  constructor(connection: Connection,  
    private emailService: EmailService,
    private fileService: FileService,
    @InjectRepository(EmailAssetEntity)
    private attachmentRepo: Repository<EmailAssetEntity>,
    private tmOneService: TMOneService,
    @InjectQueue('email-correspondence-process') private emailCorrespondenceQueue: Queue
  ) {
    
    connection.subscribers.push(this);
  }

  listenTo() {
    return EmailEntity;
  }

  async beforeInsert(event: InsertEvent<EmailEntity>){
    try{    
      // check if reference unique
      // const { reference } = event.entity;
      // const email = await event.manager.findOne(EmailEntity, { where: { reference } });

      // if(email){
      //   throw new BadRequestException('Email with reference already exists');
      // }

    } catch(e){
      getErrorMessage(e, 'EmailSubscriber', 'beforeInsert');
    }
  }

  async afterInsert(event: any) {
    const { deliveryStatus, id, attachmentFiles, inline, inlineFiles } = event.entity;    

    try {
      const attachments = []
        if (attachmentFiles?.length > 0) {
          const folder = 'Email-attachments';
      
          // First, resolve all file promises
          const resolvedFiles = await Promise.all(attachmentFiles);

          const mimeToExtensionMap: Record<string, string> = {
            "image/bmp": "bmp",
            "application/msword": "doc",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
            "text/htm": "htm",
            "text/html": "html",
            "image/jpg": "jpg",
            "image/jpeg": "jpeg",
            "application/pdf": "pdf",
            "image/png": "png",
            "application/vnd.ms-powerpoint": "ppt",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
            "image/tiff": "tiff",
            "text/plain": "txt",
            "application/vnd.ms-excel": "xls",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx"
          };
      
          // Process files sequentially using for...of loop
          for (const file of resolvedFiles) {
              const { key } = await this.fileService.uploadGqlFile(file, folder);
              await this.tmOneService.waitForFileUpload(key);
  
              const emailAsset = new EmailAssetEntity();
              emailAsset.emailId = id;
              emailAsset.assetKey = key;
              emailAsset.assetType = EmailAssetType.Attachment;
              emailAsset.fileExtension =  mimeToExtensionMap[file.mimetype] || file.mimetype.split('/')[1];
              emailAsset.rawMetadata = '{}';
              emailAsset.name = file.filename;
              const res = await event.manager.save(EmailAssetEntity, emailAsset);
              attachments.push(res);              
          }
        }
    
        if (deliveryStatus === EmailDeliveryStatus.Sending) {
          
            // Add job to the queue instead of directly calling the service
            await this.emailCorrespondenceQueue.add(
              'send-email-correspondence',
              { id: event.entity.id },
              {
                attempts: 3,
                backoff: {
                  type: 'exponential',
                  delay: 1500, // 1.5 seconds
                },
              }
            );
                        
            const currentEmail = await event.manager.findOne(EmailEntity, { where: { id: id } });
            if (currentEmail) {                
                await event.manager.save(EmailEntity, currentEmail);
            }
        }

    } catch (e) {
        getErrorMessage(e, 'EmailSubscriber', 'afterInsert');
    }
  }
}
