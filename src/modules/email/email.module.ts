import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { UseUploadFilePipe } from 'src/pipes/gql-upload.pipe';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlRolesGuard } from '@guards/roles.guard';
import { IntegrationModule } from '@modules/integration/integration.module';
import { EmailEntity } from './entity/email.entity';
import { EmailDto, CreateEmailInputDTO, UpdateEmailInputDTO } from './dto/email.gql.dto';
import { EmailService } from './email.service';
import { EmailController } from './email.controller';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';
import { EmailSubscriber } from './email.subscriber';
import { UserEntity } from '@modules/user/entity/user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';
import { EmailResolver } from './email.resolver';
import { EmailAssetService } from '@modules/email-asset/email-asset.services';
import { NovuService } from '@modules/integration/novu/novu.service';
import { BullModule } from '@nestjs/bullmq';
import { EmailCorrespondenceProcessor } from './email.processor';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([EmailEntity, EmailAssetEntity, UserEntity, ProjectEntity, ContactsEmailEntity]), IntegrationModule],
      resolvers: [
        {
          DTOClass: EmailDto,
          EntityClass: EmailEntity,
          CreateDTOClass: CreateEmailInputDTO,
          UpdateDTOClass: UpdateEmailInputDTO,
          decorators: [UseUploadFilePipe()],
          guards: [GqlAuthGuard, GqlRolesGuard],
        }
      ],
      services: [EmailResolver, EmailService, EmailAssetService, NovuService]
    }),
    // Add BullModule to register the email correspondence queue
    BullModule.registerQueue({
      name: 'email-correspondence-process',
    }),
  ],
  controllers: [EmailController],
  providers: [EmailService, EmailSubscriber, EmailCorrespondenceProcessor]
})
export class EmailModule {}
