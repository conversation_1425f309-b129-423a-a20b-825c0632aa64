import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { EmailEntity } from './entity/email.entity';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';
import { getErrorMessage } from '@common/error';
import { MailgunService } from '@modules/integration/mailgun/mailgun.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { EmailDeliveryStatus } from './dto/email.api.dto';
import moment from 'moment';
import * as cheerio from 'cheerio';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';
import { EmailAssetType } from '@constants';
import { FileService } from '@modules/integration/file.service';
import axios from 'axios';
import { Readable } from 'stream';
import { EmailAssetService } from '@modules/email-asset/email-asset.services';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import  validator  from 'validator';
const { MAILGUN_API_KEY } = process.env;

@Injectable()
export class EmailService extends TypeOrmQueryService<EmailEntity> {
  constructor(
    @InjectRepository(EmailEntity)
    private emailRepo: Repository<EmailEntity>,
    @InjectRepository(EmailAssetEntity)
    private attachmentRepo: Repository<EmailAssetEntity>,
    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
    private mailgunService: MailgunService,
    @InjectRepository(ProjectEntity)
    private projectRepo: Repository<ProjectEntity>,
    @InjectRepository(ContactsEmailEntity)
    private contactEmailRepo: Repository<ContactsEmailEntity>,
    private fileService: FileService,
    private emailAssetService: EmailAssetService,
    private novuService: NovuService,
  ) {
    // pass the use soft delete option to the service.
    super(emailRepo, { useSoftDelete: true });
  }

  async extractEmail(email:any) {
    
    try {
      const recipient = email.recipient           
      const sender = email.from;                    
      const subject = email.subject;
      const references = email.References?.replace(/<|>/g, '');      
      const uuidEmail = email.To?.split('@')[0]?.replace(/<|>/g, '');  

      let project
      // if user send new email using project email use uuid to get project
      // else use recipient email to get project      
      if (validator.isUUID(uuidEmail)) {
        const email = await this.emailRepo.findOne({ where: { uuid: uuidEmail } });        
        project = await this.projectRepo.findOne({where : { id: email?.projectId }})
      } else {
        project = await this.projectRepo.findOne({ where: { projectEmail: email.recipient } });
      }    

      //break if no project is found
      if (!project) return;

      const senderEmail = sender.match(/<(.+)>/)?.[1] || sender;
      const senderDetail = await this.contactEmailRepo.findOne({ where:{ email: senderEmail , projectId: project.id}})      
      if(!senderDetail) return            
      
      if (references){
      // find email with mailgun id
        const emailExist = await this.emailRepo.findOne({ where: { mailgunId: references }});
        
        if(emailExist && !uuidEmail){
          return;
        }
      }          
      
      // Load the HTML content into cheerio
      const $ = cheerio.load(email['body-html']);
      
      // Extract the first <div> with dir="ltr"
      const desiredDiv = $('div[dir="ltr"]').first(); // .first() ensures you get the first occurrence 

      // Save email to database
      const emailEntity = new EmailEntity();
      emailEntity.rawResponse = JSON.stringify(email);
      emailEntity.senderId = senderDetail?.userId || senderDetail?.id;
      emailEntity.subject = subject;
      emailEntity.body = desiredDiv.html();
      emailEntity.projectId = project?.id;
      emailEntity.deliveryStatus = EmailDeliveryStatus.Delivered;
       
      if(validator.isUUID(uuidEmail)){        
        // find email with uuid
        const emailToReply = await this.emailRepo.findOne({where: {uuid: uuidEmail}});    
        const user = await this.userRepo.findOne({ id: emailToReply?.senderId });    
        emailEntity.receivers = user ? await this.contactEmailRepo.find({ where: { userId: user.id , projectId: project.id} }) : [];                
        emailEntity.mailgunId = references;
        emailEntity.replyToId = emailToReply?.id;
        emailEntity.replyAt = moment().toDate();
        emailEntity.recipientId = senderDetail?.id;

        if(emailEntity.reference){
          emailEntity.reference = `Re: ${emailToReply?.reference}`;
        }
        const res = await this.emailRepo.save(emailEntity);
        const company = await getRepository(CompanyEntity).findOne({ id: project?.companyId });

        const mobileLink = `correspondence/${res?.id}`;
        const link = `/correspondence?id=${res?.id}&projectId=${project?.id}&companyId=${company?.id}`;  
        const isNonMember  = await this.userRepo.findOne({ email: senderEmail });  
        
        const payload: INovuPayload = {
          user: {
            avatar: senderDetail.avatarKey,
            name: senderDetail.name,
            email: senderDetail.email
          },
          header : 'New Reply in Correspondence',
          event: 'new-reply-correspondence',
          company:company?.name,
          title: project.title,
          head: senderDetail?.name,
          body: `${isNonMember ? '' : '(Non Member)'} replied to your correspondence`,
          tail: email?.subject,
          bodyColon: true,
          subscriber: {
            firstName: senderDetail.name
          },
          link: {
            mobile: mobileLink,
            web: link,
            redirect: link,
            uri: ''
          }
        };

        const receiver = await this.userRepo.findOne({ id: emailToReply?.senderId });
        if(receiver){
        
        await this.novuService.trigger('secondary-workflow', {
          to: {
            subscriberId: receiver.id.toString(),            
          },
          payload,
          overrides: {
            android: {
              priority: 'high'
            },
            fcm: {
              data: {
                link: mobileLink.toString(),
                projectId: project.id.toString(),
                companyId: project.companyId.toString()
              }
            }
          }
        });
       }
      }else{
        await this.emailRepo.save(emailEntity);
      }

      await this.extractEmailAsset(email, emailEntity.id)

      // status-correspondence-to-non-member
      await this.mailgunService.sendMail({
        type: 'correspondence',
        to: sender,
        from:`noreply@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}`,
        subject: subject,
        template: 'status-correspondence-to-non-member',
        message: desiredDiv.html(),
        title: subject,
        sentAt: moment().tz('Asia/Kuala_Lumpur').format('DD MMMM YYYY [at] hh:mm A'),
        projectTitle: project?.title,
        name: senderDetail?.name,
        email: senderDetail?.email,
        recipient: recipient || project.projectEmail,
        reply: false
      });

    } catch (error) {
      getErrorMessage(error, 'EmailService', 'extractEmail');
    }
  }

  async sendEmailCorrespondence(email: any, attachments: any) {              
    // find email receivers
    const receivers = await this.contactEmailRepo.findByIds(email?.receivers);            
    
    try {      
      const creator = await this.userRepo.findOne({ where: { id: email.createdBy } });                          
      const project = await this.projectRepo.findOne({ id: email.projectId });
      
      const attachmentUrls = [];
      if (attachments?.length > 0) {
        for (const attachment of attachments) {
          
          const assetUrl = await this.emailAssetService.getPresignedUrl(attachment);
          attachmentUrls.push({url: assetUrl, name: attachment.name});
        }
      }
      
      // Send individual emails to each recipient
      for (const receiver of receivers) {
        // send email to recipient
        const res = await this.mailgunService.sendMail({
          type: 'correspondence',
          to: receiver.email,
          from: `reply@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}`,
          subject: email?.subject,              
          template: 'send_email_correspondence_out',
          message: email.body,
          replyTo: `${email.uuid}@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}`,
          name: creator?.name,     
          email: creator?.email,
          title: email.subject,
          recipient: `${receiver.name} <${receiver.email}>`, // Individual recipient
          reference: email.reference,
          sentAt: moment().tz('Asia/Kuala_Lumpur').format('DD MMMM YYYY [at] hh:mm A'),
          projectTitle: project?.title,               
          attachmentUrls: attachmentUrls,        
          inline: email?.inline
        });

        // Store the mailgunId from the first email sent
        if (!email.mailgunId) {          
          email.mailgunId = res.id?.replace(/<|>/g, ''); // Remove angle brackets if present
          await this.emailRepo.save(email); // Save the updated email object
        }
      }
      
      // send a copy of the email to the sender
      await this.mailgunService.sendMail({
        type: 'correspondence',
        to: creator?.email,
        from: `no-reply@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}`,
        subject: email?.subject,              
        template: 'send_email_correspondence_out_copy',
        message: email.body,
        replyTo: `${email.uuid}@${process.env.MAILGUN_CORRESPONDENCE_DOMAIN}`,   
        name: creator?.name,     
        email: creator?.email,
        title: email.subject,
        recipient: receivers.map((receiver) => `${receiver?.name} <${receiver?.email}>`).join(', '), // Show all recipients in sender's copy
        reference: email.reference,
        sentAt: moment().tz('Asia/Kuala_Lumpur').format('DD MMMM YYYY [at] hh:mm A'),
        projectTitle: project?.title,
        inline: email?.inline
      });

      const mobileLink = `correspondence/${email?.id}`;
      const link = `/correspondence?id=${email?.id}&projectId=${project?.id}&companyId=${creator?.companyId}`;        

        const payload: INovuPayload = {
            user: {
              avatar: creator.avatar,
              name: creator.name,
              email: creator.email
            },
            header : 'New Correspondence',
            event: 'new-site-diary',            
            title: project.title,
            head: creator?.name,
            body: `has sent you a new correspondence`,
            tail: email?.subject,
            bodyColon: true,
            subscriber: {
              firstName: creator.name
            },
            link: {
              mobile: mobileLink,
              web: link,
              redirect: link,
              uri: ''
            }
        };
      
        for (const receiver of receivers) {
     
          await this.novuService.trigger('secondary-workflow', {
            to: {
              subscriberId: receiver.userId?.toString(),
              email: receiver.email
            },
            payload,
            overrides: {
              android: {
                priority: 'high'
              },
              fcm: {
                data: {
                  link: mobileLink.toString(),
                  projectId: project.id.toString(),
                  companyId: project.companyId.toString()
                }
              }
            }
          });
        }

      return { mailgunId: email.mailgunId };
    } catch(error) {
      getErrorMessage(error, 'EmailService', 'sendEmailCorrespondence');
    }
  }

  async captureStatus(payload: any) {
    const eventData = payload['event-data'];        
    const message = eventData?.message; 
    const messageId = message?.headers['message-id'];                
    const clientInfo = eventData?.['client-info'] // Extract client details
    const botType = clientInfo?.bot; // Check if bot field exists
  
    // will be replace with bullmq
    const maxRetries = 5;
    let retryCount = 0;
    let email;

    // Retry fetching the email from the database
    while (retryCount < maxRetries) {
        email = await this.emailRepo.findOne({ where: { mailgunId: messageId } });

        if (email) break; // Found email, exit loop
        await new Promise(res => setTimeout(res, 1000)); // Wait 1 second before retrying
        retryCount++;
    }
        
    if(email){
      
      if(eventData.event === 'accepted') {
        email.deliveryStatus = EmailDeliveryStatus.Accepted;
      } else if(eventData.event === 'delivered') {        
        email.deliveryStatus = EmailDeliveryStatus.Delivered;
      } else if(eventData.event === 'opened') {
        const userAgent = clientInfo?.user_agent;
        // List of known bot names
        const botIdentifiers = ['gmail', 'outlook', 'appleproxy', 'yahoo', 'barracuda', 'proofpoint', 'mimecast'];
        
        if (botType && botIdentifiers.includes(botType.toLowerCase())) {
          console.log(`Ignoring bot/scanner open event: Bot=${botType}, User-Agent=${userAgent}`);
        } else {
          email.deliveryStatus = EmailDeliveryStatus.Opened;
        }
      } else if(eventData.event === 'clicked') {
        email.deliveryStatus = EmailDeliveryStatus.Clicked;        
      } else if(eventData.event === 'failed') {        
        if(eventData.severity === 'temporary') {
          email.deliveryStatus = EmailDeliveryStatus.TemporaryFailures;
        } else{
          email.deliveryStatus = EmailDeliveryStatus.PermanentFailures;
        }
      } else if(eventData.event === 'complained') {
        email.deliveryStatus = EmailDeliveryStatus.SpamComplaints;
      }
  
      await this.emailRepo.save(email);
    }
  }
    
  async parsedBody(email: EmailEntity) {
    const imageTag = /<img\b([^>]*)src=["']cid:([^"']+)["']([^>]*)>/gi;
  
    let parsedBody = email.body;
    const matches = [...email.body.matchAll(imageTag)] ;
  
    for (const match of matches) {
      const [fullMatch, before, contentId, after] = match;
      const newSrc = await this.emailAssetService.getPresignedUrlFromContentId(email.id, contentId);
      const replacedTag = `<img${before}src="${newSrc}"${after}>`;
  
      parsedBody = parsedBody.replace(fullMatch, replacedTag);
    }
  
    return parsedBody;
  }

  private async extractEmailAsset(email, emailId: number) {    
    
    const attachments = email.attachments ? JSON.parse(email.attachments) : null;    
    
    if (!attachments || attachments?.length === 0) {
      return
    }
    
    const contentMapper = (contentMap, attachmentUrl) => {
      const parsedContentMap = JSON.parse(contentMap)
      const ids = Object.keys(parsedContentMap);
      const contentKey = ids.find(id => parsedContentMap[id] === attachmentUrl);
      return contentKey ? contentKey.replace('<', '').replace('>', '') : ''; // Return `null` if not found
    };

    const getFileExtension = (fileName) => {
      return fileName.split('.').pop();
    };

    const getAssetType = (emailBody: string, contentId: string) => {
      return emailBody.includes(contentId) ? EmailAssetType.Inline : EmailAssetType.Attachment;
    }

    const downloadFile = async (url) => {
      const authHeader = 'Basic ' + Buffer.from(`api:${MAILGUN_API_KEY}`).toString('base64');

      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        headers: {
          Authorization: authHeader,
        },
      });

      return Buffer.from(response.data)
    }

    for (const attachment of attachments) {
      const fileBuffer = await downloadFile(attachment.url)
      const createReadStream = () => {
        const readableStream = new Readable();
        readableStream.push(fileBuffer);
        readableStream.push(null);
        return readableStream;
      };
      
      const file = {
        filename: attachment.name,
        mimetype: attachment['content-type'],
        encoding: '7bit',
        createReadStream
      };

      const { key } = await this.fileService.uploadGqlFile(file as any, `email-assets/${emailId}`);
      const emailAsset = new EmailAssetEntity()
      emailAsset.rawMetadata = JSON.stringify(attachment)
      emailAsset.name = attachment["name"]
      emailAsset.contentId = contentMapper(email["content-id-map"], attachment.url)
      emailAsset.fileExtension = getFileExtension(attachment["name"])
      emailAsset.emailId = emailId
      emailAsset.assetKey = key
      emailAsset.assetType = getAssetType(email["stripped-html"], emailAsset.contentId)

      this.attachmentRepo.save(emailAsset)
    }
  }
  
}
