import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { EmailService } from './email.service';
import { AnyFilesInterceptor } from '@nestjs/platform-express';

@ApiTags('Email API')
@Controller('email')
export class EmailController {
  constructor(
    private emailService: EmailService    
  ) {}

  @Post('/webhooks/extract-emails')
  @UseInterceptors(AnyFilesInterceptor())
  async extractEmail(@Body() body: any) {
    await this.emailService.extractEmail(body);
    return { message: 'Email processed successfully' };
  }

  @Post('/webhooks/capture-status')
  async captureStatus(@Body() payload: any): Promise<any> {
    
    await this.emailService.captureStatus(payload);

    return { message: 'Status has been captured.' };
  }
}
