import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';
import { GqlAuthGuard } from '@guards/auth.guard';
import { UseGuards } from '@nestjs/common';
import { ResolveField, Resolver, Parent } from '@nestjs/graphql';
import { EmailDto } from './dto/email.gql.dto';
import { EmailService } from './email.service';
import { getRepository, Repository } from 'typeorm';
import { UserEntity } from '@modules/user/entity/user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserDto } from '@modules/user/dto/user.gql.dto';

@UseGuards(GqlAuthGuard)
@Resolver(() => EmailDto)
export class EmailResolver {
  constructor(
    private readonly emailService: EmailService, 
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
  ) {}

  @ResolveField('parsedBody', () => String)
  async parsedBody(@Parent() parent: EmailDto) {
    return await this.emailService.parsedBody(parent);
  }

  @ResolveField('sender', () => UserDto)
  async sender(@Parent() parent: EmailDto) {  

    // Update query to include project relationship
    let user = await this.userRepo
    .createQueryBuilder('user')
    .leftJoin('user.projectUsers', 'projectUser')
    .where('user.id = :userId', { userId: parent?.senderId })
    .andWhere('projectUser.projectId = :projectId', { projectId: parent?.projectId })
    .getOne();

    if (!user) {
      
      // If user not found in UserEntity, try to find in ContactsEmailEntity with projectId
      const contactEmail = await getRepository(ContactsEmailEntity)
      .createQueryBuilder('contactEmail')
      .where('contactEmail.id = :id', { id: parent?.senderId })
      .andWhere('contactEmail.projectId = :projectId', { projectId: parent?.projectId })
      .getOne();
      
      // Convert to UserEntity and mark as non-member
      user = contactEmail as unknown as UserEntity;
      user.name = `${user.name} (Non member)`;
    }
        
    if(!user) return null;
    return user;
  }
}
