import { BaseEntity } from '@modules/base/base';
import {
  <PERSON><PERSON><PERSON>,
  Column,
  JoinColum<PERSON>,
  ManyToMany,
  JoinTable,
  TreeChildren,
  TreeParent,
  ManyToOne,
  OneToMany,
  Tree,
  Generated
} from 'typeorm';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID, Field, GraphQLISODateTime } from '@nestjs/graphql';

import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';
import { ContactsEmailEntity } from '@modules/contacts-email/entity/contacts-email.entity';
import { EmailDeliveryStatus } from '../dto/email.api.dto';
import { ProjectEntity } from '@modules/project/entity/project.entity';

@ObjectType()
@Entity('emails')
@Tree('materialized-path')
export class EmailEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true , nullable: true })
  creatorId: number;

  @IDField(() => ID)
  @Column({ unsigned: true, nullable: true })
  recipientId: number;

  @IDField(() => ID)
  @Column({ unsigned: true, nullable: true })
  senderId: number;

  @IDField(() => ID)
  @FilterableField({ nullable: true })
  @Column({ unsigned: true, nullable: true })
  replyToId: number;

  @FilterableField({ nullable: true })
  @Column('varchar')
  subject: string;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  body: string;

  @FilterableField(() => EmailDeliveryStatus, { nullable: true })
  @Column('enum', { enum: EmailDeliveryStatus, nullable: true , default: EmailDeliveryStatus.Sending })
  deliveryStatus: EmailDeliveryStatus;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  openedAt: Date;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  receivedAt: Date;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  sentAt: Date;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  privatedAt: Date;

  @FilterableField({ nullable: true })
  @Column('varchar', { nullable: true })
  reference: string;

  @Field({ nullable: true })
  @Column('text', { nullable: true })
  rawResponse: string;

  @Column()
  @Generated("uuid")
  uuid: string

  @Field({ nullable: true })
  @Column('text', { nullable: true })
  size: string;

  @Field({ nullable: true })
  @Column('text', { nullable: true })
  mailgunId: string;

  @FilterableField(() => GraphQLISODateTime, { nullable: true })
  @Column({ nullable: true })
  replyAt: Date;

  @IDField(() => ID)
  @Column({ unsigned: true, nullable: false })
  projectId: number;

  /* -------------------------------- Relations ------------------------------- */

  @TreeChildren()
  replies: EmailEntity[];

  @TreeParent()
  @JoinColumn({ name: 'replyToId' })
  parent: EmailEntity;

  @ManyToOne(() => ContactsEmailEntity, account => account.sentEmails)
  @JoinColumn({ name: 'senderId' })
  sender: ContactsEmailEntity;

  @ManyToMany(() => ContactsEmailEntity, account => account.receivedEmails,  { cascade: true })
   @JoinTable({
    name: 'contact_email_to_email', // Bridge table name
    joinColumn: { name: 'emailsId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'emailAccountsId', referencedColumnName: 'id' },
  })
  receivers: ContactsEmailEntity[];

  @ManyToOne(() => ProjectEntity, project => project.emails)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;
  
  @OneToMany(() => EmailAssetEntity, asset => asset.email)
  assets: EmailAssetEntity[];
}
