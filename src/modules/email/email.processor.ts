import { Processor } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { EmailService } from '@modules/email/email.service';
import { WorkerHostProcessor } from '@modules/bull-mq/bullmq-process';
import { getErrorMessage } from '@common/error';
import { getRepository } from 'typeorm';
import { EmailEntity } from './entity/email.entity';
import { EmailAssetEntity } from '@modules/email-asset/entity/email-asset.entity';

@Injectable()
@Processor('email-correspondence-process')
export class EmailCorrespondenceProcessor extends WorkerHostProcessor {
  constructor(private readonly emailService: EmailService) {
    super();
  }

  async process(job: Job<EmailEntity>): Promise<any> {    
    const { id } = job.data;

    // get email
    const email = await getRepository(EmailEntity).findOne(id, { relations: ['receivers'] });
    //get attachments
    const attachments = await getRepository(EmailAssetEntity).find({ where: { emailId: { id } } });

    try {
      
      const result = await this.emailService.sendEmailCorrespondence(email, attachments);      
      return result;
    } catch (error) {      
      getErrorMessage('EmailCorrespondenceProcessor', error, 'process')
    }
  }
}