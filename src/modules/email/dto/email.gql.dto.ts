import { defaultQueryOptions } from '@constants';
import { BeforeCreateOne, CreateOneInputType, FilterableOffsetConnection, QueryOptions, UnPagedRelation } from '@nestjs-query/query-graphql';
import { Field, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { EmailEntity } from '../entity/email.entity';
import { GqlContext } from '@types';
import { EmailDeliveryStatus } from './email.api.dto';
import { relationOption } from '@constants/query.constant';
import { ContactsEmailDTO } from '@modules/contacts-email/dto/contacts-email.gql.dto';
import { EmailAssetDto } from '@modules/email-asset/dto/email-asset.gql.dto';
import { GraphQLUpload, FileUpload } from "graphql-upload";

@ObjectType('Email')
@BeforeCreateOne(
  (instance: CreateOneInputType<EmailDto>, context: GqlContext) => {
    
    const addedBy = context.req.user.id;
    const projectId = context.req.headers['project-id'] || context.req.headers['Project-Id'];
    
    if(addedBy){
      instance.input.createdBy = addedBy;          
    }

    if(projectId){
      instance.input.projectId = Number(projectId);
    }

    return instance;
  }
)
@UnPagedRelation('assets', () => EmailAssetDto, {
  ...relationOption(true),
  relationName: 'assets'
})
@QueryOptions({ ...defaultQueryOptions })
@FilterableOffsetConnection("receivers", () => ContactsEmailDTO, relationOption())
@UnPagedRelation('parent', () => EmailDto, {
  ...relationOption(true),
  relationName: 'parent'
})
@UnPagedRelation('replies', () => EmailDto, {
  ...relationOption(true),
  relationName: 'replies',
})

export class EmailDto extends EmailEntity {}

@InputType()
export class ReceiverInput {
  @Field(() => Number)
  id: number;
}

@InputType()
export class CreateEmailInputDTO {
  subject: string;
  body: string;
  deliveryStatus: EmailDeliveryStatus;
  openedAt?: Date;
  receivedAt?: Date;
  sentAt?: Date;
  privatedAt?: Date;
  reference: string;
  receivers: ReceiverInput[];
  cc?: string[];
  senderId: number;
  replyToId?: number
  @Field(() => [GraphQLUpload], { nullable: true })
  attachmentFiles?: FileUpload[]; 
  @Field(() => [GraphQLUpload], { nullable: true })  
  inline? : string[]
}

@InputType()
export class UpdateEmailInputDTO extends PartialType(CreateEmailInputDTO) {
  replyAt?: Date;
}
