import { registerEnumType } from "@nestjs/graphql";

export enum EmailActionType {
  Receiving = 'Receiving',
  Sending = 'Sending'
}
registerEnumType(EmailActionType, { name: 'EmailActionType' });

// Accepted, Delivered, Clicks, Spam Complaints, Unsubscribes, Permanent Failures, Temporary Failures, Spam Complaints
export enum EmailDeliveryStatus {
  Accepted = 'Accepted',
  Delivered = 'Delivered',
  Clicked = 'Clicked',
  SpamComplaints = 'Spam Complaints',  
  PermanentFailures = 'Permanent Failures',
  Sending = 'Sending',  
  Opened = 'Opened',
  TemporaryFailures = 'Temporary Failures',
}
registerEnumType(EmailDeliveryStatus, { name: 'EmailDeliveryStatus' });
