import { Global, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import loggerConfig from './config/logger.config';
import { LOGGER, loggerFactory } from './factories/logger.factory';
import { NestJSLoggerService } from './services/nestjs-logger.service';
import { LogHttp } from './middleware/log-http.middleware';

@Global()
@Module({
  imports: [ConfigModule.forFeature(loggerConfig)],
  providers: [loggerFactory, NestJSLoggerService],
  exports: [LOGGER, NestJSLoggerService],
})

export class LoggerModule implements NestModule {
  configure(consumer: MiddlewareConsumer): any {
    consumer.apply(LogHttp).forRoutes('*');
  }
}
