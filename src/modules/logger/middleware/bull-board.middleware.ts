import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class BullBoardAuthMiddleware implements NestMiddleware {
  use = (req: Request, res: Response, next: NextFunction) => {
    const auth = req.headers.authorization;

    if (!auth || !this.isAuthorized(auth)) {
      res.setHeader('WWW-Authenticate', 'Basic realm="BullBoard"');
      throw new UnauthorizedException('Unauthorized access');
    }

    next();
  }

  private isAuthorized(auth: string): boolean {
    if (!auth) return false;
    const basicAuth = Buffer.from(auth.split(' ')[1], 'base64').toString();
    const [username, password] = basicAuth.split(':');

    // Replace these with your own credentials or implement a more complex auth strategy
    return username === process.env.BULLBOARD_USER && password === process.env.BULLBOARD_PASS;
  }
}
