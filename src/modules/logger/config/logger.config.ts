import { registerAs } from '@nestjs/config';
import winston from 'winston';
import { prettyConsoleFormatter } from '../utils/pretty-console-formatter.util';
import { errorStackFormatter } from '../utils/error-stack-formatter.util';
import { errorStackFormatterInline } from '../utils/error-stack-formatter-inline.util';

export const LOGGER_CONFIG_KEY = 'logger-config';

export default registerAs(LOGGER_CONFIG_KEY, () => {
  let format;
  const defaultMeta = {
    layer: 'App',
    service: process.env.SERVICE_NAME, // this is an optional meta field
    context: 'unspecified', // is overridden by the logger factory and the NestJSLoggerService
  };
  switch (process.env.NODE_ENV) {
    case 'production':
      format = winston.format.combine(
        winston.format.timestamp(),
        errorStackFormatter(),
        winston.format.json(),
      );
      break;
    default:
      format = winston.format.combine(
        errorStackFormatterInline(),
        prettyConsoleFormatter(),
      );
      break;
  }
  return {
    winston: {
      level: process.env.LOGGER_MIN_LEVEL || 'debug',
      silent: false,
      transports: [new winston.transports.Console()],
      format,
      defaultMeta,
    },
    /* http log middleware configuration */
    http: {
      body: {
        enabled: process.env.LOGGER_BODY || false,
        maxSize: 3 * 1024, // default is 3KB
        // although, it's the endpoint's responsibility to remove any sensitive data, we can proactively remove some
        // well-known sensitive fields.
        blackListedFields: ['password'],
      },
      query: {
        enabled: process.env.LOGGER_QUERY || false,
        maxSize: 3 * 1024, // default is 3KB
      },
      cookies: {
        enabled: process.env.LOGGER_COOKIES || false,
        maxSize: 3 * 1024, // default is 3KB
      },
    },
  };
});