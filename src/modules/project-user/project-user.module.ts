import { Module } from '@nestjs/common';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { ProjectUserEntity } from './entity/project-user.entity';
import { ProjectUserDto, CreateProjectUserInputDTO, UpdateProjectUserInputDTO } from './dto/project-user.gql.dto';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard, GqlRolesGuard } from '@guards/roles.guard';
import { ProjectUserService } from './project-user.service';
import { ProjectUserResolver } from './project-user.resolver';
import { ProjectUserSubscriber } from './project-user.subscriber';
import { ProjectUserController } from './project-user.controller';

@Module({
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ProjectUserEntity])],
      resolvers: [
        {
          ServiceClass: ProjectUserService,
          DTOClass: ProjectUserDto,
          EntityClass: ProjectUserEntity,
          CreateDTOClass: CreateProjectUserInputDTO,
          UpdateDTOClass: UpdateProjectUserInputDTO,
          guards: [GqlAuthGuard, GqlRolesGuard, GqlProjectRolesGuard],
          delete: { disabled: true }
        }
      ],
      services: [ProjectUserResolver, ProjectUserService, ProjectUserSubscriber]
    })
  ],
  controllers: [ProjectUserController],
})
export class ProjectUserModule {}
