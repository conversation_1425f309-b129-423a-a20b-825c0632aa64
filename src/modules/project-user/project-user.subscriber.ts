import { Injectable } from '@nestjs/common';
import { Connection, EntitySubscriberInterface, EventSubscriber, UpdateEvent } from 'typeorm';
import { ProjectUserEntity } from './entity/project-user.entity';
import { WebSocketService } from '@modules/websocket/websocket.service';
import { UserEntity } from '@modules/user/entity/user.entity';
import { AuditLogActionType, AuditLogModuleType } from '@constants';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { getErrorMessage } from '@common/error';

@Injectable()
@EventSubscriber()
export class ProjectUserSubscriber implements EntitySubscriberInterface<ProjectUserEntity> {
  constructor(connection: Connection, private webSocketService: WebSocketService) {
    connection.subscribers.push(this);
  }

  listenTo() {
    return ProjectUserEntity;
  }

  async afterUpdate(event: UpdateEvent<ProjectUserEntity>) {
    try {
      const { entity } = event;

      //? check if project user is updated
      const socketEvent = 'event:project-user-updated';
      const room = `project-user-room-${entity.projectId}-${entity.userId}`;

      // AUDIT LOG SCHEDULE (ADD MANAGER / VALIDATOR)
      if (event.updatedColumns.find(column => column.propertyName === 'scheduleRole' && entity.scheduleRole)) {
        const user = await event.manager.getRepository(UserEntity).findOne({ id: entity.addedBy });
        const proUser = await event.manager.getRepository(UserEntity).findOne({ id: entity.userId });

        const msg = user.name + ' added ' + proUser.name + ' as Schedule ' + entity.scheduleRole;

        const auditLog = event.manager.getRepository(AuditLogEntity).create({
          userId: entity.addedBy,
          projectId: entity.projectId,
          // taskId: entity.id,
          resourceId: entity.id,
          module: AuditLogModuleType.Schedule,
          action: AuditLogActionType.AddRole,
          content: msg
        });
        await auditLog.save();
      }

      return this.webSocketService.socket.to(room).emit(socketEvent, entity);
    } catch (e) {
      getErrorMessage(e, 'ProjectUserSubscriber', 'afterUpdate');
    }
  }
}
