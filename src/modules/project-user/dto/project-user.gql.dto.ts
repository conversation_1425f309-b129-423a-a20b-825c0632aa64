import { defaultQueryOptions, ProjectUserRoleType, ScheduleUserRole } from '@constants';
import { relationOption } from '@constants/query.constant';
import { UserDto } from '@modules/user/dto/user.gql.dto';
import {
  Authorize,
  BeforeCreateOne,
  FilterableOffsetConnection,
  IDField,
  QueryOptions,
  Relation
} from '@nestjs-query/query-graphql';
import { ID, InputType, ObjectType, PartialType } from '@nestjs/graphql';
import { ProjectUserEntity } from '../entity/project-user.entity';
import { ProjectUserAuthorizer } from '../project-user.authorizer';
import * as Hooks from '@hooks/nest-graphql.hooks';

@ObjectType('ProjectUser')
@BeforeCreateOne(Hooks.CreatedByOneHook)
@Authorize(ProjectUserAuthorizer)
@Relation('user', () => UserDto, relationOption())
@Relation('owner', () => UserDto, relationOption())
@FilterableOffsetConnection('user', () => UserDto, relationOption())
@QueryOptions({ ...defaultQueryOptions })
export class ProjectUserDto extends ProjectUserEntity {}

@InputType()
export class CreateProjectUserInputDTO {
  @IDField(() => ID) userId: number;
  @IDField(() => ID) projectId: number;
  role: ProjectUserRoleType;
  scheduleRole?: ScheduleUserRole;
  addedBy?: number;
}
@InputType()
export class UpdateProjectUserInputDTO extends PartialType(CreateProjectUserInputDTO) {}

@InputType()
export class DeleteProjectUserInputDTO {
  @IDField(() => ID) id: number;
}
