import { BadRequestException, ForbiddenException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { getRepository, Repository } from 'typeorm';
import { ProjectUserEntity } from './entity/project-user.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { ProjectUserRoleType } from '@constants';
import { getErrorMessage } from '@common/error';

@Injectable()
export class ProjectUserService extends TypeOrmQueryService<ProjectUserEntity> {
  constructor(
    @InjectRepository(ProjectUserEntity)
    private projectUserRepo: Repository<ProjectUserEntity>
  ) {
    super(projectUserRepo, { useSoftDelete: true });
  }

  async deleteOneProjectUser(id: number, userId: number) {
    try {
      const projectUser = await this.projectUserRepo.findOne({ id });

      const remover = await this.projectUserRepo.findOne({
        userId,
        projectId: projectUser.projectId
      });

      if (
        remover.role === ProjectUserRoleType.CloudCoordinator &&
        (projectUser.role === ProjectUserRoleType.ProjectOwner ||
          projectUser.role === ProjectUserRoleType.CloudCoordinator)
      ) {
        throw new ForbiddenException('Insufficient Role permission for this action.');
      }

      return await this.projectUserRepo.softRemove(projectUser);
    } catch (e) {
      getErrorMessage(e, 'ProjectUserService', 'deleteOneProjectUser');
    }
  }

  // Get UserMe
  async getProjectUserMe(userId: number, projectId: number): Promise<ProjectUserEntity> {
    const user = await this.projectUserRepo.findOne({ where: { userId: userId, projectId } });
    return user;
  }

  // Create Project Invitations
  async updateMemberRole(data: any, user: any, projectId) {
    try {
      // const inviteId = data.inviteId;
      if (!data.userId) throw new BadRequestException(`Member ID is invalid`);
      if (!data.role) throw new BadRequestException('Role is invalid');
      if (!projectId) throw new BadRequestException('Project Id not found');

      // throw new BadRequestException('OK');
      // CHECKING IF USER HAS PERMISSION
      let userId = user.id;

      const inviterInProject = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

      if (!inviterInProject) throw new BadRequestException('Inviter details not found');

      if (
        inviterInProject.role !== ProjectUserRoleType.ProjectOwner &&
        inviterInProject.role !== ProjectUserRoleType.CloudCoordinator
      )
        throw new ForbiddenException('Insufficient Role permission for this action.');
      // ------

      // UPDATING MEMBER ROLE
      userId = data.userId;

      const member = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

      if (!member) throw new BadRequestException('Member details not found');

      this.projectUserRepo.merge(member, { role: data.role });
      // ------

      await this.projectUserRepo.save(member);

      return { message: 'Member role updated' };
    } catch (e) {
      getErrorMessage(e, 'ProjectUserService', 'updateMemberRole');
    }
  }
}
