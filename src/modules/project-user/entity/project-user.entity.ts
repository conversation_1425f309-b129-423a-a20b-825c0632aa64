import { ProjectUserRoleType, ScheduleUserRole } from '@constants';
import { BaseEntity } from '@modules/base/base';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { IDField, FilterableField } from '@nestjs-query/query-graphql';
import { ObjectType, ID } from '@nestjs/graphql';
import { Entity, Column, JoinColumn, ManyToOne, ManyToMany } from 'typeorm';

@ObjectType()
@Entity('project_users')
export class ProjectUserEntity extends BaseEntity {
  @IDField(() => ID)
  @Column({ unsigned: true })
  userId: number;

  @IDField(() => ID)
  @Column({ unsigned: true })
  projectId: number;

  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  addedBy: number;

  @FilterableField(() => ProjectUserRoleType)
  @Column('enum', { enum: ProjectUserRoleType, default: ProjectUserRoleType.CanView })
  role: ProjectUserRoleType;

  @FilterableField(() => ScheduleUserRole, { nullable: true })
  @Column('enum', {nullable: true, enum: ScheduleUserRole, default: null })
  scheduleRole?: ScheduleUserRole;

  /* -------------------------------- Relations ------------------------------- */
  @ManyToOne(() => UserEntity, user => user.projectUsers)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => ProjectEntity, project => project.projectUsers)
  @JoinColumn({ name: 'projectId' })
  project: ProjectEntity;

  @ManyToOne(() => UserEntity, user => user.addedProjectUsers)
  @JoinColumn({ name: 'addedBy' })
  owner: UserEntity;

  @ManyToMany(() => TaskEntity, task => task.assignees)
  tasks: Promise<TaskEntity[]>;

  @ManyToMany(() => TaskEntity, task => task.copies)
  tasksC: Promise<TaskEntity[]>;

  @ManyToMany(() => ScheduleEntity, schedule => schedule.assignees)
  schedules: Promise<ScheduleEntity[]>;

  @ManyToMany(() => ScheduleEntity, schedule => schedule.copies)
  schedulesC: Promise<ScheduleEntity[]>;
}
