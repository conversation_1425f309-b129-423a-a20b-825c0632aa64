import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { Authorizer } from '@nestjs-query/query-graphql';
import { Filter } from '@nestjs-query/core';
import { ProjectUserDto } from './dto/project-user.gql.dto';
import { getRepository } from 'typeorm';
import { ProjectUserEntity } from './entity/project-user.entity';
import { RoleTypeEnum } from '@constants';

@Injectable()
export class ProjectUserAuthorizer implements Authorizer<ProjectUserDto> {
  async authorize(context: any): Promise<Filter<ProjectUserDto>> {
    // Admin authorizer
    if (context?.req?.user?.type === RoleTypeEnum.Admin) return Promise.resolve({});

    // User authorizer
    const projectId = context?.req?.headers?.['project-id'];
    if (!projectId) throw new BadRequestException('Project Id not found');
    const userId = context?.req?.user?.id;
    if (!projectId) throw new UnauthorizedException('User not found');
    const projectUser = await getRepository(ProjectUserEntity).findOne({ userId, projectId });

    if (projectUser) {
      return Promise.resolve({
        projectId: { eq: projectId }
      });
    }
    throw new UnauthorizedException('You are not involved in this project');
  }

  authorizeRelation(relationName: string, context: any): Promise<Filter<unknown> | undefined> {
    const projectId = context?.req?.headers?.['project-id'];

    if (relationName === 'project' && projectId) {
      return Promise.resolve({
        id: { eq: projectId }
      });
    }
    return Promise.resolve({});
  }
}
