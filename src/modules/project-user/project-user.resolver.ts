import { ProjectUserRoleType } from '@constants';
import { GqlGetGqlAuthData, GqlGetGqlProjectData, ProjectRoles } from '@decorators/auth.decorator';
import { GqlAuthGuard } from '@guards/auth.guard';
import { GqlProjectRolesGuard } from '@guards/roles.guard';
import { UseGuards } from '@nestjs/common';
import { Args, Mutation, Resolver, Query } from '@nestjs/graphql';
import { AuthData } from '@types';
import { DeleteProjectUserInputDTO, ProjectUserDto } from './dto/project-user.gql.dto';
import { ProjectUserService } from './project-user.service';

@UseGuards(GqlAuthGuard, GqlProjectRolesGuard)
@Resolver(() => ProjectUserDto)
export class ProjectUserResolver {
  constructor(private projectUserService: ProjectUserService) {}

  @Query(() => ProjectUserDto)
  async getProjectUserMe(@GqlGetGqlAuthData() user: AuthData, @GqlGetGqlProjectData() projectId: number): Promise<any> {
    return await this.projectUserService.getProjectUserMe(user.id, projectId);
  }

  @ProjectRoles(ProjectUserRoleType.ProjectOwner, ProjectUserRoleType.CloudCoordinator)
  @Mutation(() => ProjectUserDto)
  async deleteOneProjectUser(@Args('input') input: DeleteProjectUserInputDTO, @GqlGetGqlAuthData() user: AuthData) {
    return await this.projectUserService.deleteOneProjectUser(input.id, user.id);
  }
}
