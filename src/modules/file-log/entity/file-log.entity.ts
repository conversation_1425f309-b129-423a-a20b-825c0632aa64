import { BaseEntity } from '@modules/base/base';
import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { FilterableField, IDField } from '@nestjs-query/query-graphql';
import { ID, ObjectType } from '@nestjs/graphql';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CategoryType } from '@constants';

@ObjectType()
@Entity('file_logs')
export class FileLogEntity extends BaseEntity {
  @IDField(() => ID, { nullable: true })
  @Column({ unsigned: true, nullable: true })
  user_id: number;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  user_name: string;

  @FilterableField(() => CategoryType, { nullable: true })
  @Column('enum', { enum: CategoryType, nullable: true })
  category: CategoryType;

  @FilterableField({ nullable: true })
  @Column('text', { nullable: true })
  before_file_name: string;

  @FilterableField({ nullable: true })
  @Column('float', { nullable: true })
  before_file_size: number;

  @FilterableField()
  @Column('text', { nullable: true })
  after_file_url: string;

  @FilterableField()
  @Column('text', { nullable: true })
  after_file_name: string;

  @FilterableField()
  @Column('float', { nullable: true })
  after_file_size: number;

  @FilterableField()
  @Column('text', { nullable: true })
  after_timestamp: string;

  /* -------------------------------- Relations ------------------------------- */

  @ManyToOne(() => UserEntity, user => user.fileLog)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;
}
