import { IntegrationModule } from '@modules/integration/integration.module';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { NestjsQueryGraphQLModule } from '@nestjs-query/query-graphql';
import { NestjsQueryTypeOrmModule } from '@nestjs-query/query-typeorm';
import { Module } from '@nestjs/common';
import { CronService } from './cron.service';

@Module({
  
  imports: [
    NestjsQueryGraphQLModule.forFeature({
      imports: [NestjsQueryTypeOrmModule.forFeature([ScheduleEntity, SchedulesMediaEntity]), IntegrationModule],   
       
      services: [CronService],
    }),
    IntegrationModule
  ],
})
export class CronModule {}
