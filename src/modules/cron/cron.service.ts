import { getErrorMessage } from '@common/error';
import { CategoryType, ProjectUserRoleType } from '@constants';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { INovuPayload, NovuService } from '@modules/integration/novu/novu.service';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ScheduleEntity } from '@modules/schedule/entity/schedule.entity';
import { SchedulesMediaEntity } from '@modules/schedules-media/entity/schedules-media.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { TypeOrmQueryService } from '@nestjs-query/query-typeorm';
import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import moment from 'moment';
import { Repository, getRepository } from 'typeorm';

const { APP_URI } = process.env;

interface SiteDiary {
  id: number | null;
  projectId: number;
  submittedAt: Date | null;
  workspaceGroup: WorkspaceGroupEntity | null;
  projectTitle: string;
  company: CompanyEntity;
}

@Injectable()
export class CronService extends TypeOrmQueryService<ScheduleEntity> {
  constructor(
    
    private novuService: NovuService,
    @InjectRepository(SchedulesMediaEntity)
    public schedulesMediaRepo: Repository<SchedulesMediaEntity>
  ) {
    super(getRepository(ScheduleEntity));
  }
  // run every working day at 12.30pm, once a day
  @Cron('30 12 * * 1-5', {
    timeZone: 'Asia/Kuala_Lumpur'
  })
  async siteDiaryReminder() {    
    
    try {
      
      const processName = process.env.name || 'bina-be-primary';
      if (processName.search(/bina-be-primary/) !== -1) {
        try {
          const allProject = await getRepository(ProjectEntity)
          .createQueryBuilder('project')
          .select(['project.id', 'project.title'])
          .where('project.isRemindSiteDiary = :isRemindSiteDiary', { isRemindSiteDiary: true })
          .leftJoin('project.company', 'company')
          .addSelect(['company.name', 'company.id'])
          .getMany();
          

          // get the latest site diary for each project
          const latestSiteDiary = await Promise.all(
            allProject.map(async (project: ProjectEntity) => {
              const siteDiary = await getRepository(ProjectDocumentEntity)
                .createQueryBuilder('projectDocument')
                .select(['projectDocument.id', 'projectDocument.submittedAt', 'projectDocument.projectId'])
                .leftJoin('projectDocument.workspaceGroup', 'workspaceGroup', 'workspaceGroup.name = :name', {
                  name: 'Site Diary'
                })
                .addSelect(['workspaceGroup.name', 'workspaceGroup.id'])
                .where('(projectDocument.category = :category) AND (projectDocument.workspaceGroupId IS NOT NULL)', {
                  category: CategoryType?.AllForm
                })
                .andWhere('projectDocument.projectId = :projectId', { projectId: project.id })
                .orderBy('projectDocument.submittedAt', 'DESC')
                .getOne();

              // Check if siteDiary is null and return projectId instead

              const sanitizedSiteDiary: SiteDiary = {
                id: siteDiary?.id ?? null,
                projectId: project.id,
                submittedAt: siteDiary?.submittedAt ?? null,
                workspaceGroup: siteDiary?.workspaceGroup ?? null,
                projectTitle: project.title,
                company: project.company
              };

              return sanitizedSiteDiary;
            })
          );
          
          latestSiteDiary?.map?.(async (siteDiary: SiteDiary) => {
            
            //? check the latest site diary is submitted more than 20 hours from now
            //! calculate using GMT +0 time
            const submittedAt = moment(siteDiary?.submittedAt);
            const now = moment();
            const diff = now.diff(submittedAt, 'hours');
            

            if (diff > 20 || !siteDiary?.submittedAt) {
              const projectUsers = await getRepository(ProjectUserEntity)
                .createQueryBuilder('projectUser')
                .where(
                  '(projectUser.projectId = :projectId) AND ((projectUser.role = :projectOwner) OR (projectUser.role = :cloudCoordinator))',
                  {
                    projectId: siteDiary?.projectId,
                    projectOwner: ProjectUserRoleType.ProjectOwner,
                    cloudCoordinator: ProjectUserRoleType.CloudCoordinator
                  }
                )
                .select(['projectUser.userId', 'projectUser.projectId'])
                .leftJoin('projectUser.user', 'user')
                .addSelect(['user.name', 'user.email', 'user.id', 'user.avatar'])
                .getMany();

              const mobileLink = 'dashboard/site-diary';
              const link = `/projects/dashboard?size=10&page=1&projectId=${siteDiary?.projectId}&companyId=${siteDiary?.company.id}`;
              const header = '📓 Site Diary Reminder';

              await Promise.all(
                projectUsers.map(async (projectUser: ProjectUserEntity) => {
                  const user = projectUser?.user;
                  const payload: INovuPayload = {
                    user: {
                      avatar: user.avatar,
                      name: user.name,
                      email: user.email
                    },
                    header,
                    event: 'site-diary-reminder',
                    company: siteDiary?.company?.name,
                    title: siteDiary?.projectTitle,
                    head: '',
                    headColon: false,
                    body: `Please upload today's site diary`,
                    subscriber: {
                      firstName: user.name
                    },
                    link: {
                      web: link,
                      uri: APP_URI,
                      mobile: mobileLink,
                      redirect: ''
                    }
                  };                  

                  return await this.novuService.trigger('secondary-workflow', {
                    to: {
                      subscriberId: user.id.toString(),
                      email: user.email
                    },
                    payload,
                    overrides: {
                      fcm: {
                        android: {
                          priority: 'high'
                        },
                        data: {
                          link: mobileLink.toString(),
                          projectId: siteDiary?.projectId.toString(),
                          companyId: siteDiary?.company?.id.toString()
                        }
                      }
                    }
                  });
                })
              );
            }
          });
          
        } catch (error) {
          getErrorMessage(error, 'ScheduleService', 'siteDiaryReminder');
        }
      }
    } catch (error) {
      getErrorMessage(error, 'ScheduleService', 'siteDiaryReminder');
    }
  }

}
