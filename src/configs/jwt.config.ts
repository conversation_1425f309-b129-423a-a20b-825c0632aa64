const secret = process.env.JWT_SECRET || 'oTAyM9Eem91T1HLZ';

const userAccessTokenConfig = {
  secret: `access_${secret}`,
  expiresIn: '7d',
};

const userRefreshTokenConfig = {
  secret: `refresh_${secret}`,
  expiresIn: '20d',
};

const userResetPasswordTokenConfig = {
  secret: `reset_password_${secret}_user`,
  expiresIn: '30m'
};

const userSignUpTokenConfig = {
  secret: `sign_up_${secret}_user`,
  expiresIn: '30m'
};

const adminAccessTokenConfig = {
  secret: `access_${secret}_admin`,
  expiresIn: '1d'
};

const adminRefreshTokenConfig = {
  secret: `refresh_${secret}_admin`,
  expiresIn: '5d'
};

const adminResetPasswordTokenConfig = {
  secret: `reset_password_${secret}_admin`,
  expiresIn: '30m'
};

const userChangeSignatureTokenConfig = {
  secret: `change_signature_${secret}_user`,
  expiresIn: '30m'
};

const JwtConfig = {
  userAccessTokenConfig,
  userRefreshTokenConfig,
  userResetPasswordTokenConfig,
  userSignUpTokenConfig,
  userChangeSignatureTokenConfig,
  adminAccessTokenConfig,
  adminRefreshTokenConfig,
  adminResetPasswordTokenConfig
};

export default JwtConfig;
