import { BullModule } from '@nestjs/bullmq';

export const BullMQConfig = BullModule.forRoot({
  connection: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT),
    password: process.env.REDIS_PASSWORD
  },
  prefix: process.env.BULLMQ_PREFIX,
  // this is configuring the number of jobs that is stored in redis
  defaultJobOptions: {
    removeOnComplete: 1000,
    removeOnFail: 5000,
    attempts: 3,
  },
});
