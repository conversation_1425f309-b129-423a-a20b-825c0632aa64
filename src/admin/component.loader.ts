import { ComponentLoader } from 'adminjs';

export const adminComponentLoader = new ComponentLoader();

export const AdminComponents = {
  Dashboard: adminComponentLoader.add('Dashboard', './components/dashboard'),
  ExportCSV: adminComponentLoader.add('ExportCSV', './components/export-csv'),
  DownloadUsersCSV: adminComponentLoader.add('DownloadUsersCSV', './components/download-user-csv'),
  DownloadCompaniesCSV: adminComponentLoader.add('DownloadCompaniesCSV', './components/download-company-csv'),
  CreateSalesOrder: adminComponentLoader.add('CreateSalesOrder', './components/create-sales-order'),
  ChangeLogWeb: adminComponentLoader.add('ChangeLogWeb', './components/change-log/change-log-web'),
  ChangeLogMobile: adminComponentLoader.add('ChangeLogMobile', './components/change-log/change-log-mobile')
};
