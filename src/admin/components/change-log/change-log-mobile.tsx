// components/MyFileUploader.jsx
import React, { useState, useRef } from 'react';
import { Box, Label, Button, H5, Icon, Input } from '@adminjs/design-system';
import { ActionProps, ApiClient, useNotice } from 'adminjs';

const api = new ApiClient();

const ChangeLogMobile: React.FC<ActionProps> = props => {
  const { action } = props;

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const sendNotice = useNotice();

  const [file, setFile] = useState(null);
  const [title, setTitle] = useState('');
  const [buttonName, setButtonName] = useState('');
  const [buttonUrl, setButtonUrl] = useState('');

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!file || !title || !buttonName || !buttonUrl) {
      sendNotice({ message: 'Please fill all the fields', type: 'error' });
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', title);
    formData.append('buttonName', buttonName);
    formData.append('buttonUrl', buttonUrl);
    api
      .resourceAction({
        method: 'post',
        resourceId: action.resourceId,
        actionName: 'new',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(response => {
        sendNotice({ message: response?.data?.notice?.message, type: 'success' });
        history.back();
      })
      .catch(error => {
        sendNotice({ message: error, type: 'error' });
      });
  };

  const handleClearFile = () => {
    if (fileInputRef.current) {
      fileInputRef.current.value = ''; // Reset the input
    }
    setFile(null);
    URL.revokeObjectURL(file); // Revoke the object URL to free up memory
  };

  return (
    <Box>
      <form onSubmit={handleSubmit}>
        <Box marginBottom="lg">
          <Label htmlFor="Title">Title</Label>
          <Input
            type="text"
            name="title"
            value={title}
            onChange={e => setTitle(e?.target?.value)}
            width="100%"
          />
        </Box>
        <Box marginBottom="lg">
          <Label htmlFor="button-name">Button Name</Label>
          <Input
            type="text"
            name="ButtonName"
            value={buttonName}
            onChange={e => setButtonName(e?.target?.value)}
            width="100%"
          />
        </Box>
        <Box marginBottom="lg">
          <Label htmlFor="button-url">Button Url</Label>
          <Input
            type="text"
            name="buttonUrl"
            value={buttonUrl}
            onChange={e => setButtonUrl(e?.target?.value)}
            width="100%"
          />
        </Box>
        <Box marginBottom="lg">
          <Label htmlFor="Image">Image</Label>
          <Box
            style={{
              width: '100%',
              height: '300px',
              border: `1px dashed ${isHovered ? 'blue' : '#CCCCCC'}`,
              borderRadius: '10px',
              textAlign: 'center',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              flexDirection: 'column'
            }}
            onClick={() => fileInputRef?.current?.click?.()}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <H5>Click to browse</H5>
            <H5>Image/png</H5>
            <H5>1024 x 1700</H5>
            <input
              type="file"
              name="file"
              accept="image/png"
              style={{ display: 'none' }}
              ref={fileInputRef}
              onChange={e => setFile(e.target.files[0])}
            />
          </Box>
          {file && (
            <Box style={{ position: 'relative' }}>
              <Button
                type="button"
                variant="link"
                onClick={handleClearFile}
                style={{
                  display: 'absolute',
                  right: '10px',
                  position: 'absolute',
                  top: '30px',
                  textAlign: 'center',
                  border: 'none',
                  background: 'none',
                  color: '#cccccc'
                }}
              >
                <Icon icon="Close" size={32} />
              </Button>
              <img src={URL.createObjectURL(file)} alt="preview" style={{ width: '100%', marginTop: '10px' }} />
            </Box>
          )}
        </Box>

        <Button variant="primary" type="submit">
          Submit
        </Button>
      </form>
    </Box>
  );
};

export default ChangeLogMobile;
