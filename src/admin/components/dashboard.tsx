import React from 'react';
import { ApiClient } from 'adminjs';
import { useEffect } from 'react';
import { Box, Header, Table, TableRow, TableCell, TableHead, TableBody } from '@adminjs/design-system';

const Dashboard = () => {
  const [env, setEnv] = React.useState(null);
  const client = new ApiClient();

  useEffect(() => {
    client
      .getDashboard()
      .then(response => response.data)
      .then(data => setEnv(data))
      .catch(error => {
        console.log(error);
      });
  }, []);

  return (
    <Box margin="md">
      <Header.H4>Secret ENV</Header.H4>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Key</TableCell>
            <TableCell>Value</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {env &&
            Object.keys(env).map(key => (
              <TableRow key={key}>
                <TableCell style={{ width: '30%' }}>{key}</TableCell>
                <TableCell style={{ width: '70%' }}>{env[key]}</TableCell>
              </TableRow>
            ))}
        </TableBody>
      </Table>
    </Box>
  );
};
export default Dashboard;
