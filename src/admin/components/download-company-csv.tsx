import { useEffect } from 'react';
import { ApiClient } from 'adminjs';
import { Box } from '@adminjs/design-system';

const DownloadCompanyCSV = () => {
  const api = new ApiClient();

  useEffect(() => {
    const getData = async () => {
      return await api.resourceAction({
        resourceId: 'CompanyEntity',
        actionName: 'exportAllCompaniesCSV',
      });
    };
    getData()
      .then((res: any) => {
        const csv = res?.data?.csv;
        const fileName = res?.data?.fileName;
        if (!csv || !fileName) return;
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.setAttribute('hidden', '');
        a.setAttribute('href', url);
        a.setAttribute('download', fileName);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      })
      .finally(() => {
        history.back();
      });
  }, []);

  return (
    <Box>
      <p>Exporting CSV...</p>
    </Box>
  );
};

export default DownloadCompanyCSV;
