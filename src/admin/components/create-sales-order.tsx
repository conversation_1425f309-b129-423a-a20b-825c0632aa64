import React, { useState, useEffect } from 'react';
import { useNotice, ActionProps, ApiClient } from 'adminjs';

import { Box, Label, Button, Select } from '@adminjs/design-system';

const CreateOrderSales: React.FC<ActionProps> = props => {
  const { resource, action, record } = props;
  const sendNotice = useNotice();
  const [subscriptionPackages, setSubscriptionPackages] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [subscriptionPackageId, setSubscriptionPackageId] = useState('');
  const [companyId, setCompanyId] = useState('');

  const api = new ApiClient();

  useEffect(() => {
    api
      .resourceAction({
        resourceId: action.resourceId,
        actionName: 'createSalesOrder'
      })
      .then(response => {
        setSubscriptionPackages(response.data.subscriptionPackages);
        setCompanies(response.data.companies);
      })
      .catch(error => {
        sendNotice({ message: error, type: 'error' });
      });
  }, []);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    api
      .resourceAction({
        method: 'post',
        resourceId: action.resourceId,
        actionName: 'createSalesOrder',
        data: {
          payload: {
            subscriptionPackageId,
            companyId
          }
        }
      })
      .then(response => {
        sendNotice({ message: response?.data?.notice?.message, type: 'success' });
        history.back();
      })
      .catch(error => {
        sendNotice({ message: error, type: 'error' });
      });
  };

  return (
    <Box>
      <form onSubmit={handleSubmit}>
        <Box marginBottom="lg">
          <Label htmlFor="subscription-package">Subscription Package</Label>
          <Select
            value={subscriptionPackageId}
            onChange={e => setSubscriptionPackageId(e)}
            options={subscriptionPackages.map(subscriptionPackage => ({
              value: subscriptionPackage.id,
              label: subscriptionPackage.title
            }))}
          />
        </Box>

        <Box marginBottom="lg">
          <Label htmlFor="company">Company</Label>
          <Select
            value={companyId}
            onChange={e => setCompanyId(e)}
            options={companies.map(company => ({
              value: company.id,
              label: company.name
            }))}
          />
        </Box>

        <Button variant="primary" type="submit">
          Submit
        </Button>
      </form>
    </Box>
  );
};

export default CreateOrderSales;
