import { useEffect } from 'react'
import { ActionProps } from 'adminjs'
import { Box } from '@adminjs/design-system'
import csvDownload from 'json-to-csv-export'

const ExportCSV = (props: ActionProps) => {
    const { records } = props

    const csv = records.map((record) => record.params).map((record) => ({
        ...record,
        password: '********',
        refreshToken: '********',
        resetPasswordToken: '********',
        signUpToken: '********',
        fcmToken: '********',
    }))

    const dataToConvert = {
        data: csv,
        filename: 'users',
        delimiter: ','
    }

    useEffect(() => {
        csvDownload(dataToConvert)
        history.back()
    }, [])

    return (
        <Box>
            <p>Exporting CSV...</p>
        </Box>
    )
}

export default ExportCSV