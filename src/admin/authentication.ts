import { AdminEntity } from '@modules/admin/entity/admin.entity';
import { comparePassword } from '@providers/bcrypt.service';
import { getRepository } from 'typeorm';

export const adminAuthenticate = async (email: string, password: string) => {
  const admin = await getRepository(AdminEntity).findOne({ where: { email } });
  if (admin && (await comparePassword(password, admin.password))) {
    return Promise.resolve({
      email: admin.email,
      password: admin.password
    });
  }
  return null;
};
