import { AdminEntity } from '@modules/admin/entity/admin.entity';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { ChecklistItemEntity } from '@modules/checklist/entity/checklist-item.entity';
import { ChecklistEntity } from '@modules/checklist/entity/checklist.entity';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { ContactCompanyEntity } from '@modules/contact/entity/contact-company.entity';
import { ContactEntity } from '@modules/contact/entity/contact.entity';
import { EventEntity } from '@modules/event/entity/event.entity';
import { FormCategoryEntity } from '@modules/form-category/entity/form-category.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { ProjectCarouselEntity } from '@modules/project-carousel/entity/project-carousel.entity';
import { ProjectDocumentCommentEntity } from '@modules/project-document-comment/entity/project-document-comment.entity';
import { ProjectDocumentUserEntity } from '@modules/project-document-user/entities/project-document-user.entity';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { ProjectInvitationEntity } from '@modules/project-invitation/entity/project-invitation.entity';
import { ProjectTeamEntity } from '@modules/project-team/entity/project-team.entity';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { SalesOrderEntity } from '@modules/sales-order/entity/sales-order.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { TaskCommentEntity } from '@modules/task-comment/entity/task-comment.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import { TasksAttachmentEntity } from '@modules/tasks-attachment/entity/tasks-attachment.entity';
import { TasksMediaEntity } from '@modules/tasks-media/entity/tasks-media.entity';
import { UserFcmTokenEntity } from '@modules/user-fcm-token/entity/user-fcm-token.entity';
import { UserEntity } from '@modules/user/entity/user.entity';
import { WorkspaceAttachmentEntity } from '@modules/workspace-attachment/entity/workspace-attachment.entity';
import { WorkspaceCCEntity } from '@modules/workspace-cc/entity/workspace-cc.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { WorkspacePhotoEntity } from '@modules/workspace-photo/entity/workspace-photo.entity';
import { AdminJSOptions } from 'adminjs';
import { toNumber } from 'lodash';
import { createConnection, getRepository } from 'typeorm';
import { AdminComponents } from './component.loader';
import { TimezoneEntity } from '@modules/timezone/entity/timezone.entity';
import { Parser } from '@json2csv/plainjs';
import moment from 'moment';
import { HttpException, HttpStatus } from '@nestjs/common';
import { generateInvoiceNumber } from '@common/common-helper';
import { createId } from '@paralleldrive/cuid2';
import { MobileVersionEntity } from '@modules/mobile-version/entities/mobile-version.entity';
import { ChangeLogEntity } from '@modules/change-log/entity/change-log.entity';
import { ChangeLogMobileEntity } from '@modules/change-log-mobile/entity/change-log-mobile.entity';

export const connection = createConnection({
  name: 'admin-default',
  type: process.env.DB_DRIVER as any,
  host: process.env.DB_HOST,
  port: toNumber(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  entities: [__dirname + '../../**/*.entity{.ts,.js}'],
  synchronize: false,
  logging: false
});

export const adminJSResources = async (): Promise<AdminJSOptions['resources']> => {
  const entities = [
    ContactCompanyEntity,
    ContactEntity,
    NotificationTransactionEntity,
    ProjectTeamEntity,
    ChecklistItemEntity,
    ChecklistEntity,
    FormCategoryEntity,
    RequestForSignatureEntity,
    ProjectDocumentCommentEntity,
    ProjectDocumentUserEntity,
    WorkspaceGroupEntity,
    WorkspacePhotoEntity,
    WorkspaceAttachmentEntity,
    WorkspaceCCEntity,
    ProjectDocumentEntity,
    ProjectGroupEntity,
    TasksAttachmentEntity,
    TasksMediaEntity,
    TaskEntity,
    TaskCommentEntity,
    EventEntity,
    UserFcmTokenEntity,
    AuditLogEntity,
    ProjectInvitationEntity,
    ProjectCarouselEntity,
    ProjectEntity,
    ProjectUserEntity
  ];

  entities.forEach(async entity => entity.useConnection(await connection));

  return [
    ...entities.map(entity => ({
      resource: entity,
      options: {
        actions: {
          new: {
            isAccessible: false
          },
          edit: {
            isAccessible: false
          },
          delete: {
            isAccessible: false
          },
          show: {
            isAccessible: false
          },
          list: {
            isAccessible: false
          }
        }
      }
    })),
    {
      title: 'Users',
      resource: UserEntity,
      options: {
        listProperties: ['id', 'email', 'name', 'isEmailVerified', 'createdAt', 'stampUrl', 'signUrl', 'removedAt'],
        showProperties: ['email', 'name', 'companyId', 'isEmailVerified','signUrl', 'signKey', 'stampUrl', 'stampKey', 'stampAndSignKey','stampAndSignUrl', 'removedAt'],
        editProperties: ['email', 'name', 'companyId', 'isEmailVerified','signUrl', 'signKey', 'stampUrl', 'stampKey', 'stampAndSignKey','stampAndSignUrl', 'removedAt'],
        filterProperties: ['email', 'name', 'isEmailVerified', 'createdAt', 'companyId'],
        actions: {
          delete: {
            isAccessible: false
          },
          exportCSV: {
            actionType: 'bulk',
            component: AdminComponents.ExportCSV,
            handler: async (request, response, context) => {
              const { currentAdmin, records } = context;
              return { records: records.map(r => r.toJSON(currentAdmin)) };
            }
          },
          exportAllUsersCSV: {
            actionType: 'resource',
            component: AdminComponents.DownloadUsersCSV,
            handler: async () => {
              const users = await getRepository(UserEntity).find();
              const sanitizedUsers = users.map(record => ({
                ...record,
                password: '********',
                refreshToken: '********',
                resetPasswordToken: '********',
                signUpToken: '********',
                fcmToken: '********'
              }));

              const parser = new Parser();
              const csv = parser.parse(sanitizedUsers);
              const fileName = `users-${Date.now()}.csv`;
              return {
                fileName,
                csv
              };
            }
          },
          syncUsersToCompanies: {
            actionType: 'resource',
            component: false,
            handler: async () => {
              const users = await getRepository(UserEntity).find({ relations: ['company', 'companies'] });
              Promise.all(
                users.map(user => {
                  if (user.company && !user.companies.find(c => c.id === user.company.id)) {
                    user.companies = [...user.companies, user.company];
                    return getRepository(UserEntity).save(user);
                  }
                })
              );
              return true;
            }
          }
        }
      }
    },
    {
      resource: AdminEntity,
      options: {
        listProperties: ['id', 'email', 'name'],
        showProperties: ['email', 'name', 'isActive'],
        editProperties: ['email', 'name', 'password', 'isActive'],
        filterProperties: ['email'],
        actions: {
          delete: {
            isAccessible: false
          }
        }
      }
    },
    {
      resource: CompanyEntity,
      options: {
        listProperties: ['id', 'name', 'ownerId', 'stampKey', 'stampUrl', 'maxUsers', 'hasUsedFreeTrial'],
        showProperties: ['name', 'ownerId', 'maxUsers', 'hasUsedFreeTrial'],
        editProperties: ['name', 'ownerId', 'stampKey', 'stampUrl', 'maxUsers', 'hasUsedFreeTrial'],
        filterProperties: ['name', 'ownerId', 'maxUsers', 'hasUsedFreeTrial'],
        actions: {
          delete: {
            isAccessible: false
          },
          exportAllCompaniesCSV: {
            actionType: 'resource',
            label: 'Export All Companies',
            component: AdminComponents.DownloadCompaniesCSV,
            handler: async () => {
              const companies = await getRepository(CompanyEntity).find({
                relations: ['owner', 'companySubscriptions', 'companySubscriptions.subscriptionPackage']
              });
              const sanitizedCompanies = companies.map(record => ({
                id: record?.id,
                company_name: record?.name,
                owner_name: record?.owner?.name,
                max_users: record?.maxUsers,
                has_used_free_trial: record?.hasUsedFreeTrial,
                subscription_package: record?.companySubscriptions?.[0]?.subscriptionPackage?.title || 'N/A',
                subscription_end_date: record?.companySubscriptions?.[0]?.subscriptionEndDate
                  ? moment(record?.companySubscriptions?.[0]?.subscriptionEndDate).format('DD/MM/YYYY')
                  : 'N/A',
                subscription_expired: record?.companySubscriptions?.[0]?.subscriptionEndDate
                  ? record?.companySubscriptions?.[0]?.subscriptionEndDate < new Date()
                  : 'N/A'
              }));

              const parser = new Parser();
              const csv = parser.parse(sanitizedCompanies);
              const fileName = `companies-${Date.now()}.csv`;
              return {
                fileName,
                csv
              };
            }
          }
        }
      }
    },
    {
      resource: CompanySubscriptionEntity,
      options: {
        listProperties: ['id', 'subscriptionPackageId', 'companyId', 'subscriptionEndDate'],
        showProperties: ['subscriptionPackageId', 'companyId', 'subscriptionEndDate'],
        editProperties: ['subscriptionPackageId', 'companyId', 'subscriptionEndDate'],
        filterProperties: ['subscriptionPackageId', 'companyId', 'subscriptionEndDate'],
        actions: {
          delete: {
            isAccessible: false
          }
        }
      }
    },
    {
      resource: SubscriptionPackageEntity,
      options: {
        listProperties: ['id', 'title', 'amount', 'availableDuration'],
        showProperties: [
          'title',
          'description',
          'amount',
          'availableDuration',
          'totalProjects',
          'totalUsers',
          'allowTask',
          'allowProjectDocument',
          'allowWorkProgramme',
          'allowEmailCorrespondence',
          'allowWorkspaceDocument',
          'allowWorkspaceTemplate',
          'allowDrawing',
          'allowBimModel',
          'allowPhoto',
          'allowScheduleChart',
          'allowScheduleActivity',
          'allowDashboard'
        ],
        editProperties: [
          'title',
          'description',
          'amount',
          'availableDuration',
          'totalProjects',
          'totalUsers',
          'allowTask',
          'allowProjectDocument',
          'allowWorkProgramme',
          'allowEmailCorrespondence', // Change this from 'allowCorrespondence'
          'allowWorkspaceDocument',
          'allowWorkspaceTemplate',
          'allowDrawing',
          'allowBimModel',
          'allowPhoto',
          'allowScheduleChart',
          'allowScheduleActivity',
          'allowDashboard'
        ],
        properties: {
          description: {
            type: 'richtext',
          },
        },
        filterProperties: ['title', 'amount', 'availableDuration'],
        actions: {
          delete: {
            isAccessible: false
          },
          createSalesOrder: {
            actionType: 'resource',
            component: AdminComponents.CreateSalesOrder,
            icon: 'Add',
            name: 'create-sales-order',
            label: 'Create Sales Order',
            handler: async (
              request: Record<string, any>,
              response: Record<string, any>,
              context: Record<string, any>
            ) => {
              if (request.method === 'get') {
                const companies = await getRepository(CompanyEntity).find({
                  select: ['id', 'name']
                });
                const subscriptionPackages = await getRepository(SubscriptionPackageEntity).find({
                  select: ['id', 'title']
                });
                return {
                  companies,
                  subscriptionPackages
                };
              }
              const { subscriptionPackageId, companyId } = request.payload;

              const subscriptionPackage = await getRepository(SubscriptionPackageEntity).findOne(subscriptionPackageId);

              const company = await getRepository(CompanyEntity).findOne(companyId);

              if (!subscriptionPackage || !company) {
                throw new HttpException('Subscription package or company not found', HttpStatus.NOT_FOUND);
              }

              await getRepository(SalesOrderEntity).save({
                userId: company.ownerId,
                cuid: createId(),
                companyId: company.id,
                subscriptionPackageId: subscriptionPackage.id,
                total: subscriptionPackage.amount,
                invoiceNumber: generateInvoiceNumber()
              });

              return {
                notice: {
                  message: 'Successfully created sales order',
                  type: 'success'
                }
              };
            }
          }
        }
      }
    },
    {
      resource: TimezoneEntity,
      options: {
        listProperties: ['id', 'name', 'value'],
        showProperties: ['id', 'name', 'value'],
        editProperties: ['id', 'name', 'value'],
        filterProperties: ['id', 'name', 'value'],
        actions: {
          delete: {
            isAccessible: false
          }
        }
      }
    },
    {
      resource: SalesOrderEntity,
      options: {
        actions: {
          delete: {
            isAccessible: false
          }
        },
        listProperties: ['id', 'invoiceNumber', 'userId', 'companyId', 'subscriptionPackageId', 'total', 'status'],
        showProperties: [
          'id',
          'invoiceNumber',
          'userId',
          'companyId',
          'subscriptionPackageId',
          'total',
          'status',
          'transactionId',
          'message',
          'data',
          'isRecurrence'
        ],
        editProperties: [
          'total',
          'status',
          'invoiceNumber',
          'isRecurrence',
          'userId',
          'companyId',
          'subscriptionPackageId',
          'total'
        ],
        filterProperties: [
          'id',
          'invoiceNumber',
          'companyId',
          'userId',
          'subscriptionPackageId',
          'status',
          'isRecurrence',
          'transactionId'
        ]
      }
    },
    {
      resource: MobileVersionEntity,
      options: {
        listProperties: ['id', 'platformName', 'buildCode', 'versionCode', 'updatedAt'],
        showProperties: ['id', 'platformName', 'buildCode', 'versionCode'],
        editProperties: ['id', 'platformName', 'buildCode', 'versionCode'],
        filterProperties: ['id', 'platformName', 'buildCode', 'versionCode'],
        actions: {
          delete: {
            isAccessible: false
          }
        }
      }
    },
    {
      resource: ChangeLogEntity,
      options: {
        listProperties: ['id', 'title', 'imageUrl', 'buttonName', 'buttonUrl'],
        showProperties: ['id', 'title', 'imageUrl', 'buttonName', 'buttonUrl'],
        actions: {
          bulkDelete: { isVisible: true },
          delete: {
            isVisible: true,
          },
          new: {
            actionType: 'resource',
            component: AdminComponents.ChangeLogWeb,
            icon: 'Add',
            name: 'create-new',
            label: 'Create New',
            handler: async (
              request: Record<string, any>,
            ) => {
              if (request.method === 'get') {
                return {};
              }
              try {
                const { file, title, buttonName, buttonUrl } = request.payload;
                await getRepository(ChangeLogEntity).save({
                  cuid: createId(),
                  imageUrl: file,
                  title,
                  buttonName,
                  buttonUrl
                });

                return {
                  notice: {
                    message: 'Successfully created change log',
                    type: 'success'
                  }
                };
              } catch (error) {
                console.log(error, 'error')
                return {
                  notice: {
                    message: 'Failed to create change log',
                    type: 'error'
                  }
                };
              }
            }
          }
        },
      }
    },
    {
      resource: ChangeLogMobileEntity,
      options: {
        listProperties: ['id', 'title', 'imageUrl', 'buttonName', 'buttonUrl'],
        showProperties: ['id', 'title', 'imageUrl', 'buttonName', 'buttonUrl'],
        actions: {
          bulkDelete: { isVisible: true },
          delete: {
            isVisible: true,
          },
          new: {
            actionType: 'resource',
            component: AdminComponents.ChangeLogMobile,
            icon: 'Add',
            name: 'create-new',
            label: 'Create New',
            handler: async (
              request: Record<string, any>,
            ) => {
              if (request.method === 'get') {
                return {};
              }
              try {
                const { file, title, buttonName, buttonUrl } = request.payload;
                await getRepository(ChangeLogMobileEntity).save({
                  cuid: createId(),
                  imageUrl: file,
                  title,
                  buttonName,
                  buttonUrl
                });

                return {
                  notice: {
                    message: 'Successfully created change log',
                    type: 'success'
                  }
                };
              } catch (error) {
                console.log(error, 'error')
                return {
                  notice: {
                    message: 'Failed to create change log',
                    type: 'error'
                  }
                };
              }
            }
          }
        },
      }
    }
  ];
};
