import { toNumber } from 'lodash';

const express = require('express');
require('dotenv').config();
const session = require('express-session');
const app = express();
const cors = require('cors');
app.use(cors());
const mysqlStore = require('express-mysql-session')(session);

const options = {
  user: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  host: process.env.DB_HOST,
  port: toNumber(process.env.DB_PORT),
  connectionLimit: 10,
  createDatabaseTable: true
};

const sessionStore = new mysqlStore(options);

export const sessionOptions = {
  resave: true,
  saveUninitialized: true,
  secret: process.env.ADMIN_SECRET,
  store: sessionStore
};
