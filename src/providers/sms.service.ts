import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class SmsService {
  username = process.env.SMS_USERNAME;
  password = process.env.SMS_PASSWORD;
  senderId = process.env.SMS_SENDER_ID;

  async send(mobileNumber: string, message: string) {
    await axios.get('https://gatewayd2.onewaysms.sg/api.aspx', {
      params: {
        apiusername: this.username,
        apipassword: this.password,
        senderid: this.senderId,
        languagetype: 1,
        mobileno: mobileNumber,
        message
      }
    });
  }
}
