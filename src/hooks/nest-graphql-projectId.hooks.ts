import {
  BeforeCreate<PERSON><PERSON>Hook,
  BeforeCreateOneHook,
  CreateManyInputType,
  CreateOneInputType
} from '@nestjs-query/query-graphql';
import { Injectable } from '@nestjs/common';
import { GqlContext } from '@types';

interface ProjectId {
  projectId: number;
}

@Injectable()
export class ProjectIdOneHook<T extends ProjectId> implements BeforeCreateOneHook<T, GqlContext> {
  async run(instance: CreateOneInputType<T>, context: GqlContext): Promise<CreateOneInputType<T>> {
    const projectId = Number(context.req.headers['project-id']);
    instance.input.projectId = projectId;
    return instance;
  }
}

@Injectable()
export class ProjectIdManyHook<T extends ProjectId> implements BeforeCreateManyHook<T, GqlContext> {
  async run(instance: CreateManyInputType<T>, context: GqlContext): Promise<CreateManyInputType<T>> {
    const projectId = Number(context.req.headers['project-id']);
    instance.input = instance.input.map(p => ({ ...p, projectId }));
    return instance;
  }
}
