import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BeforeCreateMany<PERSON>ook,
  <PERSON>C<PERSON><PERSON><PERSON>,
  BeforeCreateOneHook,
  BeforeUp<PERSON><PERSON>any,
  BeforeUpdateManyHook,
  BeforeUpdate<PERSON>ne,
  BeforeUpdateOneHook,
  CreateManyInputType,
  CreateOneInputType,
  UpdateManyInputType,
  UpdateOneInputType
} from '@nestjs-query/query-graphql';
import { applyDecorators, Injectable } from '@nestjs/common';
import { GqlContext } from '@types';

interface CreatedBy {
  createdBy: number;
}

interface UpdatedBy {
  updatedBy: number;
}

@Injectable()
export class CreatedByOneHook<T extends CreatedBy> implements BeforeCreateOneHook<T, GqlContext> {
  async run(instance: CreateOneInputType<T>, context: GqlContext): Promise<CreateOneInputType<T>> {
    const createdBy = context.req.user.id;
    instance.input.createdBy = createdBy;
    return instance;
  }
}

@Injectable()
export class CreatedByManyHook<T extends CreatedBy> implements BeforeCreateManyHook<T, GqlContext> {
  async run(instance: CreateManyInputType<T>, context: GqlContext): Promise<CreateManyInputType<T>> {
    const createdBy = context.req.user.id;
    instance.input = instance.input.map(c => ({ ...c, createdBy }));
    return instance;
  }
}

@Injectable()
export class UpdatedByOneHook<T extends UpdatedBy> implements BeforeUpdateOneHook<T, GqlContext> {
  async run(instance: UpdateOneInputType<T>, context): Promise<UpdateOneInputType<T>> {
    instance.update.updatedBy = context.req.user.id;
    return instance;
  }
}

@Injectable()
export class UpdatedByManyHook<T extends UpdatedBy> implements BeforeUpdateManyHook<T, any, GqlContext> {
  async run(instance: UpdateManyInputType<T, T>, context: GqlContext): Promise<UpdateManyInputType<T, T>> {
    instance.update.updatedBy = context.req.user.id;
    return instance;
  }
}

export const UseRecordHooks = () => {
  return applyDecorators(
    BeforeCreateOne(CreatedByOneHook),
    BeforeCreateMany(CreatedByManyHook),
    BeforeUpdateOne(UpdatedByOneHook),
    BeforeUpdateMany(UpdatedByManyHook)
  );
};
