import { BadRequestException, ConflictException, InternalServerErrorException } from '@nestjs/common';

export const getErrorMessage = (error: any, destination: string, functionName: string) => {
  console.error('error', error);

  if (error.status === 409) {
    throw new ConflictException({
      statusCode: 409,
      message: error.message,
      error: 'test'
    });
  } else if (error.status >= 400 && error.status < 500) {
    throw new BadRequestException(error.message);
  } else {
    throw new InternalServerErrorException({
      message: `Something went wrong in the ${destination}. Function Name: ${functionName}. Error message: ${
        error.message ?? error
      }. `
    });
  }
};
