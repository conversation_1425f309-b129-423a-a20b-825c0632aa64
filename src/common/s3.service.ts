import * as _ from 'lodash';
import * as AWS from 'aws-sdk';
// import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import mime from 'mime';
import * as R from 'ramda';

export class S3Service {
  constructor(private _s3Bucket?: any, private _s3PublicBucket?: any) {
    this._s3Bucket = new AWS.S3({
      params: { Bucket: process.env.AWS_S3_BUCKET, timeout: 6000000 },
      region: 'ap-southeast-1'
    });

    this._s3PublicBucket = new AWS.S3({
      params: { Bucket: process.env.AWS_S3_BUCKET_PUBLIC, timeout: 6000000 },
      region: 'ap-southeast-1'
    });
  }

  uploadToS3(
    s3Directory: string,
    fileStream: any,
    mimeType: string,
    {
      publicAccess = true,
      publicBucket = false,
      fileName
    }: {
      publicAccess?: boolean;
      publicBucket?: boolean;
      fileName?: string;
    } = {}
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        if (R.isNil(fileStream) || R.isEmpty(fileStream)) return resolve({ path: null, fileName: null });
        const configs = {
          ContentType: mimeType
        };
        if (publicAccess) _.set(configs, 'ACL', 'public-read');
        const bucket = publicBucket ? this._s3PublicBucket : this._s3Bucket;

        // const generatedFileName = `${uuidv4()}.${mime.extension(mimeType)}`;

        // bucket.putObject();
        // return bucket.upload(
        //   {
        //     Key: `${s3Directory}${fileName || generatedFileName}`,
        //     Body: fileStream,
        //     ContentType: mimeType,
        //     ...configs,
        //   },
        //   (err: any, res: any) => {
        //     if (err) {
        //       return reject(err);
        //     }
        //     return resolve({
        //       path: `https://${bucket.config.params.Bucket}.${bucket.config.endpoint}/${res.key}`,
        //       fileName: generatedFileName,
        //     });
        //   },
        // );
      } catch (e) {
        return reject(e);
      }
    });
  }

  deleteObjectFromS3 = (filePath: string, publicBucket = false): Promise<any> =>
    new Promise(async (resolve, reject) => {
      const bucket = publicBucket ? this._s3PublicBucket : this._s3Bucket;
      bucket.deleteObject({ Key: this._parseFileUrlForDeletion(filePath) }, (err, data) => {
        if (err != null) return reject(err);
        return resolve(data);
      });
    });

  _parseFileUrlForDeletion = (url: string) => {
    return _.join(_.drop(url, _.size(process.env.AWS_S3_URL) + 1), '');
  };
}
