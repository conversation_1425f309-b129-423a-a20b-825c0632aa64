import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

// these are the errors that we dont want to capture in sentry
// need better way to handle this
// ie: moved this to constants file
// maybe just ignore all bad request exceptions
const handledErrors = [
  'Email or Password is incorrect',
  'You are not involved in this project',
  'This invitation has expired',
  'New password must be different from old password',
  'Email Not Found',
  'Please check your email to verify account before login',
  'Error fetching Memos',
  'Error fetching Signatures',
  'Error fetching Stamps',
  'This code already exist',
  'Password contains invalid characters. Only letters, digits, and @$!%*?& are allowed.',
  "Password must be at least 8 characters long.",
  "Password must include at least one uppercase letter (A-Z).",
  "Password must include at least one lowercase letter (a-z).",
  "Password must include at least one digit (0-9)."
];
import * as Sentry from '@sentry/node';
const enableSentry = err => {
  if (!handledErrors.includes(err?.response?.message)) {
    Sentry.captureException(err);
  }

  return throwError(() => err);
};
@Injectable()
export class SentryInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler<any>): Observable<any> | Promise<Observable<any>> {
    return (next.handle() as Observable<any>).pipe(catchError(enableSentry));
  }
}