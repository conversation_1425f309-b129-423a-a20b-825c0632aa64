import { PDFDocument } from 'pdf-lib';
import fetch from 'node-fetch';
import axios from 'axios';

interface Pdfs {
  fileUrl: string;
  type: string;
}

async function loadPdf(url) {
  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`Could not fetch ${url}: ${response.statusText}`);
    const arrayBuffer = await response.arrayBuffer();
    return await PDFDocument.load(arrayBuffer);
  } catch (error) {
    console.error(`Error loading PDF ${url}:`, error);
    throw error;
  }
}
async function loadImagesAsPdf(imagesFiles: Pdfs[]) {
  const pdfDoc = await PDFDocument.create();
  const pageSize: any = [595.28, 841.89]; // A4 size in points

  for (let i = 0; i < imagesFiles.length; i++) {
    // Add a new page for each pair of images or for the first image
    if (i % 2 === 0) {
      pdfDoc.addPage(pageSize);
    }

    const currentPage = pdfDoc.getPages()[Math.floor(i / 2)];
    const imagePlacement = i % 2; // 0 for top, 1 for bottom
    await embedImageOnPage(pdfDoc, imagesFiles[i].fileUrl, currentPage, imagePlacement);
  }

  return pdfDoc;
}

async function embedImageOnPage(pdfDoc, imageUrl, mediaPage, imagePlacement) {
  try {
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    let image;

    try {
      image = await pdfDoc.embedPng(response.data);
    } catch (error) {
      if (error === 'The input is not a PNG file!') {
        image = await pdfDoc.embedJpg(response.data);
      }
    }

    const pageWidth = mediaPage.getWidth();
    const pageHeight = mediaPage.getHeight();
    const imageMargin = 20; // Margin from the image to the edge of the page

    // Calculate the maximum image dimensions to fit within half the page height
    const maxImageWidth = pageWidth - 2 * imageMargin;
    const maxImageHeight = (pageHeight / 2) - 2 * imageMargin;

    // Scale the image to fit within the max dimensions
    const { width: scaledWidth, height: scaledHeight } = image.scaleToFit(maxImageWidth, maxImageHeight);

    // Determine the y position based on whether the image is at the top or bottom
    const yPosition = imagePlacement === 0
      ? pageHeight - scaledHeight - imageMargin // Top image
      : imageMargin; // Bottom image

    // Center the image horizontally
    const xPosition = (pageWidth - scaledWidth) / 2;

    mediaPage.drawImage(image, { x: xPosition, y: yPosition, width: scaledWidth, height: scaledHeight });
  } catch (error) {
    console.error('Error embedding image:', imageUrl, error);
  }
}

async function mergePdfs(pdfs: Pdfs[], title) {
  const mergedPdfDoc = await PDFDocument.create();

  // different types of files
  const pdfFiles = pdfs.filter(pdf => pdf.type?.toLowerCase?.() === 'pdf');
  const imageFiles = pdfs.filter(pdf =>
    ['jpg', 'jpeg', 'png', 'gif'].some(ext => pdf.type?.toLowerCase()?.includes(ext))
  );

  for (const pdf of pdfFiles) {
    try {
      const pdfDoc = await loadPdf(pdf.fileUrl);
      const copiedPages = await mergedPdfDoc.copyPages(pdfDoc, pdfDoc.getPageIndices());
      copiedPages.forEach(page => mergedPdfDoc.addPage(page));
    } catch (error) {
      console.error(`Error processing PDF ${pdf.fileUrl}:`, error);
    }
  }

  if (imageFiles.length > 0) {
    try {
      const imagePdfDoc = await loadImagesAsPdf(imageFiles);
      const copiedPages = await mergedPdfDoc.copyPages(imagePdfDoc, imagePdfDoc.getPageIndices());
      copiedPages.forEach(page => mergedPdfDoc.addPage(page));
    } catch (error) {
      console.error(`Error processing image files:`, error);
    }
  }
  const finalPdfBytes = await mergedPdfDoc.save();
  const buffer = Buffer.from(finalPdfBytes);
  const folder = 'Store';
  const file = {
    filename: `${title}.pdf`,
    mimetype: 'application/pdf',
    encoding: '7bit'
  };

  // Add the size property to the return object (size in bytes)
  const size = buffer.length;

  return { buffer, folder, file, finalPdfBytes, size };
}

function isImage(url) {
  return /\.(jpg|jpeg|png|gif)$/.test(url?.toLowerCase?.());
}

export { mergePdfs };
