import { AdminJwtAuthGuard, UserJwtAuthGuard } from '@guards/auth.guard';
import { applyDecorators, createParamDecorator, ExecutionContext, SetMetadata, UseGuards } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { ApiBearerAuth } from '@nestjs/swagger';

export const Roles = (...roles: string[]) => SetMetadata('roles', roles);

export const ProjectRoles = (...projectRoles: string[]) => SetMetadata('projectRoles', projectRoles);

export const GetAuthData = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return request.user;
});

export const GqlGetGqlAuthData = createParamDecorator((_data, context: ExecutionContext) => {
  const ctx = GqlExecutionContext.create(context);
  const { req } = ctx.getContext();
  return req.user;
});

export const GetProjectData = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  return Number(request.headers['project-id']);
});

export const GetHeaders = createParamDecorator((_data: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();

  const headers = {
    headers: request.headers,
    user: request.user
  };
  return headers;
});

export const GqlGetGqlProjectData = createParamDecorator((_data, context: ExecutionContext) => {
  const ctx = GqlExecutionContext.create(context);
  const { req } = ctx.getContext();
  return Number(req.headers['project-id']);
});

export const UseApiAdminAuthGuard = () => {
  return applyDecorators(ApiBearerAuth(), UseGuards(AdminJwtAuthGuard));
};

export const UseApiUserAuthGuard = () => {
  return applyDecorators(ApiBearerAuth(), UseGuards(UserJwtAuthGuard));
};
