import { OriginConfig } from '@configs/app.config';
import { IS_PRODUCTION_ENV } from '@constants';
import { AdminModule } from '@modules/admin/admin.module';
import { AuditLogModule } from '@modules/audit-log/audit-log.module';
import { AuthModule } from '@modules/auth/auth.module';
import { ChecklistModule } from '@modules/checklist/checklist.module';
import { CompanySubscriptionModule } from '@modules/company-subscription/company-subscription.module';
import { CompanyModule } from '@modules/company/company.module';
import { ContactModule } from '@modules/contact/contact.module';
import { DashboardModule } from '@modules/dashboard/dashboard.module';
import { EventModule } from '@modules/event/event.module';
import { FormCategoryModule } from '@modules/form-category/form-category.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { NotificationTransactionModule } from '@modules/notification-transaction/notification-transaction.module';
import { ProjectDocumentCommentModule } from '@modules/project-document-comment/project-document-comment.module';
import { ProjectDocumentModule } from '@modules/project-document/project-document.module';
import { ProjectInvitationModule } from '@modules/project-invitation/project-invitation.module';
import { ProjectTeamModule } from '@modules/project-team/project-team.module';
import { ProjectCarouselModule } from '@modules/project-carousel/project-carousel.module';
import { ProjectUserModule } from '@modules/project-user/project-user.module';
import { ProjectModule } from '@modules/project/project.module';
import { RequestForSignatureModule } from '@modules/request-for-signature/request-for-signature.module';
import { SalesOrderModule } from '@modules/sales-order/sales-order.module';
import { SubscriptionPackageModule } from '@modules/subscription-package/subscription-package.module';
import { TaskCommentModule } from '@modules/task-comment/task-comment.module';
import { TaskModule } from '@modules/task/task.module';
import { TasksAttachmentModule } from '@modules/tasks-attachment/tasks-attachment.module';
import { TasksMediaModule } from '@modules/tasks-media/tasks-media.module';
import { UserFcmTokenModule } from '@modules/user-fcm-token/user-fcm-token.module';
import { UserModule } from '@modules/user/user.module';
import { WebSocketModule } from '@modules/websocket/websocket.module';
import { WorkspaceAttachmentModule } from '@modules/workspace-attachment/workspace-attachment.module';
import { WorkspaceCCModule } from '@modules/workspace-cc/workspace-cc.module';
import { WorkspaceGroupModule } from '@modules/workspace-group/workspace-group.module';
import { WorkspacePhotoModule } from '@modules/workspace-photo/workspace-photo.module';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module, Inject, OnApplicationBootstrap } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { TypeOrmModule } from '@nestjs/typeorm';
import { toNumber } from 'lodash';
import { ProjectGroupModule } from './modules/project-group/project-group.module';
import { ProjectDocumentUserModule } from './modules/project-document-user/project-document-user.module';
import { AdminModule as AdminJSModule } from '@adminjs/nestjs';
import AdminJS from 'adminjs';
import { Resource, Database } from '@adminjs/typeorm';
import { adminJSResources } from './admin/loader';
import { adminAuthenticate } from './admin/authentication';
import { AdminComponents, adminComponentLoader } from './admin/component.loader';
import { locale } from './admin/locale';
import { branding } from './admin/branding';
import { sessionOptions } from './admin/session';
import { TimezoneModule } from './modules/timezone/timezone.module';
import { WorkspaceDocumentModule } from '@modules/workspace-document/workspace-document.module';
import { FileLogModule } from './modules/file-log/file-log.module';
import { MobileVersionModule } from '@modules/mobile-version/mobile-version.module';
import { ScheduleModule } from '@modules/schedule/schedule.module';
import { HttpModule } from '@nestjs/axios';
import { SchedulesMediaModule } from '@modules/schedules-media/schedules-media.module';
import { ScheduleCommentModule } from '@modules/schedule-comment/schedule-comment.module';
import { ScheduleLinksModule } from '@modules/schedule-links/schedule-links.module';
import { SystemModule } from '@modules/system/system.module';
import { ProjectScheduleModule } from '@modules/project-schedules/project-schedule.module';
import { ProjectOverviewModule } from '@modules/project-overview/project-overview.module';
import { ChangeLogModule } from '@modules/change-log/change-log.module';
import { ChangeLogMobileModule } from '@modules/change-log-mobile/change-log-mobile.module';
import { DrawingRevisionModule } from '@modules/drawing-revision/drawing-revision.module';
import { EventAssigneeModule } from '@modules/event-assignee/event-assignee.module';
import { SyncModule } from '@modules/sync/sync.module';
import { PrometheusModule } from '@modules/prometheus/prometheus.module';
import { Logger } from 'winston';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from '@modules/logger/logger.module';
import { LOGGER } from '@modules/logger/factories/logger.factory';
import { CronModule } from '@modules/cron/cron.module';
import { DrawingLinksModule } from '@modules/drawing-links/drawing-links.module';
import { DrawingLinkAttachmentModule } from '@modules/drawing-link-attachment/drawing-link-attachment.module';
import { DrawingLinkCommentModule } from '@modules/drawing-link-comment/drawing-link-comment.module';
import { BullMQModule } from '@modules/bull-mq/bullmq-module';
import { CsvToJsonService } from '@modules/conversion/csv-to-json.service';
import { BullMQConfig } from "@configs/bullmq.config";
import { BullBoardModule } from "@bull-board/nestjs";
import { ExpressAdapter } from "@bull-board/express";
import { UserSettingsModule } from '@modules/user-settings/user-settings.module';
import { BimAssetsModule } from '@modules/bim-assets/bim-assets.module';
import { WorkspaceGroupUserModule } from '@modules/workspace-group-user/workspace-group-user.module';
import { ContactsEmailModule } from '@modules/contacts-email/contacts-email.module';
import { EmailModule } from '@modules/email/email.module';
import { EmailAssetModule } from '@modules/email-asset/email-asset.module';

AdminJS.registerAdapter({  Resource, Database });

@Module({
  imports: [
    PrometheusModule,
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter // Or FastifyAdapter from `@bull-board/fastify`
    }),
    BullMQConfig,
    TypeOrmModule.forRoot({
      type: process.env.DB_DRIVER as any,
      host: process.env.DB_HOST,
      port: toNumber(process.env.DB_PORT),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      autoLoadEntities: true,
      synchronize: false, // ALWAYS HAVE THIS SET TO false FOR MIGRATION FILES TO EXECUTE
      logging: process.env.NODE_ENV === 'staging' && ['error', 'warn']
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: 'schema.gql',
      transformAutoSchemaFile: true,
      playground: process.env.GRAPHQL_PLAYGROUND === 'true',
      sortSchema: true,
      cache: 'bounded',
      persistedQueries: false,
      debug: !IS_PRODUCTION_ENV,
      cors: {
        credentials: true,
        origin: OriginConfig
      },
      formatError: error => {
        return {
          message: error.message
        };
      }
    }),
    AdminJSModule.createAdminAsync({
      useFactory: async () => ({
        adminJsOptions: {
          rootPath: '/admin',
          componentLoader: adminComponentLoader,
          resources: await adminJSResources(),
          dashboard: {
            component: AdminComponents.Dashboard,
            handler: async () => process.env
          },
          branding,
          locale
        },
        auth: {
          authenticate: adminAuthenticate,
          cookieName: 'binamin',
          cookiePassword: process.env.ADMIN_SECRET
        },
        sessionOptions
      })
    }),
    ProjectDocumentModule,
    AdminModule,
    AuthModule,
    UserModule,
    UserFcmTokenModule,
    AuditLogModule,
    ChecklistModule,
    CompanyModule,
    CompanySubscriptionModule,
    ContactModule,
    DashboardModule,
    EventModule,
    FormCategoryModule,
    IntegrationModule,
    NotificationTransactionModule,
    ProjectModule,
    ProjectGroupModule,
    ProjectCarouselModule,
    ProjectDocumentUserModule,
    ProjectDocumentCommentModule,
    ProjectInvitationModule,
    ProjectTeamModule,
    ProjectUserModule,
    RequestForSignatureModule,
    SalesOrderModule,
    SubscriptionPackageModule,
    TaskModule,
    TaskCommentModule,
    TasksAttachmentModule,
    TasksMediaModule,
    WorkspaceGroupModule,
    WorkspaceCCModule,
    WorkspaceAttachmentModule,
    WorkspacePhotoModule,
    WebSocketModule,
    TimezoneModule,
    WorkspaceDocumentModule,
    FileLogModule,
    MobileVersionModule,
    ScheduleModule,
    SchedulesMediaModule,
    ScheduleCommentModule,
    HttpModule,
    ScheduleLinksModule,
    SystemModule,
    ProjectScheduleModule,
    ProjectOverviewModule,
    ChangeLogModule,
    ChangeLogMobileModule,
    DrawingRevisionModule,
    EventAssigneeModule,
    SyncModule,
    ConfigModule.forRoot(),
    LoggerModule,
    CronModule,
    DrawingLinksModule,
    DrawingLinkAttachmentModule,
    DrawingLinkCommentModule,    
    BullMQModule,
    UserSettingsModule,
    BimAssetsModule,
    WorkspaceGroupUserModule,
    ContactsEmailModule,
    EmailModule,
    EmailAssetModule,
  ],
  providers: [CsvToJsonService]
})

export class AppModule implements OnApplicationBootstrap {
  constructor(@Inject(LOGGER) private logger: Logger) {}

  onApplicationBootstrap(): any {
    this.logger.info('Application bootstrap success!', {
      type: 'APP_BOOTSTRAP',
    });
  }
}
