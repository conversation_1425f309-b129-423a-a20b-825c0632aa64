## Get it up for development

Developers must use Docker to ensure standardize environment. This setup include the designated node & mysql server version that you need.

We use `yarn` instead of `npm`.

We use Node `v18.17 LTS`

Make sure `Docker Environments` inside `.env` is setup properly, using `env.sample` as example.

Build the container: `docker-compose up`

Ensure the docker is up by using command `docker-compose ps`.

Now, we need to use `yarn` to install required packages. Before this, remove `node_modules` folder and any package lock files (if exsits). Type `yarn install`.

Then, we wanto check on any new migration to appy onto DB. Type `yarn migrate && yarn seed`.

Finally, you can run the app using: `yarn run dev`. Your backend should be available and can test at `http://localhost:3000/api`

## Database

Run to reset database. NOTE: This will wipe off all existing data in it.
`yarn typeorm schema:drop && yarn migrate && yarn seed`

## Admin

You can use seeded admin credential (u: `<EMAIL>`, p: `1234`) also from `/database/seeds/admin.seed.ts` to login.

Login `http://localhost:3000/admin/login`

## CMD

You need to run cmd inside the docker. Use the `cmd.sh` file.

First, make the file executable: `chmod +x cmd.sh`

Enter the docker containe's' command line: `./cmd.sh`

After you are in, try with: `node -v`. You should see `v18.18.0`

## Prettier

We use [Prettier](https://prettier.io/) to format our code. Please make sure you have the [Prettier extension](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) installed in your IDE.

Run the following command to format your code before commiting.

```bash
$ yarn prettify
```

## Useful command

```bash

# to start the nest and database services together
$ docker-compose up -d

# to start only the database (optional)
$ docker-compose up -d bina-db

# to start only the nest application for Docker image testing (optional)
$ docker-compose up -d bina-be

# list the running services
$ docker-compose ps

# stop the running container
$ docker-compose down

# stop and delete all containers, networks, images, and volumes
$ docker-compose down --rmi all --volumes

# to get the logs of the service [bina-be | bina-db]
$ docker-compose logs bina-be

# switch node version if you running multiple
$ nvm use 18
```

## Troubleshoot

Reset database for issue caused by ORM auto synce (WARNING: ORM should always off!)
`yarn typeorm schema:drop && yarn migrate && yarn seed`

If it complaint on 'The engine "node" is incompatible with this module version', bypass by typing:
`yarn install --ignore-engines`.

## Others

Minimally, use the db from the docker using command `docker-compose up -d bina-db` then start the nest with `yarn run dev`. Login `http://localhost:3000/admin/login` to ensure it is up and running.

## Deploy to Staging

test 9
