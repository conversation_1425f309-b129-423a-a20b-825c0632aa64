#!/bin/bash

DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-3306}
MAX_RETRIES=${MAX_RETRIES:-50}

log() {
  echo "$(date +%Y-%m-%dT%H:%M:%S) [ENTRYPOINT]: $1"
}

wait_for_mysql() {
  log "Waiting for MySQL at $DB_HOST:$DB_PORT to be ready..."
  retries=0
  while ! nc -z $DB_HOST $DB_PORT; do
    sleep 1
    retries=$((retries + 1))
    if [ $retries -ge $MAX_RETRIES ]; then
      log "MySQL is not ready after $MAX_RETRIES retries. Exiting."
      exit 1
    fi
  done
  log "MySQL is ready."
}

run_migration() {
  log "Running typeorm migration..."
  if npm run migrate; then
    log "Typeorm migration completed successfully."
  else
    log "Error in typeorm migration."
    exit 1
  fi
}

run_seed() {
  log "Running typeorm seed..."
  if npm run seed; then
    log "Typeorm seed completed successfully."
  else
    log "Error in typeorm seed."
    exit 1
  fi
}

start_nestjs() {
  log "Starting NestJS..."
  if npm run start:prod; then
    log "NestJS started successfully."
  else
    log "Error starting NestJS."
    exit 1
  fi
}

if [ "$RUN_MIGRATION" = "true" ]; then
  wait_for_mysql
  run_migration
fi
# run_seed
start_nestjs
log "Application startup complete."