import {MigrationInterface, QueryRunner} from "typeorm";

export class addSize1743582443178 implements MigrationInterface {
    name = 'addSize1743582443178'

    public async up(queryRunner: QueryRunner): Promise<void> {       
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`size\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> { 
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`size\``);
    }

}
