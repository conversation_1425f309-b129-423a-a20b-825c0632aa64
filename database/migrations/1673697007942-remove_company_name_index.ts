import {MigrationInterface, QueryRunner} from "typeorm";

export class removeCompanyNameIndex1673697007942 implements MigrationInterface {
    name = 'removeCompanyNameIndex1673697007942'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_3dacbb3eb4f095e29372ff8e13\` ON \`companies\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_3dacbb3eb4f095e29372ff8e13\` ON \`companies\` (\`name\`)`);
    }

}
