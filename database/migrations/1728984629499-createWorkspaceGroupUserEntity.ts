import { MigrationInterface, QueryRunner } from 'typeorm';

export class createWorkspaceGroupUserEntity1728984629499 implements MigrationInterface {
  name = 'createWorkspaceGroupUserEntity1728984629499';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`workspace_group_users\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`workspaceGroupId\` int UNSIGNED NOT NULL, <PERSON><PERSON>AR<PERSON> KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_group_users\` ADD CONSTRAINT \`FK_e9a1fc61ff12ea64dcbbc5e57c4\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_group_users\` ADD CONSTRAINT \`FK_6369f0a9304d79feabeeb1f662e\` FOREIGN KEY (\`workspaceGroupId\`) REFERENCES \`workspace_groups\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`workspace_group_users\` DROP FOREIGN KEY \`FK_6369f0a9304d79feabeeb1f662e\``
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_group_users\` DROP FOREIGN KEY \`FK_e9a1fc61ff12ea64dcbbc5e57c4\``
    );
    await queryRunner.query(`DROP TABLE \`workspace_group_users\``);
  }
}
