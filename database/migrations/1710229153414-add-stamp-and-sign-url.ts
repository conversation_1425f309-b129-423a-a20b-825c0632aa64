import {MigrationInterface, QueryRunner} from "typeorm";

export class addStampAndSignUrl1710229153414 implements MigrationInterface {
    name = 'addStampAndSignUrl1710229153414'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`stampAndSignUrl\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`stampAndSignUrl\``);
    }

}
