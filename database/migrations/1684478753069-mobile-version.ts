import {MigrationInterface, QueryRunner} from "typeorm";

export class mobileVersion1684478753069 implements MigrationInterface {
    name = 'mobileVersion1684478753069'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`mobile_versions\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`platformName\` varchar(255) NOT NULL, \`buildCode\` varchar(255) NOT NULL, \`versionCode\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`mobile_versions\``);
    }

}
