import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateProjectUserRoleTypeEnum1658828827817 implements MigrationInterface {
  name = 'updateProjectUserRoleTypeEnum1658828827817';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_users\` <PERSON>AN<PERSON> \`role\` \`role\` enum ('Can Edit', 'Can View') NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`role\` \`role\` enum ('Can Edit', 'Can View') NOT NULL`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`role\` \`role\` enum ('Can edit', 'Can view') NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` CHAN<PERSON> \`role\` \`role\` enum ('Can edit', 'Can view') NOT NULL`,
    );
  }
}
