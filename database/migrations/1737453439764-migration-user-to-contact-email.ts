import { MigrationInterface, QueryRunner } from "typeorm";

export class migrationUserToContactEmail1737448745607 implements MigrationInterface {
    name = 'migrationUserToContactEmail1737448745607';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            INSERT INTO contacts_email (name, userId, email, projectId, position, company, avatarKey)
            SELECT
                COALESCE(u.name, u.email) AS name,
                u.id AS userId,
                u.email,               
                p.id AS projectId,
                u.position,
                u.companyOrigin,
                u.avatarKey
            FROM users u
            INNER JOIN project_users pu ON u.id = pu.userId
            INNER JOIN projects p ON pu.projectId = p.id;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DELETE FROM contacts_email
            WHERE userId IN (SELECT id FROM users);
        `);
    }
}