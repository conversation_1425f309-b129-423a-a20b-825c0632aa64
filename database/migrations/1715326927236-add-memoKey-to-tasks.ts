import { MigrationInterface, QueryRunner } from 'typeorm';

export class addMemoKeyToTasks1715326927236 implements MigrationInterface {
  name = 'addMemoKeyToTasks1715326927236';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\`
      ADD \`memoKey\` text NULL,
      ADD \`previewMemoKey\` text NULL`);
    // get all tasks and update obsKey with cleaned fileUrl and videoThumbnailKey with cleaned videoThumbnail
    await queryRunner.query(`UPDATE tasks
      SET memoKey = REGEXP_REPLACE(memoUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      previewMemoKey = REGEXP_REPLACE(previewMemoUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\`
    DROP COLUMN \`memoKey\`,
    DROP COLUMN \`previewMemoKey\``);
  }
}
