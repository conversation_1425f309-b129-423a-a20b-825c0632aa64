import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendOnProjectDocumentTable1658895241905 implements MigrationInterface {
  name = 'ammendOnProjectDocumentTable1658895241905';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_a373b7fe40bbc055441213e5ca9\``,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`projectDocumentFolderId\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`projectDocumentId\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`fileSystemType\` enum ('Document', 'Folder') NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`nsleft\` int NOT NULL DEFAULT '1'`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`nsright\` int NOT NULL DEFAULT '2'`);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileUrl\` \`fileUrl\` varchar(255) NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`type\` \`type\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`status\` enum ('Pending', 'Approved') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('Project Document', 'Work Programme', 'Correspondence', 'All Form', 'Standard Form', 'Photo', '2D Drawings', 'BIM Drawings') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_4f2f3042dc38d34833a6b26833f\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`DROP TABLE \`project_document_folders\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_4f2f3042dc38d34833a6b26833f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('Project Document', 'Work Programme', 'Correspondence') NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`status\` varchar(255) NULL DEFAULT 'Pending'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`type\` \`type\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileUrl\` \`fileUrl\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`nsright\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`nsleft\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileSystemType\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`projectDocumentId\``);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`projectDocumentFolderId\` int UNSIGNED NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_a373b7fe40bbc055441213e5ca9\` FOREIGN KEY (\`projectDocumentFolderId\`) REFERENCES \`project_document_folders\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
