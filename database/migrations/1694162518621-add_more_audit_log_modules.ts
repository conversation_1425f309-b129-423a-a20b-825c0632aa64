import {MigrationInterface, QueryRunner} from "typeorm";

export class addMoreAuditLogModules1694162518621 implements MigrationInterface {
    name = 'addMoreAuditLogModules1694162518621'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member') NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }
}
