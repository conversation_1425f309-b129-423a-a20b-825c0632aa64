import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendOnUserFcmToken1667184726028 implements MigrationInterface {
  name = 'ammendOnUserFcmToken1667184726028';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`user_fcm_tokens\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`fcmToken\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`fcmToken\``);
    await queryRunner.query(
      `ALTER TABLE \`user_fcm_tokens\` ADD CONSTRAINT \`FK_9e490d67ebeccb50ad20655d3ff\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_fcm_tokens\` DROP FOREIGN KEY \`FK_9e490d67ebeccb50ad20655d3ff\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`fcmToken\` varchar(255) NULL`);
    await queryRunner.query(`DROP TABLE \`user_fcm_tokens\``);
  }
}
