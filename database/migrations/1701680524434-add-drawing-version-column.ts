import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawingVersionColumn1701680524434 implements MigrationInterface {
    name = 'addDrawingVersionColumn1701680524434'

    public async up(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD \`fileName\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD \`versionName\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD \`notes\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD \`xfdf\` longtext NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP COLUMN \`xfdf\``);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP COLUMN \`notes\``);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP COLUMN \`versionName\``);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP COLUMN \`fileName\``);
       
    }

}
