import { MigrationInterface, QueryRunner } from "typeorm";
import randomColor from "randomcolor";  // Import randomcolor library

export class addRandomColor1741248613640 implements MigrationInterface {
    name = 'addRandomColor1741248613640'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add the color column to the users table
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`color\` text NULL`);

        // Fetch all users and update them with dark random colors
        const users = await queryRunner.query(`SELECT * FROM \`users\``);

        // Loop through users and set a random dark color
        for (const user of users) {
            const darkColor = randomColor({ luminosity: 'dark' });
            await queryRunner.query(
                `UPDATE \`users\` SET \`color\` = ? WHERE \`id\` = ?`,
                [darkColor, user.id]  // Update the color for each user
            );
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the color column from the users table
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`color\``);
    }
}
