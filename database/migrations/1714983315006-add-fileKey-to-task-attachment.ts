import {MigrationInterface, QueryRunner} from "typeorm";

export class addFileKeyToTaskAttachment1714983315006 implements MigrationInterface {
  name = 'addFileKeyToTaskAttachment1714983315006'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD \`fileKey\` text NULL`);

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE tasks_attachments
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP COLUMN \`fileKey\``);
  }
}
