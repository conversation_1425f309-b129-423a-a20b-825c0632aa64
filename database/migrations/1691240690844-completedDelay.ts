import {MigrationInterface, QueryRunner} from "typeorm";

export class completedDelay1691240690844 implements MigrationInterface {
    name = 'completedDelay1691240690844'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`completedDelay\` int NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`completedDelay\``);
    }
}
