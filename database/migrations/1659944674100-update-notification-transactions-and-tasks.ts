import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateNotificationTransactionsAndTasks1659944674100 implements MigrationInterface {
  name = 'updateNotificationTransactionsAndTasks1659944674100';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`content\``);
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` ADD \`actorId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` ADD \`actionName\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` ADD \`actionType\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`isSentBeforeOneDay\` tinyint NOT NULL DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`isSentBeforeOneHour\` tinyint NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`isSentBeforeOneHour\``);
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`isSentBeforeOneDay\``);
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`actionType\``);
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`actionName\``);
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`actorId\``);
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` ADD \`content\` varchar(255) NOT NULL`,
    );
  }
}
