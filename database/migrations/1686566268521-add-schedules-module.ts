import {MigrationInterface, QueryRunner} from "typeorm";

export class addSchedulesModule1686566268521 implements MigrationInterface {
    name = 'addSchedulesModule1686566268521'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedules\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ownerId\` int UNSIGNED NOT NULL, \`projectId\` int UNSIGNED NOT NULL, \`suid\` text NOT NULL, \`taskMode\` text NOT NULL, \`name\` text NOT NULL, \`baselineDuration\` text NOT NULL, \`baselineStart\` text NOT NULL, \`actualStart\` text NOT NULL, \`baselineFinish\` text NOT NULL, \`actualFinish\` text NOT NULL, \`outlineLevel\` text NOT NULL, \`outlineNumber\` text NOT NULL, \`percentComplete\` text NOT NULL, \`percentWorkComplete\` text NOT NULL, \`predecessors\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD CONSTRAINT \`FK_ec4296ed765dec016ca6cb0b2eb\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD CONSTRAINT \`FK_703d0851258b726cda954c309ee\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP FOREIGN KEY \`FK_703d0851258b726cda954c309ee\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP FOREIGN KEY \`FK_ec4296ed765dec016ca6cb0b2eb\``);
        await queryRunner.query(`DROP TABLE \`schedules\``);
    }

}
