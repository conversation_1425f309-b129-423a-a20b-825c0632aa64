import {MigrationInterface, QueryRunner} from "typeorm";

export class removeIsDraft1736414849493 implements MigrationInterface {
    name = 'removeIsDraft1736414849493'

    public async up(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`isDraft\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`isDraft\` tinyint NOT NULL DEFAULT '1'`);
       
    }

}
