import {MigrationInterface, QueryRunner} from "typeorm";

export class CreateProductGroupsTable1667540427343 implements MigrationInterface {
    name = 'CreateProductGroupsTable1667540427343'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`project_groups\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`groupId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_groups\` ADD CONSTRAINT \`FK_de7c995a9f8852aa1a847b57923\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_groups\` DROP FOREIGN KEY \`FK_de7c995a9f8852aa1a847b57923\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`groupId\``);
        await queryRunner.query(`DROP TABLE \`project_groups\``);
    }

}
