import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateNotificationTransaction1659675220335 implements MigrationInterface {
  name = 'updateNotificationTransaction1659675220335';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`thumbnail\` \`thumbnail\` text NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`read\` \`read\` tinyint NOT NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`type\` \`type\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`type\` \`type\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`read\` \`read\` tinyint NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` CHANGE \`thumbnail\` \`thumbnail\` text NOT NULL`,
    );
  }
}
