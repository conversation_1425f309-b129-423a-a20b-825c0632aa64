import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsCritical1706709748524 implements MigrationInterface {
    name = 'addIsCritical1706709748524'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`isCritical\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`isCritical\``);
    }

}
