import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeFieldNameSignatureXfdfToXfdf1661412097325 implements MigrationInterface {
  name = 'changeFieldNameSignatureXfdfToXfdf1661412097325';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`signatureXfdf\` \`xfdf\` longtext NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`xfdf\` \`signatureXfdf\` longtext NULL`,
    );
  }
}
