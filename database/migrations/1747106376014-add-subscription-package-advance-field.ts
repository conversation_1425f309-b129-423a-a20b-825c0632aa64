import {MigrationInterface, QueryRunner} from "typeorm";

export class addSubscriptionPackageAdvanceField1747106376014 implements MigrationInterface {
    name = 'addSubscriptionPackageAdvanceField1747106376014'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`storage\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`isProjectBased\` tinyint NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`features\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`nonFeatures\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` CHANGE \`allowEmailCorrespondence\` \`allowEmailCorrespondence\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` CHANGE \`isPublic\` \`isPublic\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` CHANGE \`isPublic\` \`isPublic\` tinyint(1) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` CHANGE \`allowEmailCorrespondence\` \`allowEmailCorrespondence\` tinyint NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`nonFeatures\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`features\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`isProjectBased\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`storage\``);
    }

}
