import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateEnumValue1660530951635 implements MigrationInterface {
  name = 'updateEnumValue1660530951635';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_users\` CHANGE \`role\` \`role\` enum ('CanEdit', 'CanView', 'ProjectOwner', 'CloudCoordinator') NOT NULL DEFAULT 'CanView'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('InProgress', 'Completed', 'Overdue') NOT NULL DEFAULT 'InProgress'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NULL DEFAULT 'Pending'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`role\` \`role\` enum ('CanEdit', 'CanView', 'ProjectOwner', 'CloudCoordinator') NOT NULL DEFAULT 'CanView'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`projects\` CHANGE \`status\` \`status\` enum ('InProgress', 'Completed', 'Overdue') NOT NULL DEFAULT 'InProgress'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment') NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'Task Comment') NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`projects\` CHANGE \`status\` \`status\` enum ('In Progress', 'Completed', 'Overdue') NOT NULL DEFAULT 'In Progress'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`role\` \`role\` enum ('Can Edit', 'Can View') NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('Project Document', 'Work Programme', 'Correspondence', 'All Form', 'Standard Form', 'Photo', '2D Drawings', 'BIM Drawings') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('In Progress', 'Completed', 'Overdue') NOT NULL DEFAULT 'In Progress'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` CHANGE \`role\` \`role\` enum ('Can Edit', 'Can View') NOT NULL`,
    );
  }
}
