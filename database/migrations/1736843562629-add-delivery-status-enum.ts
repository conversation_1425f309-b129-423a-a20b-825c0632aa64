import {MigrationInterface, QueryRunner} from "typeorm";

export class addDeliveryStatusEnum1736843562629 implements MigrationInterface {
    name = 'addDeliveryStatusEnum1736843562629'

    public async up(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`deliveryStatus\``);
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`deliveryStatus\` enum ('Accepted', 'Delivered', 'Clicks', 'Spam Complaints', 'Permanent Failures', 'Sending', 'Temporary Failures') NULL DEFAULT 'Sending'`);      
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`deliveryStatus\``);
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`deliveryStatus\` varchar(255) NULL`);
       
    }

}
