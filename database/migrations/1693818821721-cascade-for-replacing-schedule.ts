import {MigrationInterface, QueryRunner} from "typeorm";

export class cascadeForReplacingSchedule1693818821721 implements MigrationInterface {
    name = 'cascadeForReplacingSchedule1693818821721'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP FOREIGN KEY \`FK_b363aee24027f76bc66c84903db\``);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP FOREIGN KEY \`FK_c40cca22bdd0b8b1ff07abe240d\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`workspace_document\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'GanttChart') NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD CONSTRAINT \`FK_b363aee24027f76bc66c84903db\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD CONSTRAINT \`FK_c40cca22bdd0b8b1ff07abe240d\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP FOREIGN KEY \`FK_c40cca22bdd0b8b1ff07abe240d\``);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP FOREIGN KEY \`FK_b363aee24027f76bc66c84903db\``);
        await queryRunner.query(`ALTER TABLE \`workspace_document\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD CONSTRAINT \`FK_c40cca22bdd0b8b1ff07abe240d\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD CONSTRAINT \`FK_b363aee24027f76bc66c84903db\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
