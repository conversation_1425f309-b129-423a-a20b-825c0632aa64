import {MigrationInterface, QueryRunner} from "typeorm";

export class addImageURLOnTask1687316680375 implements MigrationInterface {
    name = 'addImageURLOnTask1687316680375'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`imageURL\` text NULL`);
        // await queryRunner.query(`ALTER TABLE \`workspace_document\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'GanttChart') NULL`);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        // await queryRunner.query(`ALTER TABLE \`workspace_document\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph') NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`imageURL\``);
    }

}
