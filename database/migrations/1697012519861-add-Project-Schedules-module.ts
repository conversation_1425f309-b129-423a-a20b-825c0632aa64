import {MigrationInterface, QueryRunner} from "typeorm";

export class addProjectSchedulesModule1697012519861 implements MigrationInterface {
    name = 'addProjectSchedulesModule1697012519861'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`project_schedules\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`name\` varchar(255) NOT NULL, \`description\` text NULL, \`isScheduleTracked\` tinyint NOT NULL DEFAULT 0, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` ADD \`projectScheduleId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`projectScheduleId\` int UNSIGNED NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`projectScheduleId\``);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` DROP COLUMN \`projectScheduleId\``);
        await queryRunner.query(`DROP TABLE \`project_schedules\``);
    }

}
