import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateProjectDocumentAndProjectDocumentFolder1658468920917 implements MigrationInterface {
  name = 'updateProjectDocumentAndProjectDocumentFolder1658468920917';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` ADD \`addedBy\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD \`formCategoryId\` int UNSIGNED NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`addedBy\` int UNSIGNED NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`formCategoryId\` int UNSIGNED NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`fileSize\` varchar(255) NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`status\` varchar(255) NULL DEFAULT 'Pending'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD CONSTRAINT \`FK_13ecb8d925ef0483f507ba94750\` FOREIGN KEY (\`addedBy\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD CONSTRAINT \`FK_b97d26aeadda5d3a86efbe22d66\` FOREIGN KEY (\`formCategoryId\`) REFERENCES \`form_categories\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_2f049b7930f2e8db6ea737c93fe\` FOREIGN KEY (\`addedBy\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_a65a8d25716d350cc01b29ffb13\` FOREIGN KEY (\`formCategoryId\`) REFERENCES \`form_categories\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_a65a8d25716d350cc01b29ffb13\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_2f049b7930f2e8db6ea737c93fe\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` DROP FOREIGN KEY \`FK_b97d26aeadda5d3a86efbe22d66\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` DROP FOREIGN KEY \`FK_13ecb8d925ef0483f507ba94750\``,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`status\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`formCategoryId\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`addedBy\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`formCategoryId\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`addedBy\``);
  }
}
