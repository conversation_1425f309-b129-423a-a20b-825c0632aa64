import { MigrationInterface, QueryRunner } from 'typeorm';

export class addEmailAssets1736731312542 implements MigrationInterface {
  name = 'addEmailAssets1736731312542';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`email_assets\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`emailId\` int UNSIGNED NOT NULL, \`assetKey\` varchar(255) NOT NULL, \`assetType\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `ALTER TABLE \`email_assets\` ADD CONSTRAINT \`FK_882e3dee7834847053d0d018892\` FOREIGN KEY (\`emailId\`) REFERENCES \`emails\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP FOREIGN KEY \`FK_882e3dee7834847053d0d018892\``);
    await queryRunner.query(`DROP TABLE \`email_assets\``);
    await queryRunner.query(
      `ALTER TABLE \`email_account_to_email\` ADD CONSTRAINT \`FK_662891dacba8852c144d5e26d56\` FOREIGN KEY (\`emailAccountsId\`) REFERENCES \`contacts_email\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }
}
