import {MigrationInterface, QueryRunner} from "typeorm";

export class convertLog<PERSON>ontentColumn1678133052273 implements MigrationInterface {
    name = 'convertLogContentColumn1678133052273'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`content\``);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`content\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`content\``);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`content\` varchar(255) CHARACTER SET "utf8" COLLATE "utf8_general_ci" NULL DEFAULT ''`);
    }

}
