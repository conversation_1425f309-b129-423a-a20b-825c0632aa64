import {MigrationInterface, QueryRunner} from "typeorm";

export class makeDeliveryStatusNullable1736478324516 implements MigrationInterface {
    name = 'makeDeliveryStatusNullable1736478324516'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`deliveryStatus\` \`deliveryStatus\` varchar(255) NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`deliveryStatus\` \`deliveryStatus\` varchar(255) NOT NULL`);
        
    }

}
