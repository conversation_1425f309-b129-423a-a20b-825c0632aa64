import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFileKeyToDrawingRevisions1715331367357 implements MigrationInterface {
  name = 'addFileKeyToDrawingRevisions1715331367357';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`drawing_revisions\`
              ADD \`fileKey\` text NULL`);
    // get all drawing_revisions and update obsKey with cleaned fileUrl and videoThumbnailKey with cleaned videoThumbnail
    await queryRunner.query(`UPDATE drawing_revisions
              SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
              updatedAt = updatedAt
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`drawing_revisions\`
                DROP COLUMN \`fileKey\``);
  }
}
