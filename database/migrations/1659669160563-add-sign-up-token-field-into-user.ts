import { MigrationInterface, QueryRunner } from 'typeorm';

export class addSignUpTokenFieldIntoUser1659669160563 implements MigrationInterface {
  name = 'addSignUpTokenFieldIntoUser1659669160563';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`signUpToken\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`signUpToken\``);
  }
}
