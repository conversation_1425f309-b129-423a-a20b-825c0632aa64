import {MigrationInterface, QueryRunner} from "typeorm";

export class addedSupportForTaskMediaAuditlogs1677360291100 implements MigrationInterface {
    name = 'addedSupportForTaskMediaAuditlogs1677360291100'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` ADD \`userId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` ADD CONSTRAINT \`FK_92c5c87a67ff17a927d18b8d8c3\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` DROP FOREIGN KEY \`FK_92c5c87a67ff17a927d18b8d8c3\``);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` DROP COLUMN \`userId\``);
    }

}
