import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertDefaultSubscriptionPackages1747106376015
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const pkgs = [
      {
        title: 'Basic',
        description: 'Basic subscription package with essential features',
        amount: 129,
        availableDuration: 30,         
        totalProjects: 10,
        totalUsers: 9999,
        storage: 50,
        features: [
          'Document management & access control',
          'Correspondence tracking',
          'Drawing version control',
          'Site photo & video',
          'Member Management',
        ],
        nonFeatures: [
          'Autodesk integration',
          'CPM integration',
          'Collaboration Workflow',
        ],
        isProjectBased: false,
        isPublic: true,
      },
      {
        title: 'Advance',
        description: 'Advanced subscription package with additional integrations',
        amount: 169,
        availableDuration: 30,         
        totalProjects: 10,
        totalUsers: 9999,
        storage: 100,
        features: [
          'Document management & access control',
          'Correspondence tracking',
          'Drawing version control',
          'Site photo & video',
          'Member Management',
          'Autodesk integration',
          'CPM integration',
        ],
        nonFeatures: ['Collaboration Workflow'],
        isProjectBased: false,
        isPublic: true,
      },
      {
        title: 'Business',
        description: 'Complete business solution with all features',
        amount: 239,
        availableDuration: 30,         
        totalProjects: 10,
        totalUsers: 9999,
        storage: 250,
        features: [
          'Document management & access control',
          'Correspondence tracking',
          'Drawing version control',
          'Site photo & video',
          'Member Management',
          'Autodesk integration',
          'CPM integration',
          'Collaboration Workflow',
        ],
        nonFeatures: [],
        isProjectBased: false,
        isPublic: true,
      },
      {
        title: 'Enterprise',
        description: 'Enterprise‑grade solution with unlimited capabilities',
        amount: 9999,                  
        availableDuration: 30,         
        totalProjects: 10,
        totalUsers: 9999,
        storage: null,                 
        features: [
          'Unlimited user',
          'Task Management',
          'Document management & access control',
          'Correspondence tracking',
          'Drawing version control & Annotation',
          'Site photo & video',
          'Member Management',
          'Autodesk integration',
          'CPM integration & Delay Detection',
          'Collaboration Workflow',
        ],
        nonFeatures: [],
        isProjectBased: true,
        isPublic: true,
      },
    ];

    for (const pkg of pkgs) {
      await queryRunner.manager.insert('subscription_packages', pkg);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.manager.delete('subscription_packages', {
      title: ['Basic', 'Advance', 'Business', 'Enterprise'],
    });
  }
}
