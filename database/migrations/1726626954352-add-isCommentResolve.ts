import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsCommentResolve1726626954352 implements MigrationInterface {
    name = 'addIsCommentResolve1726626954352'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`isCommentResolve\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`isCommentResolve\``);
    }

}
