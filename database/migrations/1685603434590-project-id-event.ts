import {MigrationInterface, QueryRunner} from "typeorm";

export class projectIdEvent1685603434590 implements MigrationInterface {
    name = 'projectIdEvent1685603434590'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`events\` ADD \`projectId\` int UNSIGNED NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`projectId\``);
    }

}
