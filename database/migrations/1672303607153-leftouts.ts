import {MigrationInterface, QueryRunner} from "typeorm";

export class leftouts1672303607153 implements MigrationInterface {
    name = 'leftouts1672303607153'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`fileUrlProgress\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`fileUrlProgress\``);
    }

}
