import { MigrationInterface, QueryRunner } from 'typeorm';

export class uniqueForSignByIdBasedOnProjectDocument1661145376435 implements MigrationInterface {
  name = 'uniqueForSignByIdBasedOnProjectDocument1661145376435';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\` (\`projectDocumentId\`, \`signById\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\``);
  }
}
