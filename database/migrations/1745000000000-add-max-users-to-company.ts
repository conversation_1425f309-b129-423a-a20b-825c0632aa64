import { MigrationInterface, QueryRunner } from 'typeorm';

export class addMaxUsersToCompany1745000000000 implements MigrationInterface {
  name = 'addMaxUsersToCompany1745000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`companies\` ADD \`maxUsers\` int NOT NULL DEFAULT 9999`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`companies\` DROP COLUMN \`maxUsers\``);
  }
}
