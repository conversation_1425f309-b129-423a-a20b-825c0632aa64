import {MigrationInterface, QueryRunner} from "typeorm";

export class bimAssetTableGenerate1728464163888 implements MigrationInterface {
    name = 'bimAssetTableGenerate1728464163888'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`bim_asset\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`dbId\` int UNSIGNED NOT NULL, \`assetKey\` text NULL, \`assetValue\` text NULL, \`projectDocumentId\` int UNSIGNED NULL, \`projectId\` int UNSIGNED NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`bim_asset\` ADD CONSTRAINT \`FK_db14cdae68ba7a60ea5958ce6a9\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`bim_asset\` ADD CONSTRAINT \`FK_eb0fc37953116d0e957baf59730\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`bim_asset\` DROP FOREIGN KEY \`FK_eb0fc37953116d0e957baf59730\``);
        await queryRunner.query(`ALTER TABLE \`bim_asset\` DROP FOREIGN KEY \`FK_db14cdae68ba7a60ea5958ce6a9\``);
        await queryRunner.query(`DROP TABLE \`bim_asset\``);
    }

}
