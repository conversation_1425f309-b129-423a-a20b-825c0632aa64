import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateDefaultRequestForSignatureStatus1659928088866 implements MigrationInterface {
  name = 'updateDefaultRequestForSignatureStatus1659928088866';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NOT NULL DEFAULT 'Pending'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` <PERSON>ANGE \`status\` \`status\` enum ('Pending', 'Approved') NOT NULL`,
    );
  }
}
