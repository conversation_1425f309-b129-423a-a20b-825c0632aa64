import {MigrationInterface, QueryRunner} from "typeorm";

export class makeFileUrlNullable1744791263580 implements MigrationInterface {
    name = 'makeFileUrlNullable1744791263580'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_carousels\` CHANGE \`fileUrl\` \`fileUrl\` text NULL`);       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_carousels\` CHANGE \`fileUrl\` \`fileUrl\` text CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NOT NULL`);       
    }

}
