import {MigrationInterface, QueryRunner} from "typeorm";

export class addTypeColumnInSchedules1691252849994 implements MigrationInterface {
    name = 'addTypeColumnInSchedules1691252849994'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`type\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`type\``);
    }

}
