import { MigrationInterface, QueryRunner } from 'typeorm';

export class fixProjectUserAddedBy1658386890192 implements MigrationInterface {
  name = 'fixProjectUserAddedBy1658386890192';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX \`FK_53864e10e2263359e4b895133c3\` ON \`project_invitations\``);
    await queryRunner.query(
      `ALTER TABLE \`project_users\` ADD CONSTRAINT \`FK_8b76e02d6959da9f8d1040ae220\` FOREIGN KEY (\`addedBy\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_users\` DROP FOREIGN KEY \`FK_8b76e02d6959da9f8d1040ae220\``,
    );
    await queryRunner.query(
      `CREATE INDEX \`FK_53864e10e2263359e4b895133c3\` ON \`project_invitations\` (\`companyId\`)`,
    );
  }
}
