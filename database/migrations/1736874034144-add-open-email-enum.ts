import {MigrationInterface, QueryRunner} from "typeorm";

export class addOpenEmailEnum1736874034144 implements MigrationInterface {
    name = 'addOpenEmailEnum1736874034144'

    public async up(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`deliveryStatus\` \`deliveryStatus\` enum ('Accepted', 'Delivered', 'Clicked', 'Spam Complaints', 'Permanent Failures', 'Sending', 'Opened', 'Temporary Failures') NULL DEFAULT 'Sending'`);
      
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`deliveryStatus\` \`deliveryStatus\` enum ('Accepted', 'Delivered', 'Clicks', 'Spam Complaints', 'Permanent Failures', 'Sending', 'Temporary Failures') NULL DEFAULT 'Sending'`);
        
    }

}
