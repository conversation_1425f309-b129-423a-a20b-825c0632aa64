import {MigrationInterface, QueryRunner} from "typeorm";

export class uniqueFields1673334276839 implements MigrationInterface {
    name = 'uniqueFields1673334276839'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD UNIQUE INDEX \`IDX_27b521ae82391c7d690e51758c\` (\`title\`)`);
        await queryRunner.query(`ALTER TABLE \`form_categories\` ADD UNIQUE INDEX \`IDX_3dca5f522ffcfd6d3ead358fa9\` (\`name\`)`);
        await queryRunner.query(`ALTER TABLE \`companies\` ADD UNIQUE INDEX \`IDX_3dacbb3eb4f095e29372ff8e13\` (\`name\`)`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD UNIQUE INDEX \`IDX_97672ac88f789774dd47f7c8be\` (\`email\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP INDEX \`IDX_97672ac88f789774dd47f7c8be\``);
        await queryRunner.query(`ALTER TABLE \`companies\` DROP INDEX \`IDX_3dacbb3eb4f095e29372ff8e13\``);
        await queryRunner.query(`ALTER TABLE \`form_categories\` DROP INDEX \`IDX_3dca5f522ffcfd6d3ead358fa9\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP INDEX \`IDX_27b521ae82391c7d690e51758c\``);
    }

}
