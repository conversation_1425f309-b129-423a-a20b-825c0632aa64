import {MigrationInterface, QueryRunner} from "typeorm";

export class addEventAssignee1704689432458 implements MigrationInterface {
    name = 'addEventAssignee1704689432458'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`event_assignee\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`eventId\` int UNSIGNED NOT NULL, \`assigneeId\` int UNSIGNED NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`event_assignee\` ADD CONSTRAINT \`FK_01caa1cdc9a44821fd07ca9c908\` FOREIGN KEY (\`eventId\`) REFERENCES \`events\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`event_assignee\` ADD CONSTRAINT \`FK_e1910c2fc5c1b51906edc758991\` FOREIGN KEY (\`assigneeId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`event_assignee\` DROP FOREIGN KEY \`FK_e1910c2fc5c1b51906edc758991\``);
        await queryRunner.query(`ALTER TABLE \`event_assignee\` DROP FOREIGN KEY \`FK_01caa1cdc9a44821fd07ca9c908\``);
        await queryRunner.query(`DROP TABLE \`event_assignee\``);
    }

}
