import {MigrationInterface, QueryRunner} from "typeorm";

export class addShareActionToAuditlog1694544277919 implements MigrationInterface {
    name = 'addShareActionToAuditlog1694544277919'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined', 'Shared') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }

}
