import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateFieldsNullable1657784973377 implements MigrationInterface {
  name = 'updateFieldsNullable1657784973377';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_invitations\` ADD \`role\` varchar(255) NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` DROP FOREIGN KEY \`FK_be77588a6727c9a27075b590048\``,
    );
    await queryRunner.query(`ALTER TABLE \`task_comments\` CHANGE \`userId\` \`userId\` int UNSIGNED NULL`);
    await queryRunner.query(`ALTER TABLE \`project_users\` CHANGE \`addedBy\` \`addedBy\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` ADD CONSTRAINT \`FK_be77588a6727c9a27075b590048\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` DROP FOREIGN KEY \`FK_be77588a6727c9a27075b590048\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` CHANGE \`addedBy\` \`addedBy\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` CHANGE \`userId\` \`userId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` ADD CONSTRAINT \`FK_be77588a6727c9a27075b590048\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`role\``);
  }
}
