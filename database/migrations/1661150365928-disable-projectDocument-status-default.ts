import { MigrationInterface, QueryRunner } from 'typeorm';

export class disableProjectDocumentStatusDefault1661150365928 implements MigrationInterface {
  name = 'disableProjectDocumentStatusDefault1661150365928';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NULL DEFAULT 'Pending'`,
    );
  }
}
