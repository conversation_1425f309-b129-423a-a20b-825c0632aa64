import {MigrationInterface, QueryRunner} from "typeorm";

export class extendSalesOrder1679934490012 implements MigrationInterface {
    name = 'extendSalesOrder1679934490012'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`paymentStatus\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`transactionId\` text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`transactionId\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`paymentStatus\``);
    }

}
