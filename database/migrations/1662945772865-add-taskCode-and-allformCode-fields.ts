import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTaskCodeAndAllformCodeFields1662945772865 implements MigrationInterface {
  name = 'addTaskCodeAndAllformCodeFields1662945772865';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`taskCode\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`allFormCode\` varchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`allFormCode\``);
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`taskCode\``);
  }
}
