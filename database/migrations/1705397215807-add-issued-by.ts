import {MigrationInterface, QueryRunner} from "typeorm";

export class addIssuedBy1705397215807 implements MigrationInterface {
    name = 'addIssuedBy1705397215807'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`issuedById\` int UNSIGNED NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`issuedById\``);
    }

}
