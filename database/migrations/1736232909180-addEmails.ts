import { MigrationInterface, QueryRunner } from 'typeorm';

export class addEmails1736232909180 implements MigrationInterface {
  name = 'addEmails1736232909180';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`emails\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`creatorId\` int UNSIGNED NOT NULL, \`recipientId\` int UNSIGNED NULL, \`senderId\` int UNSIGNED NULL, \`replyToId\` int UNSIGNED NULL, \`subject\` varchar(255) NOT NULL, \`body\` text NULL, \`deliveryStatus\` varchar(255) NOT NULL, \`openedAt\` datetime NULL, \`receivedAt\` datetime NULL, \`sentAt\` datetime NULL, \`privatedAt\` datetime NULL, \`reference\` varchar(255) NOT NULL, \`rawResponse\` text NULL, \`isDraft\` tinyint NOT NULL DEFAULT 1, \`mpath\` varchar(255) NULL DEFAULT '', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `CREATE TABLE \`contact_email_to_email\` (\`emailsId\` int UNSIGNED NOT NULL, \`emailAccountsId\` int UNSIGNED NOT NULL, INDEX \`IDX_ebd68978dfbe10b62d8054faad\` (\`emailsId\`), INDEX \`IDX_662891dacba8852c144d5e26d5\` (\`emailAccountsId\`), PRIMARY KEY (\`emailsId\`, \`emailAccountsId\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(
      `ALTER TABLE \`emails\` ADD CONSTRAINT \`FK_5b484dc37f2a36015d602825349\` FOREIGN KEY (\`replyToId\`) REFERENCES \`emails\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE \`emails\` ADD CONSTRAINT \`FK_7346dd09401c768d4a64d420b7a\` FOREIGN KEY (\`senderId\`) REFERENCES \`contacts_email\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_email_to_email\` ADD CONSTRAINT \`FK_ebd68978dfbe10b62d8054faad2\` FOREIGN KEY (\`emailsId\`) REFERENCES \`emails\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_email_to_email\` ADD CONSTRAINT \`FK_662891dacba8852c144d5e26d56\` FOREIGN KEY (\`emailAccountsId\`) REFERENCES \`contacts_email\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`contact_email_to_email\` DROP FOREIGN KEY \`FK_662891dacba8852c144d5e26d56\``
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_email_to_email\` DROP FOREIGN KEY \`FK_ebd68978dfbe10b62d8054faad2\``
    );
    await queryRunner.query(`ALTER TABLE \`emails\` DROP FOREIGN KEY \`FK_7346dd09401c768d4a64d420b7a\``);
    await queryRunner.query(`ALTER TABLE \`emails\` DROP FOREIGN KEY \`FK_5b484dc37f2a36015d602825349\``);
    await queryRunner.query(`DROP INDEX \`IDX_662891dacba8852c144d5e26d5\` ON \`contact_email_to_email\``);
    await queryRunner.query(`DROP INDEX \`IDX_ebd68978dfbe10b62d8054faad\` ON \`contact_email_to_email\``);
    await queryRunner.query(`DROP TABLE \`contact_email_to_email\``);
    await queryRunner.query(`DROP TABLE \`emails\``);
  }
}
