import { MigrationInterface, QueryRunner } from 'typeorm';

export class addPermDeleteFieldToTask1701661959525 implements MigrationInterface {
  name = 'addPermDeleteFieldToTask1701661959525';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`permanentlyDeleted\` tinyint NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`permanentlyDeleted\``);
  }
}
