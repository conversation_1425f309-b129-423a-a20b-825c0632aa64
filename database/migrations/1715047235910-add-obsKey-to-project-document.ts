import { MigrationInterface, QueryRunner } from 'typeorm';

export class addObsKeyToProjectDocument1715047235910 implements MigrationInterface {
  name = 'addObsKeyToProjectDocument1715047235910';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\`
      ADD \`videoThumbnailKey\` text NULL,
      ADD \`obsKey\` text NULL`);
    // get all project_documents and update obsKey with cleaned fileUrl and videoThumbnailKey with cleaned videoThumbnail
    await queryRunner.query(`UPDATE project_documents
      SET obsKey = REGEXP_REPLACE(obsUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      videoThumbnailKey = REGEXP_REPLACE(videoThumbnail, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\`
        DROP COLUMN \`obsKey\`,
        DROP COLUMN \`videoThumbnailKey\``);
  }
}
