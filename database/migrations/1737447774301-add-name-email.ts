import {MigrationInterface, QueryRunner} from "typeorm";

export class addNameEmail1737447774301 implements MigrationInterface {
    name = 'addNameEmail1737447774301'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`isActive\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`address\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`email\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`name\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`company\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`position\` varchar(255) NULL`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`address\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`isActive\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`name\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`email\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`position\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`company\``);
       
    }

}
