import {MigrationInterface, QueryRunner} from "typeorm";

export class addFinancialScurve1684914903929 implements MigrationInterface {
    name = 'addFinancialScurve1684914903929'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`fileUrlProgressFinance\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`fileUrlProgressFinance\``);
    }

}
