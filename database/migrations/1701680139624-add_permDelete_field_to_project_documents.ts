import { MigrationInterface, QueryRunner } from 'typeorm';

export class addPermDeleteFieldToProjectDocuments1701680139624 implements MigrationInterface {
  name = 'addPermDeleteFieldToProjectDocuments1701680139624';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`permanentlyDeleted\` tinyint NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`permanentlyDeleted\``);
  }
}
