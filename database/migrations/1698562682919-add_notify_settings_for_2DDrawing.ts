import {MigrationInterface, QueryRunner} from "typeorm";

export class addNotifySettingsFor2DDrawing1698562682919 implements MigrationInterface {
    name = 'addNotifySettingsFor2DDrawing1698562682919'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`isNotify2DUploaded\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`isNotify2DUploaded\``);
    }

}
