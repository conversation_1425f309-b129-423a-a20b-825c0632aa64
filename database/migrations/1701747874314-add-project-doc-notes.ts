import {MigrationInterface, QueryRunner} from "typeorm";

export class addProjectDocNotes1701747874314 implements MigrationInterface {
    name = 'addProjectDocNotes1701747874314'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`notes\` varchar(255) NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`notes\``);
       
    }

}
