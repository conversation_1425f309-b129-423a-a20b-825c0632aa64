import {MigrationInterface, QueryRunner} from "typeorm";

export class addDefaultVal1727664887370 implements MigrationInterface {
    name = 'addDefaultVal1727664887370'

    public async up(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` CHANGE \`type\` \`type\` text NULL `);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`project_document_comments\` CHANGE \`type\` \`type\` text CHARACTER SET "latin1" COLLATE "latin1_swedish_ci" NOT NULL`);
        
    }

}
