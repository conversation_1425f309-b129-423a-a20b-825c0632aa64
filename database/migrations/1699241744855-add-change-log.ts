import {MigrationInterface, QueryRunner} from "typeorm";

export class addChangeLog1699241744855 implements MigrationInterface {
    name = 'addChangeLog1699241744855'

    public async up(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`CREATE TABLE \`change_logs\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT,\`descriptions\` varchar(2000) NULL, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL,  PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
       
    }

    public async down(queryRunner: Query<PERSON>unner): Promise<void> {
       
        await queryRunner.query(`DROP TABLE \`change_logs\``);
    }

}
