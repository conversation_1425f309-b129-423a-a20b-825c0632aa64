import {MigrationInterface, QueryRunner} from "typeorm";

export class projectStatus1700840078649 implements MigrationInterface {
    name = 'projectStatus1700840078649'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`issuesAndProblem\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`solution\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`solution\``);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`issuesAndProblem\``);
    }

}
