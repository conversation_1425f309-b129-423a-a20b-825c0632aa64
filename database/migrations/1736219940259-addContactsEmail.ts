import { MigrationInterface, QueryRunner } from 'typeorm';

export class addEmailAccount1736219940259 implements MigrationInterface {
  name = 'addContactsEmail1736219940259';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`contacts_email\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NULL, \`address\` varchar(255) NOT NULL, \`isActive\` tinyint NOT NULL DEFAULT 1, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`
    );
    await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`avatarKey\` text NULL`);
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` ADD CONSTRAINT \`FK_2db1809eaf00100778e6c3b5e18\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` ADD CONSTRAINT \`FK_ee0e10deb96943f49a94e11653d\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP FOREIGN KEY \`FK_ee0e10deb96943f49a94e11653d\``);    
    await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP FOREIGN KEY \`FK_2db1809eaf00100778e6c3b5e18\``);
    await queryRunner.query(`DROP TABLE \`contacts_email\``);
  }
}
