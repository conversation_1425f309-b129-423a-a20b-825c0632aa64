import { MigrationInterface, QueryRunner } from "typeorm";

export class addProjectEmail1737618310818 implements MigrationInterface {
    name = 'addProjectEmail1737618310818'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add the new column
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`projectEmail\` text NOT NULL`);

        // Use environment variable for the email domain, with a default fallback
        const emailDomain = process.env.MAILGUN_CORRESPONDENCE_DOMAIN;

        // Update existing rows to populate projectEmail based on the name column
        await queryRunner.query(`
            UPDATE \`projects\`
            SET \`projectEmail\` = CONCAT(
                REPLACE(LOWER(TRIM(SUBSTRING_INDEX(\`title\`, ' ', 2))), ' ', ''),
                '@${emailDomain}'
            )
        `);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the column
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`projectEmail\``);
    }
}
