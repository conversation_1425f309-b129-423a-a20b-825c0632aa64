import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTables1658203103208 implements MigrationInterface {
  name = 'updateTables1658203103208';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_invitations\` ADD \`invitedBy\` int UNSIGNED NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`contacts\` ADD \`userId\` int UNSIGNED NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` ADD CONSTRAINT \`FK_53864e10e2263359e4b895133c3\` FOREIGN KEY (\`invitedBy\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`contacts\` ADD CONSTRAINT \`FK_30ef77942fc8c05fcb829dcc61d\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`DROP TABLE \`contact_pics\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`contacts\` DROP FOREIGN KEY \`FK_30ef77942fc8c05fcb829dcc61d\``);
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` DROP FOREIGN KEY \`FK_53864e10e2263359e4b895133c3\``,
    );
    await queryRunner.query(`ALTER TABLE \`contacts\` DROP COLUMN \`userId\``);
    await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`invitedBy\``);
  }
}
