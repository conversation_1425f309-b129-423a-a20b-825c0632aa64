import { MigrationInterface, QueryRunner } from 'typeorm';

export class addAutosaveToProjectDocuments1718174999201 implements MigrationInterface {
  name = 'addAutosaveToProjectDocuments1718174999201';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`autosavedAt\` datetime NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`autosavedAt\``);
  }
}
