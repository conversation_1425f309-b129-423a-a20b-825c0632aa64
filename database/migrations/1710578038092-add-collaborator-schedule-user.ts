import {MigrationInterface, QueryRunner} from "typeorm";

export class addCollaboratorScheduleUser1710578038092 implements MigrationInterface {
    name = 'addCollaboratorScheduleUser1710578038092'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_users\` CHANGE \`scheduleRole\` \`scheduleRole\` enum ('Manager', 'Validator', 'Collaborator') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_users\` CHANGE \`scheduleRole\` \`scheduleRole\` enum ('Manager', 'Validator') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL`);
    }

}
