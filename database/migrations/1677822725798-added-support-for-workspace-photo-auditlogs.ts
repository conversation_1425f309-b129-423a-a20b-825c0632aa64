import {MigrationInterface, QueryRunner} from "typeorm";

export class addedSupportForWorkspacePhotoAuditlogs1677822725798 implements MigrationInterface {
    name = 'addedSupportForWorkspacePhotoAuditlogs1677822725798'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` ADD \`userId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` ADD CONSTRAINT \`FK_abdd01a789eaea8a7590a3066b3\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` DROP FOREIGN KEY \`FK_abdd01a789eaea8a7590a3066b3\``);
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` DROP COLUMN \`userId\``);
    }

}
