import {MigrationInterface, QueryRunner} from "typeorm";

export class changeToText1699265663305 implements MigrationInterface {
    name = 'changeToText1699265663305'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`descriptions\``);
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`descriptions\` longtext NULL`);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`descriptions\``);
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`descriptions\` text NULL`);
        
    }

}
