import {MigrationInterface, QueryRunner} from "typeorm";

export class addColumnForBIM1687165081771 implements MigrationInterface {
    name = 'addColumnForBIM1687165081771'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`spriteId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`elementId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`pos3D\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`bimId\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`bimId\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`pos3D\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`elementId\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`spriteId\``);
    }

}
