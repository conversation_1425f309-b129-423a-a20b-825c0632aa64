import {MigrationInterface, QueryRunner} from "typeorm";

export class addSchedulesMediaTable1689569864051 implements MigrationInterface {
    name = 'addSchedulesMediaTable1689569864051'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedules_medias\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`scheduleId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NULL, \`name\` text NOT NULL, \`fileUrl\` text NOT NULL, \`type\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD CONSTRAINT \`FK_b363aee24027f76bc66c84903db\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD CONSTRAINT \`FK_668b49975412805b75e845e1c2a\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP FOREIGN KEY \`FK_668b49975412805b75e845e1c2a\``);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP FOREIGN KEY \`FK_b363aee24027f76bc66c84903db\``);
        await queryRunner.query(`DROP TABLE \`schedules_medias\``);
    }

}
