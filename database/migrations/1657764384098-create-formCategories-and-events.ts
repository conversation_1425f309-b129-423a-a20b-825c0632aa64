import { MigrationInterface, QueryRunner } from 'typeorm';

export class createFormCategoriesAndEvents1657764384098 implements MigrationInterface {
  name = 'createFormCategoriesAndEvents1657764384098';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`tasks_attachments\` CHANGE \`attachmentRef\` \`fileUrl\` text NOT NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE \`form_categories\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`name\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`events\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, \`description\` varchar(255) NOT NULL, \`startAt\` datetime NOT NULL, \`endAt\` datetime NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`form_categories\` ADD CONSTRAINT \`FK_92081d4e37d93941d4f4afcaeef\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`events\` ADD CONSTRAINT \`FK_9929fa8516afa13f87b41abb263\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` DROP FOREIGN KEY \`FK_9929fa8516afa13f87b41abb263\``);
    await queryRunner.query(
      `ALTER TABLE \`form_categories\` DROP FOREIGN KEY \`FK_92081d4e37d93941d4f4afcaeef\``,
    );
    await queryRunner.query(`DROP TABLE \`events\``);
    await queryRunner.query(`DROP TABLE \`form_categories\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks_attachments\` CHANGE \`fileUrl\` \`attachmentRef\` text NOT NULL`,
    );
  }
}
