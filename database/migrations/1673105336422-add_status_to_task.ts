import {MigrationInterface, QueryRunner} from "typeorm";

export class addStatusToTask1673105336422 implements MigrationInterface {
    name = 'addStatusToTask1673105336422'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('Open', 'InProgress', 'Hold', 'Completed', 'Overdue') NOT NULL DEFAULT 'InProgress'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('InProgress', 'Completed', 'Overdue') NOT NULL DEFAULT 'InProgress'`);
    }

}
