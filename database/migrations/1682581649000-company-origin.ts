import {MigrationInterface, QueryRunner} from "typeorm";

export class companyOrigin1682581649000 implements MigrationInterface {
    name = 'companyOrigin1682581649000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`companyOrigin\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`companyOrigin\``);
    }

}
