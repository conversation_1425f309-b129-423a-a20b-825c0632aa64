import {MigrationInterface, QueryRunner} from "typeorm";

export class addFile<PERSON>eyToWorkspacePhotos1716363804665 implements MigrationInterface {
  name = 'addFileKeyToWorkspacePhotos1716363804665'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`workspace_photos\` ADD \`fileKey\` text NULL`);

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE workspace_photos
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`workspace_photos\` DROP COLUMN \`fileKey\``);
  }

}
