import {MigrationInterface, QueryRunner} from "typeorm";

export class generateFilesForLogs1677589251813 implements MigrationInterface {
    name = 'generateFilesForLogs1677589251813'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'DocumentComment') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }

}
