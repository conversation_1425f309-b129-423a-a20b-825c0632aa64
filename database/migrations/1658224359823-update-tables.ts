import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTables1658224359823 implements MigrationInterface {
  name = 'updateTables1658224359823';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` DROP FOREIGN KEY \`FK_53864e10e2263359e4b895133c3\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`invitedBy\` \`companyId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`type\` \`type\` varchar(20) NOT NULL DEFAULT 'User'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` CHANGE \`type\` \`type\` varchar(20) NOT NULL DEFAULT 'Admin'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`admins\` CHANGE \`type\` \`type\` varchar(20) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`type\` \`type\` varchar(20) NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` CHANGE \`companyId\` \`invitedBy\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` ADD CONSTRAINT \`FK_53864e10e2263359e4b895133c3\` FOREIGN KEY (\`invitedBy\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
