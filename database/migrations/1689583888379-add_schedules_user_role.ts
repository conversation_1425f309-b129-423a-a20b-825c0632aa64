import {MigrationInterface, QueryRunner} from "typeorm";

export class addSchedulesUserRole1689583888379 implements MigrationInterface {
    name = 'addSchedulesUserRole1689583888379'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedule_comments\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`scheduleId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NULL, \`message\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`project_users\` ADD \`scheduleRole\` enum ('Manager', 'Validator') NULL`);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD CONSTRAINT \`FK_c40cca22bdd0b8b1ff07abe240d\` FOREIGN KEY (\`scheduleId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD CONSTRAINT \`FK_2aaec0d32379175929a124d4b7b\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP FOREIGN KEY \`FK_2aaec0d32379175929a124d4b7b\``);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP FOREIGN KEY \`FK_c40cca22bdd0b8b1ff07abe240d\``);
        await queryRunner.query(`ALTER TABLE \`project_users\` DROP COLUMN \`scheduleRole\``);
        await queryRunner.query(`DROP TABLE \`schedule_comments\``);
    }

}
