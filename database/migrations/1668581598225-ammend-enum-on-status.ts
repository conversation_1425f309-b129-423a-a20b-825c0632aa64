import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendEnumOnStatus1668581598225 implements MigrationInterface {
  name = 'ammendEnumOnStatus1668581598225';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('InReview', 'Rejected', 'Approved') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Draft', 'Submitted', 'InReview', 'Rejected', 'Approved') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Approved') NOT NULL DEFAULT 'Pending'`,
    );
  }
}
