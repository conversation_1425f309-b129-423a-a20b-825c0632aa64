import {MigrationInterface, QueryRunner} from "typeorm";

export class workspaceDynamicStatus1700445461120 implements MigrationInterface {
    name = 'workspaceDynamicStatus1700445461120'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Proceed', 'Approved', 'Amend') NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Draft', 'Submitted', 'InReview', 'Rejected', 'Approved', 'InProgress') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` <PERSON>ANGE \`status\` \`status\` enum ('Pending', 'Draft', 'Submitted', 'InReview', 'Rejected', 'Approved') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL`);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Approved', 'Amend') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL DEFAULT 'Pending'`);
    }

}
