import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateEnum1658987510150 implements MigrationInterface {
  name = 'updateEnum1658987510150';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` <PERSON>ANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete') NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('create', 'update', 'delete') NOT NULL`,
    );
  }
}
