import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTasksAttachementsTable1661314462022 implements MigrationInterface {
  name = 'updateTasksAttachementsTable1661314462022';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD \`name\` text NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD \`type\` text NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP COLUMN \`type\``);
    await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP COLUMN \`name\``);
  }
}
