import {MigrationInterface, QueryRunner} from "typeorm";

export class addContractorValueOverview1685349655429 implements MigrationInterface {
    name = 'addContractorValueOverview1685349655429'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`contractorValue\` int UNSIGNED NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`contractorValue\``);
    }

}
