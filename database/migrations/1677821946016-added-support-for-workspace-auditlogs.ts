import {MigrationInterface, QueryRunner} from "typeorm";

export class addedSupportForWorkspaceAuditlogs1677821946016 implements MigrationInterface {
    name = 'addedSupportForWorkspaceAuditlogs1677821946016'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` ADD \`userId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` ADD CONSTRAINT \`FK_1ed287bcc6475eadd54c8faaaf9\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` DROP FOREIGN KEY \`FK_1ed287bcc6475eadd54c8faaaf9\``);
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` DROP COLUMN \`userId\``);
    }

}
