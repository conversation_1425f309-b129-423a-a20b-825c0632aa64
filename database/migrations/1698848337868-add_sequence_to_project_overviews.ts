import {MigrationInterface, QueryRunner} from "typeorm";

export class addSequenceToProjectOverviews1698848337868 implements MigrationInterface {
    name = 'addSequenceToProjectOverviews1698848337868'

    public async up(queryRunner: Query<PERSON>unner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_overviews\` ADD \`sequence\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_overviews\` DROP COLUMN \`sequence\``);
    }

}
