import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateJoinColumn1661247161385 implements MigrationInterface {
  name = 'updateJoinColumn1661247161385';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` ADD CONSTRAINT \`FK_072601fa82ce5c520d3acd98316\` FOREIGN KEY (\`actorId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` DROP FOREIGN KEY \`FK_072601fa82ce5c520d3acd98316\``,
    );
  }
}
