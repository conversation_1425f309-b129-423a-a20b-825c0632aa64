import {MigrationInterface, QueryRunner} from "typeorm";

export class addFileKeyForPrivateBucket1711421613432 implements MigrationInterface {
  name = 'addFileKeyToProjectDocuments1711421613432'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for project_documents table
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`fileKey\` text NULL`);
    // get all project documents where fileUrl is not null and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE project_documents
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
      WHERE fileUrl IS NOT NULL AND category != 'BIMDrawings'
    `);

    // for users
    await queryRunner.query(`ALTER TABLE \`users\`
      ADD \`avatar<PERSON>ey\` text NULL,
      ADD \`stampKey\` text NULL,
      ADD \`signKey\` text NULL,
      ADD \`stampAndSignKey\` text NULL
    `);
    // get all users and update the fileKeys for avatar, stamp, sign, stampAndSign
    await queryRunner.query(`UPDATE users 
      SET avatarKey = REGEXP_REPLACE(avatar, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      stampKey = REGEXP_REPLACE(stampUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      signKey = REGEXP_REPLACE(signUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      stampAndSignKey = REGEXP_REPLACE(stampAndSignUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);

    // for project_carousel table
    await queryRunner.query(`ALTER TABLE \`project_carousels\` ADD \`fileKey\` text NULL`);
    // get all project documents where fileUrl is not null and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE project_carousels
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
      WHERE fileUrl IS NOT NULL
    `);

    // for schedules_medias table
    await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD \`fileKey\` text NULL`);
    // get all project documents where fileUrl is not null and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE schedules_medias
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
      WHERE fileUrl IS NOT NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileKey\``);
    await queryRunner.query(`ALTER TABLE \`users\`
      DROP COLUMN \`avatarKey\`,
      DROP COLUMN \`stampKey\`,
      DROP COLUMN \`signKey\`,
      DROP COLUMN \`stampAndSignKey\`
    `);
    await queryRunner.query(`ALTER TABLE \`project_carousels\` DROP COLUMN \`fileKey\``);
    await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP COLUMN \`fileKey\``);
  }
}
