import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawingLinks1717485878101 implements MigrationInterface {
    name = 'addDrawingLinks1717485878101'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`drawing_links\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`annotationId\` varchar(255) NULL, \`name\` text NOT NULL, \`projectDocumentId\` int UNSIGNED NULL, \`description\` varchar(255) NULL, \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'GanttChart') NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`drawing_links\` ADD CONSTRAINT \`FK_a955d1d06498185630b0cbbca08\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_links\` DROP FOREIGN KEY \`FK_a955d1d06498185630b0cbbca08\``);
        await queryRunner.query(`DROP TABLE \`drawing_links\``);
    }
}
