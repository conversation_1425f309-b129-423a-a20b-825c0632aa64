import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendRequestForSignatureEnum1669283643658 implements MigrationInterface {
  name = 'ammendRequestForSignatureEnum1669283643658';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Sent', 'InReview', 'Rejected', 'Approved') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('InReview', 'Rejected', 'Approved') NULL`,
    );
  }
}
