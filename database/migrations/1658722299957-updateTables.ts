import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTables1658722299957 implements MigrationInterface {
  name = 'updateTables1658722299957';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD \`type\` varchar(255) NOT NULL DEFAULT 'Folder'`,
    );
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` ADD \`fileSize\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` ADD \`status\` varchar(255) NULL`);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileSize\` \`fileSize\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileSize\` \`fileSize\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`status\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`type\``);
  }
}
