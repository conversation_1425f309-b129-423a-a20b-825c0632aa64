import {MigrationInterface, QueryRunner} from "typeorm";

export class addEmailUuid1736760398951 implements MigrationInterface {
    name = 'addEmailUuid1736760398951'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`uuid\` varchar(36) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`uuid\``);
      
    }

}
