import {MigrationInterface, QueryRunner} from "typeorm";

export class addColumnsForScheduleRevamp1710567703780 implements MigrationInterface {
    name = 'addColumnsForScheduleRevamp1710567703780'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`revision\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`startVariance\` float NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`finishVariance\` float NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`finishVariance\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`startVariance\``);
        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`revision\``);
    }

}
