import {MigrationInterface, QueryRunner} from "typeorm";

export class addIndexingProjectId1741071501252 implements MigrationInterface {
    name = 'addIndexingProjectId1741071501252'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX \`IDX_f45c0dc27313262f03ef705df1\` ON \`project_documents\` (\`projectId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_f45c0dc27313262f03ef705df1\` ON \`project_documents\``);
    }

}
