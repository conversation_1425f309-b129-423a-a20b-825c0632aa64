import {MigrationInterface, QueryRunner} from "typeorm";

export class addPdftronModuleForAuditLog1704694597393 implements MigrationInterface {
    name = 'addPdftronModuleForAuditLog1704694597393'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member', 'Pdftron') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }

}
