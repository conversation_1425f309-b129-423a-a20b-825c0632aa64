import {MigrationInterface, QueryRunner} from "typeorm";

export class isDocsStoredForWorkspace1695112509530 implements MigrationInterface {
    name = 'isDocsStoredForWorkspace1695112509530'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`isDocsStored\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`isDocsStored\``);
    }

}
