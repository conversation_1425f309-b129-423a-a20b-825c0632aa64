import { MigrationInterface, QueryRunner } from "typeorm";

export class changeAuditModuleType1734338185112 implements MigrationInterface {
    name = 'changeAuditModuleType1734338185112';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update the enum definition to include 'DocumentEditor' and remove 'Pdftron'
        await queryRunner.query(`
            ALTER TABLE \`audit_logs\` 
            CHANGE \`module\` \`module\` 
            enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member', 'ProjectGroup', 'WorkspaceGroup', 'Pdftron', 'DocumentEditor') 
            NOT NULL
        `);

        // Change all 'Pdftron' values to 'DocumentEditor'
        await queryRunner.query(`
            UPDATE \`audit_logs\` 
            SET \`module\` = 'DocumentEditor',
                updatedAt = updatedAt
            WHERE \`module\` = 'Pdftron'
        `);

        // Remove 'Pdftron' from the enum definition
        await queryRunner.query(`
            ALTER TABLE \`audit_logs\` 
            CHANGE \`module\` \`module\` 
            enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member', 'ProjectGroup', 'WorkspaceGroup', 'DocumentEditor') 
            NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Restore the enum definition to include 'Pdftron' and remove 'DocumentEditor'
        await queryRunner.query(`
            ALTER TABLE \`audit_logs\` 
            CHANGE \`module\` \`module\` 
            enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member', 'ProjectGroup', 'WorkspaceGroup', 'Pdftron', 'DocumentEditor') 
            NOT NULL
        `);

        // Revert the 'DocumentEditor' values back to 'Pdftron'
        await queryRunner.query(`
            UPDATE \`audit_logs\` 
            SET \`module\` = 'Pdftron' 
            WHERE \`module\` = 'DocumentEditor'
        `);

        // Restore the original enum definition
        await queryRunner.query(`
            ALTER TABLE \`audit_logs\` 
            CHANGE \`module\` \`module\` 
            enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule', 'Member', 'Pdftron') 
            CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" 
            NOT NULL
        `);
    }
}
