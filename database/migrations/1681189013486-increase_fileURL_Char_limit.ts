import { MigrationInterface, QueryRunner } from 'typeorm';

export class increaseFileURLCharLimit1681189013486 implements MigrationInterface {
  name = 'increaseFileURLCharLimit1681189013486';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
    // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
    await queryRunner.query('ALTER TABLE \`project_documents\` MODIFY COLUMN \`updatedAt\` DATETIME(6) NULL');
    await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE COLUMN \`fileUrl\` \`oldFileUrl\` VARCHAR(510)`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD COLUMN \`fileUrl_new\` TEXT`);
    await queryRunner.query(`UPDATE \`project_documents\` SET \`fileUrl_new\` = \`oldFileUrl\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`oldFileUrl\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE COLUMN \`fileUrl_new\` \`fileUrl\` TEXT`);
    await queryRunner.query(
        `ALTER TABLE \`project_documents\` MODIFY COLUMN \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`
    );
    // await queryRunner.query(
    //   `ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`
    // );
    // await queryRunner.query(
    //   `ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    // );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
    // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
    await queryRunner.query('ALTER TABLE \`project_documents\` MODIFY COLUMN \`updatedAt\` DATETIME(6) NULL');
    await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE COLUMN \`fileUrl\` \`oldFileUrl\` VARCHAR(510)`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD COLUMN \`fileUrl_new\` VARCHAR(510)`);
    await queryRunner.query(`UPDATE \`project_documents\` SET \`fileUrl_new\` = \`oldFileUrl\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`oldFileUrl\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE COLUMN \`fileUrl_new\` \`fileUrl\` VARCHAR(510)`);
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` MODIFY COLUMN \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)`
    );
    // await queryRunner.query(
    //   `ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    // );
    // await queryRunner.query(
    //   `ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`
    // );
  }
}
