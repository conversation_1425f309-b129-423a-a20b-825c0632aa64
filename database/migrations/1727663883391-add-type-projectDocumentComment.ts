import {MigrationInterface, QueryRunner} from "typeorm";

export class addTypeProjectDocumentComment1727663883391 implements MigrationInterface {
    name = 'addTypeProjectDocumentComment1727663883391'

    public async up(queryRunner: Query<PERSON>unner): Promise<void> {       
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` ADD \`type\` text NOT NULL`);       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {      
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` DROP COLUMN \`type\``);       
    }

}
