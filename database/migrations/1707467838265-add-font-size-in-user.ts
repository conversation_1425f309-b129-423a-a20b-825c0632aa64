import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFontSizeInUser1707467838265 implements MigrationInterface {
  name = 'addFontSizeInUser1707467838265';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`fontSize\` int NULL DEFAULT '12'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`fontSize\``);
  }
}
