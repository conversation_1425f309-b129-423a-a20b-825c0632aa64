import {MigrationInterface, QueryRunner} from "typeorm";

export class addStatusToSchedule1697516309322 implements MigrationInterface {
    name = 'addStatusToSchedule1697516309322'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`status\` \`status\` enum ('Pending', 'Upcoming', 'InProgress', 'Hold', 'Completed', 'Delay') NOT NULL DEFAULT 'Pending'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`status\` \`status\` enum ('Pending', 'InProgress', 'Hold', 'Completed', 'Delay') NOT NULL DEFAULT 'Pending'`);
    }

}
