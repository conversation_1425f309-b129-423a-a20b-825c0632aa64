import {MigrationInterface, QueryRunner} from "typeorm";

export class updateDeletedIdsColumnType1702554453662 implements MigrationInterface {
    name = 'updateDeletedIdsColumnType1702554453662'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`deletedChildrenIds\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`deletedChildrenIds\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`deletedChildrenIds\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`deletedChildrenIds\` varchar(255) NULL`);
    }

}
