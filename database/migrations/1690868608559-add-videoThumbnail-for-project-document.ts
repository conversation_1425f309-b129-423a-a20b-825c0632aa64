import { MigrationInterface, QueryRunner } from 'typeorm';

export class addVideoThumbnailForProjectDocument1690868608559 implements MigrationInterface {
  name = 'addVideoThumbnailForProjectDocument1690868608559';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`videoThumbnail\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`videoThumbnail\``);
  }
}
