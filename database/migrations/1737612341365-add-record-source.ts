import {MigrationInterface, QueryRunner} from "typeorm";

export class addRecordSource1737612341365 implements MigrationInterface {
    name = 'addRecordSource1737612341365'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`recordSource\``);
    }

}
