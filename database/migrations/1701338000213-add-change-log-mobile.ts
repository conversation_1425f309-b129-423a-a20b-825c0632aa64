import {MigrationInterface, QueryRunner} from "typeorm";

export class addChangeLogMobile1701338000213 implements MigrationInterface {
    name = 'addChangeLogMobile1701338000213'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`isReadChangeLogMobile\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`isReadChangeLogMobile\``);
    }

}
