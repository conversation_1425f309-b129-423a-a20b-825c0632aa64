import {MigrationInterface, QueryRunner} from "typeorm";

export class addAppPermission1680873790799 implements MigrationInterface {
    name = 'addAppPermission1680873790799'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`totalProjects\` int NOT NULL DEFAULT '9999'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`totalUsers\` int NOT NULL DEFAULT '9999'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowTask\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowProjectDocument\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowWorkProgramme\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowCorrespondence\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowWorkspaceDocument\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowWorkspaceTemplate\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowDrawing\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowBimModel\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowPhoto\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowPhoto\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowBimModel\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowDrawing\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowWorkspaceTemplate\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowWorkspaceDocument\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowCorrespondence\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowWorkProgramme\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowProjectDocument\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowTask\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`totalUsers\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`totalProjects\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
