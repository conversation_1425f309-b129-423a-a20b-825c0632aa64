import {MigrationInterface, QueryRunner} from "typeorm";

export class enumForScheduleType1693815657692 implements MigrationInterface {
    name = 'enumForScheduleType1693815657692'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`type\` enum ('task', 'milestone', 'project') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`type\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`type\` text CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL`);
    }

}
