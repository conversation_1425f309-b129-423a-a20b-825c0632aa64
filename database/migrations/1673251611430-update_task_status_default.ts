import {MigrationInterface, QueryRunner} from "typeorm";

export class updateTaskStatusDefault1673251611430 implements MigrationInterface {
    name = 'updateTaskStatusDefault1673251611430'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('Open', 'InProgress', 'Hold', 'Completed', 'Overdue') NOT NULL DEFAULT 'Open'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` CHANGE \`status\` \`status\` enum ('Open', 'InProgress', 'Hold', 'Completed', 'Overdue') NOT NULL DEFAULT 'InProgress'`);
    }

}
