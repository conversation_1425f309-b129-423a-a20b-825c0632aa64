import { MigrationInterface, QueryRunner } from 'typeorm';

export class addDeletedProjectDocIdToProjectDocuments1701834308849 implements MigrationInterface {
  name = 'addDeletedProjectDocIdToProjectDocuments1701834308849';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`deletedProjectDocumentId\` int UNSIGNED NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`deletedProjectDocumentId\``);
  }
}
