import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawing<PERSON>inkComment1717493140770 implements MigrationInterface {
    name = 'addDrawingLinkComment1717493140770'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`drawing_link_comment\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`message\` text NULL, \`drawingLinkId\` int UNSIGNED NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` ADD CONSTRAINT \`FK_adacc1a5dc92722a5b076b5800e\` FOREIGN KEY (\`drawingLinkId\`) REFERENCES \`drawing_links\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` DROP FOREIGN KEY \`FK_adacc1a5dc92722a5b076b5800e\``);
        await queryRunner.query(`DROP TABLE \`drawing_link_comment\``);
    }

}
