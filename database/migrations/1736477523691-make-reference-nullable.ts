import {MigrationInterface, QueryRunner} from "typeorm";

export class makeReferenceNullable1736477523691 implements MigrationInterface {
    name = 'makeReferenceNullable1736477523691'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`reference\` \`reference\` varchar(255) NULL`);
      
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`reference\` \`reference\` varchar(255) NOT NULL`);
       
    }

}
