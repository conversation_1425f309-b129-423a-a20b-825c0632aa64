import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTables1658822021259 implements MigrationInterface {
  name = 'updateTables1658822021259';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD \`category\` enum ('Project Document', 'Work Programme', 'Correspondence') NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`category\` enum ('Project Document', 'Work Programme', 'Correspondence') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`category\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`category\``);
  }
}
