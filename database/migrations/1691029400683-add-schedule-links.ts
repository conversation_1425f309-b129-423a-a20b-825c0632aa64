import {MigrationInterface, QueryRunner} from "typeorm";

export class addScheduleLinks1691029400683 implements MigrationInterface {
    name = 'addScheduleLinks1691029400683'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedules_links\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ownerId\` int UNSIGNED NOT NULL, \`projectId\` int UNSIGNED NOT NULL, \`suid\` text NOT NULL, \`lag_unit\` varchar(255) NOT NULL, \`lag\` int NULL, \`source\` int NULL, \`target\` int NULL, \`type\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` ADD CONSTRAINT \`FK_bbae994ff7dcb4682a110f60ef4\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` ADD CONSTRAINT \`FK_456083b6ce15a2f8c664916cf51\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {;
        await queryRunner.query(`ALTER TABLE \`schedules_links\` DROP FOREIGN KEY \`FK_456083b6ce15a2f8c664916cf51\``);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` DROP FOREIGN KEY \`FK_bbae994ff7dcb4682a110f60ef4\``);
        await queryRunner.query(`DROP TABLE \`schedules_links\``);
    }

}
