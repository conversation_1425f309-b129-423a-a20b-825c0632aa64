import {MigrationInterface, QueryRunner} from "typeorm";

export class addResourceIdToAuditlogs1676968336571 implements MigrationInterface {
    name = 'addResourceIdToAuditlogs1676968336571'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`resourceId\` int UNSIGNED NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`resourceId\``);
    }

}
