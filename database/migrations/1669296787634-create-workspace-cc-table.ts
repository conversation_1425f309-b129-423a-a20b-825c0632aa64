import { MigrationInterface, QueryRunner } from 'typeorm';

export class createWorkspaceCcTable1669296787634 implements MigrationInterface {
  name = 'createWorkspaceCcTable1669296787634';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`workspace_ccs\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectDocumentId\` int UNSIGNED NOT NULL, \`ownerId\` int UNSIGNED NOT NULL, \`ccId\` int UNSIGNED NOT NULL, \`status\` enum ('Unsent', 'Sent') NOT NULL DEFAULT 'Unsent', UNIQUE INDEX \`IDX_ddab8703489d0116b0e4fcb530\` (\`projectDocumentId\`, \`ccId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Unsent', 'Sent', 'InReview', 'Rejected', 'Approved') NULL DEFAULT 'Unsent'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` ADD CONSTRAINT \`FK_df474a92622d610c66afa5e7979\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` ADD CONSTRAINT \`FK_9c41b22c48afe72c6720d35e353\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` ADD CONSTRAINT \`FK_8c93e5fb03d0ad56fdcdc43a510\` FOREIGN KEY (\`ccId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` DROP FOREIGN KEY \`FK_8c93e5fb03d0ad56fdcdc43a510\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` DROP FOREIGN KEY \`FK_9c41b22c48afe72c6720d35e353\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` DROP FOREIGN KEY \`FK_df474a92622d610c66afa5e7979\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Sent', 'InReview', 'Rejected', 'Approved') NULL`,
    );
    await queryRunner.query(`DROP INDEX \`IDX_ddab8703489d0116b0e4fcb530\` ON \`workspace_ccs\``);
    await queryRunner.query(`DROP TABLE \`workspace_ccs\``);
  }
}
