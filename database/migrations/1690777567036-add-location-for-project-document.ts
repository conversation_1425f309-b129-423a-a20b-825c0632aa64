import { MigrationInterface, QueryRunner } from 'typeorm';

export class addLocationForProjectDocument1690777567036 implements MigrationInterface {
  name = 'addLocationForProjectDocument1690777567036';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`uploadLatitude\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`uploadLongitude\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`uploadAddress\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`uploadAddress\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`uploadLongitude\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`uploadLatitude\``);
  }
}
