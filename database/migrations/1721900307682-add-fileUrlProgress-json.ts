import {MigrationInterface, QueryRunner} from "typeorm";

export class addFileUrlProgressJson1721900307682 implements MigrationInterface {
    name = 'addFileUrlProgressJson1721900307682'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`fileUrlProgressJson\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`fileUrlProgressFinanceJson\` json NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`fileUrlProgressFinanceJson\``);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`fileUrlProgressJson\``);
    }

}
