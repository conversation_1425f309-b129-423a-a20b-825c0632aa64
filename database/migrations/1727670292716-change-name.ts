import {MigrationInterface, QueryRunner} from "typeorm";

export class changeName1727670292716 implements MigrationInterface {
    name = 'changeName1727670292716'

    public async up(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` <PERSON><PERSON><PERSON> \`type\` \`commentType\` text CHARACTER SET "latin1" COLLATE "latin1_swedish_ci" NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {       
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` CHANGE \`commentType\` \`type\` text CHARACTER SET "latin1" COLLATE "latin1_swedish_ci" NULL`);
      
    }

}
