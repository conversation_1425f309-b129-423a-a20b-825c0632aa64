import {MigrationInterface, QueryRunner} from "typeorm";

export class addObsDetailsToProjectDocuments1700746047054 implements MigrationInterface {
    name = 'addObsDetailsToProjectDocuments1700746047054'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`obsFileSize\` float NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`obsFileType\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`obsFileType\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`obsFileSize\``);
    }

}
