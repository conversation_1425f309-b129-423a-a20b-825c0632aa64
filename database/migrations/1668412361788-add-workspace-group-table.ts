import { MigrationInterface, QueryRunner } from 'typeorm';

export class addWorkspaceGroupTable1668412361788 implements MigrationInterface {
  name = 'addWorkspaceGroupTable1668412361788';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`workspace_groups\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`name\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`workspaceGroupId\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`workspace_groups\` ADD CONSTRAINT \`FK_d20b556833ecb5470710b299172\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_843c62fe05b3018e5a5a61a051c\` FOREIGN KEY (\`workspaceGroupId\`) REFERENCES \`workspace_groups\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_843c62fe05b3018e5a5a61a051c\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_groups\` DROP FOREIGN KEY \`FK_d20b556833ecb5470710b299172\``,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`workspaceGroupId\``);
    await queryRunner.query(`DROP TABLE \`workspace_groups\``);
  }
}
