import {MigrationInterface, QueryRunner} from "typeorm";

export class addSalesOrderFields1679981776136 implements MigrationInterface {
    name = 'addSalesOrderFields1679981776136'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`paymentUrl\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`paymentStatus\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`cuid\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`status\` enum ('pending', 'paid', 'declined', 'cancelled') NOT NULL DEFAULT 'pending'`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`message\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`hash\` text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`hash\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`message\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`cuid\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`paymentStatus\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`paymentUrl\` text NOT NULL`);
    }

}
