import {MigrationInterface, QueryRunner} from "typeorm";

export class makeNameNull1737453351521 implements MigrationInterface {
    name = 'makeNameNull1737453351521'

    public async up(queryRunner: QueryRunner): Promise<void> {
    
        await queryRunner.query(`ALTER TABLE \`contacts_email\` <PERSON><PERSON><PERSON> \`name\` \`name\` varchar(255) NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`contacts_email\` CHAN<PERSON> \`name\` \`name\` varchar(255) NOT NULL`);
       
    }

}
