import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendRequestForSignatureAndWorkspaceCc1669341855532 implements MigrationInterface {
  name = 'ammendRequestForSignatureAndWorkspaceCc1669341855532';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_6a6938e9fc292c860a4fd596f11\``,
    );
    await queryRunner.query(`DROP INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\``);
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` DROP FOREIGN KEY \`FK_df474a92622d610c66afa5e7979\``,
    );
    await queryRunner.query(`DROP INDEX \`IDX_ddab8703489d0116b0e4fcb530\` ON \`workspace_ccs\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`workspace_ccs\` ADD CONSTRAINT \`FK_df474a92622d610c66afa5e7979\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_ddab8703489d0116b0e4fcb530\` ON \`workspace_ccs\` (\`projectDocumentId\`, \`ccId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_6a6938e9fc292c860a4fd596f11\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\` (\`projectDocumentId\`, \`signById\`)`,
    );
  }
}
