import {MigrationInterface, QueryRunner} from "typeorm";

export class weeklyEmails1686227658907 implements MigrationInterface {
    name = 'weeklyEmails1686227658907'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`receiveWeeklyEmails\` tinyint NOT NULL DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`receiveWeeklyEmails\``);
    }

}
