import {MigrationInterface, QueryRunner} from "typeorm";

export class addMailgunId1736845437481 implements MigrationInterface {
    name = 'addMailgunId1736845437481'

    public async up(queryRunner: QueryRunner): Promise<void> {
 
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`mailgunId\` text NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`mailgunId\``);

    }

}
