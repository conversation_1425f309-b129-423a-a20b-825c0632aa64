import {MigrationInterface, QueryRunner} from "typeorm";

export class siteDiaryReminder1695871148074 implements MigrationInterface {
    name = 'siteDiaryReminder1695871148074'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`isRemindSiteDiary\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`isRemindSiteDiary\``);
    }

}
