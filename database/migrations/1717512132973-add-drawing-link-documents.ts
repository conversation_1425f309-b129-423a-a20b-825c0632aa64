import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawingLinkDocuments1717512132973 implements MigrationInterface {
    name = 'addDrawingLinkDocuments1717512132973'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`drawing_link_documents\` (\`drawingLinksId\` int UNSIGNED NOT NULL, \`projectDocumentsId\` int UNSIGNED NOT NULL, INDEX \`IDX_f17bdc9e07cdc78f6dd5766bed\` (\`drawingLinksId\`), INDEX \`IDX_3196b848256e3a0b1dc9811384\` (\`projectDocumentsId\`), PRIMARY KEY (\`drawingLinksId\`, \`projectDocumentsId\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_link_documents\` DROP FOREIGN KEY \`FK_f17bdc9e07cdc78f6dd5766bed3\``);
    }

}
