import {MigrationInterface, QueryRunner} from "typeorm";

export class workspaceSubgroup1697073602050 implements MigrationInterface {
    name = 'workspaceSubgroup1697073602050'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` ADD \`workspaceGroupId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` ADD \`mpath\` varchar(255) NULL DEFAULT ''`);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` ADD CONSTRAINT \`FK_bc0bca297e610fefbc607e7a08d\` FOREIGN KEY (\`workspaceGroupId\`) REFERENCES \`workspace_groups\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` DROP FOREIGN KEY \`FK_bc0bca297e610fefbc607e7a08d\``);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` DROP COLUMN \`mpath\``);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` DROP COLUMN \`workspaceGroupId\``);
    }

}
