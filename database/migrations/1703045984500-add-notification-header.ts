import {MigrationInterface, QueryRunner} from "typeorm";

export class addNotificationHeader1703045984500 implements MigrationInterface {
    name = 'addNotificationHeader1703045984500'

    public async up(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE \`notification_transactions\` ADD \`header\` varchar(255) NULL`);
      
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
    
    
        await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`header\``);
        
    }

}
