import { MigrationInterface, QueryRunner } from 'typeorm';

export class projectDocumentAndProjectDocumentFolder1658465729225 implements MigrationInterface {
  name = 'projectDocumentAndProjectDocumentFolder1658465729225';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` DROP FOREIGN KEY \`FK_81e478269eafb8b924c2f5d83e7\``,
    );
    await queryRunner.query(`DROP INDEX \`IDX_81e478269eafb8b924c2f5d83e\` ON \`tasks_documents\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` CHANGE \`documentsId\` \`projectDocumentsId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_document_folders\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`projectDocumentFolderId\` int UNSIGNED NULL, \`name\` varchar(255) NOT NULL, \`mpath\` varchar(255) NULL DEFAULT '', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_documents\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`projectDocumentFolderId\` int UNSIGNED NULL, \`name\` varchar(255) NOT NULL, \`fileUrl\` varchar(255) NOT NULL, \`type\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_dd31f628f866146aff26ef6d14\` ON \`tasks_documents\` (\`projectDocumentsId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD CONSTRAINT \`FK_ca6330a495ccfa60814a3e27d90\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` ADD CONSTRAINT \`FK_444a05a50c44d4962f7d6d17616\` FOREIGN KEY (\`projectDocumentFolderId\`) REFERENCES \`project_document_folders\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_f45c0dc27313262f03ef705df1d\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_a373b7fe40bbc055441213e5ca9\` FOREIGN KEY (\`projectDocumentFolderId\`) REFERENCES \`project_document_folders\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` ADD CONSTRAINT \`FK_dd31f628f866146aff26ef6d143\` FOREIGN KEY (\`projectDocumentsId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(`DROP TABLE \`documents\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` DROP FOREIGN KEY \`FK_dd31f628f866146aff26ef6d143\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_a373b7fe40bbc055441213e5ca9\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_f45c0dc27313262f03ef705df1d\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` DROP FOREIGN KEY \`FK_444a05a50c44d4962f7d6d17616\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_folders\` DROP FOREIGN KEY \`FK_ca6330a495ccfa60814a3e27d90\``,
    );
    await queryRunner.query(`DROP INDEX \`IDX_dd31f628f866146aff26ef6d14\` ON \`tasks_documents\``);
    await queryRunner.query(`DROP TABLE \`project_documents\``);
    await queryRunner.query(`DROP TABLE \`project_document_folders\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` CHANGE \`projectDocumentsId\` \`documentsId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX \`IDX_81e478269eafb8b924c2f5d83e\` ON \`tasks_documents\` (\`documentsId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` ADD CONSTRAINT \`FK_81e478269eafb8b924c2f5d83e7\` FOREIGN KEY (\`documentsId\`) REFERENCES \`documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
