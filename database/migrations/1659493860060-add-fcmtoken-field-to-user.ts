import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFcmtokenFieldToUser1659493860060 implements MigrationInterface {
  name = 'addFcmtokenFieldToUser1659493860060';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`fcmToken\` varchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`fcmToken\``);
  }
}
