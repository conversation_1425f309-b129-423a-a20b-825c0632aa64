import {MigrationInterface, QueryRunner} from "typeorm";

export class addSchedulePriority1710652761395 implements MigrationInterface {
    name = 'addSchedulePriority1710652761395'

    public async up(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`isPriority\` tinyint NULL DEFAULT 0`);
      
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`isPriority\``);
      
    }

}
