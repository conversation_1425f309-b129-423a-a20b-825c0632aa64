import {MigrationInterface, QueryRunner} from "typeorm";

export class addRequestForSignatureInProgress1701454910857 implements MigrationInterface {
    name = 'addRequestForSignatureInProgress1701454910857'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Proceed', 'Approved', 'Amend', 'InProgress') NULL DEFAULT 'Pending'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Proceed', 'Approved', 'Amend') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL DEFAULT 'Pending'`);
    }

}
