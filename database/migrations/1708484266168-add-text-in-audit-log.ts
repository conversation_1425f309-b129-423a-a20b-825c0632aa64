import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTextInAuditLog1708484266168 implements MigrationInterface {
  name = 'addTextInAuditLog1708484266168';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`text\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`text\``);
  }
}
