import { MigrationInterface, QueryRunner } from 'typeorm';

export class addNotifiedSchedule1711049393629 implements MigrationInterface {
  name = 'addNotifiedSchedule1711049393629';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD \`isNotified\` tinyint NOT NULL DEFAULT 1`);
    await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`isNotified\` tinyint NOT NULL DEFAULT 1`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`isNotified\``);
    await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP COLUMN \`isNotified\``);
  }
}
