import {MigrationInterface, QueryRunner} from "typeorm";

export class addStampFieldsToCompanyUsers1675742524440 implements MigrationInterface {
    name = 'addStampFieldsToCompanyUsers1675742524440'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`companies\` ADD \`stampUrl\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`stampUrl\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`stampUrl\``);
        await queryRunner.query(`ALTER TABLE \`companies\` DROP COLUMN \`stampUrl\``);
    }

}
