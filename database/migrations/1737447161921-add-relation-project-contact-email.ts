import {MigrationInterface, QueryRunner} from "typeorm";

export class addRelationProjectContactEmail1737447161921 implements MigrationInterface {
    name = 'addRelationProjectContactEmail1737447161921'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`projectId\` int UNSIGNED NOT NULL`);        
        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD CONSTRAINT \`FK_841c9b45ed3697df848f0180e72\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);

    }
    
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP FOREIGN KEY \`FK_841c9b45ed3697df848f0180e72\``);
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`projectId\``);
    }
    
}
