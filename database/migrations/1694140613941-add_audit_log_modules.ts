import {MigrationInterface, QueryRunner} from "typeorm";

export class addAuditLogModules1694140613941 implements MigrationInterface {
    name = 'addAuditLogModules1694140613941'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment', 'Calendar', 'SCurve', 'Overview', 'CloudDocument', 'Schedule') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'Photo', 'Drawing', 'Workspace', 'WorkspaceComment') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }
}
