import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFilekeyWorkspcaeDoc1727333202713 implements MigrationInterface {
  name = 'addFilekeyWorkspcaeDoc1727333202713';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`workspace_document\`
            ADD \`fileKey\` text NULL`);

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE workspace_document
            SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),            
            updatedAt = updatedAt
        `);

    // run this manually to update db
    // await queryRunner.query(`UPDATE workspace_document SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my:443/', ''), updatedAt = updatedAt`);
    // await queryRunner.query(`UPDATE workspace_document SET fileKey = REGEXP_REPLACE(fileKey,'\\?.*$', ''), updatedAt = updatedAt`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`workspace_document\`
            DROP COLUMN \`fileKey\`
          `);
  }
}
