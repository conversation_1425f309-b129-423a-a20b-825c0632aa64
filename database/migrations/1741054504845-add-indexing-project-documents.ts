import {MigrationInterface, QueryRunner} from "typeorm";

export class addIndexingProjectDocuments1741054504845 implements MigrationInterface {
name = 'addIndexingProjectDocuments1741054504845'

public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE INDEX \`IDX_3e40b0cf35a87dcf8d37cbe7d5\` ON \`project_document_users\` (\`type\`)`);
    await queryRunner.query(`CREATE INDEX \`IDX_899b525cb33db67d69a7d3db81\` ON \`project_document_users\` (\`userId\`)`);
}

public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX \`IDX_899b525cb33db67d69a7d3db81\` ON \`project_document_users\``);
    await queryRunner.query(`DROP INDEX \`IDX_3e40b0cf35a87dcf8d37cbe7d5\` ON \`project_document_users\``);
}

}
