import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendOnEventAddTimeField1665113479987 implements MigrationInterface {
  name = 'ammendOnEventAddTimeField1665113479987';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` ADD \`startTime\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`events\` ADD \`endTime\` varchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`endTime\``);
    await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`startTime\``);
  }
}
