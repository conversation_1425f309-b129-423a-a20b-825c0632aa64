import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawingRevision1701673464483 implements MigrationInterface {
    name = 'addDrawingRevision1701673464483'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`drawing_revisions\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`fileUrl\` text NOT NULL, \`projectDocumentId\` int UNSIGNED NULL, \`version\` float NULL, \`status\` enum ('Obsolete', 'Revision') NULL DEFAULT 'Obsolete', \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'GanttChart') NULL DEFAULT 'TwoDDrawings', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD CONSTRAINT \`FK_781f93fd0a304a4cd3032c7101b\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP FOREIGN KEY \`FK_781f93fd0a304a4cd3032c7101b\``);
        await queryRunner.query(`DROP TABLE \`drawing_revisions\``);
    }

}
