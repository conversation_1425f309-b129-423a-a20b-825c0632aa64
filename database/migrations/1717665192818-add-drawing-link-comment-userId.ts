import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawing<PERSON>inkCommentUserId1717665192818 implements MigrationInterface {
    name = 'addDrawingLinkCommentUserId1717665192818'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` ADD \`userId\` int UNSIGNED NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` DROP COLUMN \`userId\``);
    }

}
