import {MigrationInterface, QueryRunner} from "typeorm";

export class addDrawing<PERSON>inkAttachment1717487865530 implements MigrationInterface {
    name = 'addDrawingLinkAttachment1717487865530'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`CREATE TABLE \`drawing_link_attachment\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`fileUrl\` text NULL, \`fileKey\` text NULL, \`fileName\` text NULL, \`drawingLinkId\` int UNSIGNED NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);    
        await queryRunner.query(`ALTER TABLE \`drawing_link_attachment\` ADD CONSTRAINT \`FK_6c622476d69cc5021b8a062e6db\` FOREIGN KEY (\`drawingLinkId\`) REFERENCES \`drawing_links\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`drawing_link_attachment\` DROP FOREIGN KEY \`FK_6c622476d69cc5021b8a062e6db\``);
        await queryRunner.query(`DROP TABLE \`drawing_link_attachment\``);
    }
}
