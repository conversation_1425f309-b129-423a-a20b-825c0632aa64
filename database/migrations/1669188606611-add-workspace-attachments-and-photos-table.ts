import { MigrationInterface, QueryRunner } from 'typeorm';

export class addWorkspaceAttachmentsAndPhotosTable1669188606611 implements MigrationInterface {
  name = 'addWorkspaceAttachmentsAndPhotosTable1669188606611';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`workspace_photos\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectDocumentId\` int UNSIGNED NOT NULL, \`name\` text NOT NULL, \`fileUrl\` text NOT NULL, \`type\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`workspace_attachments\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectDocumentId\` int UNSIGNED NOT NULL, \`name\` text NOT NULL, \`fileUrl\` text NOT NULL, \`type\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`description\` text NULL`);
    await queryRunner.query(
      `ALTER TABLE \`workspace_photos\` ADD CONSTRAINT \`FK_5a4e46e4d0840075a02c8ac6427\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_attachments\` ADD CONSTRAINT \`FK_659f8aa2a48651882ad46fcd557\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`workspace_attachments\` DROP FOREIGN KEY \`FK_659f8aa2a48651882ad46fcd557\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`workspace_photos\` DROP FOREIGN KEY \`FK_5a4e46e4d0840075a02c8ac6427\``,
    );
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`description\``);
    await queryRunner.query(`DROP TABLE \`workspace_attachments\``);
    await queryRunner.query(`DROP TABLE \`workspace_photos\``);
  }
}
