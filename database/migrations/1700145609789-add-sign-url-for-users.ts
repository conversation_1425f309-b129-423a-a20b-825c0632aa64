import {MigrationInterface, QueryRunner} from "typeorm";

export class addSignUrlForUsers1700145609789 implements MigrationInterface {
    name = 'addSignUrlForUsers1700145609789'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`signUrl\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`signUrl\``);
    }

}
