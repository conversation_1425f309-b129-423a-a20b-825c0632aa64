import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsPublicToSubscriptionPackage1715057600000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE subscription_packages ADD COLUMN isPublic TINYINT(1) NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE subscription_packages DROP COLUMN isPublic`);
  }
}
