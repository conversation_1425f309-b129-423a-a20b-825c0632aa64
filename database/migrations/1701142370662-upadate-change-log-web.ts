import {MigrationInterface, QueryRunner} from "typeorm";

export class upadateChangeLogWeb1701142370662 implements MigrationInterface {
    name = 'upadateChangeLogWeb1701142370662'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`title\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`buttonName\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`buttonUrl\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`buttonUrl\``);
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`buttonName\``);
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`title\``);
    }

}
