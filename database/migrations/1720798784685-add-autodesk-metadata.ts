import {MigrationInterface, QueryRunner} from "typeorm";

export class addAutodeskMetadata1720798784685 implements MigrationInterface {
    name = 'addAutodeskMetadata1720798784685'

    public async up(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`autoDeskMetadata\` json NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`autoDeskMetadata\``);

    }

}
