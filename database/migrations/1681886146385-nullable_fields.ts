import {MigrationInterface, QueryRunner} from "typeorm";

export class nullableFields1681886146385 implements MigrationInterface {
    name = 'nullableFields1681886146385'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`transactionId\` \`transactionId\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`message\` \`message\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`hash\` \`hash\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`data\` \`data\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`paymentMethod\` \`paymentMethod\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`paymentMethod\` \`paymentMethod\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`data\` \`data\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`hash\` \`hash\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`message\` \`message\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`transactionId\` \`transactionId\` text NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
    }

}
