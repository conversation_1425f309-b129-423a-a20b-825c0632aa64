import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateProjectDocumentTreeEntity1659063334145 implements MigrationInterface {
  name = 'updateProjectDocumentTreeEntity1659063334145';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`nsleft\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`nsright\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`mpath\` varchar(255) NULL DEFAULT ''`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`mpath\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`nsright\` int NOT NULL DEFAULT '2'`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`nsleft\` int NOT NULL DEFAULT '1'`);
  }
}
