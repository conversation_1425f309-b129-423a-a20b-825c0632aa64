import { MigrationInterface, QueryRunner } from 'typeorm';

export class addColumnProposedPercentComplete1695373866141 implements MigrationInterface {
  name = 'addColumnProposedPercentComplete1695373866141';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`proposedPercentComplete\` text NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`proposedPercentComplete\``);
  }
}
