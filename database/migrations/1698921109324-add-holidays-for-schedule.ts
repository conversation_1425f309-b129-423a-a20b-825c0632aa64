import {MigrationInterface, QueryRunner} from "typeorm";

export class addHolidaysForSchedule1698921109324 implements MigrationInterface {
    name = 'addHolidaysForSchedule1698921109324'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`holidays\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`holidays\``);
    }

}
