import {MigrationInterface, QueryRunner} from "typeorm";

export class changeLogWeb1701054249650 implements MigrationInterface {
    name = 'changeLogWeb1701054249650'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`imageUrl\` text NULL`);
    }
    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`imageUrl\``);
    }
}
