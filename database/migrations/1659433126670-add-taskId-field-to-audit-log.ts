import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTaskIdFieldToAuditLog1659433126670 implements MigrationInterface {
  name = 'addTaskIdFieldToAuditLog1659433126670';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`taskId\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'Task Comment') NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD CONSTRAINT \`FK_d038705a567505c35b669ab00ca\` FOREIGN KEY (\`taskId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP FOREIGN KEY \`FK_d038705a567505c35b669ab00ca\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'Comment') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`taskId\``);
  }
}
