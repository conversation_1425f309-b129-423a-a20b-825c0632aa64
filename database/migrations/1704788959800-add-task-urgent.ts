import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTaskUrgent1704788959800 implements MigrationInterface {
  name = 'addTaskUrgent1704788959800';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`isUrgent\` tinyint NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`isUrgent\``);
  }
}
