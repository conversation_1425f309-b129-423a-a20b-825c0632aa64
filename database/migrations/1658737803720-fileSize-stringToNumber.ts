import { MigrationInterface, QueryRunner } from 'typeorm';

export class fileSizeStringToNumber1658737803720 implements MigrationInterface {
  name = 'fileSizeStringToNumber1658737803720';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` ADD \`fileSize\` float NULL`);
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`fileSize\` float NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`fileSize\` varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` DROP COLUMN \`fileSize\``);
    await queryRunner.query(`ALTER TABLE \`project_document_folders\` ADD \`fileSize\` varchar(255) NULL`);
  }
}
