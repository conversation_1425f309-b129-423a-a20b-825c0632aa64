import {MigrationInterface, QueryRunner} from "typeorm";

export class addFile<PERSON>eyToWorkspaceAttachment1716362516385 implements MigrationInterface {
  name = 'addFileKeyToWorkspaceAttachment1716362516385'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`workspace_attachments\` ADD \`fileKey\` text NULL`);

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE workspace_attachments
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`workspace_attachments\` DROP COLUMN \`fileKey\``);
  }
}
