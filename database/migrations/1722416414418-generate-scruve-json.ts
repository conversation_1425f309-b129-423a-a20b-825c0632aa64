import { MigrationInterface, QueryRunner } from "typeorm";
import axios from 'axios';
import { CsvToJsonService } from "@modules/conversion/csv-to-json.service";
import { ProjectEntity } from "@modules/project/entity/project.entity";
import { <PERSON><PERSON><PERSON>, Not } from "typeorm";
import { FileUpload } from "graphql-upload";
import { ProjectService } from "@modules/project/project.service";
import { TMOneService } from "@modules/integration/tmOneObs/tmOneObs.service";
import { HttpService } from "@nestjs/axios";

export class generateScruveJson1722416414418 implements MigrationInterface {
    name = 'generateScruveJson1722416414418'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const projectEntityRepository = queryRunner.manager.getRepository(ProjectEntity);
        const csvToJsonService = new CsvToJsonService();
        const projects = await queryRunner.manager.find(ProjectEntity);

        const httpService = new HttpService();
        const tmOneService = new TMOneService(httpService);

        const projectService = new ProjectService(
            projectEntityRepository,
            tmOneService
        );
    
        for (const project of projects) {
            let updatesNeeded = false;
    
            if (project.fileUrlProgress && !project.fileUrlProgressJson) {
                const url = await projectService.getPresignedUrl(project, 'fileProgressKey');

                // convert the fileUrlProgress to a Buffer
                const fileUpload = await this.downloadFile(url);

                if (!fileUpload) {
                    continue;
                }

                const jsonResult = await csvToJsonService.parseScurveFile(fileUpload, 'physical');
                project.fileUrlProgressJson = jsonResult;
                updatesNeeded = true;
            }
    
            if (project.fileUrlProgressFinance && !project.fileUrlProgressFinanceJson) {
                const url = await projectService.getPresignedUrl(project, 'fileProgressFinanceKey');

                // convert the fileUrlProgressFinance to a Buffer
                const fileUpload = await this.downloadFile(url);

                if (!fileUpload) {
                    continue;
                }

                const jsonResult = await csvToJsonService.parseScurveFile(fileUpload, 'financial');
                project.fileUrlProgressFinanceJson = jsonResult;
                updatesNeeded = true;
            }
    
            if (updatesNeeded) {
                await queryRunner.manager.save(project);
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const projects = await queryRunner.manager.find(ProjectEntity, {
            where: [
                { fileUrlProgressJson: Not(IsNull()) },
                { fileUrlProgressFinanceJson: Not(IsNull()) }
            ]
        });

        for (const project of projects) {
            project.fileUrlProgressJson = null;
            project.fileUrlProgressFinanceJson = null;
            await queryRunner.manager.save(project);
        }
    }

    private async downloadFile(url: string): Promise<FileUpload>  {
        try {
            const response = await axios({
                url,
                method: 'GET',
                responseType: 'stream'
            });
    
            const filename = url.split('/').pop();
    
            return {
                filename,
                mimetype: 'text/csv',
                encoding: '7bit',
                createReadStream: () => response.data
            };
        } catch (error) {
            console.error('Error downloading file:', error);
            return null;
        }
    }
}
