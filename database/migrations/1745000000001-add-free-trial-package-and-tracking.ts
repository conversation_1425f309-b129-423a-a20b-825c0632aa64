import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFreeTrialPackageAndTracking1745000000001 implements MigrationInterface {
  name = 'AddFreeTrialPackageAndTracking1745000000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add hasUsedFreeTrial column to companies table
    await queryRunner.query(`ALTER TABLE \`companies\` ADD \`hasUsedFreeTrial\` boolean NOT NULL DEFAULT false`);

    // Create a new free trial subscription package
    await queryRunner.query(`
      INSERT INTO \`subscription_packages\` 
      (
        \`title\`, 
        \`description\`, 
        \`amount\`, 
        \`availableDuration\`, 
        \`totalProjects\`, 
        \`totalUsers\`, 
        \`allowTask\`, 
        \`allowProjectDocument\`, 
        \`allowWorkProgramme\`, 
        \`allowCorrespondence\`, 
        \`allowWorkspaceDocument\`, 
        \`allowWorkspaceTemplate\`, 
        \`allowDrawing\`, 
        \`allowBimModel\`, 
        \`allowPhoto\`, 
        \`allowScheduleChart\`, 
        \`allowScheduleActivity\`, 
        \`allowDashboard\`, 
        \`allowEmailCorrespondence\`,
        \`createdAt\`,
        \`updatedAt\`
      ) 
      VALUES 
      (
        '30-Day Free Trial', 
        'Try all features free for 30 days (except BIM and Schedule).', 
        0, 
        1, 
        1, 
        5, 
        true, 
        true, 
        true, 
        true, 
        true, 
        true, 
        true, 
        false, 
        true, 
        false, 
        false, 
        true, 
        true,
        NOW(),
        NOW()
      )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the free trial package
    await queryRunner.query(`DELETE FROM \`subscription_packages\` WHERE \`title\` = '30-Day Free Trial'`);
    
    // Remove hasUsedFreeTrial column from companies table
    await queryRunner.query(`ALTER TABLE \`companies\` DROP COLUMN \`hasUsedFreeTrial\``);
  }
}
