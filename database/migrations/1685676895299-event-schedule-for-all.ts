import {MigrationInterface, QueryRunner} from "typeorm";

export class eventScheduleForAll1685676895299 implements MigrationInterface {
    name = 'eventScheduleForAll1685676895299'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`events\` ADD \`scheduleForAll\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`scheduleForAll\``);
    }

}
