import { MigrationInterface, QueryRunner } from 'typeorm';

export class addedEventIsAllDay1665756880763 implements MigrationInterface {
  name = 'addedEventIsAllDay1665756880763';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` ADD \`isAllDay\` tinyint NOT NULL DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`isAllDay\``);
  }
}
