import {MigrationInterface, QueryRunner} from "typeorm";

export class addStartAndFinishDelay1710907168201 implements MigrationInterface {
    name = 'addStartAndFinishDelay1710907168201'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`status\` \`status\` enum ('Pending', 'Upcoming', 'InProgress', 'Hold', 'Completed', 'Delay', 'StartDelay', 'FinishDelay') NOT NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`proposedStatus\` \`proposedStatus\` enum ('Pending', 'Upcoming', 'InProgress', 'Hold', 'Completed', 'Delay', 'StartDelay', 'FinishDelay') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`proposedStatus\` \`proposedStatus\` enum ('Pending', 'InProgress', 'Hold', 'Completed', 'Delay') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` CHANGE \`status\` \`status\` enum ('Pending', 'Upcoming', 'InProgress', 'Hold', 'Completed', 'Delay') CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NOT NULL DEFAULT 'Pending'`);
    }

}
