import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsNotify1725728133059 implements MigrationInterface {
    name = 'addIsNotify1725728133059'

    public async up(queryRunner: QueryRunner): Promise<void> {
 
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`isNotify\` tinyint NULL DEFAULT 0`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`isNotify\``);
       
    }

}
