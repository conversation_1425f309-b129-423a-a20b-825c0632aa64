import {MigrationInterface, QueryRunner} from "typeorm";

export class changeRawmetadataNullable1740731554088 implements MigrationInterface {
    name = 'changeRawmetadataNullable1740731554088'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`email_assets\` CHANGE \`rawMetadata\` \`rawMetadata\` varchar(255) NULL`);       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`email_assets\` CHANGE \`rawMetadata\` \`rawMetadata\` varchar(255) NOT NULL`);       
    }
}
