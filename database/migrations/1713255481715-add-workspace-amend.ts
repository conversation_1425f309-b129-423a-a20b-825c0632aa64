import {MigrationInterface, QueryRunner} from "typeorm";

export class addWorkspaceAmend1713255481715 implements MigrationInterface {
    name = 'addWorkspaceAmend1713255481715'

    public async up(queryRunner: QueryRunner): Promise<void> {      
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Draft', 'Submitted', 'InReview', 'Rejected', 'Approved', 'InProgress', 'Amend') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Draft', 'Submitted', 'InReview', 'Rejected', 'Approved', 'InProgress') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NULL`);      
    }

}
