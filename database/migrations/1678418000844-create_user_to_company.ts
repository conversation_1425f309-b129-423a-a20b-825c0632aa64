import {MigrationInterface, QueryRunner} from "typeorm";

export class createUserToCompany1678418000844 implements MigrationInterface {
    name = 'createUserToCompany1678418000844'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`user_to_company\` (\`companiesId\` int UNSIGNED NOT NULL, \`usersId\` int UNSIGNED NOT NULL, INDEX \`IDX_22cb753ffea586ff3c0d278a3f\` (\`companiesId\`), INDEX \`IDX_bd1476d398594d91615ec3061a\` (\`usersId\`), PRIMARY KEY (\`companiesId\`, \`usersId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`DROP INDEX \`IDX_bd1476d398594d91615ec3061a\` ON \`user_to_company\``);
        await queryRunner.query(`DROP INDEX \`IDX_22cb753ffea586ff3c0d278a3f\` ON \`user_to_company\``);
        await queryRunner.query(`DROP TABLE \`user_to_company\``);
    }

}
