import {MigrationInterface, QueryRunner} from "typeorm";

export class addAuditLogActions1694156042994 implements MigrationInterface {
    name = 'addAuditLogActions1694156042994'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }
}
