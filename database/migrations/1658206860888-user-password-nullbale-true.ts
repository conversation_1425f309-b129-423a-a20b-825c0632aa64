import { MigrationInterface, QueryRunner } from 'typeorm';

export class userPasswordNullbaleTrue1658206860888 implements MigrationInterface {
  name = 'userPasswordNullbaleTrue1658206860888';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`password\` \`password\` varchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` CHANGE \`password\` \`password\` varchar(255) NOT NULL`);
  }
}
