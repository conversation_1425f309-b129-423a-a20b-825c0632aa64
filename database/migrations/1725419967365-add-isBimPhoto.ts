import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsBimPhoto1725419967365 implements MigrationInterface {
    name = 'addIsBimPhoto1725419967365'

    public async up(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`isBimPhoto\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`isBimPhoto\``);
    }

}
