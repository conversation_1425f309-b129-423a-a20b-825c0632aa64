import { MigrationInterface, QueryRunner } from 'typeorm';

export class addSubGroup1697169975091 implements MigrationInterface {
  name = 'addSubGroup1697169975091';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_groups\` ADD \`projectGroupId\` int UNSIGNED NULL`);
    await queryRunner.query(`ALTER TABLE \`project_groups\` ADD \`mpath\` varchar(255) NULL DEFAULT ''`);
    await queryRunner.query(
      `ALTER TABLE \`project_groups\` ADD CONSTRAINT \`FK_736d632cbc74915612329bef89b\` FOREIGN KEY (\`projectGroupId\`) REFERENCES \`project_groups\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_groups\` DROP COLUMN \`mpath\``);
    await queryRunner.query(`ALTER TABLE \`project_groups\` DROP COLUMN \`projectGroupId\``);
    await queryRunner.query(`ALTER TABLE \`project_groups\` DROP FOREIGN KEY \`FK_736d632cbc74915612329bef89b\``);
  }
}
