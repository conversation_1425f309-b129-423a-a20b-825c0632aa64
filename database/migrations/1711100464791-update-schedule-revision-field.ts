import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateScheduleRevisionField1711093325812 implements MigrationInterface {
  name = 'updateScheduleRevisionField1711093325812';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('UPDATE `project_schedules` SET `revision` = 0 WHERE `revision` IS NULL');
    await queryRunner.query("ALTER TABLE `project_schedules` CHANGE `revision` `revision` int NOT NULL DEFAULT '0'");
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE `project_schedules` CHANGE `revision` `revision` int NULL');
  }
}
