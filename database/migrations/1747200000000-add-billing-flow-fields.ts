import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddB<PERSON>ing<PERSON>lowFields1747200000000 implements MigrationInterface {
  name = 'AddBillingFlowFields1747200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new fields to company_subscriptions table
    await queryRunner.query(`ALTER TABLE \`company_subscriptions\`
      ADD \`seatCount\` int NOT NULL DEFAULT 1,
      ADD \`isYearly\` boolean NOT NULL DEFAULT false,
      ADD \`nextBillingDate\` datetime NULL,
      ADD \`creditBalance\` decimal(10,2) NOT NULL DEFAULT 0.00
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`company_subscriptions\`
      DROP COLUMN \`seatCount\`,
      DROP COLUMN \`isYearly\`,
      DROP COLUMN \`nextBillingDate\`,
      DROP COLUMN \`creditBalance\`
    `);
  }
}
