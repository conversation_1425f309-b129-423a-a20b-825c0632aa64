import {MigrationInterface, QueryRunner} from "typeorm";

export class workspaceDynamicFlow1700096334015 implements MigrationInterface {
    name = 'workspaceDynamicFlow1700096334015'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` ADD \`assigneeNo\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`currentUserId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD UNIQUE INDEX \`IDX_5cf4197c260ef2689dbb600241\` (\`currentUserId\`)`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`workflow\` enum ('Linear', 'Dynamic') NULL`);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Approved', 'Amend') NULL DEFAULT 'Pending'`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`REL_5cf4197c260ef2689dbb600241\` ON \`project_documents\` (\`currentUserId\`)`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_5cf4197c260ef2689dbb6002413\` FOREIGN KEY (\`currentUserId\`) REFERENCES \`request_for_signatures\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_5cf4197c260ef2689dbb6002413\``);
        await queryRunner.query(`DROP INDEX \`REL_5cf4197c260ef2689dbb600241\` ON \`project_documents\``);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Approved') NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`workflow\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP INDEX \`IDX_5cf4197c260ef2689dbb600241\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`currentUserId\``);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` DROP COLUMN \`assigneeNo\``);
    }

}
