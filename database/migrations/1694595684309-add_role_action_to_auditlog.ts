import {MigrationInterface, QueryRunner} from "typeorm";

export class addRoleActionToAuditlog1694595684309 implements MigrationInterface {
    name = 'addRoleActionToAuditlog1694595684309'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddRole', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined', 'Shared') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined', 'Shared') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }

}
