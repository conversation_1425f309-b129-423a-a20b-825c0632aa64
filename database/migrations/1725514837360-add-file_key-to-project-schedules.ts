import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFileKeyToProjectSchedules1725514837360 implements MigrationInterface {
  name = 'addFileKeyToProjectSchedules1725514837360';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`fileKey\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`fileKey\``);
  }
}
