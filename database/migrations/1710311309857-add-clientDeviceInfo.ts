import { MigrationInterface, QueryRunner } from 'typeorm';

export class addClientDeviceInfo1710311309857 implements MigrationInterface {
  name = 'addClientDeviceInfo1710311309857';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`clientDeviceInfo\` json NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`clientDeviceInfo\``);
  }
}
