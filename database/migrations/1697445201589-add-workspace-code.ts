import {MigrationInterface, QueryRunner} from "typeorm";

export class addWorkspaceCode1697445201589 implements MigrationInterface {
    name = 'addWorkspaceCode1697445201589'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` ADD \`code\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`groupCode\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`groupCode\``);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` DROP COLUMN \`code\``);
    }

}
