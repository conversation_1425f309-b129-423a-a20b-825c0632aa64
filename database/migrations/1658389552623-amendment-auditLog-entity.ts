import { MigrationInterface, QueryRunner } from 'typeorm';

export class amendmentAuditLogEntity1658389552623 implements MigrationInterface {
  name = 'amendmentAuditLogEntity1658389552623';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP FOREIGN KEY \`FK_efa597fecfef3d3ec683d693324\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`companyId\` \`projectId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD CONSTRAINT \`FK_f9f426dea4567b98c3ad0f372d1\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP FOREIGN KEY \`FK_f9f426dea4567b98c3ad0f372d1\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`projectId\` \`companyId\` int UNSIGNED NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD CONSTRAINT \`FK_efa597fecfef3d3ec683d693324\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
