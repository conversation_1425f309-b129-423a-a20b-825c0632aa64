import {MigrationInterface, QueryRunner} from "typeorm";

export class addSsigneesToSchedules1693538545631 implements MigrationInterface {
    name = 'addSsigneesToSchedules1693538545631'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedule_assignees\` (\`schedulesId\` int UNSIGNED NOT NULL, \`projectUsersId\` int UNSIGNED NOT NULL, INDEX \`IDX_ffa67065b92a7ea49fa44b6918\` (\`schedulesId\`), INDEX \`IDX_c57868fdcd97c1998cf286b2cd\` (\`projectUsersId\`), PRIMARY KEY (\`schedulesId\`, \`projectUsersId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`schedule_copies\` (\`schedulesId\` int UNSIGNED NOT NULL, \`projectUsersId\` int UNSIGNED NOT NULL, INDEX \`IDX_cadf2d92cdcc647ea5910c7802\` (\`schedulesId\`), INDEX \`IDX_a28e8607844595b051ebdbd670\` (\`projectUsersId\`), PRIMARY KEY (\`schedulesId\`, \`projectUsersId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedule_assignees\` ADD CONSTRAINT \`FK_ffa67065b92a7ea49fa44b6918c\` FOREIGN KEY (\`schedulesId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`schedule_assignees\` ADD CONSTRAINT \`FK_c57868fdcd97c1998cf286b2cd9\` FOREIGN KEY (\`projectUsersId\`) REFERENCES \`project_users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedule_copies\` ADD CONSTRAINT \`FK_cadf2d92cdcc647ea5910c78028\` FOREIGN KEY (\`schedulesId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`schedule_copies\` ADD CONSTRAINT \`FK_a28e8607844595b051ebdbd670b\` FOREIGN KEY (\`projectUsersId\`) REFERENCES \`project_users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedule_copies\` DROP FOREIGN KEY \`FK_a28e8607844595b051ebdbd670b\``);
        await queryRunner.query(`ALTER TABLE \`schedule_copies\` DROP FOREIGN KEY \`FK_cadf2d92cdcc647ea5910c78028\``);
        await queryRunner.query(`ALTER TABLE \`schedule_assignees\` DROP FOREIGN KEY \`FK_c57868fdcd97c1998cf286b2cd9\``);
        await queryRunner.query(`ALTER TABLE \`schedule_assignees\` DROP FOREIGN KEY \`FK_ffa67065b92a7ea49fa44b6918c\``);
        await queryRunner.query(`DROP INDEX \`IDX_a28e8607844595b051ebdbd670\` ON \`schedule_copies\``);
        await queryRunner.query(`DROP INDEX \`IDX_cadf2d92cdcc647ea5910c7802\` ON \`schedule_copies\``);
        await queryRunner.query(`DROP TABLE \`schedule_copies\``);
        await queryRunner.query(`DROP INDEX \`IDX_c57868fdcd97c1998cf286b2cd\` ON \`schedule_assignees\``);
        await queryRunner.query(`DROP INDEX \`IDX_ffa67065b92a7ea49fa44b6918\` ON \`schedule_assignees\``);
        await queryRunner.query(`DROP TABLE \`schedule_assignees\``);
    }

}
