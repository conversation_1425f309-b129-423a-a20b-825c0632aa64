import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsReceiveMemo1705052416598 implements MigrationInterface {
    name = 'addIsReceiveMemo1705052416598'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`isMemoReceive\` tinyint NULL DEFAULT 0`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
      
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`isMemoReceive\``);
      
    }

}
