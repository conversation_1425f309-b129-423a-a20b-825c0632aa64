import {MigrationInterface, QueryRunner} from "typeorm";

export class addDocumentIdWorkspaceDoc1683088002594 implements MigrationInterface {
    name = 'addDocumentIdWorkspaceDoc1683088002594'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`workspace_document\` ADD \`documentId\` int UNSIGNED NOT NULL`);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_22cb753ffea586ff3c0d278a3fb\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` ADD CONSTRAINT \`FK_bd1476d398594d91615ec3061af\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_bd1476d398594d91615ec3061af\``);
        // await queryRunner.query(`ALTER TABLE \`user_to_company\` DROP FOREIGN KEY \`FK_22cb753ffea586ff3c0d278a3fb\``);
        await queryRunner.query(`ALTER TABLE \`workspace_document\` DROP COLUMN \`documentId\``);
    }

}
