import { MigrationInterface, QueryRunner } from 'typeorm';

export class addUsersRemovedAt1736838921933 implements MigrationInterface {
  name = 'addUsersRemovedAt1736838921933'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` ADD \`removedAt\` datetime NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`removedAt\``);
  }
}
