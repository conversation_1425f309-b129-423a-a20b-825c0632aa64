import { MigrationInterface, QueryRunner } from 'typeorm';

export class initTable1657703873352 implements MigrationInterface {
  name = 'initTable1657703873352';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`admins\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`type\` varchar(20) NOT NULL, \`isActive\` tinyint NOT NULL DEFAULT 1, \`name\` varchar(200) NOT NULL, \`email\` varchar(200) NOT NULL, \`password\` varchar(255) NOT NULL, \`refreshToken\` varchar(255) NULL, \`resetPasswordToken\` text NULL, UNIQUE INDEX \`IDX_051db7d37d478a69a7432df147\` (\`email\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_teams\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`checklist_items\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`checklistId\` int UNSIGNED NOT NULL, \`description\` text NOT NULL, \`completed\` tinyint NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`checklists\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`taskId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`documents\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`fileUrl\` varchar(255) NOT NULL, \`type\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`task_comments\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`taskId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NOT NULL, \`message\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`tasks_attachments\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`taskId\` int UNSIGNED NOT NULL, \`attachmentRef\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`tasks\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, \`description\` varchar(255) NULL, \`dueDate\` datetime NULL, \`status\` varchar(255) NOT NULL DEFAULT 'In Progress', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_users\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`projectId\` int UNSIGNED NOT NULL, \`role\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`projects\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`companyId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NOT NULL, \`refNo\` varchar(255) NULL, \`title\` varchar(255) NOT NULL, \`description\` text NULL, \`status\` varchar(255) NOT NULL DEFAULT 'In Progress', \`managedBy\` varchar(255) NULL, \`deputySuperintendent\` varchar(255) NULL, \`client\` varchar(255) NULL, \`contractor\` varchar(255) NULL, \`startDate\` datetime NULL, \`completionDate\` datetime NULL, \`cnsConsultant\` varchar(255) NULL, \`mneConsultant\` varchar(255) NULL, \`qsConsultant\` varchar(255) NULL, \`environmentConsultant\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`contact_companies\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`name\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`contacts\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`contactCompanyId\` int UNSIGNED NOT NULL, \`projectId\` int UNSIGNED NOT NULL, \`name\` varchar(255) NOT NULL, \`phoneNo\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`notification_transactions\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`title\` varchar(255) NOT NULL, \`content\` varchar(255) NOT NULL, \`thumbnail\` text NOT NULL, \`deeplink\` text NOT NULL, \`read\` tinyint NOT NULL, \`type\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`users\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`companyId\` int UNSIGNED NULL, \`name\` varchar(255) CHARACTER SET "utf8mb4" NULL, \`email\` varchar(255) NOT NULL, \`password\` varchar(255) NOT NULL, \`facebookId\` varchar(255) NULL, \`googleId\` varchar(255) NULL, \`appleId\` varchar(255) NULL, \`phoneNo\` varchar(255) NULL, \`avatar\` text NULL, \`reportTo\` varchar(255) NULL, \`position\` varchar(255) NULL, \`lastLogin\` datetime NULL, \`isEmailVerified\` tinyint NOT NULL DEFAULT 0, \`type\` varchar(20) NOT NULL, \`isFirstTimeLogin\` tinyint NOT NULL DEFAULT 1, \`refreshToken\` text NULL, \`resetPasswordToken\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`sales_orders\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`subscriptionPackageId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NOT NULL, \`companyId\` int UNSIGNED NOT NULL, \`total\` float NOT NULL, \`paymentUrl\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`subscription_packages\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`description\` text NOT NULL, \`amount\` float NOT NULL, \`availableDuration\` int NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`company_subscriptions\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`subscriptionPackageId\` int UNSIGNED NOT NULL, \`companyId\` int UNSIGNED NOT NULL, \`subscriptionEndDate\` datetime NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`companies\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`ownerId\` int UNSIGNED NULL, \`name\` varchar(255) NOT NULL, \`avatar\` varchar(255) NULL, UNIQUE INDEX \`REL_6dcdcbb7d72f64602307ec4ab3\` (\`ownerId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`audit_logs\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`userId\` int UNSIGNED NOT NULL, \`companyId\` int UNSIGNED NOT NULL, \`module\` varchar(255) NOT NULL, \`action\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_team_users\` (\`projectTeamsId\` int UNSIGNED NOT NULL, \`usersId\` int UNSIGNED NOT NULL, INDEX \`IDX_6e52b865081460d28d0ad493c4\` (\`projectTeamsId\`), INDEX \`IDX_58394028b9d350a58eb1f5b796\` (\`usersId\`), PRIMARY KEY (\`projectTeamsId\`, \`usersId\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`task_assignees\` (\`tasksId\` int UNSIGNED NOT NULL, \`projectUsersId\` int UNSIGNED NOT NULL, INDEX \`IDX_836169568c5c001ee34e7aa78f\` (\`tasksId\`), INDEX \`IDX_ff1bb0fb09f4f4f6a08a5f3449\` (\`projectUsersId\`), PRIMARY KEY (\`tasksId\`, \`projectUsersId\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`tasks_documents\` (\`tasksId\` int UNSIGNED NOT NULL, \`documentsId\` int UNSIGNED NOT NULL, INDEX \`IDX_6a30d244f428668521cfdd44dc\` (\`tasksId\`), INDEX \`IDX_81e478269eafb8b924c2f5d83e\` (\`documentsId\`), PRIMARY KEY (\`tasksId\`, \`documentsId\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`contact_pics\` (\`contactsId\` int UNSIGNED NOT NULL, \`usersId\` int UNSIGNED NOT NULL, INDEX \`IDX_3743c86d0d1f07c37b88c9b02d\` (\`contactsId\`), INDEX \`IDX_66a6137358997e2640664acb55\` (\`usersId\`), PRIMARY KEY (\`contactsId\`, \`usersId\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`company_subscription_transactions\` (\`companiesId\` int UNSIGNED NOT NULL, \`salesOrdersId\` int UNSIGNED NOT NULL, INDEX \`IDX_61b4a2374b7c2f1c95c7c4c8b0\` (\`companiesId\`), INDEX \`IDX_a92b94ac6a377ae30b5750e88c\` (\`salesOrdersId\`), PRIMARY KEY (\`companiesId\`, \`salesOrdersId\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_teams\` ADD CONSTRAINT \`FK_d71c230aff02473fe1ecb50942d\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`checklist_items\` ADD CONSTRAINT \`FK_318b40686e72c5ede465984cf9e\` FOREIGN KEY (\`checklistId\`) REFERENCES \`checklists\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`checklists\` ADD CONSTRAINT \`FK_4e21015c59b988c7c2096e112ee\` FOREIGN KEY (\`taskId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` ADD CONSTRAINT \`FK_ba265816ca1d93f51083e06c520\` FOREIGN KEY (\`taskId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` ADD CONSTRAINT \`FK_be77588a6727c9a27075b590048\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_attachments\` ADD CONSTRAINT \`FK_28e78d448cc335bd4fe2bf2c242\` FOREIGN KEY (\`taskId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD CONSTRAINT \`FK_e08fca67ca8966e6b9914bf2956\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` ADD CONSTRAINT \`FK_6ebc83af455ff1ed9573c823e23\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` ADD CONSTRAINT \`FK_1905d9d76173d09c07ba1f0cd84\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`projects\` ADD CONSTRAINT \`FK_87fa45e3f4517658b98e5c55b9c\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`projects\` ADD CONSTRAINT \`FK_361a53ae58ef7034adc3c06f09f\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`contacts\` ADD CONSTRAINT \`FK_c577fcea897a095c7e5d1cebaf1\` FOREIGN KEY (\`contactCompanyId\`) REFERENCES \`contact_companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`contacts\` ADD CONSTRAINT \`FK_f52f1002a82a47b15c7ac231441\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` ADD CONSTRAINT \`FK_7a42f9a888603b493e08b320f43\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD CONSTRAINT \`FK_6f9395c9037632a31107c8a9e58\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` ADD CONSTRAINT \`FK_cbaa3873657695866618b5c158a\` FOREIGN KEY (\`subscriptionPackageId\`) REFERENCES \`subscription_packages\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` ADD CONSTRAINT \`FK_efea3fff06a644a0ced31cf03cc\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` ADD CONSTRAINT \`FK_49b3a29fc5f1bdc25960355292c\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscriptions\` ADD CONSTRAINT \`FK_c0d1bfbc7c00437105b87a8ed9e\` FOREIGN KEY (\`subscriptionPackageId\`) REFERENCES \`subscription_packages\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscriptions\` ADD CONSTRAINT \`FK_cae9d2297645c98f23978e1e9c1\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`companies\` ADD CONSTRAINT \`FK_6dcdcbb7d72f64602307ec4ab39\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD CONSTRAINT \`FK_cfa83f61e4d27a87fcae1e025ab\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD CONSTRAINT \`FK_efa597fecfef3d3ec683d693324\` FOREIGN KEY (\`companyId\`) REFERENCES \`companies\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_team_users\` ADD CONSTRAINT \`FK_6e52b865081460d28d0ad493c41\` FOREIGN KEY (\`projectTeamsId\`) REFERENCES \`project_teams\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_team_users\` ADD CONSTRAINT \`FK_58394028b9d350a58eb1f5b796f\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_assignees\` ADD CONSTRAINT \`FK_836169568c5c001ee34e7aa78f7\` FOREIGN KEY (\`tasksId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_assignees\` ADD CONSTRAINT \`FK_ff1bb0fb09f4f4f6a08a5f34498\` FOREIGN KEY (\`projectUsersId\`) REFERENCES \`project_users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` ADD CONSTRAINT \`FK_6a30d244f428668521cfdd44dcd\` FOREIGN KEY (\`tasksId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` ADD CONSTRAINT \`FK_81e478269eafb8b924c2f5d83e7\` FOREIGN KEY (\`documentsId\`) REFERENCES \`documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_pics\` ADD CONSTRAINT \`FK_3743c86d0d1f07c37b88c9b02d6\` FOREIGN KEY (\`contactsId\`) REFERENCES \`contacts\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_pics\` ADD CONSTRAINT \`FK_66a6137358997e2640664acb55b\` FOREIGN KEY (\`usersId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscription_transactions\` ADD CONSTRAINT \`FK_61b4a2374b7c2f1c95c7c4c8b0e\` FOREIGN KEY (\`companiesId\`) REFERENCES \`companies\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscription_transactions\` ADD CONSTRAINT \`FK_a92b94ac6a377ae30b5750e88cd\` FOREIGN KEY (\`salesOrdersId\`) REFERENCES \`sales_orders\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`company_subscription_transactions\` DROP FOREIGN KEY \`FK_a92b94ac6a377ae30b5750e88cd\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscription_transactions\` DROP FOREIGN KEY \`FK_61b4a2374b7c2f1c95c7c4c8b0e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_pics\` DROP FOREIGN KEY \`FK_66a6137358997e2640664acb55b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_pics\` DROP FOREIGN KEY \`FK_3743c86d0d1f07c37b88c9b02d6\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` DROP FOREIGN KEY \`FK_81e478269eafb8b924c2f5d83e7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`tasks_documents\` DROP FOREIGN KEY \`FK_6a30d244f428668521cfdd44dcd\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_assignees\` DROP FOREIGN KEY \`FK_ff1bb0fb09f4f4f6a08a5f34498\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_assignees\` DROP FOREIGN KEY \`FK_836169568c5c001ee34e7aa78f7\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_team_users\` DROP FOREIGN KEY \`FK_58394028b9d350a58eb1f5b796f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_team_users\` DROP FOREIGN KEY \`FK_6e52b865081460d28d0ad493c41\``,
    );
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP FOREIGN KEY \`FK_efa597fecfef3d3ec683d693324\``);
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP FOREIGN KEY \`FK_cfa83f61e4d27a87fcae1e025ab\``);
    await queryRunner.query(`ALTER TABLE \`companies\` DROP FOREIGN KEY \`FK_6dcdcbb7d72f64602307ec4ab39\``);
    await queryRunner.query(
      `ALTER TABLE \`company_subscriptions\` DROP FOREIGN KEY \`FK_cae9d2297645c98f23978e1e9c1\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`company_subscriptions\` DROP FOREIGN KEY \`FK_c0d1bfbc7c00437105b87a8ed9e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` DROP FOREIGN KEY \`FK_49b3a29fc5f1bdc25960355292c\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` DROP FOREIGN KEY \`FK_efea3fff06a644a0ced31cf03cc\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sales_orders\` DROP FOREIGN KEY \`FK_cbaa3873657695866618b5c158a\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_6f9395c9037632a31107c8a9e58\``);
    await queryRunner.query(
      `ALTER TABLE \`notification_transactions\` DROP FOREIGN KEY \`FK_7a42f9a888603b493e08b320f43\``,
    );
    await queryRunner.query(`ALTER TABLE \`contacts\` DROP FOREIGN KEY \`FK_f52f1002a82a47b15c7ac231441\``);
    await queryRunner.query(`ALTER TABLE \`contacts\` DROP FOREIGN KEY \`FK_c577fcea897a095c7e5d1cebaf1\``);
    await queryRunner.query(`ALTER TABLE \`projects\` DROP FOREIGN KEY \`FK_361a53ae58ef7034adc3c06f09f\``);
    await queryRunner.query(`ALTER TABLE \`projects\` DROP FOREIGN KEY \`FK_87fa45e3f4517658b98e5c55b9c\``);
    await queryRunner.query(
      `ALTER TABLE \`project_users\` DROP FOREIGN KEY \`FK_1905d9d76173d09c07ba1f0cd84\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_users\` DROP FOREIGN KEY \`FK_6ebc83af455ff1ed9573c823e23\``,
    );
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP FOREIGN KEY \`FK_e08fca67ca8966e6b9914bf2956\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks_attachments\` DROP FOREIGN KEY \`FK_28e78d448cc335bd4fe2bf2c242\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` DROP FOREIGN KEY \`FK_be77588a6727c9a27075b590048\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`task_comments\` DROP FOREIGN KEY \`FK_ba265816ca1d93f51083e06c520\``,
    );
    await queryRunner.query(`ALTER TABLE \`checklists\` DROP FOREIGN KEY \`FK_4e21015c59b988c7c2096e112ee\``);
    await queryRunner.query(
      `ALTER TABLE \`checklist_items\` DROP FOREIGN KEY \`FK_318b40686e72c5ede465984cf9e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_teams\` DROP FOREIGN KEY \`FK_d71c230aff02473fe1ecb50942d\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_a92b94ac6a377ae30b5750e88c\` ON \`company_subscription_transactions\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_61b4a2374b7c2f1c95c7c4c8b0\` ON \`company_subscription_transactions\``,
    );
    await queryRunner.query(`DROP TABLE \`company_subscription_transactions\``);
    await queryRunner.query(`DROP INDEX \`IDX_66a6137358997e2640664acb55\` ON \`contact_pics\``);
    await queryRunner.query(`DROP INDEX \`IDX_3743c86d0d1f07c37b88c9b02d\` ON \`contact_pics\``);
    await queryRunner.query(`DROP TABLE \`contact_pics\``);
    await queryRunner.query(`DROP INDEX \`IDX_81e478269eafb8b924c2f5d83e\` ON \`tasks_documents\``);
    await queryRunner.query(`DROP INDEX \`IDX_6a30d244f428668521cfdd44dc\` ON \`tasks_documents\``);
    await queryRunner.query(`DROP TABLE \`tasks_documents\``);
    await queryRunner.query(`DROP INDEX \`IDX_ff1bb0fb09f4f4f6a08a5f3449\` ON \`task_assignees\``);
    await queryRunner.query(`DROP INDEX \`IDX_836169568c5c001ee34e7aa78f\` ON \`task_assignees\``);
    await queryRunner.query(`DROP TABLE \`task_assignees\``);
    await queryRunner.query(`DROP INDEX \`IDX_58394028b9d350a58eb1f5b796\` ON \`project_team_users\``);
    await queryRunner.query(`DROP INDEX \`IDX_6e52b865081460d28d0ad493c4\` ON \`project_team_users\``);
    await queryRunner.query(`DROP TABLE \`project_team_users\``);
    await queryRunner.query(`DROP TABLE \`audit_logs\``);
    await queryRunner.query(`DROP INDEX \`REL_6dcdcbb7d72f64602307ec4ab3\` ON \`companies\``);
    await queryRunner.query(`DROP TABLE \`companies\``);
    await queryRunner.query(`DROP TABLE \`company_subscriptions\``);
    await queryRunner.query(`DROP TABLE \`subscription_packages\``);
    await queryRunner.query(`DROP TABLE \`sales_orders\``);
    await queryRunner.query(`DROP TABLE \`users\``);
    await queryRunner.query(`DROP TABLE \`notification_transactions\``);
    await queryRunner.query(`DROP TABLE \`contacts\``);
    await queryRunner.query(`DROP TABLE \`contact_companies\``);
    await queryRunner.query(`DROP TABLE \`projects\``);
    await queryRunner.query(`DROP TABLE \`project_users\``);
    await queryRunner.query(`DROP TABLE \`tasks\``);
    await queryRunner.query(`DROP TABLE \`tasks_attachments\``);
    await queryRunner.query(`DROP TABLE \`task_comments\``);
    await queryRunner.query(`DROP TABLE \`documents\``);
    await queryRunner.query(`DROP TABLE \`checklists\``);
    await queryRunner.query(`DROP TABLE \`checklist_items\``);
    await queryRunner.query(`DROP TABLE \`project_teams\``);
    await queryRunner.query(`DROP INDEX \`IDX_051db7d37d478a69a7432df147\` ON \`admins\``);
    await queryRunner.query(`DROP TABLE \`admins\``);
  }
}
