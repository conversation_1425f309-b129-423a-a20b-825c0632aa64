import {MigrationInterface, QueryRunner} from "typeorm";

export class addChangeSignatureToken1734422148735 implements MigrationInterface {
    name = 'addChangeSignatureToken1734422148735'

    public async up(queryRunner: QueryRunner): Promise<void> {        
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`changeSignatureToken\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`changeSignatureToken\``);    
    }

}
