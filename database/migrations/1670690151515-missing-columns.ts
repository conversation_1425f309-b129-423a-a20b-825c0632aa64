import {MigrationInterface, QueryRunner} from "typeorm";

export class missingColumns1670690151515 implements MigrationInterface {
    name = 'missingColumns1670690151515'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`tasks_medias\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`taskId\` int UNSIGNED NOT NULL, \`name\` text NOT NULL, \`fileUrl\` text NOT NULL, \`type\` text NOT NULL, <PERSON>IMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`project_carousels\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`name\` text NOT NULL, \`fileUrl\` text NOT NULL, \`type\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`task_copies\` (\`tasksId\` int UNSIGNED NOT NULL, \`projectUsersId\` int UNSIGNED NOT NULL, INDEX \`IDX_454a6d517bec8f7bc9f3ac5bce\` (\`tasksId\`), INDEX \`IDX_131badeff0bdf2037b69fe5358\` (\`projectUsersId\`), PRIMARY KEY (\`tasksId\`, \`projectUsersId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`annotId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`fileUrlCover\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`metTownId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`fcmToken\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Pending', 'Unsent', 'Sent', 'InReview', 'Rejected', 'Approved') NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_899b525cb33db67d69a7d3db81f\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_326c6fe9b379c137c26be4521c1\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`userId\` \`userId\` int UNSIGNED NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`projectDocumentId\` \`projectDocumentId\` int UNSIGNED NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`type\` \`type\` enum ('Include', 'Exclude') NOT NULL DEFAULT 'Include'`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Pending', 'Draft', 'Submitted', 'InReview', 'Rejected', 'Approved') NULL`);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc') NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\` (\`projectDocumentId\`, \`signById\`)`);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` ADD CONSTRAINT \`FK_37f9cbf5cff18f329fee8b12a7b\` FOREIGN KEY (\`taskId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD CONSTRAINT \`FK_d670df4239252dbd7c04ad6e848\` FOREIGN KEY (\`groupId\`) REFERENCES \`project_groups\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_899b525cb33db67d69a7d3db81f\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_326c6fe9b379c137c26be4521c1\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_carousels\` ADD CONSTRAINT \`FK_0ff9671647b6ebff497cb8ea09f\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`task_copies\` ADD CONSTRAINT \`FK_454a6d517bec8f7bc9f3ac5bce9\` FOREIGN KEY (\`tasksId\`) REFERENCES \`tasks\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`task_copies\` ADD CONSTRAINT \`FK_131badeff0bdf2037b69fe53586\` FOREIGN KEY (\`projectUsersId\`) REFERENCES \`project_users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`task_copies\` DROP FOREIGN KEY \`FK_131badeff0bdf2037b69fe53586\``);
        await queryRunner.query(`ALTER TABLE \`task_copies\` DROP FOREIGN KEY \`FK_454a6d517bec8f7bc9f3ac5bce9\``);
        await queryRunner.query(`ALTER TABLE \`project_carousels\` DROP FOREIGN KEY \`FK_0ff9671647b6ebff497cb8ea09f\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_326c6fe9b379c137c26be4521c1\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_899b525cb33db67d69a7d3db81f\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP FOREIGN KEY \`FK_d670df4239252dbd7c04ad6e848\``);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` DROP FOREIGN KEY \`FK_37f9cbf5cff18f329fee8b12a7b\``);
        await queryRunner.query(`DROP INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\``);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete', 'Assigned', 'Unassigned') NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`status\` \`status\` enum ('Draft', 'Submitted', 'InReview', 'Rejected', 'Approved') NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`type\` \`type\` enum ('Include', 'Exclude') NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`projectDocumentId\` \`projectDocumentId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` CHANGE \`userId\` \`userId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_326c6fe9b379c137c26be4521c1\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_899b525cb33db67d69a7d3db81f\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` CHANGE \`status\` \`status\` enum ('Unsent', 'Sent', 'InReview', 'Rejected', 'Approved') NULL DEFAULT 'Unsent'`);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`fcmToken\``);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`metTownId\``);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`fileUrlCover\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`annotId\``);
        await queryRunner.query(`DROP INDEX \`IDX_131badeff0bdf2037b69fe5358\` ON \`task_copies\``);
        await queryRunner.query(`DROP INDEX \`IDX_454a6d517bec8f7bc9f3ac5bce\` ON \`task_copies\``);
        await queryRunner.query(`DROP TABLE \`task_copies\``);
        await queryRunner.query(`DROP TABLE \`project_carousels\``);
        await queryRunner.query(`DROP TABLE \`tasks_medias\``);
    }

}
