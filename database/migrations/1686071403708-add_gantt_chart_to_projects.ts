import {MigrationInterface, QueryRunner} from "typeorm";

export class addGanttChartToProjects1686071403708 implements MigrationInterface {
    name = 'addGanttChartToProjects1686071403708'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`ganttChartUrl\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'GanttChart') NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph', 'Gantt<PERSON>hart') NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph') NULL`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` CHANGE \`category\` \`category\` enum ('ProjectDocument', 'WorkProgramme', 'Correspondence', 'AllForm', 'StandardForm', 'Photo', 'TwoDDrawings', 'BIMDrawings', 'SCurveGraph') NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`ganttChartUrl\``);
    }

}
