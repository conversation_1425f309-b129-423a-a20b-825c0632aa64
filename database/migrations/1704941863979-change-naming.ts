import { MigrationInterface, QueryRunner } from 'typeorm';

export class changeNaming1704941863979 implements MigrationInterface {
  name = 'changeNaming1704941863979';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`companies\` CHANGE \`avatar\` \`logoUrl\` varchar(255) CHARACTER SET "utf8" COLLATE "utf8_general_ci" NULL`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`companies\` CHANGE \`logoUrl\` \`avatar\` varchar(255) CHARACTER SET "utf8" COLLATE "utf8_general_ci" NULL`
    );
  }
}
