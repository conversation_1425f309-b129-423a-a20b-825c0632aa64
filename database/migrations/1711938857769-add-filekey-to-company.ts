import {MigrationInterface, QueryRunner} from "typeorm";

export class addFilekeyToCompany1711938857769 implements MigrationInterface {
  name = 'addFilekeyToCompany1711938857769'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`companies\`
      ADD \`logoKey\` text NULL,
      ADD \`stampKey\` text NULL,
      ADD \`avatarKey\` text NULL`
    );

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE companies
      SET logoKey = REGEXP_REPLACE(logoUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      stampKey = REGEXP_REPLACE(stampUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      avatarKey = REGEXP_REPLACE(avatar, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`companies\`
      DROP COLUMN \`logoKey\`,
      DROP COLUMN \`stampKey\`,
      DROP COLUMN \`avatarKey\`
    `);
  }
}
