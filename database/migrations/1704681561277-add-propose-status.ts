import { MigrationInterface, QueryRunner } from 'typeorm';

export class addProposeStatus1704681561277 implements MigrationInterface {
  name = 'addProposeStatus1704681561277';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD \`proposedStatus\` enum ('Open', 'InProgress', 'Hold', 'Completed', 'Overdue') NULL AFTER \`status\``
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`proposedStatus\``);
  }
}
