import {MigrationInterface, QueryRunner} from "typeorm";

export class addDeletedIdsToProjectDocuments1702549885444 implements MigrationInterface {
    name = 'addDeletedIdsToProjectDocuments1702549885444'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`deletedChildrenIds\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`deletedChildrenIds\``);
    }

}
