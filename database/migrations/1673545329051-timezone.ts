import {MigrationInterface, QueryRunner} from "typeorm";

export class timezone1673545329051 implements MigrationInterface {
    name = 'timezone1673545329051'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`timezones\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(200) NOT NULL, \`value\` varchar(200) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`timezoneId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD CONSTRAINT \`FK_f2e8e78ec7c6dabe5fc330bd199\` FOREIGN KEY (\`timezoneId\`) REFERENCES \`timezones\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_f2e8e78ec7c6dabe5fc330bd199\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`timezoneId\``);
        await queryRunner.query(`DROP TABLE \`timezones\``);
    }

}
