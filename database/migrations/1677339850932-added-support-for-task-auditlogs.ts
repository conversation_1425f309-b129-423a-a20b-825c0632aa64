import {MigrationInterface, QueryRunner} from "typeorm";

export class addedSupportForTaskAuditlogs1677339850932 implements MigrationInterface {
    name = 'addedSupportForTaskAuditlogs1677339850932'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD \`userId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD CONSTRAINT \`FK_0ce2a3af7f4e6eb24863ae4813b\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP FOREIGN KEY \`FK_0ce2a3af7f4e6eb24863ae4813b\``);
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP COLUMN \`userId\``);
    }

}
