import { MigrationInterface, QueryRunner } from 'typeorm';

export class addCascadeForTreeEntity1659082733156 implements MigrationInterface {
  name = 'addCascadeForTreeEntity1659082733156';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_4f2f3042dc38d34833a6b26833f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_4f2f3042dc38d34833a6b26833f\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` DROP FOREIGN KEY \`FK_4f2f3042dc38d34833a6b26833f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD CONSTRAINT \`FK_4f2f3042dc38d34833a6b26833f\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
