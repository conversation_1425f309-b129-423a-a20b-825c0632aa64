import { MigrationInterface, QueryRunner } from 'typeorm';

export class projectDocumentCommentAndRequestForSignature1658929242162 implements MigrationInterface {
  name = 'projectDocumentCommentAndRequestForSignature1658929242162';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`request_for_signatures\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectDocumentId\` int UNSIGNED NOT NULL, \`ownerId\` int UNSIGNED NOT NULL, \`signById\` int UNSIGNED NOT NULL, \`status\` enum ('Pending', 'Approved') NOT NULL, UNIQUE INDEX \`REL_c438ced26f5f26eb4b69c88c30\` (\`ownerId\`), UNIQUE INDEX \`REL_5a0ab72abe4603bac2cc19a2f2\` (\`signById\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`project_document_comments\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectDocumentId\` int UNSIGNED NOT NULL, \`userId\` int UNSIGNED NULL, \`message\` text NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_6a6938e9fc292c860a4fd596f11\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_c438ced26f5f26eb4b69c88c30d\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_5a0ab72abe4603bac2cc19a2f2b\` FOREIGN KEY (\`signById\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_comments\` ADD CONSTRAINT \`FK_3004aabae7e6e38b71a397add37\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_comments\` ADD CONSTRAINT \`FK_15e932cff4144a306f3d8dc133e\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_document_comments\` DROP FOREIGN KEY \`FK_15e932cff4144a306f3d8dc133e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_document_comments\` DROP FOREIGN KEY \`FK_3004aabae7e6e38b71a397add37\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_5a0ab72abe4603bac2cc19a2f2b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_c438ced26f5f26eb4b69c88c30d\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_6a6938e9fc292c860a4fd596f11\``,
    );
    await queryRunner.query(`DROP TABLE \`project_document_comments\``);
    await queryRunner.query(`DROP INDEX \`REL_5a0ab72abe4603bac2cc19a2f2\` ON \`request_for_signatures\``);
    await queryRunner.query(`DROP INDEX \`REL_c438ced26f5f26eb4b69c88c30\` ON \`request_for_signatures\``);
    await queryRunner.query(`DROP TABLE \`request_for_signatures\``);
  }
}
