import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendProjectDocumentsDefaultForFileChannel1664873217581 implements MigrationInterface {
  name = 'ammendProjectDocumentsDefaultForFileChannel1664873217581';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileChannel\` \`fileChannel\` enum ('OBS', 'FORGE') NOT NULL DEFAULT 'OBS'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` CHANGE \`fileChannel\` \`fileChannel\` enum ('OBS', 'FORGE') NOT NULL`,
    );
  }
}
