import {MigrationInterface, QueryRunner} from "typeorm";

export class fixProjectScheduleNaming1697081198602 implements MigrationInterface {
    name = 'fixProjectScheduleNaming1697081198602'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_links\` ADD CONSTRAINT \`FK_4a53c56a179b951bf829af91e41\` FOREIGN KEY (\`projectScheduleId\`) REFERENCES \`project_schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD CONSTRAINT \`FK_36dd88942908cd866ca31631a1d\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD CONSTRAINT \`FK_ffbd5e32da624c2e816ba5756d6\` FOREIGN KEY (\`projectScheduleId\`) REFERENCES \`project_schedules\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP FOREIGN KEY \`FK_ffbd5e32da624c2e816ba5756d6\``);
        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP FOREIGN KEY \`FK_36dd88942908cd866ca31631a1d\``);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` DROP FOREIGN KEY \`FK_4a53c56a179b951bf829af91e41\``);
    }

}
