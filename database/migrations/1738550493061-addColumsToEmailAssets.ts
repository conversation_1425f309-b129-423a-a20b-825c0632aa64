import { MigrationInterface, QueryRunner } from 'typeorm';

export class addColumsToEmailAssets1738550493061 implements MigrationInterface {
  name = 'addColumsToEmailAssets1738550493061';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`contentId\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`rawMetadata\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`name\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`fileExtension\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`assetType\``);
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`assetType\` enum ('Inline', 'Attachment') NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`assetType\``);
    await queryRunner.query(`ALTER TABLE \`email_assets\` ADD \`assetType\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`fileExtension\``);
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`name\``);
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`rawMetadata\``);
    await queryRunner.query(`ALTER TABLE \`email_assets\` DROP COLUMN \`contentId\``);
  }
}
