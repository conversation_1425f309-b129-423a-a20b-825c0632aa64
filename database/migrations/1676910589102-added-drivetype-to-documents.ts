import {MigrationInterface, QueryRunner} from "typeorm";

export class addedDrivetypeToDocuments1676910589102 implements MigrationInterface {
    name = 'addedDrivetypeToDocuments1676910589102'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`driveType\` enum ('Shared', 'Personal') NOT NULL DEFAULT 'Shared'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`driveType\``);
    }

}
