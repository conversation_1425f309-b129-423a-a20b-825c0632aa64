import {MigrationInterface, QueryRunner} from "typeorm";

export class salesOrderStoreJson1680155894203 implements MigrationInterface {
    name = 'salesOrderStoreJson1680155894203'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`data\` text NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`data\``);
    }

}
