import {MigrationInterface, QueryRunner} from "typeorm";

export class addVersionname1701759360087 implements MigrationInterface {
    name = 'addVersionname1701759360087'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`versionName\` varchar(255) NULL`);

    }

    public async down(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`versionName\``);
       
    }

}
