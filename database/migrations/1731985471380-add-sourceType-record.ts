import {MigrationInterface, QueryRunner} from "typeorm";

export class addSourceTypeRecord1731985471380 implements MigrationInterface {
    name = 'addSourceTypeRecord1731985471380'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`admins\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`company_subscriptions\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`companies\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`contact_companies\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`contacts\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_teams\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`form_categories\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`checklist_items\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`checklists\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_groups\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`task_comments\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_group_users\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_ccs\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`workspace_document\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`drawing_link_attachment\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`drawing_links\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_users\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`event_assignee\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`events\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`user_fcm_tokens\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`timezones\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`user_settings\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_invitations\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_carousels\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`project_overviews\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`bim_asset\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`change_logs_mobile\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`mobile_versions\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
        await queryRunner.query(`ALTER TABLE \`notification_transactions\` ADD \`recordSource\` enum ('OfflineApp', 'Web') NOT NULL DEFAULT 'Web'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`mobile_versions\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`change_logs_mobile\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`bim_asset\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_overviews\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_carousels\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`user_settings\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`file_logs\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`timezones\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`user_fcm_tokens\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`event_assignee\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_users\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`schedules_links\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`schedule_comments\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`drawing_links\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`drawing_link_comment\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`drawing_link_attachment\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`drawing_revisions\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_document\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_ccs\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_attachments\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_photos\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_groups\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`workspace_group_users\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`tasks_medias\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`tasks_attachments\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`task_comments\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_groups\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`checklists\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`checklist_items\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`form_categories\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`project_teams\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`contacts\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`contact_companies\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`companies\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`company_subscriptions\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`sales_orders\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`admins\` DROP COLUMN \`recordSource\``);
        await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`recordSource\``);
    }

}
