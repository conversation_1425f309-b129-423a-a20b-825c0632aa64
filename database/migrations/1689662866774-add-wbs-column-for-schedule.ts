import { MigrationInterface, QueryRunner } from 'typeorm';

export class addWbsColumnForSchedule1689662866774 implements MigrationInterface {
  name = 'addWbsColumnForSchedule1689662866774';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`wbs\` text NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`wbs\``);
  }
}
