import {MigrationInterface, QueryRunner} from "typeorm";

export class addFileKeyToProjects1711595117299 implements MigrationInterface {
  name = 'addFileKeyToProjects1711595117299'

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`projects\`
      ADD \`fileProgressKey\` text NULL,
      ADD \`ganttChartKey\` text NULL,
      ADD \`fileProgressFinanceKey\` text NULL`
    );

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE projects
      SET fileProgressKey = REGEXP_REPLACE(fileUrlProgress, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      ganttChartKey = REGEXP_REPLACE(ganttChartUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      fileProgressFinanceKey = REGEXP_REPLACE(fileUrlProgressFinance, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`projects\`
      DROP COLUMN \`fileProgressKey\`,
      DROP COLUMN \`gantChartKey\`,
      DROP COLUMN \`fileProgressFinanceKey\`
    `);
  }
}
