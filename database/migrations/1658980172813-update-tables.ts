import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTables1658980172813 implements MigrationInterface {
  name = 'updateTables1658980172813';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_5a0ab72abe4603bac2cc19a2f2b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_c438ced26f5f26eb4b69c88c30d\``,
    );
    await queryRunner.query(`DROP INDEX \`REL_c438ced26f5f26eb4b69c88c30\` ON \`request_for_signatures\``);
    await queryRunner.query(`DROP INDEX \`REL_5a0ab72abe4603bac2cc19a2f2\` ON \`request_for_signatures\``);
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_c438ced26f5f26eb4b69c88c30d\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_5a0ab72abe4603bac2cc19a2f2b\` FOREIGN KEY (\`signById\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_5a0ab72abe4603bac2cc19a2f2\` ON \`request_for_signatures\` (\`signById\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_c438ced26f5f26eb4b69c88c30\` ON \`request_for_signatures\` (\`ownerId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_c438ced26f5f26eb4b69c88c30d\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_5a0ab72abe4603bac2cc19a2f2b\` FOREIGN KEY (\`signById\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_5a0ab72abe4603bac2cc19a2f2b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_c438ced26f5f26eb4b69c88c30d\``,
    );
  }
}
