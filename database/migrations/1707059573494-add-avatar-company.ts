import {MigrationInterface, QueryRunner} from "typeorm";

export class addAvatarCompany1707059573494 implements MigrationInterface {
    name = 'addAvatarCompany1707059573494'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`companies\` ADD \`avatar\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`companies\` DROP COLUMN \`avatar\``);
    }

}
