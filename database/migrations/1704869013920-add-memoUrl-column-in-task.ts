import {MigrationInterface, QueryRunner} from "typeorm";

export class addMemoUrlColumnInTask1704869013920 implements MigrationInterface {
    name = 'addMemoUrlColumnInTask1704869013920'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`memoUrl\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`memoUrl\``);
    }

}
