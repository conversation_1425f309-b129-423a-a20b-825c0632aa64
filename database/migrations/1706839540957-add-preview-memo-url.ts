import {MigrationInterface, QueryRunner} from "typeorm";

export class addPreviewMemoUrl1706839540957 implements MigrationInterface {
    name = 'addPreviewMemoUrl1706839540957'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`previewMemoUrl\` text NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`previewMemoUrl\``);
       
    }

}
