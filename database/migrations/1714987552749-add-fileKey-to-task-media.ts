import { MigrationInterface, QueryRunner } from 'typeorm';

export class addFileKeyToTaskMedia1714987552749 implements MigrationInterface {
  name = 'addFileKeyToTaskMedia1714987552749';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // for projects table
    await queryRunner.query(`ALTER TABLE \`tasks_medias\` ADD \`fileKey\` text NULL`);

    // get all project and update fileKey with cleaned fileUrl
    await queryRunner.query(`UPDATE tasks_medias
      SET fileKey = REGEXP_REPLACE(fileUrl, '^https://(bina-dev|bina-prod)-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/', ''),
      updatedAt = updatedAt
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks_medias\` DROP COLUMN \`fileKey\``);
  }
}
