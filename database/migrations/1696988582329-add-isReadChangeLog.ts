import {MigrationInterface, QueryRunner} from "typeorm";

export class addIsReadChangeLog1696988582329 implements MigrationInterface {
    name = 'addIsReadChangeLog1696988582329'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`isReadChangeLog\` tinyint NOT NULL DEFAULT 0`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`isReadChangeLog\``);
        
    }

}
