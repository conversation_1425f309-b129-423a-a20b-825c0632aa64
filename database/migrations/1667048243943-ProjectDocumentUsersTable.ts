import {MigrationInterface, QueryRunner} from "typeorm";

export class ProjectDocumentUsersTable1667048243943 implements MigrationInterface {
    name = 'ProjectDocumentUsersTable1667048243943'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`project_document_users\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`type\` enum ('Include', 'Exclude') NOT NULL, \`userId\` int UNSIGNED NULL, \`projectDocumentId\` int UNSIGNED NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`taskCode\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`taskCode\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_899b525cb33db67d69a7d3db81f\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` ADD CONSTRAINT \`FK_326c6fe9b379c137c26be4521c1\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_326c6fe9b379c137c26be4521c1\``);
        await queryRunner.query(`ALTER TABLE \`project_document_users\` DROP FOREIGN KEY \`FK_899b525cb33db67d69a7d3db81f\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`taskCode\``);
        await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`taskCode\` varchar(255) NOT NULL`);
        await queryRunner.query(`DROP TABLE \`project_document_users\``);
    }

}
