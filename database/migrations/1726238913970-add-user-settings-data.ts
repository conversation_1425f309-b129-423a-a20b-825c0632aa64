import { MigrationInterface, QueryRunner } from 'typeorm';

export class addUserSettingsData1726238913970 implements MigrationInterface {
  name = 'addUserSettingsData1726238913970';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, retrieve all users from the `UserEntity` table.
    const users = await queryRunner.query(`SELECT id FROM users`);

    // For each user, check if they already have a corresponding settings row.
    for (const user of users) {
      const userSettings = await queryRunner.query(
        `SELECT * FROM user_settings WHERE userId = ?`,  // Use ? placeholder for MySQL
        [user.id]
      );

      // If no user settings exist, insert a new row with default values.
      if (userSettings.length === 0) {
        await queryRunner.query(
          `INSERT INTO user_settings (userId, productTourCompleted, createdAt, updatedAt)
           VALUES (?, ?, NOW(), NOW())`,  // Use ? placeholders for MySQL
          [user.id, '[]']
        );
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Optionally, if you want to reverse this migration, you can delete the added rows.
    await queryRunner.query(`DELETE FROM user_settings`);
  }
}
