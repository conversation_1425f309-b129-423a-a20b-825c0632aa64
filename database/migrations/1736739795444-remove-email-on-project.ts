import { MigrationInterface, QueryRunner } from 'typeorm';

export class removeEmailAccountFromProjects1736739795444 implements MigrationInterface {
  name = 'removeEmailAccountFromProjects1736739795444';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the foreign key constraint on `contacts_email` referencing `projects`
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` DROP FOREIGN KEY \`FK_2db1809eaf00100778e6c3b5e18\``
    );

    // Drop the `projectId` column from `contacts_email` table
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` DROP COLUMN \`projectId\``
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add the `projectId` column back to `contacts_email`
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` ADD \`projectId\` int UNSIGNED NOT NULL`
    );

    // Recreate the foreign key constraint between `contacts_email` and `projects`
    await queryRunner.query(
      `ALTER TABLE \`contacts_email\` ADD CONSTRAINT \`FK_2db1809eaf00100778e6c3b5e18\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }
}
