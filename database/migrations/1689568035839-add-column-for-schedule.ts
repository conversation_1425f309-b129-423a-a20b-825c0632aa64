import {MigrationInterface, QueryRunner} from "typeorm";

export class addColumnForSchedule1689568035839 implements MigrationInterface {
    name = 'addColumnForSchedule1689568035839'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`status\` enum ('Pending', 'InProgress', 'Hold', 'Completed', 'Delay') NOT NULL DEFAULT 'Pending'`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`proposedStatus\` enum ('Pending', 'InProgress', 'Hold', 'Completed', 'Delay') NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`proposedStatusDate\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`proposedUserId\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`daysDelayed\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`notes\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`notes\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`daysDelayed\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`proposedUserId\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`proposedStatusDate\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`proposedStatus\``);
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`status\``);
    }

}
