import {MigrationInterface, QueryRunner} from "typeorm";

export class addErrorLog1725518915969 implements MigrationInterface {
    name = 'addErrorLog1725518915969'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_schedules\` ADD \`errorLog\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_schedules\` DROP COLUMN \`errorLog\``);
    }

}
