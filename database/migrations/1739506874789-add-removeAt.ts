import {MigrationInterface, QueryRunner} from "typeorm";

export class addRemoveAt1739506874789 implements MigrationInterface {
    name = 'addRemoveAt1739506874789'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`contacts_email\` ADD \`removedAt\` datetime NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
  
        await queryRunner.query(`ALTER TABLE \`contacts_email\` DROP COLUMN \`removedAt\``);
        
    }

}
