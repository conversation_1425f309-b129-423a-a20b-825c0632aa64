import {MigrationInterface, QueryRunner} from "typeorm";

export class changeCidNullable1740731314728 implements MigrationInterface {
    name = 'changeCidNullable1740731314728'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`email_assets\` CHANGE \`contentId\` \`contentId\` varchar(255) NULL`);    
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`email_assets\` CHANGE \`contentId\` \`contentId\` varchar(255) NOT NULL`);      
    }

}
