import { MigrationInterface, QueryRunner } from 'typeorm';

export class removeDescriptionFieldOnEvent1659428693308 implements MigrationInterface {
  name = 'removeDescriptionFieldOnEvent1659428693308';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`events\` DROP COLUMN \`description\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete', 'Assigned', 'Unassigned') NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`events\` ADD \`description\` varchar(255) NOT NULL`);
  }
}
