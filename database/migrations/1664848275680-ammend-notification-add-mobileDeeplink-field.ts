import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendNotificationAddMobileDeeplinkField1664848275680 implements MigrationInterface {
  name = 'ammendNotificationAddMobileDeeplinkField1664848275680';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` ADD \`mobileDeeplink\` text NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`notification_transactions\` DROP COLUMN \`mobileDeeplink\``);
  }
}
