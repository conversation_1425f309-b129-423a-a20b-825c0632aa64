import { MigrationInterface, QueryRunner } from 'typeorm';

export class addSubmittedColumnInProjectDocs1692389003366 implements MigrationInterface {
  name = 'addSubmittedColumnInProjectDocs1692389003366';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`submittedAt\` datetime NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`submittedAt\``);
  }
}
