import {MigrationInterface, QueryRunner} from "typeorm";

export class addTwoFA1723779552400 implements MigrationInterface {
    name = 'addTwoFA1723779552400'

    public async up(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`otpToken\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`otpExpiredAt\` timestamp NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`enableTwoFA\` tinyint NULL DEFAULT 0`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`enableTwoFA\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`otpExpiredAt\``);
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`otpToken\``);
        
    }

}
