import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateTable1657724398043 implements MigrationInterface {
  name = 'updateTable1657724398043';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`project_invitations\` (\`id\` int UNSIGNED NOT NULL AUTO_INCREMENT, \`createdBy\` int UNSIGNED NULL, \`updatedBy\` int UNSIGNED NULL, \`deletedBy\` int UNSIGNED NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`projectId\` int UNSIGNED NOT NULL, \`isAccepted\` tinyint NOT NULL DEFAULT 0, \`invitationRef\` varchar(255) NOT NULL, \`expireAt\` datetime NOT NULL, \`email\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(`ALTER TABLE \`project_users\` ADD \`addedBy\` int UNSIGNED NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE \`contact_companies\` ADD CONSTRAINT \`FK_5a1ae0d846bb4f84540293a32ea\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` ADD CONSTRAINT \`FK_55c8a93fcb3af8430c930e3a26a\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` DROP FOREIGN KEY \`FK_55c8a93fcb3af8430c930e3a26a\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`contact_companies\` DROP FOREIGN KEY \`FK_5a1ae0d846bb4f84540293a32ea\``,
    );
    await queryRunner.query(`ALTER TABLE \`project_users\` DROP COLUMN \`addedBy\``);
    await queryRunner.query(`DROP TABLE \`project_invitations\``);
  }
}
