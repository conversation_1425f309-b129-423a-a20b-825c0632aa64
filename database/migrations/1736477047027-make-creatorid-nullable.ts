import {MigrationInterface, QueryRunner} from "typeorm";

export class makeCreatoridNullable1736477047027 implements MigrationInterface {
    name = 'makeCreatoridNullable1736477047027'

    public async up(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`creatorId\` \`creatorId\` int UNSIGNED NULL`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
   
        await queryRunner.query(`ALTER TABLE \`emails\` CHANGE \`creatorId\` \`creatorId\` int UNSIGNED NOT NULL`);
       
    }

}
