import {MigrationInterface, QueryRunner} from "typeorm";

export class addRelationsForDocuments1689568591002 implements MigrationInterface {
    name = 'addRelationsForDocuments1689568591002'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`schedules_documents\` (\`schedulesId\` int UNSIGNED NOT NULL, \`projectDocumentsId\` int UNSIGNED NOT NULL, INDEX \`IDX_ebe48da76c86497f28f1a46336\` (\`schedulesId\`), INDEX \`IDX_f14b9deb347b2bd323d66bb495\` (\`projectDocumentsId\`), PRIMARY KEY (\`schedulesId\`, \`projectDocumentsId\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`schedules_documents\` ADD CONSTRAINT \`FK_ebe48da76c86497f28f1a463367\` FOREIGN KEY (\`schedulesId\`) REFERENCES \`schedules\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE \`schedules_documents\` ADD CONSTRAINT \`FK_f14b9deb347b2bd323d66bb4952\` FOREIGN KEY (\`projectDocumentsId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_documents\` DROP FOREIGN KEY \`FK_f14b9deb347b2bd323d66bb4952\``);
        await queryRunner.query(`ALTER TABLE \`schedules_documents\` DROP FOREIGN KEY \`FK_ebe48da76c86497f28f1a463367\``);
        await queryRunner.query(`DROP TABLE \`schedules_documents\``);
    }

}
