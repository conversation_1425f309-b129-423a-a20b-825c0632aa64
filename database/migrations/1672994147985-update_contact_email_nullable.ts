import {MigrationInterface, QueryRunner} from "typeorm";

export class updateContactEmailNullable1672994147985 implements MigrationInterface {
    name = 'updateContactEmailNullable1672994147985'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`email\` \`email\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`contacts\` CHANGE \`email\` \`email\` varchar(255) NOT NULL`);
    }

}
