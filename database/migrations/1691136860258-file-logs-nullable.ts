import {MigrationInterface, QueryRunner} from "typeorm";

export class fileLogsNullable1691136860258 implements MigrationInterface {
    name = 'fileLogsNullable1691136860258'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`file_logs\` DROP FOREIGN KEY \`FK_c01515766e85696afd74bad2fbe\``);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`user_id\` \`user_id\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`user_name\` \`user_name\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`before_file_name\` \`before_file_name\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`before_file_size\` \`before_file_size\` float NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` ADD CONSTRAINT \`FK_c01515766e85696afd74bad2fbe\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`file_logs\` DROP FOREIGN KEY \`FK_c01515766e85696afd74bad2fbe\``);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`before_file_size\` \`before_file_size\` float NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`before_file_name\` \`before_file_name\` text CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`user_name\` \`user_name\` text CHARACTER SET "utf8mb3" COLLATE "utf8mb3_general_ci" NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` CHANGE \`user_id\` \`user_id\` int UNSIGNED NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`file_logs\` ADD CONSTRAINT \`FK_c01515766e85696afd74bad2fbe\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
