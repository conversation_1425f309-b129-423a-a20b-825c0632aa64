import { MigrationInterface, QueryRunner } from 'typeorm';

export class addContentFieldInAuditLog1658396217820 implements MigrationInterface {
  name = 'addContentFieldInAuditLog1658396217820';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`content\` varchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`content\``);
  }
}
