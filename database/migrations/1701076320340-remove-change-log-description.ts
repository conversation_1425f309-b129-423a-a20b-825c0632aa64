import {MigrationInterface, QueryRunner} from "typeorm";

export class removeChangeLogDescription1701076320340 implements MigrationInterface {
    name = 'removeChangeLogDescription1701076320340'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` DROP COLUMN \`descriptions\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`change_logs\` ADD \`descriptions\` longtext NULL`);
    }

}
