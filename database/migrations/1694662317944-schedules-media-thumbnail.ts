import {MigrationInterface, QueryRunner} from "typeorm";

export class schedulesMediaThumbnail1694662317944 implements MigrationInterface {
    name = 'schedulesMediaThumbnail1694662317944'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` ADD \`videoThumbnail\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules_medias\` DROP COLUMN \`videoThumbnail\``);
    }

}
