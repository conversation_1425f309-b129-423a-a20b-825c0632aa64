import { MigrationInterface, QueryRunner } from 'typeorm';

export class addProjectTitleFieldOnProjectInvitation1659355021837 implements MigrationInterface {
  name = 'addProjectTitleFieldOnProjectInvitation1659355021837';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_invitations\` ADD \`projectTitle\` varchar(255) NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`projectTitle\``);
  }
}
