import { MigrationInterface, QueryRunner } from 'typeorm';

export class addAllowEmailCorrespondence1736751575515 implements MigrationInterface {
  name = 'addAllowEmailCorrespondence1736751575515';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`subscription_packages\` ADD \`allowEmailCorrespondence\` tinyint NOT NULL DEFAULT 0`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowEmailCorrespondence\``);
  }
}
