import {MigrationInterface, QueryRunner} from "typeorm";

export class addObsUrlToProjectDocuments1700744481125 implements MigrationInterface {
    name = 'addObsUrlToProjectDocuments1700744481125'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`obsUrl\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`obsUrl\``);
    }

}
