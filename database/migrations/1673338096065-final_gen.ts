import {MigrationInterface, QueryRunner} from "typeorm";

export class finalGen1673338096065 implements MigrationInterface {
    name = 'finalGen1673338096065'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_6a6938e9fc292c860a4fd596f11\``,);
        await queryRunner.query(`DROP INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\``);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_6a6938e9fc292c860a4fd596f11\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` DROP FOREIGN KEY \`FK_6a6938e9fc292c860a4fd596f11\``);
        await queryRunner.query(`ALTER TABLE \`request_for_signatures\` ADD CONSTRAINT \`FK_6a6938e9fc292c860a4fd596f11\` FOREIGN KEY (\`projectDocumentId\`) REFERENCES \`project_documents\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_76f02d3fc70d1c913860dbec14\` ON \`request_for_signatures\` (\`projectDocumentId\`, \`signById\`)`);
    }

}
