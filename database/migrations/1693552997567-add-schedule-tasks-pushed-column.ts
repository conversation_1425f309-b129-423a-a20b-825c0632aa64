import {MigrationInterface, QueryRunner} from "typeorm";

export class addScheduleTasksPushedColumn1693552997567 implements MigrationInterface {
    name = 'addScheduleTasksPushedColumn1693552997567'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`isTaskPushed\` tinyint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`isTaskPushed\``);
    }

}
