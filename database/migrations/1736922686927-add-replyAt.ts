import {MigrationInterface, QueryRunner} from "typeorm";

export class addReplyAt1736922686927 implements MigrationInterface {
    name = 'addReplyAt1736922686927'

    public async up(queryRunner: QueryRunner): Promise<void> {
    
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`replyAt\` datetime NULL`);
        
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`replyAt\``);
       
    }

}
