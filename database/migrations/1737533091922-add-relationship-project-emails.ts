import { MigrationInterface, QueryRunner } from "typeorm";

export class addRelationshipProjectEmails1737533091922 implements MigrationInterface {
    name = 'addRelationshipProjectEmails1737533091922';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`emails\` ADD \`projectId\` int UNSIGNED NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`emails\` ADD CONSTRAINT \`FK_a972184cbaf2f164f4383a85f01\` FOREIGN KEY (\`projectId\`) REFERENCES \`projects\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`emails\` DROP FOREIGN KEY \`FK_a972184cbaf2f164f4383a85f01\``);
        await queryRunner.query(`ALTER TABLE \`emails\` DROP COLUMN \`projectId\``);
    }
}
