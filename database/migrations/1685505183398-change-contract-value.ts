import {MigrationInterface, QueryRunner} from "typeorm";

export class changeContractValue1685505183398 implements MigrationInterface {
    name = 'changeContractValue1685505183398'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` CHANGE \`contractorValue\` \`contractValue\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`contractValue\``);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`contractValue\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`contractValue\``);
        await queryRunner.query(`ALTER TABLE \`projects\` ADD \`contractValue\` int UNSIGNED NULL`);
        await queryRunner.query(`ALTER TABLE \`projects\` CHANGE \`contractValue\` \`contractorValue\` int UNSIGNED NULL`);
    }

}
