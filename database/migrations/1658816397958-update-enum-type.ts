import { MigrationInterface, QueryRunner } from 'typeorm';

export class updateEnumType1658816397958 implements MigrationInterface {
  name = 'updateEnumType1658816397958';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_users\` DROP COLUMN \`role\``);
    await queryRunner.query(
      `ALTER TABLE \`project_users\` ADD \`role\` enum ('Can edit', 'Can view') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`module\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD \`module\` enum ('Project', 'Task', 'Comment') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`action\``);
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` ADD \`action\` enum ('create', 'update', 'delete') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`role\``);
    await queryRunner.query(
      `ALTER TABLE \`project_invitations\` ADD \`role\` enum ('Can edit', 'Can view') NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`projects\` ADD \`status\` enum ('In Progress', 'Completed', 'Overdue') NOT NULL DEFAULT 'In Progress'`,
    );
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD \`status\` enum ('In Progress', 'Completed', 'Overdue') NOT NULL DEFAULT 'In Progress'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD \`status\` varchar(255) NOT NULL DEFAULT 'In Progress'`,
    );
    await queryRunner.query(`ALTER TABLE \`projects\` DROP COLUMN \`status\``);
    await queryRunner.query(
      `ALTER TABLE \`projects\` ADD \`status\` varchar(255) NOT NULL DEFAULT 'In Progress'`,
    );
    await queryRunner.query(`ALTER TABLE \`project_invitations\` DROP COLUMN \`role\``);
    await queryRunner.query(`ALTER TABLE \`project_invitations\` ADD \`role\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`action\``);
    await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`action\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`audit_logs\` DROP COLUMN \`module\``);
    await queryRunner.query(`ALTER TABLE \`audit_logs\` ADD \`module\` varchar(255) NOT NULL`);
    await queryRunner.query(`ALTER TABLE \`project_users\` DROP COLUMN \`role\``);
    await queryRunner.query(`ALTER TABLE \`project_users\` ADD \`role\` varchar(255) NOT NULL`);
  }
}
