import { MigrationInterface, QueryRunner } from 'typeorm';

export class addDocumentCommentInAuditLogModuleTypeEnum1660548409645 implements MigrationInterface {
  name = 'addDocumentCommentInAuditLogModuleTypeEnum1660548409645';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment', 'DocumentComment') NOT NULL`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`audit_logs\` CHANGE \`module\` \`module\` enum ('Project', 'Task', 'TaskComment') NOT NULL`,
    );
  }
}
