import { MigrationInterface, QueryRunner } from 'typeorm';

export class addChildrenColumnForSchedules1695693992838 implements MigrationInterface {
  name = 'addChildrenColumnForSchedules1695693992838';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` ADD \`children\` text NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`schedules\` DROP COLUMN \`children\``);
  }
}
