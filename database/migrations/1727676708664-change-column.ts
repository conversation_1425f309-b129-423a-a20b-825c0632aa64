import {MigrationInterface, QueryRunner} from "typeorm";

export class changeColumn1727676708664 implements MigrationInterface {
    name = 'changeColumn1727676708664'

    public async up(queryRunner: QueryRunner): Promise<void> {
     
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` DROP COLUMN \`commentType\``);
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` ADD \`commentType\` enum ('Workspace', 'BIM') NULL DEFAULT 'Workspace'`);
       
    }

    public async down(queryRunner: QueryRunner): Promise<void> {

        await queryRunner.query(`ALTER TABLE \`project_document_comments\` DROP COLUMN \`commentType\``);
        await queryRunner.query(`ALTER TABLE \`project_document_comments\` ADD \`commentType\` text CHARACTER SET "latin1" COLLATE "latin1_swedish_ci" NULL`);
       
    }

}
