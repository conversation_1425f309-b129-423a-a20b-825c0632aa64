import { MigrationInterface, QueryRunner } from 'typeorm';

export class addColumnFileChannelInProjectDocuments1662012540783 implements MigrationInterface {
  name = 'addColumnFileChannelInProjectDocuments1662012540783';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`project_documents\` ADD \`fileChannel\` enum ('OBS', 'FORGE') NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`fileChannel\``);
  }
}
