import {MigrationInterface, QueryRunner} from "typeorm";

export class addedMoreLogTypes1676975741824 implements MigrationInterface {
    name = 'addedMoreLogTypes1676975741824'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddMarkup', 'AddAttachment', 'AddPhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc') NOT NULL`);
    }

}
