import { MigrationInterface, QueryRunner } from 'typeorm';

export class ammendTasksOwnerIdColumn1664531417258 implements MigrationInterface {
  name = 'ammendTasksOwnerIdColumn1664531417258';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` ADD \`ownerId\` int UNSIGNED NULL`);
    await queryRunner.query(
      `ALTER TABLE \`tasks\` ADD CONSTRAINT \`FK_607de52438268ab19a406349427\` FOREIGN KEY (\`ownerId\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP FOREIGN KEY \`FK_607de52438268ab19a406349427\``);
    await queryRunner.query(`ALTER TABLE \`tasks\` DROP COLUMN \`ownerId\``);
  }
}
