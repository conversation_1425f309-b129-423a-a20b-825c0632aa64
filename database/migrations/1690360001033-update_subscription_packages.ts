import {MigrationInterface, QueryRunner} from "typeorm";

export class updateSubscriptionPackages1690360001033 implements MigrationInterface {
    name = 'updateSubscriptionPackages1690360001033'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowScheduleChart\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowScheduleActivity\` tinyint NOT NULL DEFAULT 1`);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` ADD \`allowDashboard\` tinyint NOT NULL DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowDashboard\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowScheduleActivity\``);
        await queryRunner.query(`ALTER TABLE \`subscription_packages\` DROP COLUMN \`allowScheduleChart\``);
    }

}
