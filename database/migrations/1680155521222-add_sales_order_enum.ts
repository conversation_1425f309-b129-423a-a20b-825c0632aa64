import {MigrationInterface, QueryRunner} from "typeorm";

export class addSalesOrderEnum1680155521222 implements MigrationInterface {
    name = 'addSalesOrderEnum1680155521222'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`status\` \`status\` enum ('pending', 'paid', 'declined', 'inprogress', 'cancelled') NOT NULL DEFAULT 'pending'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`sales_orders\` CHANGE \`status\` \`status\` enum ('pending', 'paid', 'declined', 'cancelled') NOT NULL DEFAULT 'pending'`);
    }

}
