import {MigrationInterface, QueryRunner} from "typeorm";

export class addNoteActionToAuditlog1694597724326 implements MigrationInterface {
    name = 'addNoteActionToAuditlog1694597724326'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddRole', 'AddNote', 'DeleteNote', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined', 'Shared', 'AddProposed', 'ValidateProposed', 'RejectProposed') NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`audit_logs\` CHANGE \`action\` \`action\` enum ('Add', 'AddRole', 'AddMarkup', 'AddAttachment', 'RemoveAttachment', 'LinkedDocument', 'UnlinkedDocument', 'AddPhoto', 'RemovePhoto', 'Create', 'Update', 'Delete', 'Assigned', 'Unassigned', 'Assigned Cc', 'Unassigned Cc', 'RequestApproval', 'UpdateStatus', 'UpdateGroup', 'Invited', 'Joined', 'Shared') CHARACTER SET "utf8" COLLATE "utf8_general_ci" NOT NULL`);
    }

}
