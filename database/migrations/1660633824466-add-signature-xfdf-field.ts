import { MigrationInterface, QueryRunner } from 'typeorm';

export class addSignatureXfdfField1660633824466 implements MigrationInterface {
  name = 'addSignatureXfdfField1660633824466';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` ADD \`signatureXfdf\` longtext NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`project_documents\` DROP COLUMN \`signatureXfdf\``);
  }
}
