import {
  FileSystemType,
  CategoryType,
  RoleTypeEnum,
  CorrespondenceType,
  AuditLogModuleType,
  AuditLogActionType,
  ProjectDocumentStatus,
  RequestForSignatureStatus,
} from '@constants';
import { UserEntity } from '@modules/user/entity/user.entity';
import { CompanyEntity } from '@modules/company/entity/company.entity';
import { Connection } from 'typeorm';
import { Factory, Seeder } from 'typeorm-seeding';
import { ProjectUserEntity } from '@modules/project-user/entity/project-user.entity';
import { ProjectEntity } from '@modules/project/entity/project.entity';
import { ProjectUserRoleType } from '@constants';
import { ProjectDocumentEntity } from '@modules/project-document/entity/project-document.entity';
import { TaskEntity } from '@modules/task/entity/task.entity';
import moment from 'moment';
import { AuditLogEntity } from '@modules/audit-log/entity/audit-log.entity';
import { NotificationTransactionEntity } from '@modules/notification-transaction/entity/notification-transaction.entity';
import { WorkspaceGroupEntity } from '@modules/workspace-group/entity/workspace-group.entity';
import { ProjectCarouselEntity } from '@modules/project-carousel/entity/project-carousel.entity';
import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { ProjectGroupEntity } from '@modules/project-group/entity/project-group.entity';
import { RequestForSignatureEntity } from '@modules/request-for-signature/entity/request-for-signature.entity';
import { log } from 'console';

export default class CreateRootUser implements Seeder {
  public async run(_factory: Factory, connection: Connection): Promise<any> {
    const userData = await connection.getRepository(UserEntity).find();
    if (userData.length > 0) {
      return;
    }
    // Seed Users
    const user = new UserEntity();
    user.name = 'dev123';
    user.email = '<EMAIL>';
    user.password = '1234';
    user.phoneNo = '+601111017225';
    user.avatar = '123';
    user.position = 'manager';
    user.isEmailVerified = true;
    user.isFirstTimeLogin = false;
    user.type = RoleTypeEnum.User;
    try {
      await connection.getRepository(UserEntity).insert(user);
    } catch (_e) { }

    const user2 = new UserEntity();
    user2.name = 'binaTester';
    user2.email = '<EMAIL>';
    user2.password = '1234';
    user2.phoneNo = '+60123456789';
    user2.avatar = '123';
    user2.position = 'CEO';
    user2.isEmailVerified = true;
    user2.isFirstTimeLogin = false;
    user2.type = RoleTypeEnum.User;
    try {
      await connection.getRepository(UserEntity).insert(user2);
    } catch (_e) { }

    const user3 = new UserEntity();
    user3.name = 'binaUser';
    user3.email = '<EMAIL>';
    user3.password = '1234';
    user3.phoneNo = '+60123456789';
    user3.avatar = '123';
    user3.position = 'CTO';
    user3.isEmailVerified = true;
    user3.isFirstTimeLogin = false;
    user3.type = RoleTypeEnum.User;
    try {
      await connection.getRepository(UserEntity).insert(user3);
    } catch (_e) { }

    // Seed for companies
    const company = new CompanyEntity();
    company.ownerId = user2.id;
    company.name = 'Bina';
    company.logoUrl = '123';
    try {
      await connection.getRepository(CompanyEntity).insert(company);
    } catch (_e) { }

    // Update users' companyId
    try {
      await connection.getRepository(UserEntity).merge(user, { companyId: company.id });
      await connection.getRepository(UserEntity).save(user);
    } catch (_e) { }

    try {
      await connection.getRepository(UserEntity).merge(user2, { companyId: company.id });
      await connection.getRepository(UserEntity).save(user2);
    } catch (_e) { }

    try {
      await connection.getRepository(UserEntity).merge(user3, { companyId: company.id });
      await connection.getRepository(UserEntity).save(user3);
    } catch (_e) { }

    // Seed for company subscription
    const companySubscription = new CompanySubscriptionEntity();
    companySubscription.companyId = company.id;
    companySubscription.subscriptionPackageId = 1;
    companySubscription.subscriptionEndDate = new Date(new Date().setFullYear(new Date().getFullYear() + 99));
    try {
      await connection.getRepository(CompanySubscriptionEntity).insert(companySubscription);
    } catch (_e) { }

    // Seed for projects
    const project1 = new ProjectEntity();
    project1.userId = user2.id;
    project1.companyId = company.id;
    project1.title = 'Project 1';
    project1.description = 'For testing purposes';
    project1.refNo = 'BNP001';
    try {
      await connection.getRepository(ProjectEntity).insert(project1);
    } catch (_e) { }

    const project2 = new ProjectEntity();
    project2.userId = user.id;
    project2.companyId = company.id;
    project2.title = 'Bina Project 2';
    project2.description = 'To test project 2';
    project2.refNo = 'bnp02';
    try {
      await connection.getRepository(ProjectEntity).insert(project2);
    } catch (_e) { }

    const projectCarousel1 = new ProjectCarouselEntity();
    projectCarousel1.name = 'cover.jpeg';
    projectCarousel1.fileUrl = 'https://bina-prod-storage.obs.my-kualalumpur-1.alphaedge.tmone.com.my/8jRWMoZQLcgHbS_-WhatsApp-Image-2023-06-26-at-8.11.51-AM.jpeg';
    projectCarousel1.projectId = project1.id;
    projectCarousel1.type = 'jpeg';
    try {
      await connection.getRepository(ProjectCarouselEntity).insert(projectCarousel1);
    } catch (_e) { }

    // Seed for task
    const task1 = new TaskEntity();
    task1.title = 'Task 1';
    task1.projectId = project1.id;
    task1.ownerId = user.id;
    task1.description = 'Task 1 description';
    task1.dueDate = moment().add(20, 'days').toDate();
    task1.taskCode = 1;
    try {
      await connection.getRepository(TaskEntity).insert(task1);
    } catch (_e) { }

    const task2 = new TaskEntity();
    task2.title = 'Task 2';
    task2.ownerId = user.id;
    task2.projectId = project1.id;
    task2.description = 'Task 2 description';
    task2.dueDate = moment().add(1, 'month').toDate();
    task2.taskCode = 1;
    try {
      await connection.getRepository(TaskEntity).insert(task2);
    } catch (_e) { }

    // Seed for project users
    const projectUser1 = new ProjectUserEntity();
    projectUser1.userId = user2.id;
    projectUser1.projectId = project1.id;
    projectUser1.role = ProjectUserRoleType.ProjectOwner;
    projectUser1.addedBy = user2.id;
    try {
      await connection.getRepository(ProjectUserEntity).insert(projectUser1);
    } catch (_e) { }

    const projectUser2 = new ProjectUserEntity();
    projectUser2.userId = user.id;
    projectUser2.projectId = project1.id;
    projectUser2.role = ProjectUserRoleType.CanEdit;
    projectUser2.addedBy = user2.id;
    try {
      await connection.getRepository(ProjectUserEntity).insert(projectUser2);
    } catch (_e) { }

    const projectUser3 = new ProjectUserEntity();
    projectUser3.userId = user.id;
    projectUser3.projectId = project2.id;
    projectUser3.role = ProjectUserRoleType.ProjectOwner;
    projectUser3.addedBy = user.id;
    try {
      await connection.getRepository(ProjectUserEntity).insert(projectUser3);
    } catch (_e) { }

    const projectUser4 = new ProjectUserEntity();
    projectUser4.userId = user3.id;
    projectUser4.projectId = project1.id;
    projectUser4.role = ProjectUserRoleType.CanEdit;
    projectUser4.addedBy = user2.id;
    try {
      await connection.getRepository(ProjectUserEntity).insert(projectUser4);
    } catch (_e) { }

    // Seed for Project Group - Ungroup Tasks
    const projectGroup1 = new ProjectGroupEntity();
    projectGroup1.projectId = project1.id;
    projectGroup1.title = 'Ungroup Tasks';
    try {
      await connection.getRepository(ProjectGroupEntity).save(projectGroup1);
    } catch (_e) { }

    // Seed for task assignee
    task1.assignees = Promise.resolve([projectUser1, projectUser2]);
    task1.groupId = projectGroup1.id;
    try {
      await connection.getRepository(TaskEntity).save(task1);
    } catch (_e) { }

    task2.assignees = Promise.resolve([projectUser2]);
    task2.groupId = projectGroup1.id;
    try {
      await connection.getRepository(TaskEntity).save(task2);
    } catch (_e) { }

    // Seed for Workspace Group - Ungroup Documents
    const workspaceGroup1 = new WorkspaceGroupEntity();
    workspaceGroup1.projectId = project1.id;
    workspaceGroup1.name = 'Ungroup Documents';
    try {
      await connection.getRepository(WorkspaceGroupEntity).save(workspaceGroup1);
    } catch (_e) { }

    const workspaceGroup2 = new WorkspaceGroupEntity();
    workspaceGroup2.projectId = project2.id;
    workspaceGroup2.name = 'Ungroup Documents';
    try {
      await connection.getRepository(WorkspaceGroupEntity).save(workspaceGroup2);
    } catch (_e) { }

    const workspaceGroup3 = new WorkspaceGroupEntity();
    workspaceGroup3.projectId = project1.id;
    workspaceGroup3.name = 'Site Diary';
    try {
      await connection.getRepository(WorkspaceGroupEntity).save(workspaceGroup3);
    } catch (_e) { }

    // Seed for Project Document Folders (Correspondence)
    const correspondenceInP1 = new ProjectDocumentEntity();
    correspondenceInP1.projectId = project1.id;
    correspondenceInP1.addedBy = project1.userId;
    correspondenceInP1.name = CorrespondenceType.CorrespondenceIn;
    correspondenceInP1.fileSystemType = FileSystemType.Folder;
    correspondenceInP1.category = CategoryType.Correspondence;
    correspondenceInP1.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(correspondenceInP1);
    } catch (_e) { }

    const correspondenceOutP1 = new ProjectDocumentEntity();
    correspondenceOutP1.projectId = project1.id;
    correspondenceOutP1.addedBy = project1.userId;
    correspondenceOutP1.name = CorrespondenceType.CorrespondenceOut;
    correspondenceOutP1.fileSystemType = FileSystemType.Folder;
    correspondenceOutP1.category = CategoryType.Correspondence;
    correspondenceOutP1.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(correspondenceOutP1);
    } catch (_e) { }

    const correspondenceInP2 = new ProjectDocumentEntity();
    correspondenceInP2.projectId = project2.id;
    correspondenceInP2.addedBy = project2.userId;
    correspondenceInP2.name = CorrespondenceType.CorrespondenceIn;
    correspondenceInP2.fileSystemType = FileSystemType.Folder;
    correspondenceInP2.category = CategoryType.Correspondence;
    correspondenceInP2.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(correspondenceInP2);
    } catch (_e) { }

    const correspondenceOutP2 = new ProjectDocumentEntity();
    correspondenceOutP2.projectId = project2.id;
    correspondenceOutP2.addedBy = project2.userId;
    correspondenceOutP2.name = CorrespondenceType.CorrespondenceOut;
    correspondenceOutP2.fileSystemType = FileSystemType.Folder;
    correspondenceOutP2.category = CategoryType.Correspondence;
    correspondenceOutP2.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(correspondenceOutP2);
    } catch (_e) { }

    // Seed for Project Document Folders
    const projectDocumentFolder1 = new ProjectDocumentEntity();
    projectDocumentFolder1.name = 'Folder A';
    projectDocumentFolder1.fileSystemType = FileSystemType.Folder;
    projectDocumentFolder1.projectId = project1.id;
    projectDocumentFolder1.addedBy = user2.id;
    projectDocumentFolder1.category = CategoryType.ProjectDocument;
    projectDocumentFolder1.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocumentFolder1);
    } catch (_e) { }

    const projectDocumentFolder2 = new ProjectDocumentEntity();
    projectDocumentFolder2.name = 'Folder A.1';
    projectDocumentFolder2.fileSystemType = FileSystemType.Folder;
    projectDocumentFolder2.projectId = project1.id;
    projectDocumentFolder2.projectDocumentId = projectDocumentFolder1.id;
    projectDocumentFolder2.parentFolder = projectDocumentFolder1;
    projectDocumentFolder2.addedBy = user2.id;
    projectDocumentFolder2.category = CategoryType.ProjectDocument;
    projectDocumentFolder2.type = 'folder';
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocumentFolder2);
    } catch (_e) { }

    // Seed for Project Documents
    const projectDocument1 = new ProjectDocumentEntity();
    projectDocument1.name = 'Example PDF 1';
    projectDocument1.fileSystemType = FileSystemType.Document;
    projectDocument1.projectId = project1.id;
    projectDocument1.fileUrl = `https://pdftron.s3.amazonaws.com/downloads/pl/demo-annotated.pdf`;
    projectDocument1.addedBy = user.id;
    projectDocument1.fileSize = 5;
    projectDocument1.type = 'pdf';
    projectDocument1.category = CategoryType.ProjectDocument;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument1);
    } catch (_e) { }

    const projectDocument2 = new ProjectDocumentEntity();
    projectDocument2.name = 'Example PDF 2';
    projectDocument2.fileSystemType = FileSystemType.Document;
    projectDocument2.projectId = project1.id;
    projectDocument2.projectDocumentId = projectDocumentFolder1.id;
    projectDocument2.parentFolder = projectDocumentFolder1;
    projectDocument2.fileUrl = `https://msnlabs.com/img/resume-sample.pdf`;
    projectDocument2.addedBy = user2.id;
    projectDocument2.fileSize = 0.5;
    projectDocument2.type = 'pdf';
    projectDocument2.category = CategoryType.ProjectDocument;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument2);
    } catch (_e) { }

    const projectDocument3 = new ProjectDocumentEntity();
    projectDocument3.name = 'Example PDF 3';
    projectDocument3.fileSystemType = FileSystemType.Document;
    projectDocument3.projectId = project1.id;
    projectDocument3.projectDocumentId = projectDocumentFolder2.id;
    projectDocument3.parentFolder = projectDocumentFolder2;
    projectDocument3.fileUrl = `https://www.entnet.org/wp-content/uploads/2021/04/Instructions-for-Adding-Your-Logo-2.pdf`;
    projectDocument3.addedBy = user2.id;
    projectDocument3.fileSize = 2;
    projectDocument3.type = 'pdf';
    projectDocument3.category = CategoryType.ProjectDocument;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument3);
    } catch (_e) { }

    const projectDocument4 = new ProjectDocumentEntity();
    projectDocument4.name = 'Example PDF 4';
    projectDocument4.fileSystemType = FileSystemType.Document;
    projectDocument4.projectId = project1.id;
    projectDocument4.projectDocumentId = projectDocumentFolder2.id;
    projectDocument4.parentFolder = projectDocumentFolder2;
    projectDocument4.fileUrl = `https://www.entnet.org/wp-content/uploads/2021/04/Instructions-for-Adding-Your-Logo-2.pdf`;
    projectDocument4.addedBy = user2.id;
    projectDocument4.fileSize = 2;
    projectDocument4.type = 'pdf';
    projectDocument4.category = CategoryType.AllForm;
    projectDocument4.status = ProjectDocumentStatus?.Submitted;
    projectDocument4.workspaceGroupId = workspaceGroup1.id;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument4);
    } catch (_e) { }

    const projectDocument5 = new ProjectDocumentEntity();
    projectDocument5.name = 'Example PDF 5';
    projectDocument5.fileSystemType = FileSystemType.Document;
    projectDocument5.projectId = project1.id;
    projectDocument5.projectDocumentId = projectDocumentFolder2.id;
    projectDocument5.parentFolder = projectDocumentFolder2;
    projectDocument5.fileUrl = `https://www.entnet.org/wp-content/uploads/2021/04/Instructions-for-Adding-Your-Logo-2.pdf`;
    projectDocument5.addedBy = user2.id;
    projectDocument5.fileSize = 2;
    projectDocument5.type = 'pdf';
    projectDocument5.category = CategoryType.AllForm;
    projectDocument5.status = ProjectDocumentStatus?.Submitted;
    projectDocument5.workspaceGroupId = workspaceGroup1.id;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument5);
    } catch (_e) { }

    const projectDocument6 = new ProjectDocumentEntity();
    projectDocument6.name = 'Example Image';
    projectDocument6.fileSystemType = FileSystemType.Document;
    projectDocument6.projectId = project1.id;
    projectDocument6.projectDocumentId = projectDocumentFolder2.id;
    projectDocument6.parentFolder = projectDocumentFolder2;
    projectDocument6.fileUrl = `https://www.w3schools.com/w3css/img_lights.jpg`;
    projectDocument6.addedBy = user2.id;
    projectDocument6.fileSize = 2;
    projectDocument6.type = 'jpg';
    projectDocument6.category = CategoryType.Photo;
    try {
      await connection.getRepository(ProjectDocumentEntity).save(projectDocument6);
    } catch (_e) { }

    const requestForApproval1 = new RequestForSignatureEntity();
    requestForApproval1.projectDocumentId = projectDocument4.id;
    requestForApproval1.createdBy = user2.id;
    requestForApproval1.status = RequestForSignatureStatus?.Sent;
    requestForApproval1.signById = user.id;
    requestForApproval1.ownerId = user2.id;
    try {
      await connection.getRepository(RequestForSignatureEntity).save(requestForApproval1);
    } catch (_e) { }

    const requestForApproval2 = new RequestForSignatureEntity();
    requestForApproval2.projectDocumentId = projectDocument5.id;
    requestForApproval2.createdBy = user2.id;
    requestForApproval2.status = RequestForSignatureStatus?.Sent;
    requestForApproval2.signById = user.id;
    requestForApproval2.ownerId = user2.id;
    try {
      await connection.getRepository(RequestForSignatureEntity).save(requestForApproval2);
    } catch (_e) { }

    // Seed for Audit Log
    const auditLog1 = new AuditLogEntity();
    auditLog1.userId = user.id;
    auditLog1.projectId = project1.id;
    auditLog1.module = AuditLogModuleType.Project;
    auditLog1.action = AuditLogActionType.Create;
    try {
      await connection.getRepository(AuditLogEntity).insert(auditLog1);
    } catch (_e) { }

    const auditLog2 = new AuditLogEntity();
    auditLog2.userId = user.id;
    auditLog2.projectId = project2.id;
    auditLog2.taskId = task1.id;
    auditLog2.module = AuditLogModuleType.Task;
    auditLog2.action = AuditLogActionType.Create;
    auditLog2.content = `${task1.title}\n${task1.description}`;
    try {
      await connection.getRepository(AuditLogEntity).insert(auditLog2);
    } catch (_e) { }

    // Seed for Notification
    const notification1 = new NotificationTransactionEntity();
    notification1.userId = user.id;
    notification1.title = project1.title;
    notification1.actorId = user2.id;
    notification1.actionType = `tagged you in a comment`;
    notification1.actionName = `${task1.title}`;
    notification1.deeplink = `/tasks?taskId=${task1.id}`;
    notification1.mobileDeeplink = `edit-task/:${task1.id}`;
    try {
      await connection.getRepository(NotificationTransactionEntity).insert(notification1);
    } catch (_e) { }

    const notification2 = new NotificationTransactionEntity();
    notification2.userId = user.id;
    notification2.title = project1.title;
    notification2.actorId = user2.id;
    notification2.actionType = `uploaded a document`;
    notification2.actionName = `${projectDocument1.name}`;
    notification2.deeplink = `/cloud-docs/project-document?dirId=${projectDocument1.projectDocumentId}&documentId=${projectDocument1.id}&documentDetails=${projectDocument1.id}&projectId=${projectDocument1.projectId}`;
    notification2.mobileDeeplink = `cloud-doc/0/:${projectDocument1.id}`;
    try {
      await connection.getRepository(NotificationTransactionEntity).insert(notification2);
    } catch (_e) { }
  }
}
