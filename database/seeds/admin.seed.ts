import { RoleTypeEnum } from '@constants';
import { AdminEntity } from 'src/modules/admin/entity/admin.entity';
import { Connection } from 'typeorm';
import { Factory, Seeder } from 'typeorm-seeding';

export default class CreateRootAdmin implements Seeder {
  public async run(_factory: Factory, connection: Connection): Promise<any> {
    const adminData = await connection.getRepository(AdminEntity).find();
    if (adminData.length > 0) {
      return;
    }

    const adminUser = await connection.getRepository(AdminEntity).findOne({
      email: "<EMAIL>"
    });
    
    const admin = new AdminEntity();
    admin.name = 'admin';
    admin.email = '<EMAIL>';
    admin.password = '1234';
    admin.type = RoleTypeEnum.Admin;
    try {
      await connection.getRepository(AdminEntity).insert(admin);
    } catch (_e) { }
  }
}