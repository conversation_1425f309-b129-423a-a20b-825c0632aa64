import { CompanySubscriptionEntity } from '@modules/company-subscription/entity/company-subscription.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { Connection } from 'typeorm';
import { Factory, Seeder } from 'typeorm-seeding';

export default class CreatePackages implements Seeder {
  public async run(_factory: Factory, connection: Connection): Promise<any> {
    const subscriptionData = await connection.getRepository(SubscriptionPackageEntity).find();
    if (subscriptionData.length > 0) {
      return;
    }

    const subscriptionPackageOne = new SubscriptionPackageEntity();
    subscriptionPackageOne.title = "1- Month Plan";
    subscriptionPackageOne.description = "You can cancel anytime.";
    subscriptionPackageOne.amount = 850;
    subscriptionPackageOne.availableDuration = 1;
    try {
      await connection.getRepository(SubscriptionPackageEntity).insert(subscriptionPackageOne);
    } catch (_e) { }

    const subscriptionPackageTwo = new SubscriptionPackageEntity();
    subscriptionPackageTwo.title = "6 Month Plan";
    subscriptionPackageTwo.description = "Billed every 6 months.\nYou can cancel anytime.";
    subscriptionPackageTwo.amount = 3300;
    subscriptionPackageTwo.availableDuration = 6;
    try {
      await connection.getRepository(SubscriptionPackageEntity).insert(subscriptionPackageTwo);
    } catch (_e) { }

    const subscriptionPackageThree = new SubscriptionPackageEntity();
    subscriptionPackageThree.title = "Yearly Plan";
    subscriptionPackageThree.description = "Billed annually.\nYou can cancel anytime.";
    subscriptionPackageThree.amount = 4200;
    subscriptionPackageThree.availableDuration = 12;
    try {
      await connection.getRepository(SubscriptionPackageEntity).insert(subscriptionPackageThree);
    } catch (_e) { }


    const subscriptionOne = new CompanySubscriptionEntity()
    subscriptionOne.companyId = 1;
    subscriptionOne.subscriptionPackageId = 1;
    subscriptionOne.subscriptionEndDate =
      new Date(new Date().setFullYear(new Date().getFullYear() + 99));
    try {
      await connection.getRepository(CompanySubscriptionEntity).insert(subscriptionOne);
    } catch (_e) { }
  }
}
