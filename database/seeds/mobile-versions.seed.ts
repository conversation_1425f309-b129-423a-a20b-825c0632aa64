import { MobileVersionEntity } from '@modules/mobile-version/entities/mobile-version.entity';
import { SubscriptionPackageEntity } from '@modules/subscription-package/entity/subscription-package.entity';
import { Connection } from 'typeorm';
import { Factory, Seeder } from 'typeorm-seeding';

export default class CreateMobileVersions implements Seeder {
  public async run(_factory: Factory, connection: Connection): Promise<any> {
    const versions = await connection.getRepository(MobileVersionEntity).find();
    if (versions.length > 0) {
      return;
    }

    const android = new MobileVersionEntity();
    android.platformName = "android";
    android.buildCode = "1";
    android.versionCode = "1.0.0";
    try {
      await connection.getRepository(MobileVersionEntity).insert(android);
    } catch (_e) { }

    const ios = new MobileVersionEntity();
    ios.platformName = "ios";
    ios.buildCode = "1";
    ios.versionCode = "1.0.0";
    try {
      await connection.getRepository(MobileVersionEntity).insert(ios);
    } catch (_e) { }

    const web = new MobileVersionEntity();
    web.platformName = "web";
    web.buildCode = "1";
    web.versionCode = "1.0.0";
    try {
      await connection.getRepository(MobileVersionEntity).insert(web);
    } catch (_e) { }
  }
}
