version: '3.7'

services:
  bina-be:
    image: bina-be
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '${BINA_BE_FORWARD_PORT}:${PORT}'
    environment:
      - NODE_OPTIONS=${NODE_OPTIONS}
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - APP_NAME=${APP_NAME}
      - JWT_SECRET=${JWT_SECRET}
      - APP_URI=${APP_URI}
      - CORS_VALIDATION=${CORS_VALIDATION}
      - CORS_ORIGINS_WHITELIST=${CORS_ORIGINS_WHITELIST}
      - DB_DRIVER=${DB_DRIVER}
      - DB_HOST=${DB_HOST}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_PORT=${DB_PORT}
      - SMS_USERNAME=${SMS_USERNAME}
      - SMS_PASSWORD=${SMS_PASSWORD}
      - SMS_SENDER_ID=${SMS_SENDER_ID}
      - ADMIN_APP_URL=${ADMIN_APP_URL}
      - MAILGUN_API_USER=${MAILGUN_API_USER}
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - MAILGUN_SENDER_EMAIL=${MAILGUN_SENDER_EMAIL}
      - MAILGUN_DOMAIN=${MAILGUN_DOMAIN}
      - AUTODESK_CLIENT_ID=${AUTODESK_CLIENT_ID}
      - AUTODESK_CLIENT_SECRET=${AUTODESK_CLIENT_SECRET}
      - AUTODESK_GRANT_TYPE=${AUTODESK_GRANT_TYPE}
      - AUTODESK_SCOPE=${AUTODESK_SCOPE}
      - AUTODESK_BUCKET=${AUTODESK_BUCKET}
      - TMONE_BUCKET=${TMONE_BUCKET}
      - TMONE_ACCESS_KEY_ID=${TMONE_ACCESS_KEY_ID}
      - TMONE_SECRET_ACCESS_KEY=${TMONE_SECRET_ACCESS_KEY}
      - TMONE_SERVER=${TMONE_SERVER}
      - TMONE_OBS_STORAGE=${TMONE_OBS_STORAGE}
    networks:
      - bina-network

networks:
  bina-network: {}
